<?xml version="1.0" encoding="UTF-8"?>
<!-- 只需配置好 log.dir 和 projectname 属性-->
<configuration debug="false">
	<springProperty scope="context" name="za.deploy.env" source="DEPLOY_ENV"/>
	<property name="za.log.level" value="INFO" />
	<property name="za.log.level.dal" value="DEBUG" />
	<property name="za.log.dir" value="/alidata1/admin/za-lincoln/logs" />
	<property name="project.name" value="za-lincoln" />
	<property name="MONITOR_PATTERN" value="%d [%thread] %-5p [%c] [%F:%L] [trace=%X{trace_id},span=%X{span_id}] - %msg [%X{traceId}] %n"/>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<target>System.out</target>
		<encoder>
			<charset>UTF-8</charset>
			<pattern>%d [%thread] %-5p [%c{50}] [%F:%L] - %msg [%X{traceId}] %n</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>INFO</level>
		</filter>
	</appender>

	<appender name="infoAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${za.log.dir}/${HOSTNAME}_app_${project.name}_lt_info.log</file>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>${za.log.level}</level>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${za.log.dir}/${HOSTNAME}_app_${project.name}_lt_info_%d{yyyy-MM-dd}.log</fileNamePattern>
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		<encoder>
			<charset>UTF-8</charset>
			<pattern>${MONITOR_PATTERN}</pattern>
		</encoder>
	</appender>

	<!-- 异步输出 infoAppender 的日志 -->
	<!-- AsyncAppender并不处理日志，只是将日志缓冲到一个BlockingQueue里面去，并在内部创建一个工作线程从队列头部获取日志，之后将获取的日志
    循环记录到附加的其他appender上去，从而达到不阻塞主线程的效果。因此AsyncAppender仅仅充当事件转发器，必须引用另一个appender来做事。-->
	<appender name="infoAppenderAsync" class="ch.qos.logback.classic.AsyncAppender">
		<!-- 包含调用者信息,默认是false,官方提到如果开启会有性能上的损失,开启用于输出[%F:%L]等信息 -->
		<includeCallerData>true</includeCallerData>
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>2048</queueSize>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref="infoAppender"/>
	</appender>

	<appender name="errorAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${za.log.dir}/${HOSTNAME}_app_${project.name}_lt_error.log</file>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${za.log.dir}/${HOSTNAME}_app_${project.name}_lt_error_%d{yyyy-MM-dd}.log</fileNamePattern>
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		<encoder>
			<charset>UTF-8</charset>
			<pattern>${MONITOR_PATTERN}</pattern>
		</encoder>
	</appender>

	<!-- 异步输出 errorAppender 的日志 -->
	<appender name="errorAppenderAsync" class="ch.qos.logback.classic.AsyncAppender">
		<includeCallerData>true</includeCallerData>
		<discardingThreshold>0</discardingThreshold>
		<queueSize>2048</queueSize>
		<appender-ref ref="errorAppender"/>
	</appender>

	<appender name="sqlRollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>${za.log.level.dal}</level>
		</filter>
		<file>${za.log.dir}/sql.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${za.log.dir}/sql-%d{yyyy-MM-dd}.log</fileNamePattern>
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		<encoder>
			<charset>UTF-8</charset>
			<pattern>${MONITOR_PATTERN}</pattern>
		</encoder>
	</appender>

	<appender name="SQL_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
		<discardingThreshold>0</discardingThreshold>
		<queueSize>2048</queueSize>
		<appender-ref ref="sqlRollingFile"/>
		<includeCallerData>true</includeCallerData>
	</appender>

	<logger name="com.taobao.tddl.common.config.diamond.PreHeatDataHandler">
		<level value="OFF" />
	</logger>

	<logger name="org.apache">
		<level value="INFO" />
	</logger>

	<logger name="org.mybatis">
		<level value="INFO" />
	</logger>

	<logger name="org.springframework">
		<level value="INFO" />
	</logger>

	<logger name="com.taobao">
		<level value="INFO" />
	</logger>

	<logger name="TDDL_SQL_LOG">
		<level value="INFO" />
	</logger>

	<logger name="springfox">
		<level value="INFO" />
	</logger>

	<logger name="com.zhongan.lincoln.dal">
		<level value="${za.log.level.dal}"/>
		<appender-ref ref="SQL_ASYNC"/>
	</logger>

	<logger name="com.zhongan.core.product.dto">
		<level value="WARN" />
	</logger>

	<root>
		<level value="${za.log.level}" />
		<if condition='property("za.deploy.env").contains("dev")'>
			<then>
				<appender-ref ref="STDOUT" />
			</then>
			<else>

				<appender-ref ref="errorAppenderAsync"/>
				<appender-ref ref="infoAppenderAsync"/>
				<appender-ref ref="STDOUT" />
			</else>
		</if>
	</root>
</configuration>