spring:
  main:
    banner-mode: LOG
  profiles:
    active: ${DEPLOY_ENV}
  cloud:
    config:
      name: za-lincoln
      uri: http://msc.zhonganonline.com
      profile: ${DEPLOY_ENV}
      label: ${LABEL:public}

management:
  metrics:
    export:
      prometheus:
        enabled: true
  endpoints:
    web:
      exposure:
        include: ["prometheus", "health"]
      base-path: /
  endpoint:
    metrics:
      enabled: true
    prometheus:
      enabled: true