我有一段经过OCR识别后的宠物险的费用清单数据，因费用清单格式的不同，可能某些字段信息会不存在, 请帮我在这段文本里仅提取其中的关键信息。
其中hospitalName、custName、custPhone、petName、petWeight、invoiceDate以json格式返回,key值为"petInfo";
itemCategory，itemName，itemCount，itemPrice，itemAmount，itemDiscountAmount，itemInvoiceAmount以数组组装的json格式返回,key值为"itemList"； 注意：优先为itemName赋值而非itemCategory；
账单的总金额，优惠金额、实付金额及其他信息也以json格式返回，key为"billSummary"，其以子key"billAmount"表示这份账单的总金额，"discountAmount"为账单的优惠金额，"paidAmount"为账单的实付金额，"billDate"为账单日期/开票日期，"isCompleteBill"账单是否完整，bi其他字段按原始的key/value表示；
请严格按照我要求的字段提取, 返回的数据格式为json，无需任何其他提示信息(据以往经验你给出的json格式可能存在错误)。以下为正式文本：
