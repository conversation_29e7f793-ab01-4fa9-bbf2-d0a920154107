我们自建了一份宠物疾病库，并对宠物疾病做了分类，包括意外、一般疾病、口腔疾病、癌症、结石、无法识别，且无其他分类。
我们对一份宠物病历进行OCR，并提取出疾病的诊断（Diagnosis）并从我们自建的宠物疾病库中基于疾病诊断查询出匹配度较高的疾病名称(optionalDisease)
请基于以上信息，请结合疾病诊断与疾病列表给出列表中匹配度最高的疾病，不需要完全匹配，同类型诊断只给出一个对应的疾病。 请一并给该病历中的诊断的疾病分类。
请直接以json格式返回，无需其他提示符， 其中diseaseName表示疾病名称，只能从疾病列表中取值、diseaseCategory表示疾病分类， matchDiagnosis表示病历中能够匹配的诊断, missMatchDiagnosis表示病历中未匹配的诊断

example1:
    Diagnosis："双肾肿大，胃痛",
    optionalDisease: ["肾肿大","双肾退变","前列腺肥大","双排牙","肾后性肾损伤","急性肾损伤","肾缺失","肾萎缩"],
    response: {"diseaseName":"肾肿大","diseaseCategory":"一般疾病","matchDiagnosis":["双肾肿大"],"missMatchDiagnosis":["胃痛"]}
example2:
    Diagnosis："双腿骨折",
    optionalDisease: ["骨折","腰椎骨折","颅骨骨折","耻骨骨折","坐骨骨折","髌骨骨折","股骨骨折","腕骨骨折"],
    response: {"diseaseName":"骨折","diseaseCategory":"意外","matchDiagnosis":["双腿骨折"],"missMatchDiagnosis":[]}

prompt:
Diagnosis："%s",
optionalDisease: [%s]