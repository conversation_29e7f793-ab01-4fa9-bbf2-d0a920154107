需求背景：
1. 宠物病历结构化数据：
- 宠物病历图片经OCR表格识别得到文本数据
- 文本数据经GPT分析提取得到的JSON格式的结构化数据
- 结构化数据主要包括宠物就医信息与就医是否在保险保障范围内等信息，诸如就诊时间、主诉、诊断、疾病、非责疾病、是否复诊等
2. 保司通过对理赔材料中宠物病历进行OCR及GPT解析得到结构化数据后，再结合保司已有信息，从而达到对宠物就医情况就分析、与保司已有信息做映射、推定同一事故等目的

需提取的关键信息：
1. diseaseMatchResult：病历中诊断与疾病等信息与保司疾病清单的匹配结果 (基于当次宠物病历结构化数据与保司疾病清单信息做比对，给出匹配度最高的疾病，不需要完全匹配，同类型诊断只给出一个对应的疾病)
	- diseaseName （疾病名称，只能保司疾病清单中取值，未匹配成功则返回空）
	- matchDiagnosis （病历结构化数据中能够匹配的疾病或诊断）
	- missMatchDiagnosis （病历结构化数据中能够匹配的疾病或诊断）
2. onceAccidentResult：同一事故或同一疾病推断结果 （基于当次病历结构化数据与历史病历结构化数据做比对分析，推断是否存在因为同一事故或者同一疾病多次就诊的情况。）
	- existSameAccident (是否存在同一事故或同一疾病)
	- nos (与当次病历为同一事故或同一疾病，多次就诊的单号)，
	- reasonOfDetermination（推定为同一事故的原因)

要求与提示：
- 按照指定字段名称及数据类型提取信息，无需额外的提示信息，若某个字段未解析成功则置为null
- 返回的数据格式应为JSON

数据与提示：
OCR+GPT解析提取的结构化宠物病历数据：
- 当次病历结构化数据：
%s
- 历史病历结构化数据（包括病历单号）：
%s
- 保司疾病清单（病历诊断与疾病与保司疾病库匹配度较高的疾病清单）：
%s
