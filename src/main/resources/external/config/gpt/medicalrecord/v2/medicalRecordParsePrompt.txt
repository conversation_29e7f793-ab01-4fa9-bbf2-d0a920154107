病历OCR文本信息：
- 宠物就诊病历数据已通过OCR识别
- 格式不统一，部分字段可能缺失

需提取的关键信息：
1. 基础信息baseInfo：
   - hospitalName：医院名称
   - doctorName：医生名称
   - custName：客户名称
   - custPhone：客户电话
   - petName：宠物名称
   - petAge：宠物年龄
   - recordDate：就诊记录日期
2. 就诊信息visitInfo：
   - chiefComplaint：主诉
   - timeOfOnset：发病时间
   - visitTime：就诊时间
   - diseaseDiagnosis：疾病诊断
   - isFollowupVisit：是否为复诊
   - treatmentPlan：治疗方案
3. 推测信息compositeInfo：
   - monthAge：宠物月龄（Integer类型，基于生日和年龄信息推测）
   - timeOfOnset：具体的发病时间（yyyy-MM-dd、yyyy-MM或yyyy格式, 注意需对包括且不限于"去年"，"几个月前"，"三天前"等相对时间做合理推断，不要求年月日都推断出来）
   - minOnsetDays：最少发病天数（Integer类型，就诊时间与发病时间差值, 若无准确时间则给出合理差值）
   - multipleVisit：是否多次就诊（布尔类型，识别病历中多次就诊的提示信息， 包括且不限于"再次手术"，"复查"，"反复就医"等提示词）
   - existPreviousVisit：是否存在既往就诊（布尔类型，判断依据：此次就诊前有过相关的就诊记录 Tips: 1. 包括且不限于"再次手术"，"复查"，"反复就医"等提示词 2. 不包括后续复查安排 3. 不包括现有病历中的多次就诊, 着重推测是否有相关的历史就诊）
   - diseaseName：疾病名称（基于主诉、诊断、出险经过等信息提取疾病名称)
   - diseaseNameIsValid：疾病名称是否有效（布尔类型，是否有效的标准：疾病部位明确、病理明确，非症状，不能过于笼统宽泛)
   - visitCategory：就医类别（分类："癌症"、"口腔疾病"、"结石疾病"、"一般疾病"、"意外"、"无法识别"。其中"意外"可以理解为因外力导致而非自身疾病的就医，诸如摔伤、车祸、骨折等情况）
   - nonCoveredDiseaseName：非保险条款保障范围内的疾病名称
   - nonCoveredVisit：是否为非保障就医（布尔类型，以下都为非保障就医：1.非保障疾病的就医 2.非保障疾病并发症的就医，诸如分娩或绝育后炎症、体弱等)
   - reasonOfDetermination：推断过程和原因（给出以上每个推测信息的推测过程与原因）

辅助信息参考：
- 客户因就诊申请保险理赔，提供索赔信息如下：
  - 出险时间：%s
  - 出险经过：%s
  - 宠物昵称：%s
- 发病时间推断基准：
  - 优先依据病历中的就诊时间或打印时间
  - 若无具体时间，则以提供的\"%s\"时间为基准
- 非保险保障疾病：
  -  保险条款会约束就诊疾病的类型，
  -  不保障诸如绝育、分娩、体检、洁牙等非疾病就医; 不保障诸如癫痫、眼睑内外翻、髌骨脱位、发育不良等先天性疾病
  -  因非保障就医导致的后续就医也不做保障
  -  包括且不限于以下疾病：%s

要求：
- 按照指定字段名称及数据类型提取信息，无需额外的提示信息，若某个字段未解析成功则置为null
- 返回的数据格式应为JSON

宠物病历OCR正式文本提供如下：
