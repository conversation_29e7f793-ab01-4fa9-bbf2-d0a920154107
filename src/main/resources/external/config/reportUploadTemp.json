{"A": {"upLoadConfig": [{"title": "被保险人证件，护照/身份证正反面", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/sf/1.png"], "attachmentType": "2", "errFlag": false, "min": 3, "max": 7, "textList": [{"text": "投保时所用证件的正反面照片，若被保险人未成年可用出生证或户口本代替。"}]}, {"title": "理赔授权书", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "exampleList": [], "attachmentType": "27", "errFlag": false, "min": 1, "max": 3, "textList": [{"text": "如赔款涉及到委托他人收款，需提供赔款授权相关材料。（请联系我司客服索取）若被保险人为成年人：请下载并请被保险人手工填写“赔款授权书”。"}]}, {"title": "登机牌", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_6.png"], "attachmentType": "6", "errFlag": false, "min": 1, "max": 2, "textList": [{"text": "登机的凭证。"}]}, {"title": "延误证明", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_7.jpg"], "attachmentType": "7", "errFlag": false, "min": 1, "max": 2, "textList": [{"text": "在航空公司官网下载延误或取消证明或者机场开具航班延误或取消的纸质证明。"}]}, {"title": "签证", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_8.jpg"], "attachmentType": "8", "errFlag": false, "min": 1, "max": 2, "textList": [{"text": "签证是一个国家的出入境管理机构（例如移民局或其驻外使领馆），对外国公民表示批准入境所签发的一种文件。"}]}, {"title": "其它资料（如关系证明，结婚证，出生证明，投、被保险人户口本页等，涉及驾驶机动车的，需要提供行驶证、驾驶证）", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "attachmentType": "4", "errFlag": false, "min": 0, "max": 5, "textList": [{"text": "根据索赔指引或保险公司理赔要求提供相应材料，如机票的订单，行程单等。"}]}], "isShoweinvoice": false, "baseConfig": {"giveUpReasonList": [{"name": "第三方已赔付"}, {"name": "金额太小，无需理赔"}, {"name": "其他"}]}}, "B": {"upLoadConfig": [{"title": "被保人身份证正面、反面", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/sf/1.png"], "attachmentType": "2", "errFlag": false, "min": 2, "max": 2, "textList": [{"text": "身份证须提供正反面。"}]}, {"title": "理赔授权书", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "exampleList": [], "attachmentType": "27", "errFlag": false, "min": 1, "max": 3, "textList": [{"text": "如赔款涉及到委托他人收款，需提供赔款授权相关材料。（请联系我司客服索取）若被保险人为成年人：请下载并请被保险人手工填写“赔款授权书”。"}]}, {"title": "公安立案证明", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/jc/3.png"], "attachmentType": "5", "errFlag": false, "min": 1, "max": 5, "textList": [{"text": "由警方提供，常见形式有：立案告知书。"}]}, {"title": "其它资料（诈骗短信、聊天记录、资金流水等截图，涉及驾驶机动车的，需要提供行驶证、驾驶证）", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "attachmentType": "4", "errFlag": false, "min": 0, "max": 5, "textList": [{"text": "诈骗短信及通话记录：通过运营商营业厅或者网站下载打印。"}, {"text": "资料流水：通过银行营业厅或者官网下载打印"}, {"text": "聊天记录：通过聊天软件截图"}]}], "isShoweinvoice": false, "baseConfig": {"giveUpReasonList": [{"name": "第三方已赔付"}, {"name": "金额太小，无需理赔"}, {"name": "其他"}]}}, "C": {"upLoadConfig": [{"title": "被保险人身份证正面、反面（被保险人为未成年人需提供户口本，出生证明，监护人身份证）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/sf/1.png"], "attachmentType": "2", "errFlag": false, "min": 2, "max": 7, "textList": [{"text": "身份证须提供正反面。"}, {"text": "被保险人未成年的需提交监护人身份证明和关系证明（出生证明）。"}, {"text": "常见的关系证明为：被保险人出生证+监护人身份证正反面；被保险人和监护人户口本页。"}]}, {"title": "理赔授权书", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "exampleList": [], "attachmentType": "27", "errFlag": false, "min": 1, "max": 3, "textList": [{"text": "如赔款涉及到委托他人收款，需提供赔款授权相关材料。（请联系我司客服索取）若被保险人为成年人：请下载并请被保险人手工填写“赔款授权书”。"}]}, {"title": "门诊病历/住院病历", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/yw/1.png", "/images/example/yw/5.png"], "attachmentType": "1", "errFlag": false, "min": 1, "max": 20, "textList": [{"text": "门/急诊：可直接向就诊医院索要门/急诊病历，如无手写病历，可索要电子病历打印件。"}, {"text": "住院病历：出院时，医院出具的住院病历，需要提供包括但不限于：病历首页、入院记录、出院小结/记录、病理及其他检查化验单。（需盖章或医生签字，若病历遗失，可前往病案室复印）"}]}, {"title": "医疗发票", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/yw/6.png"], "attachmentType": "3", "errFlag": false, "min": 1, "max": 20, "textList": [{"text": "医院财务开具门诊/住院发票（需盖章有效）"}, {"text": "单独申请住院津贴理赔可仅提供医疗费用发票复印件   "}]}, {"title": "电子票据", "isShowExample": false, "min": 0, "type": "einvoice", "attachmentType": "3", "antfinEinvoiceInfos": []}, {"title": "其他材料（药品清单，分割单、意外事故证明等，涉及驾驶机动车的，需要提供行驶证、驾驶证）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/yw/7.png"], "attachmentType": "4", "errFlag": false, "min": 1, "max": 20, "textList": [{"text": "出院时医院出具的住院费用总清单（住院）或门诊出具的药品清单（门诊）。"}, {"text": "若已在其他机构进行部分报销，须提供理赔分割单原件。"}, {"text": "由公安、交警等第三方出具，意外事故证明文件常见形式如：道路交通事故认定书、工伤事故调查报告等。"}, {"text": "若无第三方介入，本人手写事故证明即可"}]}], "isShoweinvoice": true, "baseConfig": {"giveUpReasonList": [{"name": "第三方已赔付"}, {"name": "金额太小，无需理赔"}, {"name": "其他"}]}}, "A_D": {"upLoadConfig": [{"title": "被保险人身份证正面、反面（被保险人为未成年人需提供户口本，出生证明，监护人身份证）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/sf/1.png"], "attachmentType": "2", "errFlag": false, "min": 2, "max": 7, "textList": [{"text": "身份证须提供正反面。"}, {"text": "被保险人未成年的需提交监护人身份证明和关系证明（出生证明）。"}, {"text": "常见的关系证明为：被保险人出生证+监护人身份证正反面；被保险人和监护人户口本页。"}]}, {"title": "理赔授权书", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "exampleList": [], "attachmentType": "27", "errFlag": false, "min": 1, "max": 3, "textList": [{"text": "如赔款涉及到委托他人收款，需提供赔款授权相关材料。（请联系我司客服索取）若被保险人为成年人：请下载并请被保险人手工填写“赔款授权书”。"}]}, {"title": "伤残鉴定报告", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/yw/jdbg_1.png", "/images/example/yw/jdbg_2.png"], "attachmentType": "64", "errFlag": false, "min": 1, "max": 20, "textList": [{"text": "伤残鉴定为有法医临床鉴定资质的司法鉴定机构，根据保险合同附件《人身保险伤残评定标准及代码》（标准编号为JR/T0083-2013）做出的鉴定报告。"}]}, {"title": "门诊病历/住院病历", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/yw/1.png", "/images/example/yw/5.png"], "attachmentType": "1", "errFlag": false, "min": 1, "max": 20, "textList": [{"text": "门/急诊：可直接向就诊医院索要门/急诊病历，如无手写病历，可索要电子病历打印件。"}, {"text": "住院病历：出院时，医院出具的住院病历，需要提供包括但不限于：病历首页、入院记录、出院小结/记录、病理及其他检查化验单。（需盖章或医生签字，若病历遗失，可前往病案室复印）"}]}, {"title": "医疗发票", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/yw/6.png"], "attachmentType": "3", "errFlag": false, "min": 1, "max": 20, "textList": [{"text": "医院财务开具门诊/住院发票（需盖章有效）"}, {"text": "单独申请住院津贴理赔可仅提供医疗费用发票复印件   "}]}, {"title": "其他材料（药品清单，分割单、意外事故证明等，涉及驾驶机动车的，需要提供行驶证、驾驶证）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/yw/7.png"], "attachmentType": "4", "errFlag": false, "min": 1, "max": 20, "textList": [{"text": "出院时医院出具的住院费用总清单（住院）或门诊出具的药品清单（门诊）。"}, {"text": "若已在其他机构进行部分报销，须提供理赔分割单原件。"}, {"text": "由公安、交警等第三方出具，意外事故证明文件常见形式如：道路交通事故认定书、工伤事故调查报告等。"}, {"text": "若无第三方介入，本人手写事故证明即可"}]}], "isShoweinvoice": true, "baseConfig": {"giveUpReasonList": [{"name": "第三方已赔付"}, {"name": "金额太小，无需理赔"}, {"name": "其他"}]}}, "E": {"upLoadConfig": [{"title": "被保险人身份证正面、反面，护照，或港澳台通行证（被保险人为未成年人需提供户口本，出生证明，监护人身份证）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/sf/1.png"], "attachmentType": "2", "errFlag": false, "min": 2, "max": 8, "textList": [{"text": "投保时所用证件的正反面照片，若被保险人未成年可用出生证或户口本代替。"}]}, {"title": "理赔授权书", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "exampleList": [], "attachmentType": "27", "errFlag": false, "min": 1, "max": 3, "textList": [{"text": "如赔款涉及到委托他人收款，需提供赔款授权相关材料。（请联系我司客服索取）若被保险人为成年人：请下载并请被保险人手工填写“赔款授权书”。"}]}, {"title": "登机牌", "isShowExtraJson": true, "isShowExample": true, "imgList": [], "keyList": [], "extraJsonKey": "flightNumber", "exampleList": ["/images/example/xcx_example_6.png"], "attachmentType": "6", "errFlag": false, "min": 1, "max": 2, "textList": [{"text": "登机的凭证。"}]}, {"title": "延误证明", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_7.jpg"], "attachmentType": "7", "errFlag": false, "min": 0, "max": 2, "textList": [{"text": "在航空公司官网下载延误或取消证明或者机场开具航班延误或取消的纸质证明。"}]}, {"title": "其它资料（如关系证明，结婚证，出生证明，投、被保险人户口本页等，涉及驾驶机动车的，需要提供行驶证、驾驶证）", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "attachmentType": "4", "errFlag": false, "min": 0, "max": 5, "textList": [{"text": "根据索赔指引或保险公司理赔要求提供相应材料，如机票的订单，行程单等。"}]}], "isShoweinvoice": false, "baseConfig": {"giveUpReasonList": [{"name": "第三方已赔付"}, {"name": "金额太小，无需理赔"}, {"name": "其他"}]}}, "F": {"upLoadConfig": [{"title": "被保险人身份证正面、反面，护照，或港澳台通行证（被保险人为未成年人需提供户口本，出生证明，监护人身份证）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/sf/1.png"], "attachmentType": "2", "errFlag": false, "min": 2, "max": 8, "textList": [{"text": "投保时所用证件的正反面照片，若被保险人未成年可用出生证或户口本代替。"}]}, {"title": "理赔授权书", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "exampleList": [], "attachmentType": "27", "errFlag": false, "min": 1, "max": 3, "textList": [{"text": "如赔款涉及到委托他人收款，需提供赔款授权相关材料。（请联系我司客服索取）若被保险人为成年人：请下载并请被保险人手工填写“赔款授权书”。"}]}, {"title": "登机牌", "isShowExtraJson": true, "isShowExample": true, "imgList": [], "keyList": [], "extraJsonKey": "flightNumber", "exampleList": ["/images/example/xcx_example_6.png"], "attachmentType": "6", "errFlag": false, "min": 1, "max": 2, "textList": [{"text": "登机的凭证。"}]}, {"title": "延误证明/取消证明", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_7.jpg"], "attachmentType": "7", "errFlag": false, "min": 0, "max": 2, "textList": [{"text": "在航空公司官网下载延误或取消证明或者机场开具航班延误或取消的纸质证明。"}]}, {"title": "其它资料（如关系证明，结婚证，出生证明，投、被保险人户口本页等，涉及驾驶机动车的，需要提供行驶证、驾驶证）", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "attachmentType": "4", "errFlag": false, "min": 0, "max": 5, "textList": [{"text": "根据索赔指引或保险公司理赔要求提供相应材料，如机票的订单，行程单等。"}]}], "isShoweinvoice": false, "baseConfig": {"giveUpReasonList": [{"name": "第三方已赔付"}, {"name": "金额太小，无需理赔"}, {"name": "其他"}]}}, "G": {"upLoadConfig": [{"title": "被保险人身份证正面、反面", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/sf/1.png"], "attachmentType": "2", "errFlag": false, "min": 2, "max": 7, "textList": [{"text": "身份证须提供正反面。"}]}, {"title": "理赔授权书", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "exampleList": [], "attachmentType": "27", "errFlag": false, "min": 1, "max": 3, "textList": [{"text": "如赔款涉及到委托他人收款，需提供赔款授权相关材料。（请联系我司客服索取）若被保险人为成年人：请下载并请被保险人手工填写“赔款授权书”。"}]}, {"title": "宠物就诊照片（治疗期间拍摄的宠物正脸照片、治疗过程照片、伤口照片等）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_11_1.jpg", "/images/example/xcx_example_11_2.jpg", "/images/example/xcx_example_11_3.jpg"], "attachmentType": "18", "errFlag": false, "min": 1, "max": 10, "textList": [{"text": "治疗期间拍摄的宠物正脸照片。"}, {"text": "治疗过程照片。"}, {"text": "伤口照片。"}]}, {"title": "医疗诊断（宠物病历卡首页、本次门急诊病历页面、住院病历（若有）、抢救报告、手术记录、化验单、药物处方单等）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/yw/1.png", "/images/example/yw/5.png"], "attachmentType": "1", "errFlag": false, "min": 1, "max": 10, "textList": [{"text": "请向就诊医院索取病历，如无手写病历，可索取电子病历打印件（记录有宠物信息、就诊时间、就诊情况、病情和诊断内容的页面）。"}]}, {"title": "医疗发票（发票、费用明细清单）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_13.jpg"], "attachmentType": "3", "errFlag": false, "min": 2, "max": 15, "textList": [{"text": "医院开具的增值税发票、普通发票或定额发票，非收据，非收银小票，发票抬头须为被保人本人全名"}, {"text": "包含具体诊疗项目、药品名称及其单价的消费明细"}]}, {"title": "其他能证明保险事故性质、原因、损失程度有关的材料", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_14.jpg"], "attachmentType": "4", "errFlag": false, "min": 1, "max": 10, "textList": []}], "isShoweinvoice": true, "baseConfig": {"giveUpReasonList": [{"name": "第三方已赔付"}, {"name": "金额太小，无需理赔"}, {"name": "其他"}]}}, "H": {"upLoadConfig": [{"title": "被保人身份证正面、反面", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/sf/1.png"], "attachmentType": "2", "errFlag": false, "min": 2, "max": 2, "textList": [{"text": "身份证须提供正反面。"}]}, {"title": "理赔授权书", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "exampleList": [], "attachmentType": "27", "errFlag": false, "min": 1, "max": 3, "textList": [{"text": "如赔款涉及到委托他人收款，需提供赔款授权相关材料。（请联系我司客服索取）若被保险人为成年人：请下载并请被保险人手工填写“赔款授权书”。"}]}, {"title": "财物受损情况的照片、事故现场照片1", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/jc/xczp.jpg"], "attachmentType": "20", "errFlag": false, "min": 1, "max": 5, "textList": [{"text": "出险时拍摄现场受损情况，可以采取先拍摄总体受损情况再拍摄局部特写受损情况。"}]}, {"title": "房产证明", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/fc/example_1.jpg"], "attachmentType": "26", "errFlag": false, "min": 1, "max": 3, "textList": [{"text": "通常形式有：不动产权证书、购房合同。"}]}, {"title": "与财物损失清单对应的原始购买发票或维修发票", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "attachmentType": "3", "errFlag": false, "min": 0, "max": 5, "textList": [{"text": "通常在购置财物时获取，通常形式有发票或收据；装修部分的损失可以提供装修合同等。"}]}, {"title": "警方出具的受案证明", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/jc/3.png"], "attachmentType": "5", "errFlag": false, "min": 0, "max": 5, "textList": [{"text": "由警方提供，常见形式有：报案回执、立案告知书。"}]}, {"title": "其它资料（财物损失清单、其他投保人、被保险人所能提供的，与确认保险事故的原因、性质、损失程度相关的证明与资料等截图，涉及驾驶机动车的，需要提供行驶证、驾驶证）", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "attachmentType": "4", "errFlag": false, "min": 0, "max": 5, "textList": [{"text": "其它可以证明本次事故原因、性质、损失程度的资料。"}, {"text": "请下载财务损失清单，打印后手工填写。", "type": "download", "btnText": "财务损失清单", "fileUrl": "ssqd.pdf"}]}], "isShoweinvoice": false, "baseConfig": {"giveUpReasonList": [{"name": "第三方已赔付"}, {"name": "金额太小，无需理赔"}, {"name": "其他"}]}}, "I": {"upLoadConfig": [{"title": "被保险人身份证正面、反面", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/sf/1.png"], "attachmentType": "2", "errFlag": false, "min": 2, "max": 7, "textList": [{"text": "身份证须提供正反面。"}]}, {"title": "理赔授权书", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "exampleList": [], "attachmentType": "27", "errFlag": false, "min": 1, "max": 3, "textList": [{"text": "如赔款涉及到委托他人收款，需提供赔款授权相关材料。（请联系我司客服索取）若被保险人为成年人：请下载并请被保险人手工填写“赔款授权书”。"}]}, {"title": "宠物就诊照片（治疗期间拍摄的宠物正脸照片、治疗过程照片、伤口照片等）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_11_1.jpg", "/images/example/xcx_example_11_2.jpg", "/images/example/xcx_example_11_3.jpg"], "attachmentType": "18", "errFlag": false, "min": 1, "max": 10, "textList": [{"text": "治疗期间拍摄的宠物正脸照片。"}, {"text": "治疗过程照片。"}, {"text": "伤口照片。"}]}, {"title": "医疗诊断（宠物病历卡首页、本次门急诊病历页面、住院病历（若有）、抢救报告、手术记录、化验单、药物处方单等）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/yw/1.png", "/images/example/yw/5.png"], "attachmentType": "1", "errFlag": false, "min": 1, "max": 10, "textList": [{"text": "请向就诊医院索取病历，如无手写病历，可索取电子病历打印件（记录有宠物信息、就诊时间、就诊情况、病情和诊断内容的页面）。"}]}, {"title": "医疗发票（发票、费用明细清单）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_13.jpg"], "attachmentType": "3", "errFlag": false, "min": 2, "max": 15, "textList": [{"text": "医院开具的增值税发票、普通发票或定额发票，非收据，非收银小票，发票抬头须为被保人本人全名"}, {"text": "包含具体诊疗项目、药品名称及其单价的消费明细"}]}, {"title": "宠物医院资质证明（若在非指定宠物诊疗机构治疗，需提供诊疗机构营业执照和动物诊疗许可证以及主治医生的执业兽医执照）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_12.jpg"], "attachmentType": "19", "errFlag": false, "min": 0, "max": 15, "textList": []}, {"title": "其他能证明保险事故性质、原因、损失程度有关的材料", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_14.jpg"], "attachmentType": "4", "errFlag": false, "min": 1, "max": 10, "textList": []}], "isShoweinvoice": true, "baseConfig": {"giveUpReasonList": [{"name": "第三方已赔付"}, {"name": "金额太小，无需理赔"}, {"name": "其他"}]}}, "Q": {"upLoadConfig": [{"title": "宠物照片", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_11_1.jpg", "/images/example/xcx_example_11_2.jpg", "/images/example/xcx_example_11_3.jpg"], "attachmentType": "18", "errFlag": false, "min": 1, "max": 10, "textList": [{"text": "在治疗期间拍摄的宠物照片。上传照片的宠物需与投保宠物为同一只宠物。"}]}, {"title": "病历", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/yw/1.png"], "attachmentType": "1", "errFlag": false, "min": 1, "max": 10, "textList": [{"text": "本次就诊的完整病历记录，须包含详细日期、宠物信息、主诉、临床表现、诊断及治疗情况"}]}, {"title": "费用清单", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_13.jpg"], "attachmentType": "47", "errFlag": false, "min": 1, "max": 10, "textList": [{"text": "本次产生的医疗项目费用清单，费用清单上的医疗费用项目金额应当与发票对应；费用清单上的医疗项目与病历上的疾病对应；"}]}, {"title": "发票", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/yw/6.png"], "attachmentType": "3", "errFlag": false, "min": 1, "max": 10, "textList": [{"text": "医院开具的增值税发票、普通发票或定额发票，抬头须为被保险人本人全名。"}]}, {"title": "化验报告", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_14.jpg"], "attachmentType": "4", "errFlag": false, "min": 1, "max": 10, "textList": [{"text": "本次就诊产生的包含详细日期和宠物信息的全部化验报告；"}]}], "isShoweinvoice": true, "baseConfig": {"giveUpReasonList": [{"name": "第三方已赔付"}, {"name": "金额太小，无需理赔"}, {"name": "其他"}]}}, "X": {"upLoadConfig": [{"title": "被保险人身份证正面、反面（被保险人为未成年人需提供户口本，出生证明、监护人身份证）", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_15_6.jpg"], "attachmentType": "2", "errFlag": false, "min": 2, "max": 7, "textList": [{"text": "身份证须提供正反面。"}, {"text": "被保险人未成年的需提交监护人身份证明和关系证明（出生证明）。"}, {"text": "常见的关系证明为：被保险人出生证+监护人身份证正反面；被保险人和监护人户口本页。"}]}, {"title": "隔离证明", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_15_7.jpg"], "attachmentType": "52", "errFlag": false, "min": 1, "max": 5, "textList": [{"text": "隔离证明可在隔离结束后找疾控中心、卫建委、疫情防控中心、街道办等政府相关部门索要。"}]}, {"title": "收到隔离通知的证明材料", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_15_9.png"], "attachmentType": "65", "errFlag": false, "min": 1, "max": 5, "textList": [{"text": "包括但不限于短信、微信、电话、文件等通知。"}]}, {"title": "咨询隔离政策相关材料", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_15_8.png"], "attachmentType": 66, "errFlag": false, "min": 1, "max": 5, "textList": [{"text": "包括但不限于向目的地相关部门咨询隔离政策的短信、微信、电话、文件等材料。"}]}, {"title": "通信大数据行程卡", "isShowExtraJson": false, "isShowExample": true, "imgList": [], "keyList": [], "exampleList": ["/images/example/xcx_example_15_2.jpg"], "attachmentType": "55", "errFlag": false, "min": 1, "max": 1, "textList": [{"text": "打开微信小程序，搜索通信行程卡，输入手机号以及验证码即可查询。"}]}, {"title": "理赔授权书", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "exampleList": [], "attachmentType": "27", "errFlag": false, "min": 1, "max": 3, "textList": [{"text": "如赔款涉及到委托他人收款，需提供赔款授权相关材料。（请联系我司客服索取）若被保险人为成年人：请下载并请被保险人手工填写“赔款授权书”。"}]}, {"title": "其它", "isShowExtraJson": false, "isShowExample": false, "imgList": [], "keyList": [], "exampleList": [], "attachmentType": "4", "errFlag": false, "min": 0, "max": 15, "textList": [{"text": "其他与本次隔离相关的理赔资料，如有请提供。如：隔离通知（包括但不限于短信、微信、电话、文件通知）、与确诊病例乘坐同一交通工具证明、住宿发票、行程信息等材料。"}]}]}}