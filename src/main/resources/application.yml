server:
  port: 8080
  servlet:
    session:
      timeout: 86400  # session超时时间

spring:
  application:
    name: za-lincoln
  session:
    store-type: redis # session存储方式
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  main:
    allow-circular-references: true

logging.level:
  com.netflix.config.sources.URLConfigurationSource: ERROR
  com.taobao.tddl: ERROR


cache:
  caffeine: # caffeine cache for local
    enable: true
    cache-names: local-config,common-rule,hospital-detail,mgr-user
  redis:  # redis cache for distributed
    enable: true
    root-prefix: 'lincoln:'
    cache-names: config # 用于缓存一些类配置信息，诸如责任名称、渠道名称、产品名称等； 低频变更
    ttl[default]: 300
    ttl[config]: 43200  # 12小时

#taobao.pet.ons.accessKeyId=LTAI3X8zOLoDtn7T
  #taobao.pet.ons.accessKeySecret=zaec_test_d799717fc60f85208718c491e91991fb2f7cc78aa85b31f0f2b97cf4836b6bd66bfc8761543d91c8e317b59f098e52ac3b5628b702f9cc180b5c901c4dc6e983f4a332bd112d0f3d2c56b0e9d8a54bc101
  #taobao.pet.ons.instanceAddress=http://MQ_INST_1875377503395421_BabJxSng.cn-hangzhou-finance.mq-internal.aliyuncs.com:8080
  #taobao.pet.ons.groupId=GID-D-SUPERMAN-190717
  #taobao.pet.ons.topic=D-SUPERMAN-BATMAN-190715
taobao: 
  pet:
    ons:
      accessKeyId: LTAI3X8zOLoDtn7T
      accessKeySecret: zaec_test_d799717fc60f85208718c491e91991fb2f7cc78aa85b31f0f2b97cf4836b6bd66bfc8761543d91c8e317b59f098e52ac3b5628b702f9cc180b5c901c4dc6e983f4a332bd112d0f3d2c56b0e9d8a54bc101
      instanceAddress: http://MQ_INST_1875377503395421_BabJxSng.cn-hangzhou-finance.mq-internal.aliyuncs.com:8080
      groupId: GID-D-SUPERMAN-190717
      topic: D-SUPERMAN-BATMAN-190715

zk.merch.appKey: E8606C02060F1037FB04BC9AD565CA61
zk.merch: yxgk

feign:
  client:
    config:
      default:
        connectTimeout: 2000
        readTimeout: 30000