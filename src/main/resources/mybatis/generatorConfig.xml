<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration>

    <properties resource="mybatis/generatorConfig.properties"/>
    <classPathEntry location="${generator.driverLocation}"/>
    <context id="context1" targetRuntime="MyBatis3">

        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"></plugin>
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"></plugin>
        <plugin type="org.mybatis.generator.plugins.FluentBuilderMethodsPlugin"></plugin>
        <plugin type="com.itfsw.mybatis.generator.plugins.ModelColumnPlugin"></plugin>
        <plugin type="com.itfsw.mybatis.generator.plugins.BatchInsertPlugin"></plugin>
        <plugin type="com.zhongan.lincoln.dal.generator.MySQLLimitPlugin"></plugin>
        <plugin type="com.zhongan.lincoln.dal.generator.CustomCriteriaPlugin"></plugin>
<!--        <plugin type="com.itfsw.mybatis.generator.plugins.SelectSelectivePlugin"></plugin>-->



        <plugin type="com.zhongan.lincoln.dal.generator.RenameModelPlugin">
            <property name="prefixes2Remove" value="sp"/>
            <property name="suffix2Append" value="DO"/>
        </plugin>

        <plugin type="com.zhongan.lincoln.dal.generator.ForceCreateUpdateTimePlugin">
            <property name="insertTimeColumns" value="gmt_created"/>
            <property name="lastUpdateTimeColumns" value="gmt_modified"/>
            <property name="dbCurrentTimeExpr" value="sysdate()"/>
        </plugin>

        <plugin type="com.itfsw.mybatis.generator.plugins.MapperAnnotationPlugin">
            <!-- @Mapper 默认开启 -->
            <property name="@Mapper" value="true"/>
            <!-- @Repository 默认关闭，开启后解决IDEA工具@Autowired报错 -->
            <property name="@Repository" value="false"/>
        </plugin>


        <commentGenerator type="com.zhongan.lincoln.dal.generator.DBCommentGenerator">
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="${generator.connectionURL}"
                        userId="${generator.userId}" password="${generator.password}">
            <property name="remarksReporting" value="true"/>
        </jdbcConnection>

        <javaTypeResolver type="com.zhongan.lincoln.dal.generator.MyJavaTypeResolver"/>

        <javaModelGenerator targetPackage="com.zhongan.lincoln.dal.domain"
                            targetProject="src/main/java">
            <property name="rootClass" value="com.zhongan.lincoln.dal.domain.BaseDO"/>
        </javaModelGenerator>
        <sqlMapGenerator targetPackage="mybatis.sqlmap.autogenerated"
                         targetProject="src/main/resources"/>
        <javaClientGenerator targetPackage="com.zhongan.lincoln.dal.mapper"
                             targetProject="src/main/java" type="XMLMAPPER">
        </javaClientGenerator>

<!--        <table schema="ams_00" tableName="cargo_claim" domainObjectName="CargoClaim"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;CargoClaimDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_hospital_liability_settlement_info" domainObjectName="LiabilitySettlement"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;LiabilitySettlementDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_bank_info" domainObjectName="ClaimBankInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimBankInfoDO&gt;"/>-->
<!--            <columnOverride column="payeecert_type" property="payeeCertType"/>-->
<!--            <columnOverride column="payeecert_no" property="payeeCertNo"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="cargo_claim_bank_info" domainObjectName="ClaimBankInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimBankInfoDO&gt;"/>-->
<!--            <columnOverride column="payeecert_type" property="payeeCertType"/>-->
<!--            <columnOverride column="payeecert_no" property="payeeCertNo"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_payeer_bank_info" domainObjectName="PayeerBankInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;PayeerBankInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="cargo_assess_attachment" domainObjectName="Attachment"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AttachmentDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_assess_check" domainObjectName="AssessCheck"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AssessCheckDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_log_data" domainObjectName="ClaimLogData"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimLogDataDO&gt;"/>-->
<!--        </table>-->
<!--       <table schema="ams_00" tableName="biz_config" domainObjectName="BizConfig"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BizConfigDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="material_fee_config" domainObjectName="MaterialFee"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MaterialFeeDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_extend_info" domainObjectName="ClaimExtendInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimExtendInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_push_info" domainObjectName="ClaimPushInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimPushInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="insurer_info" domainObjectName="InsurerInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;InsurerInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_insurance_info" domainObjectName="ThirdPartyLiability"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ThirdPartyLiabilityDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_insurance_info_detail" domainObjectName="ThirdPartyLiabilityExpense"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ThirdPartyLiabilityExpenseDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_visit_info" domainObjectName="RevisitRecord"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;RevisitRecordDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_visit_record" domainObjectName="VisitRecord"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;VisitRecordDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_message_info" domainObjectName="RemarksMessage"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;RemarksMessageDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_mgr_user" domainObjectName="User"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;UserDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_operator" domainObjectName="ClaimOperator"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimOperatorDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="sp_claim_order" domainObjectName="ClaimOrder"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimOrderDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="claim_tag_info" domainObjectName="ClaimTagInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimTagInfoDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="sp_auto_claim_record" domainObjectName="AutoClaimRecord"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AutoClaimRecordDO&gt;"/>-->
<!--        </table>-->

<!--       <table schema="ams_00" tableName="ant_policy_relation" domainObjectName="AntPolicyRelation"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AntPolicyRelationDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="claim_task_trigger" domainObjectName="ClaimTaskTrigger"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimTaskTriggerDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="cargo_claim_process_dealor_info" domainObjectName="ClaimProcessDealor"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimProcessDealorDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="biz_dynamic_config_meta" domainObjectName="BizDynamicConfigMeta"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BizDynamicConfigMetaDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="material_fee_config" domainObjectName="MaterialFee"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MaterialFeeDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="material_fee_equ_config" domainObjectName="MaterialFeeEquConfig"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MaterialFeeEquConfigDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="expenses_payment" domainObjectName="ExpensesPayment"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ExpensesPaymentDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="material_fee_config" domainObjectName="MaterialFee"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MaterialFeeDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="material_fee_config" domainObjectName="MaterialFee"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MaterialFeeDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="wo_info" domainObjectName="WoInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;WoInfoDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="wo_user_relation" domainObjectName="WoUserRelation"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;WoUserRelationDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="wo_role" domainObjectName="WoRole"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;WoRoleDO&gt;"/>-->
<!--        </table>        -->

<!--        <table schema="ams_00" tableName="wo_assign_rule" domainObjectName="WoAssignRule"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;WoAssignRuleDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="tpa_employee_info" domainObjectName="TpaEmployeeInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;TpaEmployeeInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_user_flag" domainObjectName="ClaimUserFlag"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimUserFlagDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="tpa_claim" domainObjectName="TpaClaim"-->
<!--        <table schema="ams_00" tableName="material_fee_config" domainObjectName="MaterialFee"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MaterialFeeDO&gt;"/>-->
<!--        </table>-->
        <!--        <table schema="ams_00" tableName="material_fee_config" domainObjectName="MaterialFee"-->
        <!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
        <!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MaterialFeeDO&gt;"/>-->
        <!--        </table>-->

<!--        <table schema="ams_00" tableName="material_fee_equ_config" domainObjectName="MaterialFeeEquConfig"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MaterialFeeEquConfigDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="expenses_payment" domainObjectName="ExpensesPayment"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ExpensesPaymentDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="expenses_item" domainObjectName="ExpensesItem"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ExpensesItemDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="generic_attachment" domainObjectName="GenericAttachment"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;GenericAttachmentDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_pre_paid_info" domainObjectName="ClaimPrePaid"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimPrePaidDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="expenses_payment" domainObjectName="ExpensesPayment"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ExpensesPaymentDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="expenses_item" domainObjectName="ExpensesItem"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ExpensesItemDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="generic_attachment" domainObjectName="GenericAttachment"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;GenericAttachmentDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_batch_relation_liability_settlement" domainObjectName="BRelationLiabilitySettlement"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BRelationLiabilitySettlementDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="claim_batch_relation_liability_info" domainObjectName="BRelationLiabilityInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BRelationLiabilityInfoDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="claim_batch_relation_bank_info" domainObjectName="BRelationBankInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BRelationBankInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_batch_relation_liability_settlement" domainObjectName="BRelationLiabilitySettlement"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BRelationLiabilitySettlementDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="claim_batch_relation_liability_info" domainObjectName="BRelationLiabilityInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BRelationLiabilityInfoDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="tc_batch_op_detail" domainObjectName="BatchOpDetail"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BatchOpDetailDO&gt;"/>-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimSettlementInfoHistoryDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="tc_batch_op_detail" domainObjectName="BatchOpDetail"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BatchOpDetailDO&gt;"/>-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimSettlementInfoHistoryDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_batch_relation_bank_info" domainObjectName="BRelationBankInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimAppointmentCallbackDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_team" domainObjectName="ClaimTeam"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimTeamDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="claim_team_member" domainObjectName="ClaimTeamMember"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimTeamMemberDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_settlement_info_history" domainObjectName="ClaimSettlementInfoHistory"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimSettlementInfoHistoryDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_hospital_bill_cost_detail" domainObjectName="ClaimHospitalBillCostDetail"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimHospitalBillCostDetailDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="claim_batch_relation_bank_info" domainObjectName="BRelationBankInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BRelationBankInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_batch_bank_info" domainObjectName="BBankInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BBankInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_batch_hospital_liability_settlement_info" domainObjectName="BHospitalLiabilitySettlementInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BHospitalLiabilitySettlementInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_batch_relation_info" domainObjectName="BRelationInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BRelationInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_settlement_info_history" domainObjectName="ClaimSettlementInfoHistory"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimSettlementInfoHistoryDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="common_claim_pet_info" domainObjectName="CommonClaimPetInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;CommonClaimPetInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_anti_money_laundering_log" domainObjectName="AntiMoneyLaunderingLog"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AntiMoneyLaunderingLogDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_hospital_disability_relation_info" domainObjectName="DisabilityRelationInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;DisabilityRelationInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_hospital_front_reserve_history" domainObjectName="FrontReserveHistory"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;FrontReserveHistoryDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_risk_info" domainObjectName="ClaimRiskInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimRiskInfoDO&gt;"/>-->
<!--        </table>-->

<!--            <table schema="ams_00" tableName="claim_batch_head_info" domainObjectName="ClaimBatchHeadInfo"-->
<!--                   enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimBatchHeadInfoDO&gt;"/>-->
<!--            </table>-->

<!--        <table schema="ams_00" tableName="claim_compensation_summary" domainObjectName="ClaimCompensationSummary"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimCompensationSummaryDO&gt;"/>-->
<!--        </table>-->

<!--            <table schema="ams_00" tableName="expenses_item" domainObjectName="ExpensesItem"-->
<!--                   enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ExpensesItemDO&gt;"/>-->
<!--            </table>-->
<!--            <table schema="ams_00" tableName="claim_risk_valid" domainObjectName="ClaimRiskValid"-->
<!--                   enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimRiskValidDO&gt;"/>-->
<!--            </table>-->

<!--        <table schema="ams_00" tableName="wo_ark_relation" domainObjectName="WoArkRelation"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;WoArkRelationDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="etcc_attachment" domainObjectName="EtccAttachment"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;EtccAttachmentDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="wo_type_dict" domainObjectName="WoTypeDict"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;WoTypeDictDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="cargo_claim_insurance_info" domainObjectName="ClaimInsuranceInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimInsuranceInfoDO&gt;"/>-->
<!--        </table>-->
<!--                <table schema="ams_00" tableName="claim_risk_valid" domainObjectName="ClaimRiskValid"-->
<!--                       enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                    <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimRiskValidDO&gt;"/>-->
<!--                </table>-->
<!--            <table schema="ams_00" tableName="claim_compensation_summary" domainObjectName="ClaimCompensationSummary"-->
<!--                   enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimCompensationSummaryDO&gt;"/>-->
<!--            </table>-->

<!--            <table schema="ams_00" tableName="expenses_item" domainObjectName="ExpensesItem"-->
<!--                   enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ExpensesItemDO&gt;"/>-->
<!--            </table>-->
<!--            <table schema="ams_00" tableName="claim_risk_valid" domainObjectName="ClaimRiskValid"-->
<!--                   enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimRiskValidDO&gt;"/>-->
<!--            </table>-->

<!--            <table schema="ams_00" tableName="claim_trace_log" domainObjectName="ClaimTraceLog"-->
<!--                   enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimTraceLogDO&gt;"/>-->
<!--                <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            </table>-->
<!--            <table schema="ams_00" tableName="expenses_item" domainObjectName="ExpensesItem"-->
<!--                   enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ExpensesItemDO&gt;"/>-->
<!--            </table>-->
<!--                <table schema="ams_00" tableName="claim_risk_valid" domainObjectName="ClaimRiskValid"-->
<!--                       enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                    <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimRiskValidDO&gt;"/>-->
<!--                </table>-->

<!--        <table schema="ams_00" tableName="claim_trace_log" domainObjectName="ClaimTraceLog"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimTraceLogDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_ocr_info" domainObjectName="ClaimOcrInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimOcrInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_disability_standard_info" domainObjectName="DisabilityStandard"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;DisabilityStandardDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="air_lines_company_info" domainObjectName="AirLinesCompany"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AirLinesCompanyDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="litigation_negotiation" domainObjectName="LitigationNegotiation"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;LitigationNegotiationDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="litigation_stage" domainObjectName="LitigationStage"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;LitigationStageDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="litigation" domainObjectName="Litigation"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;LitigationDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_insurance_info" domainObjectName="ThirdPartyLiabilityLoss"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ThirdPartyLiabilityLossDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_insurance_info_detail" domainObjectName="ThirdPartyLiabilityExpense"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ThirdPartyLiabilityExpenseDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_mgr_menu" domainObjectName="Menu"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MenuDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_risk_valid" domainObjectName="ClaimRiskValid"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimRiskValidDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="common_claim_pet_info" domainObjectName="CommonClaimPetInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;CommonClaimPetInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_product_config" domainObjectName="ClaimProductConfig"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimProductConfigDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_product_attribute_config" domainObjectName="ClaimProductAttributeConfig"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimProductAttributeConfigDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_product_processor_config" domainObjectName="ClaimProductProcessorConfig"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimProductProcessorConfigDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="ant_claim" domainObjectName="AntClaim"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AntClaimDO&gt;"/>-->
<!--        </table>-->
<!--        -->
<!--        <table schema="ams_00" tableName="claim_risk_valid" domainObjectName="ClaimRiskValid"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimRiskValidDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_trace_log" domainObjectName="ClaimTraceLog"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimTraceLogDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_trace_log" domainObjectName="ClaimTraceLog"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimTraceLogDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_hospital_history" domainObjectName="ClaimHospitalHistory"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimHospitalHistoryDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="invoice_detail_info_history" domainObjectName="InvoiceDetailInfoHistory"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;InvoiceDetailInfoHistoryDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="invoice_detail_info" domainObjectName="InvoiceDetailInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;InvoiceDetailInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="knead_bill_config" domainObjectName="KneadBillConfig"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;KneadBillConfigDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="on_kneading_bill" domainObjectName="OnKneadingBill"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;OnKneadingBillDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="anti_fraud_data" domainObjectName="AntiFraudData"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AntiFraudDataDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="loss_core_relation" domainObjectName="LossCoreRelation"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;LossCoreRelationDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="invoice_detail_info" domainObjectName="InvoiceDetailInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;InvoiceDetailInfoDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="applet_product_biz_type" domainObjectName="ProductBizType"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ProductBizTypeDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_product_sync" domainObjectName="ProductLiability"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ProductLiabilityDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="cargo_product_sync" domainObjectName="ProductLiability"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ProductLiabilityDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="cargo_express" domainObjectName="Express"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ExpressDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="lincoln_00" tableName="express_return_task" domainObjectName="ExpressReturnTask"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ExpressReturnTaskDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="cargo_product_sync" domainObjectName="ProductLiability"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ProductLiabilityDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--            <table schema="ams_00" tableName="tc_reconciliation_data" domainObjectName="TcReconciliationData"-->
<!--                   enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;TcReconciliationDataDO&gt;"/>-->
<!--            </table>-->

<!--        <table schema="ams_00" tableName="delivery_claim" domainObjectName="DeliveryClaim"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;DeliveryClaimDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="im_inbox" domainObjectName="ImInbox"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ImInboxDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="cargo_product_sync" domainObjectName="ProductLiability"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ProductLiabilityDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--                <table schema="ams_00" tableName="expenses_item" domainObjectName="ExpensesItem"-->
<!--                       enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                    <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ExpensesItemDO&gt;"/>-->
<!--                </table>-->

<!--        <table schema="ams_00" tableName="claim_hospital_front_reserve_history" domainObjectName="FrontReserveHistory"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;FrontReserveHistoryDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="loss_core_relation" domainObjectName="LossCoreRelation"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;LossCoreRelationDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_pre_report" domainObjectName="ClaimPreReport"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimPreReportDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_message_info" domainObjectName="ClaimMessageInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimMessageInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="tpa_claim" domainObjectName="TpaClaim"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;TpaClaimDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="tpa_company_info" domainObjectName="TpaCompanyInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;TpaCompanyInfoDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="tpa_employee_info" domainObjectName="TpaEmployeeInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;TpaEmployeeInfoDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="applet_product_biz_type" domainObjectName="AppletProductBizType"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AppletProductBizTypeDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="applet_product_operation_config" domainObjectName="AppletProductOperationConfig"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AppletProductOperationConfigDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="applet_insurance_type_relation" domainObjectName="AppletInsuranceTypeRelation"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AppletInsuranceTypeRelationDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_express" domainObjectName="Express"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ExpressDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="applet_guide" domainObjectName="AppletGuide"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AppletGuideDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_ocr_remark" domainObjectName="ClaimOcrRemark"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimOcrRemarkDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="claim_trace_log" domainObjectName="ClaimTraceLog"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimTraceLogDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="auto_settlement_risk_info" domainObjectName="AutoSettlementRiskInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ProductLiabilityDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="im_outbox" domainObjectName="ImOutbox"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ImOutboxDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="auto_settlement_risk_info" domainObjectName="AutoSettlementRiskInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AutoSettlementRiskInfoDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="litigation" domainObjectName="Litigation"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;LitigationDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="im_outbox" domainObjectName="ImOutbox"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ImOutboxDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="mdp_claim" domainObjectName="MdpClaim"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MdpClaimDO&gt;"/>-->
<!--        </table>-->

        <!--<table schema="lincoln_00" tableName="bill_ocr_compare_record" domainObjectName="BillOcrCompareRecord"-->
               <!--enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
            <!--<property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BillOcrCompareRecordDO&gt;"/>-->
        <!--</table>-->

        <!--<table schema="ams_00" tableName="bill_ocr_compare_count" domainObjectName="BillOcrCompareCount"-->
               <!--enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
            <!--<property name="rootInterface" value="com.zhongan.hacksaw.dal.mapper.BaseMapper&lt;BillOcrCompareCountDO&gt;"/>-->
        <!--</table>-->

        <!--<table schema="lincoln_00" tableName="claim_biz_data" domainObjectName="ClaimBizData">-->
        <!--</table>-->

<!--        <table schema="lincoln_00" tableName="mdp_claim" domainObjectName="MdpClaim"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MdpClaimDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="cargo_claim_batch_log" domainObjectName="ClaimBatchLog"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimBatchLogDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="lincoln_00" tableName="cargo_claim_batch_detail_log" domainObjectName="ClaimBatchDetailLog"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimBatchDetailLogDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="auto_claim_mvc_overview" domainObjectName="AutoClaimMvcOverview"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AutoClaimMvcOverviewDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="aic_hit_rule_detail" domainObjectName="AicHitRuleDetail"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AicHitRuleDetailDO&gt;"/>-->
<!--        </table>-->


<!--        <table schema="lincoln_00" tableName="cargo_product_sync" domainObjectName="ProductLiability"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ProductLiabilityDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="tpa_employee_info" domainObjectName="TpaEmployeeInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AicHitRuleDetailDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_pre_report" domainObjectName="ClaimPreReport"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimPreReportDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="lincoln_00" tableName="tpa_employee_info" domainObjectName="TpaEmployeeInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AicHitRuleDetailDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="cargo_claim_pre_report" domainObjectName="ClaimPreReport"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimPreReportDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="tpa_employee_info" domainObjectName="TpaEmployeeInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;TpaEmployeeInfoDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="cargo_claim_pre_report" domainObjectName="ClaimPreReport"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimPreReportDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="black_list_type" domainObjectName="BlackListType"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BlackListTypeDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="lincoln_00" tableName="black_list_record" domainObjectName="BlackListRecord"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BlackListRecordDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="black_list_type" domainObjectName="BlackListType"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BlackListTypeDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="black_list_type" domainObjectName="BlackListType"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BlackListTypeDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="black_list_record" domainObjectName="BlackListRecord"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BlackListRecordDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="black_list_record" domainObjectName="BlackListRecord"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BlackListRecordDO&gt;"/>-->
<!--        </table>-->
<!--        -->
<!--        <table schema="lincoln_00" tableName="applet_report_display" domainObjectName="AppletReportDisplay"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AppletReportDisplayDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="lincoln_00" tableName="cs_creed" domainObjectName="CsCreed"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;CsCreedDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="lincoln_00" tableName="tpa_cost" domainObjectName="TpaCost"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;TpaCostDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="black_list_record" domainObjectName="BlackListRecord"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;BlackListRecordDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="claim_blacklist_sync_ecxdecision" domainObjectName="ClaimBlacklistSyncEcxdecision"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimBlacklistSyncEcxdecisionDO&gt;"/>-->
<!--        </table>-->

<!--            <table schema="ams_00" tableName="claim_team_member" domainObjectName="ClaimTeamMember"-->
<!--                   enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimTeamMemberDO&gt;"/>-->
<!--            </table>-->

<!--        <table schema="lincoln_00" tableName="applet_report_display" domainObjectName="AppletReportDisplay"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AppletReportDisplayDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="lincoln_00" tableName="cs_creed" domainObjectName="CsCreed"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;CsCreedDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="ams_00" tableName="bd_claim_pet_info" domainObjectName="ByteDanceClaimPetInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ByteDanceClaimPetInfoDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ams_00" tableName="bd_claim_audit_result" domainObjectName="ByteDanceClaimAuditResult"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ByteDanceClaimAuditResultDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="lincoln_00" tableName="pet_bill" domainObjectName="PetBill"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;PetBillDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="lincoln_00" tableName="pet_bill_detail" domainObjectName="PetBillDetail"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;PetBillDetailDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="applet_report_display" domainObjectName="AppletReportDisplay"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AppletReportDisplayDO&gt;"/>-->
<!--        </table>-->
<!--        <table schema="lincoln_00" tableName="cs_creed" domainObjectName="CsCreed"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;CsCreedDO&gt;"/>-->
<!--        </table>-->
<!--            <table schema="ams_00" tableName="tuhu_coupon" domainObjectName="TuhuCoupon"-->
<!--                   enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--                <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;TuhuCouponDO&gt;"/>-->
<!--            </table>-->

<!--        <table schema="lincoln_00" tableName="cargo_claim_track" domainObjectName="ClaimTrack"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimTrackDO&gt;"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="cargo_claim_settlementrule" domainObjectName="CargoClaimSettlementrule"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;CargoClaimSettlementruleDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="sos_case" domainObjectName="SosCase"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;SosCaseDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="sos_case_report" domainObjectName="SosCaseReport"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;SosCaseReportDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="dwsync_claim_risk_factor" domainObjectName="DwClaimRiskFactor"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;DwClaimRiskFactorDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <ignoreColumn column="valid_policies"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="pet_claim_assistant_record" domainObjectName="PetClaimAssistantRecord"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;PetClaimAssistantRecordDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="lincoln_00" tableName="pet_claim_assistant_record" domainObjectName="PetClaimAssistantRecord"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;PetClaimAssistantRecordDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="tpa_claim_cost_item" domainObjectName="TpaClaimCostItem"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;TpaClaimCostItemDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="tpa_claim_operate_log" domainObjectName="TpaClaimOperateLog"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;TpaClaimOperateLogDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="tpa_tag_error_record" domainObjectName="TpaTagErrorRecord"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;TpaTagErrorRecordDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="assess_risk_info" domainObjectName="AssessRiskInfo"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;AssessRiskInfoDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="pet_medical_supply_entity" domainObjectName="MedicalSupplyEntity"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MedicalSupplyEntityDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="pet_medical_supply_entity" domainObjectName="MedicalSupplyEntity"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;MedicalSupplyEntityDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="claim_quality_inspection" domainObjectName="ClaimQualityInspection"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimQualityInspectionDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="pet_direct_settlement" domainObjectName="DirectSettlement"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;DirectSettlementDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="lincoln_00" tableName="pet_direct_settlement_item" domainObjectName="DirectSettlementItem"-->
<!--               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">-->
<!--            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;DirectSettlementItemDO&gt;"/>-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->
        <table schema="lincoln_00" tableName="claim_quality_inspection_history" domainObjectName="ClaimQualityInspectionHistory"
               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">
            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;ClaimQualityInspectionHistoryDO&gt;"/>
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>

        <table schema="lincoln_00" tableName="out_bound_record" domainObjectName="OutBoundRecord"
               enableDeleteByExample="false" enableDeleteByPrimaryKey="false">
            <property name="rootInterface" value="com.zhongan.lincoln.dal.mapper.BaseMapper&lt;OutBoundRecordDO&gt;"/>
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>

    </context>
</generatorConfiguration>