#generator.driverLocation=D:/resources/mysql-connector-java-5.1.6.jar
#generator.driverLocation=D:/Environment/apache-maven-3.3.9/localRepo/mysql/mysql-connector-java/5.1.8/mysql-connector-java-5.1.8.jar
generator.driverLocation=C:/work/mavenRepository/mysql/mysql-connector-java/5.1.8/mysql-connector-java-5.1.8.jar
#generator.driverLocation=D:/Tools/DevTool/repository/mysql/mysql-connector-java/5.1.45/mysql-connector-java-5.1.45.jar
#generator.driverLocation=D:/maven/apache-maven-3.8.8/repository/mysql/mysql-connector-java/5.1.49/mysql-connector-java-5.1.49.jar
#generator.driverLocation=/Users/<USER>/.m2/repository/mysql/mysql-connector-java/5.1.6/mysql-connector-java-5.1.6.jar
#generator.driverLocation=D:/resources/mysql-connector-java-5.1.6.jar
#generator.driverLocation=/Users/<USER>/.m2/repository/mysql/mysql-connector-java/5.1.6/mysql-connector-java-5.1.6.jar
#generator.driverLocation=C:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/5.1.45/mysql-connector-java-5.1.45.jar
generator.connectionURL=***************************************************************************
generator.userId=tst_user_9d19632
generator.password=tst_user_9d19632_bc4f8b
