<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.WoUserRelationDAO">
    <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.WoInfoDO">
        <result property="id" column="id" />
        <result property="woNo" column="wo_no" />
        <result property="woType" column="wo_type" />
        <result property="woStatus" column="wo_status" />
        <result property="reportNo" column="report_no" />
        <result property="policyNo" column="policy_no" />
        <result property="dispatcherNo" column="dispatcher_no" />
        <result property="attachmentId" column="attachment_id" />
        <result property="woData" column="wo_data" />
        <result property="executor" column="executor" />
        <result property="executeTime" column="execute_time" />
        <result property="executeRemark" column="execute_remark" />
        <result property="remark" column="remark" />
        <result property="isDeleted" column="is_deleted" />
        <result property="gmtCreated" column="gmt_created" />
        <result property="gmtModified" column="gmt_modified" />
        <result property="creator" column="creator" />
        <result property="modifier" column="modifier" />
        <result property="reason" column="reason" />
    </resultMap>

    <sql id="Base_Column_List" >
      a1.id, a1.wo_no, a1.wo_type, a1.wo_status, a1.report_no, a1.policy_no, a1.dispatcher_no, a1.attachment_id,
      a1.wo_data, a1.executor, a1.execute_time, a1.execute_remark, a1.remark, a1.is_deleted,
      a1.gmt_created, a1.gmt_modified, a1.creator, a1.modifier, a1.reason
    </sql>

    <sql id="doUserQueryParams">
        <if test="id != null">
            and a1.id = #{id}
        </if>
        <if test="woNo != null and woNo != ''">
            and a1.wo_no = #{woNo}
        </if>
        <if test="woType != null and woType != ''">
            and a1.wo_type = #{woType}
        </if>
        <if test="woStatus != null">
            and a1.wo_status = #{woStatus}
        </if>
        <if test="reportNo != null and reportNo != ''">
            and a1.report_no = #{reportNo}
        </if>
        <if test="policyNo != null and policyNo != ''">
            and a1.policy_no = #{policyNo}
        </if>
        <if test="attachmentId != null and attachmentId != ''">
            and a1.attachment_id = #{attachmentId}
        </if>
        <if test="woData != null">
            and a1.wo_data = #{woData}
        </if>
        <if test="executor != null and executor != ''">
            and a1.execute_time = #{executor}
        </if>
        <if test="executeTime != null">
            and a1.executor = #{executeTime}
        </if>
        <if test="executeRemark != null and executeRemark != ''">
            and a1.execute_remark = #{executeRemark}
        </if>
        <if test="remark != null and remark != ''">
            and a1.remark = #{remark}
        </if>
        <if test="gmtCreated != null">
            and a1.gmt_created = #{gmtCreated}
        </if>
        <if test="gmtModified != null">
            and a1.gmt_modified = #{gmtModified}
        </if>
        <if test="creator != null and creator != ''">
            and a1.creator = #{creator}
        </if>
        <if test="modifier != null and modifier != ''">
            and a1.modifier = #{modifier}
        </if>
        <if test="gmtCreatedBegin != null">
            <![CDATA[
            and a1.gmt_created >= #{gmtCreatedBegin}
            ]]>
        </if>
        <if test="gmtCreatedEnd != null">
            <![CDATA[
            and a1.gmt_created < #{gmtCreatedEnd}
            ]]>
        </if>
        <if test="woStatusIn != null">
            and a1.wo_status in
            <foreach item='status' collection='woStatusIn' open='(' separator=',' close=')'>
                #{status}
            </foreach>
        </if>
        <if test="woStatusNotIn != null">
            and a1.wo_status not in
            <foreach item='status' collection='woStatusNotIn' open='(' separator=',' close=')'>
                #{status}
            </foreach>
        </if>
        <if test="woInfoIds != null and woInfoIds != ''">
            <![CDATA[
            and a1.id in (#{woInfoIds})
            ]]>
        </if>
        <if test="woDataLike != null">
            <![CDATA[
            and a1.wo_data like concat('%', #{woDataLike}, '%')
          ]]>
        </if>
        <if test="userId != null">
            and a2.user_id = #{userId}
        </if>
        <if test="tpaUserId != null">
            and a2.tpa_user_id = #{tpaUserId}
        </if>
    </sql>

    <select id="listUserByQuery" resultMap="BaseResultMap" parameterType="com.zhongan.hacksaw.model.workOrder.query.WoInfoQuery" >
        SELECT
        <include refid="Base_Column_List" />
        FROM wo_info a1 LEFT JOIN wo_user_relation a2 on a1.id = a2.wo_info_id and a2.is_deleted = 'N'
        where a1.is_deleted = 'N'
        <include refid="doUserQueryParams" />
        ORDER BY a1.wo_status asc, a1.gmt_created desc LIMIT #{start},#{limit}
    </select>

    <select id="countUserByQuery" parameterType="com.zhongan.hacksaw.model.workOrder.query.WoInfoQuery" resultType="java.lang.Integer">
         SELECT COUNT(a1.id) AS num
         FROM wo_info a1 LEFT JOIN wo_user_relation a2 on a1.id = a2.wo_info_id and a1.is_deleted = 'N'
         where a2.is_deleted = 'N'
         <include refid="doUserQueryParams" />
    </select>

</mapper>