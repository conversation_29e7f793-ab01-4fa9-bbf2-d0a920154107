<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.AutoSettlementCommonRuleDAO">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AutoSettlementCommonRuleExtraDO">
    <result column="clause_name" jdbcType="VARCHAR" property="clauseName"/>
    <result column="clause_code" jdbcType="VARCHAR" property="clauseCode"/>
    <result column="id_str" jdbcType="VARCHAR" property="idStr"/>
  </resultMap>

  <select id="list" resultMap="BaseResultMap">
    select clause_code,clause_name,group_concat(id) as id_str from auto_settlement_common_rule where
    is_deleted = 'N' and clause_code is not null and clause_code != '' and rule_type = 2
    group by clause_code
  </select>

  <update id="updateStats" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementCommonRuleDO">
    update auto_settlement_common_rule set
    total_hits = total_hits + #{totalHits,jdbcType=BIGINT},
    total_misses = total_misses + #{totalMisses,jdbcType=BIGINT} ,
    total_unknowns = total_unknowns + #{totalUnknowns,jdbcType=BIGINT}
    where id=#{id,jdbcType=BIGINT}
  </update>
</mapper>