<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.dao.CargoClaimDAO">
    
    <resultMap id="taskAllocateResultMap" type="com.zhongan.lincoln.dal.domain.CargoClaimDO">
        <result property="id" column="id" />
        <result property="policyId" column="policy_id" />
        <result property="policyNo" column="policy_no" />
        <result property="reportNo" column="report_no" />
        <result property="claimNo" column="claim_no" />
        <result property="campaignDefId" column="campaign_def_id" />
        <result property="packageDefId" column="package_def_id" />
        <result property="reportAmount" column="report_amount"/>
        <result property="yzRiskScore" column="yz_risk_score"/>
        <result property="insuranceTypeCode" column="insurance_type_code"/>
        <result property="status" column="status"/>
        <result property="coreClaimStatus" column="core_claim_status"/>
        <result property="claimReportSource" column="claim_report_source"/>
        <result property="accidentDate" column="accident_date"/>
        <result property="reportDate" column="report_date"/>
        <result property="lossCause" column="loss_cause"/>
        <result property="payState" column="pay_state"/>
        <result property="closeDate" column="close_date"/>
    </resultMap>

    <sql id="noAccidentSpecialProductQueryParams">
        <if test="statusList != null">
            and status in 
            <foreach collection="statusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="insuranceTypeCode != null">
            and insurance_type_code = #{insuranceTypeCode}
        </if>
        <if test="reportNo != null">
            and report_no = #{reportNo}
        </if>
        <if test="packageDefIds != null">
            and package_def_id in
            <foreach collection="packageDefIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="campaignDefIds != null">
            and campaign_def_id in
            <foreach collection="campaignDefIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="insuranceTypeCodeList != null">
            and insurance_type_code in
            <foreach collection="insuranceTypeCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="pidAndLossCauseList != null">
            and concat(package_def_id, '-', loss_cause) in
            <foreach collection="pidAndLossCauseList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="insuranceTypeAndLossCauseList != null">
            and concat(insurance_type_code, '-', loss_cause) in
            <foreach collection="insuranceTypeAndLossCauseList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="noAccidentSpecialProduct" resultMap="taskAllocateResultMap" parameterType="com.zhongan.hacksaw.model.query.CargoClaimQuery">
        select id,policy_id,policy_no,report_no,campaign_def_id,package_def_id from cargo_claim
        where is_deleted = 'N'
        <include refid="noAccidentSpecialProductQueryParams"/>
        and is_include_attachment = 'Y' and (agency_user_id is null or agency_user_id = 0) and claim_type = 2
        and claim_report_source not in ('djichannel', 'digital') limit #{start},#{limit}
    </select>

    <select id="accidentTaskAllocateClaim" resultMap="taskAllocateResultMap" parameterType="com.zhongan.hacksaw.model.query.CargoClaimQuery">
        select id,policy_id,policy_no,report_no,campaign_def_id,package_def_id,report_amount,yz_risk_score,
        insurance_type_code,claim_report_source,accident_date,loss_cause from cargo_claim
        where is_deleted = 'N'
        <include refid="noAccidentSpecialProductQueryParams"/>
        and is_include_attachment = 'Y' and (auditor is null or auditor = "") <![CDATA[ and gmt_modified<date_sub(NOW(),INTERVAL 1 MINUTE) ]]> limit #{start},#{limit}
    </select>

    <select id="antNoAttachAllocateClaim" resultMap="taskAllocateResultMap" parameterType="com.zhongan.hacksaw.model.query.CargoClaimQuery">
        select id,policy_id,policy_no,report_no,campaign_def_id,package_def_id,report_amount,status,insurance_type_code from cargo_claim
        where is_deleted = 'N'
        <include refid="noAccidentSpecialProductQueryParams"/>
        and is_include_attachment = 'N' and claim_type ='2' and (auditor is null or auditor = "") and claim_report_source='alipay_app'
        <![CDATA[ and gmt_created<date_sub(now(),interval 6 HOUR) ]]> limit #{start},#{limit}
    </select>

    <select id="findPayFail" resultMap="taskAllocateResultMap">
        select report_no from cargo_claim where is_deleted = 'N' and claim_type = 2 and (pay_state = 3 or pay_state = 6)
    </select>

    <select id="findTerminatedStatusClaimCount" resultType="java.lang.Integer">
        select count(1) from cargo_claim
        where is_deleted = 'N' and status not in (40,41,42,43,44,57,50,52,107) and claim_type = 2 and insurance_type_code != 88 and gmt_modified > #{gmtModified}
    </select>

    <select id="findTerminatedStatusClaim" resultMap="taskAllocateResultMap">
        select id,report_no from cargo_claim
        where is_deleted = 'N' and status not in (40,41,42,43,44,57,50,52,107) and claim_type = 2 and gmt_modified > #{gmtModified} limit #{start},#{limit}
    </select>

    <select id="findCoreTerminatedStatus" resultMap="taskAllocateResultMap">
        select id,policy_id,report_no,status,core_claim_status from cargo_claim
        where is_deleted = 'N' and status not in (40,41,42,43,44,57,50,52,107) and core_claim_status = #{coreClaimStatus} and claim_type = 2 and insurance_type_code != 88 limit #{start},#{limit}
    </select>

    <select id="queryDiagnosisPreRejectClaim" resultMap="taskAllocateResultMap" parameterType="com.zhongan.hacksaw.model.query.CargoClaimQuery">
        SELECT DISTINCT(a1.report_no) FROM cargo_claim a1 LEFT JOIN claim_hospital_liability_settlement_info s ON a1.report_no =s.report_no
            where a1.is_deleted='N' AND a1.claim_type ='2' and  a1.status in('4','37') AND a1.package_def_id!='51529726'
        AND a1.loss_cause IN('16Legal infectious diseases', '14Legal infectious diseases','14Vaccination failure') AND
        (a1.accident_four_level_loss_cause LIKE '%antigenPositive%' OR a1.accident_four_level_loss_cause LIKE '%nucleicAcidPositive%' )
        AND s.settlement_type='2' AND (s.liability_name LIKE '%确诊%' OR s.liability_name LIKE '%接种失效%')
        <if test="reportNos != null">
            and a1.report_no in
            <foreach collection="reportNos" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="status != null">
            and a1.status =#{status}
        </if>
        <if test="claimTimelinessBegin != null">
            <![CDATA[ and a1.claim_timeliness >=#{claimTimelinessBegin} ]]>
        </if>
        <if test="claimTimelinessEnd != null">
            <![CDATA[ and a1.claim_timeliness <#{claimTimelinessEnd} ]]>
        </if>
         limit #{start},#{limit}
    </select>

    <select id="getFinishedAndNotPaidClaimList" resultMap="taskAllocateResultMap" parameterType="com.zhongan.hacksaw.model.query.CargoClaimQuery" >
        SELECT a1.id,a1.policy_id,a1.policy_no,a1.report_no,a1.campaign_def_id,a1.package_def_id,a1.claim_no,a1.pay_state,a1.close_date
        FROM cargo_claim a1 left join cargo_claim_bank_info b on a1.report_no=b.report_no where b.report_no is not null
        and b.is_deleted='N' and  a1.status in('40','41','42','43','44','57') and a1.pay_state is null AND a1.claim_type ='2' and a1.is_deleted = 'N'
        and a1.close_date >= #{closeDateBegin}
        <if test="campaignDefIdNotIn != null">
            and concat(a1.campaign_def_id,'-', a1.package_def_id) not in
            <foreach collection="campaignDefIdNotIn" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by a1.gmt_modified+'' desc limit #{start},#{limit}
    </select>

    <select id="countFinishedAndNotPaidClaimList" parameterType="com.zhongan.hacksaw.model.query.CargoClaimQuery" resultType="java.lang.Integer">
        SELECT COUNT(a1.id) AS num FROM cargo_claim a1 left join cargo_claim_bank_info b on a1.report_no=b.report_no where b.report_no is not null
        and b.is_deleted='N' and  a1.status in('40','41','42','43','44','57') and a1.pay_state is null AND a1.claim_type ='2' and a1.is_deleted = 'N'
        and a1.close_date >= #{closeDateBegin}
        <if test="campaignDefIdNotIn != null">
            and concat(a1.campaign_def_id,'-', a1.package_def_id) not in
            <foreach collection="campaignDefIdNotIn" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="queryAutoRegisterClaim" resultMap="taskAllocateResultMap" parameterType="com.zhongan.hacksaw.model.query.CargoClaimQuery">
        select id,policy_id,policy_no,report_no,campaign_def_id,package_def_id,accident_date,insurance_type_code from cargo_claim
        where report_no is not null and is_deleted = 'N' and status = '0' and insurance_type_code in (1,2,3,4,5,6,7,8,9,10,11,12,13,77,99)
            and (to_days(NOW()) - to_days(report_date)) >= 14 and claim_type = 2 limit #{start},#{limit}
    </select>

    <select id="queryAutoRegisterClaimCount" resultType="java.lang.Integer">
        select count(1) from cargo_claim
        where report_no is not null and is_deleted = 'N' and status = '0' and insurance_type_code in (1,2,3,4,5,6,7,8,9,10,11,12,13,77,99)
          and (to_days(NOW()) - to_days(report_date)) >= 14 and claim_type = 2
    </select>

    <select id="queryAutoRegisterBackClaimCount" resultType="java.lang.Integer">
        select count(1) from cargo_claim
        where report_no is not null and is_deleted = 'N' and status = '51'and (to_days(NOW()) - to_days(report_date)) >= 14 and claim_type = 2
    </select>

    <select id="queryAutoRegisterBackClaim" resultMap="taskAllocateResultMap" parameterType="com.zhongan.hacksaw.model.query.CargoClaimQuery">
        select id,policy_id,policy_no,report_no,campaign_def_id,package_def_id,accident_date,insurance_type_code from cargo_claim
        where report_no is not null and is_deleted = 'N' and status = '51' and (to_days(NOW()) - to_days(report_date)) >= 14 and claim_type = 2 limit #{start},#{limit}
    </select>

    <select id="queryAutoRegisterForCancelCount" resultType="java.lang.Integer">
        select count(1) from cargo_claim
        where report_no is not null and is_deleted = 'N' and status = '59'and (to_days(NOW()) - to_days(report_date)) >= 14 and claim_type = 2
    </select>

    <select id="queryAutoRegisterForCancelClaim" resultMap="taskAllocateResultMap">
        select id,policy_id,policy_no,report_no,campaign_def_id,package_def_id,accident_date,insurance_type_code from cargo_claim
        where report_no is not null and is_deleted = 'N' and status = '59' and (to_days(NOW()) - to_days(report_date)) >= 14 and claim_type = 2
    </select>

    <select id="queryYesterdayPaidClose" resultMap="taskAllocateResultMap" parameterType="com.zhongan.hacksaw.model.query.CargoClaimQuery">
        select id,policy_id,policy_no,report_no,campaign_def_id,package_def_id,accident_date,insurance_type_code from cargo_claim
        where report_no is not null and is_deleted = 'N' and status = '41' and claim_type = 2 and close_date >= #{closeDateBegin}
        <![CDATA[ and close_date <= #{closeDateEnd} ]]>
    </select>

    <update id="updatePayState">
        update cargo_claim set pay_state = #{payState} where id = #{id}
    </update>

    <select id="queryAutoReserveRelease" resultMap="taskAllocateResultMap" parameterType="com.zhongan.hacksaw.model.query.CargoClaimQuery">
        select id,policy_id,policy_no,report_no,campaign_def_id,package_def_id,accident_date,insurance_type_code,report_date from cargo_claim a1
        where  is_deleted = 'N' and claim_type = 2 and status in (1,3,4,58,53) and insurance_type_code in (1,3)
        <![CDATA[ and loss_cause_name LIKE '%医疗%' and (to_days(NOW()) - to_days(report_date)) > 90
            and report_date>#{reportDateBegin} ]]>
        and claim_type = 2
        <if test="campaignDefIdNotIn != null">
            and concat(a1.campaign_def_id,'-', a1.package_def_id) not in
            <foreach collection="campaignDefIdNotIn" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        limit #{start},#{limit}
    </select>

    <select id="countAutoReserveRelease" resultType="java.lang.Integer">
        select count(*) from cargo_claim a1
        where  is_deleted = 'N' and claim_type = 2 and status in (1,3,4,58,53) and insurance_type_code in (1,3)
        <![CDATA[ and loss_cause_name LIKE '%医疗%' and (to_days(NOW()) - to_days(report_date)) > 90
            and report_date>#{reportDateBegin} ]]>
        and claim_type = 2
        <if test="campaignDefIdNotIn != null">
            and concat(a1.campaign_def_id,'-', a1.package_def_id) not in
            <foreach collection="campaignDefIdNotIn" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>