<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zhongan.lincoln.dal.dao.SpMerchantWoDAO">
    <resultMap id="WorkOrderReport" type="com.zhongan.lincoln.dal.domain.WorkOrderReportDO">
        <result column="wo_num" jdbcType="INTEGER" property="woNum"/>
        <result column="wo_date" jdbcType="VARCHAR" property="woDate"/>
    </resultMap>
    
    <sql id="workReportParams">
        <if test="merchantId !=null">
            and a1.merchant_id=#{merchantId}
        </if>
        <if test="startTime != null">
            and a1.gmt_created >= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[
            and a1.gmt_created < #{endTime}
             ]]>
        </if>
    </sql>

    <select id="digitalWoReportByDay" parameterType="com.zhongan.lincoln.model.query.WorkOrderReportQuery" resultMap="WorkOrderReport">
        select count(distinct (a1.assign_no)) as `wo_num`, date_format(a1.gmt_created, '%Y-%m-%d') as `wo_date`
        from sp_merchant_wo a1
        where a1.is_deleted='N'
        <include refid="workReportParams"/>
        group by date_format(a1.gmt_created, '%Y-%m-%d');
    </select>

    <select id="digitalWoReportByMonth" parameterType="com.zhongan.lincoln.model.query.WorkOrderReportQuery" resultMap="WorkOrderReport">
        select count(distinct (a1.assign_no)) as `wo_num`, date_format(a1.gmt_created, '%Y-%m') as `wo_date`
        from sp_merchant_wo a1
        where a1.is_deleted='N'
        <include refid="workReportParams"/>
        group by date_format(a1.gmt_created, '%Y-%m');
    </select>

</mapper>
