<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.AutoClaimMvcDetailDAO">

  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AutoClaimMvcDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="associate_batch_no" jdbcType="VARCHAR" property="associateBatchNo" />
    <result column="paid_amount_be" jdbcType="VARCHAR" property="paidAmountBe" />
    <result column="paid_amount_af" jdbcType="VARCHAR" property="paidAmountAf" />
    <result column="paid_amount_diff" jdbcType="DECIMAL" property="paidAmountDiff" />
    <result column="paid_amount_diff_per" jdbcType="DECIMAL" property="paidAmountDiffPer" />
    <result column="non_responsible_cost_be" jdbcType="VARCHAR" property="nonResponsibleCostBe" />
    <result column="non_responsible_cost_af" jdbcType="VARCHAR" property="nonResponsibleCostAf" />
    <result column="non_responsible_cost_diff" jdbcType="DECIMAL" property="nonResponsibleCostDiff" />
    <result column="non_responsible_cost_diff_per" jdbcType="DECIMAL" property="nonResponsibleCostDiffPer" />
    <result column="self_expense_category_be" jdbcType="VARCHAR" property="selfExpenseCategoryBe" />
    <result column="self_expense_category_af" jdbcType="VARCHAR" property="selfExpenseCategoryAf" />
    <result column="self_expense_category_diff" jdbcType="DECIMAL" property="selfExpenseCategoryDiff" />
    <result column="self_expense_category_diff_per" jdbcType="DECIMAL" property="selfExpenseCategoryDiffPer" />
    <result column="self_expense_be" jdbcType="VARCHAR" property="selfExpenseBe" />
    <result column="self_expense_af" jdbcType="VARCHAR" property="selfExpenseAf" />
    <result column="self_expense_diff" jdbcType="DECIMAL" property="selfExpenseDiff" />
    <result column="self_expense_diff_per" jdbcType="DECIMAL" property="selfExpenseDiffPer" />
    <result column="medical_insurance_pay_be" jdbcType="VARCHAR" property="medicalInsurancePayBe" />
    <result column="medical_insurance_pay_af" jdbcType="VARCHAR" property="medicalInsurancePayAf" />
    <result column="medical_insurance_pay_diff" jdbcType="DECIMAL" property="medicalInsurancePayDiff" />
    <result column="medical_insurance_pay_diff_per" jdbcType="DECIMAL" property="medicalInsurancePayDiffPer" />
    <result column="close_result_be" jdbcType="VARCHAR" property="closeResultBe" />
    <result column="close_result_af" jdbcType="VARCHAR" property="closeResultAf" />
    <result column="prd_result" jdbcType="VARCHAR" property="prdResult" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>

  <sql id="Base_Column_List">
    id, report_no, batch_no, associate_batch_no, paid_amount_be, paid_amount_af, paid_amount_diff,
    paid_amount_diff_per, non_responsible_cost_be, non_responsible_cost_af, non_responsible_cost_diff,
    non_responsible_cost_diff_per, self_expense_category_be, self_expense_category_af,
    self_expense_category_diff, self_expense_category_diff_per, self_expense_be, self_expense_af,
    self_expense_diff, self_expense_diff_per, medical_insurance_pay_be, medical_insurance_pay_af,
    medical_insurance_pay_diff, medical_insurance_pay_diff_per, close_result_be, close_result_af,prd_result,
    extra_info, gmt_created, gmt_modified, creator, modifier, is_deleted
  </sql>

  <sql id="queryWhereClause">
    where is_deleted = 'N'
    <if test='type != null and type == "paidAmount"'>
      <if test="diffStart != '' and diffStart != null and diffEnd != '' and diffEnd != null">
        <![CDATA[and paid_amount_diff between #{diffStart} and #{diffEnd}]]>
      </if>
      <if test="diffPercent != '' and diffPercent != null">
        <![CDATA[and paid_amount_diff_per <= #{diffPercent}]]>
      </if>
    </if>

    <if test='type != null and type == "nonResponsible"'>
      <if test="diffStart != '' and diffStart != null and diffEnd != '' and diffEnd != null">
        <![CDATA[and non_responsible_cost_diff between #{diffStart} and #{diffEnd}]]>
      </if>
      <if test="diffPercent != '' and diffPercent != null">
        <![CDATA[and non_responsible_cost_diff_per <= #{diffPercent}]]>
      </if>
    </if>

    <if test='type != null and type == "selfCategory"'>
      <if test="diffStart != '' and diffStart != null and diffEnd != '' and diffEnd != null">
        <![CDATA[and self_expense_category_diff between #{diffStart} and #{diffEnd}]]>
      </if>
      <if test="diffPercent != '' and diffPercent != null">
        <![CDATA[and self_expense_category_diff_per <= #{diffPercent}]]>
      </if>
    </if>

    <if test='type != null and type == "self"'>
      <if test="diffStart != '' and diffStart != null and diffEnd != '' and diffEnd != null">
        <![CDATA[and self_expense_diff between #{diffStart} and #{diffEnd}]]>
      </if>
      <if test="diffPercent != '' and diffPercent != null">
        <![CDATA[and self_expense_diff_per <= #{diffPercent}]]>
      </if>
    </if>

    <if test='type != null and type == "medicalPay"'>
      <if test="diffStart != '' and diffStart != null and diffEnd != '' and diffEnd != null">
        <![CDATA[and medical_insurance_pay_diff between #{diffStart} and #{diffEnd}]]>
      </if>
      <if test="diffPercent != '' and diffPercent != null">
        <![CDATA[and medical_insurance_pay_diff_per <= #{diffPercent}]]>
      </if>
    </if>

    <if test='claimResultIsSame != null and claimResultIsSame == "Y"'>
      <![CDATA[and close_result_be = close_result_af]]>
    </if>

    <if test='claimResultIsSame != null and claimResultIsSame == "N"'>
      <![CDATA[and close_result_be != close_result_af]]>
    </if>
    <if test="hitRule != null">
      <![CDATA[and extra_info like concat('%', #{hitRule}, '%')]]>
    </if>

    <if test='batchNo != null'>
      and batch_no = #{batchNo}
    </if>

    <if test='reportNo != null'>
      and report_no = #{reportNo}
    </if>

    <if test='associateBatchNo != null'>
      and associate_batch_no = #{associateBatchNo}
    </if>
  </sql>

 <select id="settlementCount" resultType="java.lang.Long" parameterType="com.zhongan.hacksaw.model.aic.query.AutoClaimMvcDetailQuery">
   select
   count(1)
   from auto_claim_mvc_detail
   <include refid="queryWhereClause"/>
 </select>

  <select id="findSettlementByPage" resultMap="BaseResultMap" parameterType="com.zhongan.hacksaw.model.aic.query.AutoClaimMvcDetailQuery">
    select
    <include refid="Base_Column_List"/>
    from auto_claim_mvc_detail
    <include refid="queryWhereClause"/>
    limit #{offset},#{limit}
  </select>
</mapper>