<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.DeliveryClaimDAO">

  <resultMap id="DeliveryClaimStatsResultMap" type="com.zhongan.hacksaw.model.claim.dto.DeliveryClaimStatsDTO">
    <id column="success_hits" jdbcType="INTEGER" property="successHits" />
    <result column="alipay_query_successes" jdbcType="INTEGER" property="alipayQuerySuccesses" />
    <result column="alipay_query_failures" jdbcType="INTEGER" property="alipayQueryFailures" />
  </resultMap>

  <sql id="doQueryParams">
    <if test="startDate != null">
      and a1.gmt_created >= #{startDate}
    </if>
    <if test="endDate != null">
      <![CDATA[
      and a1.gmt_created < #{endDate}
       ]]>
    </if>
  </sql>

  <select id="statsDeliveryClaim" parameterType="com.zhongan.hacksaw.model.claim.query.DeliveryClaimQuery" resultMap="DeliveryClaimStatsResultMap">
    select
    <!--  查询支付宝账号成功的数量  -->
    sum(case when a1.cus_account_no != 'originalAccount' and SUBSTR(cus_account_id,1,4) = '2088' and a1.cus_account_id != 'noResult' and (a1.cus_cert_real_name = '' or a1.cus_cert_real_name is null) then 1 else 0 end) as alipay_query_successes,
    <!--  查询支付宝账号失败的数量  -->
    sum(case when a1.cus_account_id = 'noResult' then 1 else 0 end) as alipay_query_failures,
    <!--  数超撞库成功的数量  -->
    sum(case when a1.cus_cert_real_name is not null and a1.cus_cert_real_name != '' and a1.cus_account_no is not null and a1.cus_account_no != '' then 1 else 0 end) as success_hits
    from delivery_claim a1 WHERE a1.is_deleted = 'N'
    <include refid="doQueryParams"/>
  </select>
</mapper>