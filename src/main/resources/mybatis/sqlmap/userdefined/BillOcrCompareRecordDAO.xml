<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.BillOcrCompareRecordDAO">

  <resultMap id="OcrUseRateDetailEntity" type="com.zhongan.lincoln.model.dto.monitor.OcrUseRateDetailDTO">
    <result column="report_no" jdbcType="VARCHAR" property="reportNo"/>
    <result column="invoice_num" jdbcType="BIGINT" property="invoiceNum"/>
    <result column="use_ocr_invoice_num" jdbcType="BIGINT" property="useOcrInvoiceNum"/>
  </resultMap>

  <resultMap id="OcrUseRateEntity" type="com.zhongan.lincoln.model.dto.monitor.OcrUseRateDTO">
    <result column="account" jdbcType="VARCHAR" property="account"/>
    <result column="case_num" jdbcType="BIGINT" property="caseNum"/>
    <result column="invoice_num" jdbcType="BIGINT" property="invoiceNum"/>
    <result column="use_ocr_invoice_num" jdbcType="BIGINT" property="useOcrInvoiceNum"/>
  </resultMap>

  <resultMap id="OcrRecordNumEntity" type="com.zhongan.lincoln.model.dto.monitor.OcrRecordNumDTO">
    <result column="record_num" jdbcType="VARCHAR" property="recordNum"/>
    <result column="record_date" jdbcType="BIGINT" property="recordDate"/>
  </resultMap>

  <sql id="queryParams">
    <if test="source != null">
      and a1.source = #{source}
    </if>
    <if test="startTime != null">
      and a1.gmt_created > #{startTime}
    </if>
    <if test="ocrJsonIsNot != null">
      and ocr_json != #{ocrJsonIsNot}
    </if>
    <if test="ocrJsonIsNotNull != null">
      and ocr_json is not null
    </if>
    <if test="endTime != null">
      <![CDATA[
      and a1.gmt_created <= #{endTime}
      ]]>
    </if>
  </sql>

  <select id="ocrUseRateDetail" resultMap="OcrUseRateDetailEntity">
    SELECT report_no,count(*) as invoice_num,count(enter_start_time) as use_ocr_invoice_num FROM bill_ocr_compare_record 
    where report_no is not null and enter_end_time is not null and modifier = #{account}
    and gmt_modified >= #{startTime}
    <![CDATA[and gmt_modified < #{endTime}]]> group by report_no;
  </select>

  <select id="ocrUseRate" resultMap="OcrUseRateEntity">
    SELECT modifier as account,count(report_no) as case_num,count(*) as invoice_num,count(enter_start_time) as use_ocr_invoice_num FROM bill_ocr_compare_record
    where report_no is not null and enter_end_time is not null and modifier != 'system' 
    and gmt_modified >= #{startTime}
    <![CDATA[and gmt_modified < #{endTime}]]> group by modifier;
  </select>

  <update id="updateOcrJson">
    update bill_ocr_compare_record set ocr_json = #{ocrJson} where id = #{id}
  </update>

  <select id="countOcrRecords" resultMap="OcrRecordNumEntity"
    parameterType="com.zhongan.lincoln.model.query.OcrRecordInfoQuery">
    select count(distinct(report_no)) as record_num,date_format(a1.gmt_created,'%Y-%m-%d') as record_date from
    bill_ocr_compare_record a1 WHERE
    a1.is_deleted = 'N'
    <include refid="queryParams"></include>
    group by date_format(a1.gmt_created,'%Y-%m-%d')
  </select>

  <select id="listReportNo" resultType="java.lang.String"
    parameterType="com.zhongan.lincoln.model.query.OcrRecordInfoQuery">
    select distinct(report_no) from bill_ocr_compare_record a1 WHERE a1.is_deleted = 'N'
    <include refid="queryParams"></include>
  </select>

  <update id="updateReqNum" parameterType="java.util.List">
    update bill_ocr_compare_record set req_num = if(req_num is NULL, 1, req_num + 1) where id in
    <foreach item='item' collection='list' open='(' separator=',' close=')'>
      #{item}
    </foreach>
  </update>
</mapper>