<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.BillOcrCompareCountDAO">

  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.model.dto.monitor.BillCountDTO">
    <result column="count_date" jdbcType="VARCHAR" property="countDate" />
    <result column="total_num" jdbcType="INTEGER" property="totalNum" />
    <result column="hos_correct_num" jdbcType="INTEGER" property="hosCorrectNum" />
    <result column="amount_correct_num" jdbcType="INTEGER" property="amountCorrectNum" />
    <result column="bill_num_correct_num" jdbcType="INTEGER" property="billNumCorrectNum" />
    <result column="type_correct_num" jdbcType="INTEGER" property="typeCorrectNum" />
    <result column="visit_start_correct_num" jdbcType="INTEGER" property="visitStartCorrectNum" />
    <result column="visit_end_correct_num" jdbcType="INTEGER" property="visitEndCorrectNum" />
    <result column="self_expense_correct_num" jdbcType="INTEGER" property="selfExpenseCorrectNum" />
    <result column="self_expense_category_correct_num" jdbcType="INTEGER" property="selfExpenseCategoryCorrectNum" />
    <result column="medical_pay_correct_num" jdbcType="INTEGER" property="medicalPayCorrectNum" />
  </resultMap>

  <sql id="queryParams">
    <if test="source != null">
      and source = #{source}
    </if>
    <if test="startTime != null">
      and gmt_created > #{startTime}
    </if>
    <if test="endTime != null">
      <![CDATA[
      and gmt_created <= #{endTime}
      ]]>
    </if>
    <if test="province == null">
      and province is null
    </if>
    <if test="province != null">
      and province = #{province}
    </if>
  </sql>

  <update id="updateOne" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareCountDO">
    update bill_ocr_compare_count set total_num =total_num + 1
    <if test="hosCorrectNum != null">
      ,hos_correct_num = hos_correct_num + #{hosCorrectNum}
    </if>
    <if test="amountCorrectNum != null">
      ,amount_correct_num = amount_correct_num + #{amountCorrectNum}
    </if>
    <if test="billNumCorrectNum != null">
      ,bill_num_correct_num = bill_num_correct_num + #{billNumCorrectNum}
    </if>
    <if test="typeCorrectNum != null">
      ,type_correct_num = type_correct_num + #{typeCorrectNum}
    </if>
    <if test="visitStartCorrectNum != null">
      ,visit_start_correct_num = visit_start_correct_num + #{visitStartCorrectNum}
    </if>
    <if test="visitEndCorrectNum != null">
      ,visit_end_correct_num = visit_end_correct_num + #{visitEndCorrectNum}
    </if>
    <if test="selfExpenseCorrectNum != null">
      ,self_expense_correct_num = self_expense_correct_num + #{selfExpenseCorrectNum}
    </if>
    <if test="selfExpenseCategoryCorrectNum != null">
      ,self_expense_category_correct_num = self_expense_category_correct_num + #{selfExpenseCategoryCorrectNum}
    </if>
    <if test="medicalPayCorrectNum != null">
      ,medical_pay_correct_num = medical_pay_correct_num + #{medicalPayCorrectNum}
    </if>
    <if test="patientNameCorrectNum != null">
      ,patient_name_correct_num = patient_name_correct_num + #{patientNameCorrectNum}
    </if>
    where id = #{id}
  </update>

  <select id="sumBetweenDate" resultType="java.util.HashMap">
    select sum(total_num) as totalNum,sum(hos_correct_num) as hosCorrectNum,
    sum(amount_correct_num) as amountCorrectNum,sum(bill_num_correct_num) as billNumCorrectNum,
    sum(type_correct_num) as typeCorrectNum,sum(visit_start_correct_num) as visitStartCorrectNum,
    sum(visit_end_correct_num) as visitEndCorrectNum,
    sum(self_expense_correct_num) as selfExpenseCorrectNum,
    sum(self_expense_category_correct_num) as selfExpenseCategoryCorrectNum,
    sum(medical_pay_correct_num) as medicalPayCorrectNum,
    sum(patient_name_correct_num) as patientNameCorrectNum
    from bill_ocr_compare_count
    where is_deleted = 'N'
    <include refid="queryParams"></include>
  </select>

  <select id="selectByMonth" resultMap="BaseResultMap">
    select
    date_format(count_date,'%Y-%m') as count_date,sum(total_num) as total_num,
    sum(hos_correct_num) as hos_correct_num,
    sum(amount_correct_num) as amount_correct_num,
    sum(bill_num_correct_num) as bill_num_correct_num,
    sum(type_correct_num) as type_correct_num,
    sum(visit_start_correct_num) as visit_start_correct_num,
    sum(visit_end_correct_num) as visit_end_correct_num,
    sum(self_expense_correct_num) as self_expense_correct_num,
    sum(self_expense_category_correct_num) as self_expense_category_correct_num,
    sum(medical_pay_correct_num) as medical_pay_correct_num
    from bill_ocr_compare_count where is_deleted = 'N'
    <include refid="queryParams"></include>
    group by date_format(count_date,'%Y-%m') order by count_date desc
  </select>

</mapper>