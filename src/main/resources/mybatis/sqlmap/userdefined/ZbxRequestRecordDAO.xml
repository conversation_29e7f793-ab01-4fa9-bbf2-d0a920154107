<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.ZbxRequestRecordDAO">

    <resultMap id="WorkOrderReport" type="com.zhongan.lincoln.dal.domain.WorkOrderReportDO">
        <result column="wo_num" jdbcType="INTEGER" property="woNum"/>
        <result column="wo_date" jdbcType="VARCHAR" property="woDate"/>
    </resultMap>

    <sql id="workReportParams">
        <if test="isSuccess != null">
            and a1.is_success=#{isSuccess}
        </if>
        <if test="isRepeat != null">
            and a1.is_repeat=#{isRepeat}
        </if>
        <if test="startTime != null">
            and a1.gmt_created >= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[ and a1.gmt_created < #{endTime} ]]>
        </if>
    </sql>

    <sql id="countParams">
        <if test="isSuccess != null">
            and a1.is_success=#{isSuccess}
        </if>
        <if test="isRepeat != null">
            and a1.is_repeat=#{isRepeat}
        </if>
        <if test="startTime != null">
            and a1.gmt_created >= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[ and a1.gmt_created < #{endTime} ]]>
        </if>
    </sql>

    <sql id="zbxRequestQuery">
        <if test="isSuccess != null">
            and a1.is_success=#{isSuccess}
        </if>
        <if test="isRepeat != null">
            and a1.is_repeat=#{isRepeat}
        </if>
        <if test="startTime != null">
            and a1.gmt_created >= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[ and a1.gmt_created < #{endTime} ]]>
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </sql>

    <select id="zbxReportByMonth" parameterType="com.zhongan.lincoln.model.query.WorkOrderReportQuery" resultMap="WorkOrderReport">
        select count(*) as wo_num, date_format(a1.gmt_created, '%Y-%m') as wo_date
        from zbx_request_record a1
        where a1.is_deleted = 'N'
        <include refid="workReportParams"/>
        group by date_format(a1.gmt_created, '%Y-%m');
    </select>

    <select id="zbxReportByDay" parameterType="com.zhongan.lincoln.model.query.WorkOrderReportQuery" resultMap="WorkOrderReport">
        select count(*) as wo_num, date_format(a1.gmt_created, '%Y-%m-%d') as wo_date
        from zbx_request_record a1
        where a1.is_deleted = 'N'
        <include refid="workReportParams"/>
        group by date_format(a1.gmt_created, '%Y-%m-%d');
    </select>

</mapper>
