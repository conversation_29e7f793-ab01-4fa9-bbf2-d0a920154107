<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.dao.AutoClaimRecordDAO">

    <resultMap id="CountResultMap" type="com.zhongan.lincoln.model.dto.CountDTO">
        <result column="column" jdbcType="VARCHAR" property="column"/>
        <result column="c" jdbcType="VARCHAR" property="count"/>
    </resultMap>

    <select id="listRepetitive" resultMap="CountResultMap">
        select report_no `column`, count(1) `count` from sp_auto_claim_record
        where is_deleted = 'N' and gmt_created >  #{gmtCreatedStart} and  #{gmtCreatedEnd} > gmt_created
        group by report_no
        having `count` > 1
    </select>
</mapper>