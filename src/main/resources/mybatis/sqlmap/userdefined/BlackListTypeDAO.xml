<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.dao.BlackListTypeDAO">

    <select id="queryFirstLevelList" resultType="java.lang.String">
        select distinct(first_level_reason) from black_list_type where is_deleted = 'N' and insurance_type_code = #{insuranceTypeCode};
    </select>

    <select id="queryInsuranceTypeCodes" resultType="java.lang.Integer">
        select distinct(insurance_type_code) from black_list_type where is_deleted = 'N';
    </select>

    <select id="queryTypeIdsByReasons" resultType="java.lang.Long">
        select id from black_list_type where is_deleted = 'N'
        <if test="reasons != null ">
            and secend_level_reason in
            <foreach item='reason' collection='reasons' open='(' separator=',' close=')'>
                 #{reason}
            </foreach>
        </if>
    </select>
</mapper>
