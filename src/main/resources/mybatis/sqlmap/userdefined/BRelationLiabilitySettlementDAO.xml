<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.dao.BRelationLiabilitySettlementDAO">

    <select id="groupSumAmount" parameterType="string" resultType="com.zhongan.hacksaw.model.dto.AggregationDTO">
        SELECT product_id lKey, sum(reserve_amount) dSum1, sum(paid_amount) dSum2
        FROM claim_batch_relation_liability_settlement
        WHERE batch_claim_bill_no = #{batchClaimBillNo} and amount_type = 2 and is_deleted = 'N'
        GROUP BY product_id
    </select>
    <delete id="deleteById" parameterType="java.lang.Long">
        delete from claim_batch_relation_liability_settlement  where id = #{id}
    </delete>
</mapper>