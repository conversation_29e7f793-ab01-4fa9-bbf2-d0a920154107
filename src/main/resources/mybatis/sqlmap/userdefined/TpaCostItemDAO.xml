<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.TpaCostItemDAO">

    <resultMap id="TpaCostItemFlowResultMap" type="com.zhongan.lincoln.model.dto.tpa.TpaCostItemFlowDTO">
        <result property="companyId" javaType="java.lang.Long" column="companyId"/>
        <result property="companyType" javaType="java.lang.String" column="companyType"/>
        <result property="claimNum" javaType="java.lang.Integer" column="claimNum"/>
        <result property="claimAmount" javaType="java.math.BigDecimal" column="claimAmount"/>
        <result property="outboundAmount" javaType="java.math.BigDecimal" column="outboundAmount"/>
        <result property="amount" javaType="java.math.BigDecimal" column="amount"/>
    </resultMap>

    <sql id="queryParams">
        <if test="companyId != null">
            and company_id = #{companyId}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="tag != null">
            <![CDATA[
                and (tag & #{tag}) = #{tag}
            ]]>
        </if>
        <if test="effectiveTimeBegin != null">
            and effective_time > #{effectiveTimeBegin}
        </if>
        <if test="effectiveTimeEnd != null">
            <![CDATA[
                and effective_time <= #{effectiveTimeEnd}
            ]]>
        </if>
    </sql>

    <select id="summaryByQuery" resultMap="TpaCostItemFlowResultMap" parameterType="com.zhongan.lincoln.model.request.TpaCostItemQuery">
        select company_id as companyId, company_type as companyType, count(cost_no) as claimNum,
        sum(claim_price) as claimAmount, sum(outbound_price) as outboundAmount, sum(claim_price) + sum(outbound_price) as amount
        from tpa_claim_cost_item where is_deleted = 'N'
        <include refid="queryParams"/>
        group by company_id
    </select>

</mapper>