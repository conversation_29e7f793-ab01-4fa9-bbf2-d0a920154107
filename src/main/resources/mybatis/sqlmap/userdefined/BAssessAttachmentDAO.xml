<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.dao.BAssessAttachmentDAO">

    <update id="updateAttachTypeIsCompleteByBatchNo" parameterType="com.zhongan.lincoln.dal.domain.BAssessAttachmentDO">
        UPDATE claim_batch_assess_attachment set gmt_modified = now()
        <if test="modifier != null">
            ,modifier = #{modifier}
        </if>
        <if test="isComplete != null">
            ,is_complete = #{isComplete}
        </if>
        WHERE batch_claim_bill_no = #{batchClaimBillNo} and attachment_type = #{attachmentType}
    </update>

</mapper>