<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.dao.BRelationLiabilityInfoDAO">

    <select id="groupSumInsured" parameterType="string" resultType="com.zhongan.hacksaw.model.dto.AggregationDTO">
        select product_id lKey, sum(cast(sum_insured as decimal(18,2))) dSum1, sum(cast(unused_sum_insured as decimal(18,2))) dSum2
        from claim_batch_relation_liability_info
        where batch_claim_bill_no = #{batchClaimBillNo} and is_deleted = 'N'
        group by product_id
    </select>

    <delete id="deleteByBatchNo" parameterType="java.lang.String">
        delete from claim_batch_relation_liability_info where batch_claim_bill_no = #{batchClaimBillNo}
    </delete>

</mapper>