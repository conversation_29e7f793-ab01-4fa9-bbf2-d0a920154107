<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.dao.ClaimPayProblemDAO">
    <resultMap id="queryTabResultMap" type="com.zhongan.lincoln.dal.domain.ClaimPayProblemDO">
        <result property="id" column="id" />
        <result property="reportNo" column="report_no" />
        <result property="policyNo" column="policy_no" />
        <result property="productName" column="product_name" />
        <result property="policyInsurant" column="policy_insurant" />
        <result property="insuranceTypeCode" column="insurance_type_code" />
        <result property="status" column="status" />
        <result property="settleUserName" column="settle_user_name" />
        <result property="closeDate" column="close_date" />
        <result property="indemnityAmount" column="indemnity_amount" />
        <result property="payState" column="pay_state" />
        <result property="payInfo" column="pay_info" />
        <result property="paymentDate" column="payment_date" />
        <result property="isPayWait" column="is_pay_wait" />
        <result property="noticeStatus" column="notice_status" />
        <result property="auditStatus" column="audit_status" />
        <result property="visitStatus" column="visit_status" />
        <result property="creator" column="creator" />
        <result property="gmtCreated" column="gmt_created" />
        <result property="modifier" column="modifier" />
        <result property="gmtModified" column="gmt_modified" />
        <result property="isDeleted" column="is_deleted" />
        <result property="claimBankInfoId" column="claim_bank_info_id" />
    </resultMap>

    <sql id="claimPayProblem_do_columns" >
    a1.id, a1.report_no, a1.policy_no, a1.product_name, a1.policy_insurant, a1.insurance_type_code,
      a1.status,a1.settle_user_name, a1.close_date, a1.indemnity_amount, a1.pay_state,a1.pay_info, a1.payment_date, a1.is_pay_wait,
      a1.notice_status,a1.audit_status,a1.visit_status,a1.creator, a1.gmt_created, a1.modifier, a1.gmt_modified, a1.is_deleted,a1.claim_bank_info_id
  </sql>

    <sql id="doClaimPayProblemQueryParams">
        <if test="reportNo != null and reportNo != ''">
            and a1.report_no = #{reportNo}
        </if>
        <if test="policyNo != null and policyNo != ''">
            and a1.policy_no = #{policyNo}
        </if>
        <if test="policyNoIn != null and policyNoIn != ''">
            and a1.policy_no in (${policyNoIn})
        </if>
        <if test="productName != null and productName != ''">
            and a1.product_name = #{productName}
        </if>
        <if test="policyInsurant != null and policyInsurant != ''">
            and a1.policy_insurant = #{policyInsurant}
        </if>
        <if test="insuranceTypeCode != null">
            and a1.insurance_type_code = #{insuranceTypeCode}
        </if>
        <if test="insuranceTypeCodeIn != null and insuranceTypeCodeIn != ''">
            and a1.insurance_type_code in (${insuranceTypeCodeIn})
        </if>
        <if test="status != null">
            and a1.status = #{status}
        </if>
        <if test="settleUserName != null and settleUserName != ''">
            and a1.settle_user_name = #{settleUserName}
        </if>
        <if test="closeDateBegin != null">
            and a1.close_date >= #{closeDateBegin}
        </if>
        <if test="closeDateEnd != null">
            <![CDATA[   and a1.close_date < #{closeDateEnd} ]]>
        </if>
        <if test="gmtCreatedBegin != null">
            and a1.gmt_created >= #{gmtCreatedBegin}
        </if>
        <if test="gmtCreatedEnd != null">
            <![CDATA[   and a1.gmt_created< #{gmtCreatedEnd} ]]>
        </if>
        <if test='isPayWait != null and isPayWait == "N"'>
            and a1.is_pay_wait = #{isPayWait}
        </if>
        <if test='isPayWait != null and isPayWait == "Y"'>
            <![CDATA[
        AND (a1.is_pay_wait='Y' OR a1.visit_status='3')
              ]]>
        </if>
        <if test="noticeStatus != null">
            and a1.notice_status = #{noticeStatus}
        </if>
        <if test="payState != null">
            and a1.pay_state = #{payState}
        </if>
        <if test="payStateIn != null and payStateIn != ''">
            and a1.pay_state in (${payStateIn})
        </if>
        <if test="auditStatus != null">
            and a1.audit_status = #{auditStatus}
        </if>
        <if test="visitStatus != null">
            and a1.visit_status = #{visitStatus}
        </if>

        <if test="payTimelinessBegin != null">
            <![CDATA[
           and ( (a1.pay_state=2 and (to_days(a1.payment_date) - to_days(a1.close_date)) >= #{payTimelinessBegin})
          or
          (a1.pay_state in(0,1,3,4) and (to_days(NOW()) - to_days(a1.close_date)) >= #{payTimelinessBegin}) )
          ]]>
        </if>
        <if test="payTimelinessEnd != null">
            <![CDATA[
         and ( (a1.pay_state=2 and (to_days(a1.payment_date) - to_days(a1.close_date)) <= #{payTimelinessEnd})
          or
          (a1.pay_state in(0,1,3,4) and (to_days(NOW()) - to_days(a1.close_date)) <= #{payTimelinessEnd}) )
          ]]>
        </if>
        <if test='isShowNotice != null and isShowNotice == "Y"'>
            <![CDATA[
             and a1.notice_status in (0,1,2,3)
              ]]>
        </if>
        <if test='isShowNotice != null and isShowNotice == "N"'>
            <![CDATA[
              and (a1.notice_status is null or a1.notice_status =99 )
              ]]>
        </if>

        <if test='auditStatusIn != null and auditStatusIn == "other"'>
            <![CDATA[
              and a1.audit_status in (0,1)
              ]]>
        </if>

        <if test='auditStatusIn != null and auditStatusIn == "pending"'>
            <![CDATA[
              and a1.audit_status=2
              ]]>
        </if>
        <if test='auditStatusIn != null and auditStatusIn == "notAuditPass"'>
            <![CDATA[
              and a1.audit_status in (1,3)
              ]]>
        </if>

        <if test="packageDefId != null ">
            and a1.package_def_id in (${packageDefId})
        </if>
        <if test="campaignDefId != null">
            and a1.campaign_def_id = #{campaignDefId}
        </if>
        <if test='isHandle != null and isHandle == "Y"'>
            <![CDATA[
            AND a1.pay_state IN(3,6)
             and (a1.notice_status is null or a1.notice_status not in (0,3) )
             and ( a1.visit_status is null or a1.visit_status not in ('0','1') )
              ]]>
        </if>
        <if test='isHandle != null and isHandle == "N"'>
            <![CDATA[
        AND ( a1.pay_state IN(0,1,4) or a1.notice_status in (0,3)
                or a1.visit_status IN('0','1') )
              ]]>
        </if>

    </sql>

    <select id="listByQueryTab" parameterType="com.zhongan.hacksaw.model.deadpool.query.ClaimPayProblemQuery" resultMap="queryTabResultMap">
        SELECT
        <include refid="claimPayProblem_do_columns"/>
        FROM claim_pay_problem a1 WHERE a1.is_deleted = 'N'
        <include refid="doClaimPayProblemQueryParams"/>
        order by a1.gmt_modified desc limit #{start},#{limit}
    </select>

    <select id="countByQueryTab" parameterType="com.zhongan.hacksaw.model.deadpool.query.ClaimPayProblemQuery" resultType="java.lang.Integer">
        SELECT COUNT(a1.id) AS num FROM claim_pay_problem a1
        WHERE a1.is_deleted = 'N'
        <include refid="doClaimPayProblemQueryParams"/>
    </select>

    <select id="listClaimPayProblem"  resultMap="queryTabResultMap" parameterType="com.zhongan.hacksaw.model.deadpool.query.ClaimPayProblemQuery">
        SELECT
        <include refid="claimPayProblem_do_columns"/>
        FROM claim_pay_problem a1 WHERE a1.is_deleted = 'N'
        <include refid="doClaimPayProblemQueryParams"/>
        order by a1.gmt_modified
    </select>
</mapper>