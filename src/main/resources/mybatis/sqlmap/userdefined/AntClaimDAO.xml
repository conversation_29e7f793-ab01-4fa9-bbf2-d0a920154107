<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.dao.AntClaimDAO">

    <select id="queryAntClaimCampaignDefIds" resultType="java.lang.Long">
        select distinct(campaign_def_id) from ant_claim where is_deleted = 'N' ;
    </select>

    <select id="queryAntClaimProductPackageIds" resultType="java.lang.Long">
        select distinct(package_def_id) from ant_claim where campaign_def_id = #{campaignDefId,jdbcType=BIGINT}
    </select>
</mapper>
