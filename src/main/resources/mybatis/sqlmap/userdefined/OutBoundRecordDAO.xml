<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.OutBoundRecordDAO">

    <resultMap id="OutBoundRecordViewDTO" type="com.zhongan.lincoln.model.dto.outBound.OutBoundRecordViewDTO">
        <result column="count_num" jdbcType="VARCHAR" property="countNum"/>
        <result column="cno" jdbcType="VARCHAR" property="cno"/>
    </resultMap>

    <select id="countCno" parameterType="com.zhongan.lincoln.model.dto.outBound.OutBoundRecordQuery"
            resultMap="OutBoundRecordViewDTO">
        select a1.cno as cno,count(*) as count_num from out_bound_record as a1 where a1.is_deleted = 'N'
        <if test="startTime != null">
            and a1.call_time >= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[
      and a1.call_time <= #{endTime}
      ]]>
        </if>
        group by a1.cno
    </select>

    <!--  查询指定公司外呼接通量  -->
    <select id="countConnectionsCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM out_bound_record
        WHERE is_deleted = 'N'
        AND company_id = #{companyId}
        <if test="beginDate != null and endDate != null">
            AND call_time BETWEEN #{beginDate} AND #{endDate}
        </if>
        AND extra_info LIKE '%双方接听%';
    </select>

</mapper>