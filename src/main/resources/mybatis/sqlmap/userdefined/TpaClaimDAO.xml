<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.TpaClaimDAO">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.TpaClaimDO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="report_no" jdbcType="VARCHAR" property="reportNo"/>
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo"/>
    <result column="claim_no" jdbcType="VARCHAR" property="claimNo"/>
    <result column="report_amount" jdbcType="VARCHAR" property="reportAmount"/>
    <result column="report_date" jdbcType="TIMESTAMP" property="reportDate"/>
    <result column="company_id" jdbcType="BIGINT" property="companyId"/>
    <result column="employee_id" jdbcType="BIGINT" property="employeeId"/>
    <result column="is_error" jdbcType="TINYINT" property="isError"/>
    <result column="error_type" jdbcType="VARCHAR" property="errorType"/>
    <result column="error_remark" jdbcType="VARCHAR" property="errorRemark"/>
    <result column="allot_date" jdbcType="TIMESTAMP" property="allotDate"/>
    <result column="cooperate_method" jdbcType="TINYINT" property="cooperateMethod"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime"/>
    <result column="submit_num" jdbcType="TINYINT" property="submitNum"/>
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated"/>
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    <result column="creator" jdbcType="VARCHAR" property="creator"/>
    <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
  </resultMap>

  <sql id="doQueryParams">
    <if test="companyId != null">
      and a1.company_id = #{companyId}
    </if>
    <if test="employeeId != null">
      and a1.employee_id = #{employeeId}
    </if>
    <if test="reportNo != null and reportNo != ''">
      and a1.report_no = #{reportNo}
    </if>
    <if test="submitDateBegin != null">
      and a1.submit_date > #{submitDateBegin}
    </if>
    <if test="submitDateEnd != null">
      <![CDATA[   and a1.submit_date <= #{submitDateEnd} ]]>
    </if>
    <if test="allotDateBegin != null">
      and a1.allot_date > #{allotDateBegin}
    </if>
    <if test="allotDateEnd != null">
      <![CDATA[   and a1.allot_date <= #{allotDateEnd} ]]>
    </if>
    <if test="status != null">
      and a1.status = #{status}
    </if>
    <if test="statusIn != null and statusIn != ''">
      and a1.status in (${statusIn})
    </if>
    <if test="claimStatus != null">
      and c1.status = #{claimStatus}
    </if>
  </sql>

  <sql id="doQueryMonitorParams">
    <if test="companyId != null">
      and a1.company_id = #{companyId}
    </if>
    <if test="employeeId != null">
      and a1.employee_id = #{employeeId}
    </if>
    <if test="claimStatusIn != null and claimStatusIn != ''">
      <![CDATA[
      and c1.status in (${claimStatusIn})
       ]]>
    </if>
    <if test="allotDateStart != null">
      and a1.allot_date > #{allotDateStart}
    </if>
    <if test="allotDateEnd != null">
      <![CDATA[   and a1.allot_date <= #{allotDateEnd} ]]>
    </if>
    <if test="statusIn != null and statusIn != ''">
      <![CDATA[
      and a1.status in (${statusIn})
          ]]>
    </if>
  </sql>

  <sql id="Base_Column_List">
    a1.id, a1.report_no, a1.policy_no, a1.claim_no, a1.report_amount, a1.report_date, a1.company_id, a1.employee_id,
    a1.is_error, a1.error_type, a1.error_remark, a1.allot_date,
    a1.cooperate_method, a1.status, a1.submit_time, a1.submit_num, a1.extra_info, a1.remark, a1.is_deleted,
    a1.gmt_created, a1.gmt_modified, a1.creator, a1.modifier
  </sql>

  <select id="pageByQuery" parameterType="com.zhongan.hacksaw.model.tpa.query.TpaClaimQuery"
    resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM tpa_claim a1 left join cargo_claim c1 on a1.report_no = c1.report_no
    WHERE a1.is_deleted = 'N'
    <include refid="doQueryParams"/>
    order by a1.gmt_created desc limit #{offset},#{limit}
  </select>

  <select id="count" parameterType="com.zhongan.hacksaw.model.tpa.query.TpaClaimQuery"
    resultType="java.lang.Long">
    select count(*) from tpa_claim a1 left join cargo_claim c1 on a1.report_no = c1.report_no
    WHERE a1.is_deleted = 'N'
    <include refid="doQueryParams"/>
  </select>

  <select id="countByQuery" parameterType="com.zhongan.hacksaw.model.tpa.query.TpaMonitorQuery"
    resultType="java.lang.Long">
    select count(*) from tpa_claim a1 left join cargo_claim c1 on a1.report_no = c1.report_no
    WHERE a1.is_deleted = 'N'
    <include refid="doQueryMonitorParams"/>
  </select>

  <select id="countByQuerySingle" parameterType="com.zhongan.hacksaw.model.tpa.query.TpaMonitorQuery"
    resultType="java.lang.Long">
    select count(*) from tpa_claim a1 WHERE a1.is_deleted = 'N'
    <include refid="doQueryMonitorParams"/>
  </select>
</mapper>