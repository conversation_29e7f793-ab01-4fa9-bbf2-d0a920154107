<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.AutoSettlementInfoShadowDAO">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AutoSettlementInfoShadowDO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="report_no" jdbcType="VARCHAR" property="reportNo"/>
    <result column="paid_amount" jdbcType="VARCHAR" property="paidAmount"/>
    <result column="settlement_type" jdbcType="VARCHAR" property="settlementType"/>
    <result column="is_same" jdbcType="VARCHAR" property="isSame"/>
    <result column="liability_id" jdbcType="BIGINT" property="liabilityId"/>
    <result column="bill_amount_is_same" jdbcType="TINYINT" property="billAmountIsSame"/>
    <result column="diff_rate" jdbcType="INTEGER" property="diffRate"/>
    <result column="bill_amount_diff_rate" jdbcType="INTEGER" property="billAmountDiffRate"/>
  </resultMap>

  <sql id="Base_Column_List">
    a1.id,a1.report_no,a1.paid_amount,a1.settlement_type,a1.is_same,a1.liability_id,
    a1.bill_amount_is_same,a1.diff_rate,a1.bill_amount_diff_rate
  </sql>

  <sql id="queryWhereClause">
    <if test="reportDateEnd != null">
      <![CDATA[and c1.report_date < #{reportDateEnd}]]>
    </if>
    <if test="reportDateBegin != null">
      and c1.report_date > #{reportDateBegin}
    </if>
    <if test="isSame != null">
      and a1.is_same = #{isSame}
    </if>
    <if test="billAmountIsSame != null">
      and a1.bill_amount_is_same = #{billAmountIsSame}
    </if>
    <if test="settlementTypeIsSame != null">
      and a1.settlement_type_is_same = #{settlementTypeIsSame}
    </if>
    <if test="paidAmountDiffRateMin != null">
      and a1.diff_rate >= #{paidAmountDiffRateMin}
    </if>
    <if test="paidAmountDiffRateMax != null">
      <![CDATA[and a1.diff_rate <= #{paidAmountDiffRateMax}]]>
    </if>
    <if test="billAmountDiffRateMin != null">
      and a1.bill_amount_diff_rate >= #{billAmountDiffRateMin}
    </if>
    <if test="billAmountDiffRateMax != null">
      <![CDATA[and a1.bill_amount_diff_rate <= #{billAmountDiffRateMax}]]>
    </if>
    <if test="isSameOrRateNull != null and isSameOrRateNull">
      and (a1.is_same is null or a1.bill_amount_is_same is null or a1.diff_rate is null or
      a1.bill_amount_diff_rate is null or a1.settlement_type_is_same is null)
    </if>
    <if test="reportNo != null and reportNo != ''">
      and a1.report_no = #{reportNo}
    </if>
    <if test="statusIn != null and statusIn != ''">
      and c1.status in (${statusIn})
    </if>
    <if test="claimTagType != null">
      and tag.tag_type = #{claimTagType}
    </if>
    <if test="reportAmountMin != null">
      and c1.report_amount >= #{reportAmountMin}
    </if>
    <if test="reportAmountMax != null">
      <![CDATA[ and c1.report_amount <= #{reportAmountMax} ]]>
    </if>
  </sql>

  <select id="pageByQueryJoin" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from auto_settlement_info_shadow a1
    straight_join cargo_claim c1 on convert( a1.report_no using utf8) = c1.report_no
    <!--案件标签查询关联标签表claim_tag_info-->
    where a1.is_deleted = 'N'
    <include refid="queryWhereClause"/>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit #{offset},#{limit}
  </select>

  <select id="pageByQuerySingle" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from auto_settlement_info_shadow a1
    <if test="claimTagType != null">
      INNER JOIN claim_tag_info tag on a1.report_no = tag.report_no and tag.is_deleted = 'N'
    </if>
    where a1.is_deleted = 'N'
    <if test="isSame != null">
      and a1.is_same = #{isSame}
    </if>
    <if test="billAmountIsSame != null">
      and a1.bill_amount_is_same = #{billAmountIsSame}
    </if>
    <if test="settlementTypeIsSame != null">
      and a1.settlement_type_is_same = #{settlementTypeIsSame}
    </if>
    <if test="paidAmountDiffRateMin != null">
      and a1.diff_rate >= #{paidAmountDiffRateMin}
    </if>
    <if test="paidAmountDiffRateMax != null">
      <![CDATA[and a1.diff_rate <= #{paidAmountDiffRateMax}]]>
    </if>
    <if test="billAmountDiffRateMin != null">
      and a1.bill_amount_diff_rate >= #{billAmountDiffRateMin}
    </if>
    <if test="billAmountDiffRateMax != null">
      <![CDATA[and a1.bill_amount_diff_rate <= #{billAmountDiffRateMax}]]>
    </if>
    <if test="isSameOrRateNull != null and isSameOrRateNull">
      and (a1.is_same is null or a1.bill_amount_is_same is null or a1.diff_rate is null or
      a1.bill_amount_diff_rate is null or a1.settlement_type_is_same is null)
    </if>
    <if test="reportNo != null and reportNo != ''">
      and a1.report_no = #{reportNo}
    </if>
    <if test="claimTagType != null">
      and tag.tag_type = #{claimTagType}
    </if>
    limit #{offset},#{limit}
  </select>

  <select id="listByQueryJoin" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from
    auto_settlement_info_shadow a1
    straight_join cargo_claim c1 on convert( a1.report_no using utf8) = c1.report_no
    <!--案件标签查询关联标签表claim_tag_info-->
    <if test="claimTagType != null">
      INNER JOIN claim_tag_info tag on a1.report_no = tag.report_no and tag.is_deleted = 'N'
    </if>
    where a1.is_deleted = 'N'
    <if test="reportDateEnd != null">
      <![CDATA[and c1.report_date < #{reportDateEnd}]]>
    </if>
    <if test="reportDateBegin != null">
      and c1.report_date > #{reportDateBegin}
    </if>
    <if test="isSame != null">
      and a1.is_same = #{isSame}
    </if>
    <if test="billAmountIsSame != null">
      and a1.bill_amount_is_same = #{billAmountIsSame}
    </if>
    <if test="settlementTypeIsSame != null">
      and a1.settlement_type_is_same = #{settlementTypeIsSame}
    </if>
    <if test="paidAmountDiffRateMin != null">
      and a1.diff_rate >= #{paidAmountDiffRateMin}
    </if>
    <if test="paidAmountDiffRateMax != null">
      <![CDATA[and a1.diff_rate <= #{paidAmountDiffRateMax}]]>
    </if>
    <if test="billAmountDiffRateMin != null">
      and a1.bill_amount_diff_rate >= #{billAmountDiffRateMin}
    </if>
    <if test="billAmountDiffRateMax != null">
      <![CDATA[and a1.bill_amount_diff_rate <= #{billAmountDiffRateMax}]]>
    </if>
    <if test="isSameOrRateNull != null and isSameOrRateNull">
      and (a1.is_same is null or a1.bill_amount_is_same is null or a1.diff_rate is null or
      a1.bill_amount_diff_rate is null or a1.settlement_type_is_same is null)
    </if>
    <if test="statusIn != null and statusIn != ''">
      and c1.status in (${statusIn})
    </if>
    <if test="reportNo != null and reportNo != ''">
      and a1.report_no = #{reportNo}
    </if>
    <if test="claimTagType != null">
      and tag.tag_type = #{claimTagType}
    </if>
  </select>

  <select id="listByQuerySingle" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from
    auto_settlement_info_shadow a1 where a1.is_deleted = 'N'
    <if test="isSame != null">
      and a1.is_same = #{isSame}
    </if>
    <if test="billAmountIsSame != null">
      and a1.bill_amount_is_same = #{billAmountIsSame}
    </if>
    <if test="settlementTypeIsSame != null">
      and a1.settlement_type_is_same = #{settlementTypeIsSame}
    </if>
    <if test="paidAmountDiffRateMin != null">
      and a1.diff_rate >= #{paidAmountDiffRateMin}
    </if>
    <if test="paidAmountDiffRateMax != null">
      <![CDATA[and a1.diff_rate <= #{paidAmountDiffRateMax}]]>
    </if>
    <if test="billAmountDiffRateMin != null">
      and a1.bill_amount_diff_rate >= #{billAmountDiffRateMin}
    </if>
    <if test="billAmountDiffRateMax != null">
      <![CDATA[and a1.bill_amount_diff_rate <= #{billAmountDiffRateMax}]]>
    </if>
    <if test="isSameOrRateNull != null and isSameOrRateNull">
      and (a1.is_same is null or a1.bill_amount_is_same is null or a1.diff_rate is null or
      a1.bill_amount_diff_rate is null or a1.settlement_type_is_same is null)
    </if>
    <if test="reportNo != null and reportNo != ''">
      and a1.report_no = #{reportNo}
    </if>
    <if test="claimTagType != null">
      and tag.tag_type = #{claimTagType}
    </if>
  </select>

  <select id="countByQueryJoin" resultType="java.lang.Integer">
    select count(*) from auto_settlement_info_shadow a1
    straight_join cargo_claim c1 on convert( a1.report_no using utf8) = c1.report_no
    <if test="claimTagType != null">
      INNER JOIN claim_tag_info tag on a1.report_no = tag.report_no and tag.is_deleted = 'N'
    </if>
    where a1.is_deleted = 'N'
    <include refid="queryWhereClause"/>
  </select>

  <select id="countByQuerySingle" resultType="java.lang.Integer">
    select count(*) from auto_settlement_info_shadow a1
    <if test="claimTagType != null">
      INNER JOIN claim_tag_info tag on a1.report_no = tag.report_no and tag.is_deleted = 'N'
    </if>
    where a1.is_deleted = 'N'
    <if test="isSame != null">
      and a1.is_same = #{isSame}
    </if>
    <if test="reportNo != null and reportNo != ''">
      and a1.report_no = #{reportNo}
    </if>
    <if test="claimTagType != null">
      and tag.tag_type = #{claimTagType}
    </if>
    <if test="billAmountIsSame != null">
      and a1.bill_amount_is_same = #{billAmountIsSame}
    </if>
    <if test="settlementTypeIsSame != null">
      and a1.settlement_type_is_same = #{settlementTypeIsSame}
    </if>
    <if test="paidAmountDiffRateMin != null">
      and a1.diff_rate >= #{paidAmountDiffRateMin}
    </if>
    <if test="paidAmountDiffRateMax != null">
      <![CDATA[and a1.diff_rate <= #{paidAmountDiffRateMax}]]>
    </if>
    <if test="billAmountDiffRateMin != null">
      and a1.bill_amount_diff_rate >= #{billAmountDiffRateMin}
    </if>
    <if test="billAmountDiffRateMax != null">
      <![CDATA[and a1.bill_amount_diff_rate <= #{billAmountDiffRateMax}]]>
    </if>
  </select>
</mapper>