<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.ClaimPrePaidDAO">

    <select id="listDistinctRemarkByScope" resultType="java.lang.String">
        select distinct remark from cargo_claim_pre_paid_info where scope = 'serving'
    </select>

    <select id="listDistinctPayeeNameByScope" resultType="java.lang.String">
        select distinct payee_name from cargo_claim_pre_paid_info where scope = 'serving'
    </select>

</mapper>