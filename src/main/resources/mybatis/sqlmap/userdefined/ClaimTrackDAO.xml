<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.ClaimTrackDAO">

    <select id="queryNotifiableClaim" resultType="com.zhongan.lincoln.model.dto.claim.ClaimTrackGroupDTO">
        select report_no as reportNo, max(track_time) maxTrackTime from cargo_claim_track where is_deleted = 'N'
        <![CDATA[ and status <> 9 ]]>
        group by report_no;
    </select>

    <select id="queryAllTrackClaims" resultType="com.zhongan.lincoln.model.dto.claim.ClaimTrackGroupDTO">
        select report_no as reportNo from cargo_claim_track where is_deleted = 'N' group by report_no;
    </select>

</mapper>