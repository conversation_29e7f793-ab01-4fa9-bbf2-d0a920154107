<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.dao.BRelationBankInfoDAO">

    <select id="sumIndemnityAmount" resultType="java.lang.Double">
        select sum(indemnity_amount) sumIndemnityAmount
        from claim_batch_relation_bank_info
        where batch_claim_bill_no = #{batchClaimBillNo} and batch_claim_bank_info_id = #{batchClaimBankInfoId} and is_deleted='N'
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from claim_batch_relation_bank_info  where id = #{id}
    </delete>

</mapper>