<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.WoArkRelationDAO">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.WoArkDO">
    <result property="id" column="id"/>
    <result property="woNo" column="wo_no"/>
    <result property="woType" column="wo_type"/>
    <result property="woStatus" column="wo_status"/>
    <result property="reportNo" column="report_no"/>
    <result property="policyNo" column="policy_no"/>
    <result property="dispatcherNo" column="dispatcher_no" />
    <result property="attachmentId" column="attachment_id"/>
    <result property="woData" column="wo_data"/>
    <result property="executor" column="executor"/>
    <result property="executeTime" column="execute_time"/>
    <result property="executeRemark" column="execute_remark"/>
    <result property="remark" column="remark"/>
    <result property="isDeleted" column="is_deleted"/>
    <result property="gmtCreated" column="gmt_created"/>
    <result property="gmtModified" column="gmt_modified"/>
    <result property="creator" column="creator"/>
    <result property="modifier" column="modifier"/>
    <result property="assignNo" column="assign_no"/>
    <result property="relationId" column="relation_id"/>
    <result property="timeInterval" column="time_interval"/>
    <result property="lastHandTimeInterval" column="last_hand_time_interval"/>
    <result property="reason" column="reason"/>
    <result property="urgeCount" column="urge_count"/>
  </resultMap>

  <sql id="Base_Column_List">
    woInfo.id, woInfo.wo_no, woInfo.wo_type, woInfo.wo_status, woInfo.report_no, woInfo.policy_no, woInfo.dispatcher_no, woInfo.attachment_id,
    woInfo.wo_data, woInfo.executor, woInfo.execute_time, woInfo.execute_remark, woInfo.remark, woInfo.is_deleted,
    woInfo.gmt_created, woInfo.gmt_modified, woInfo.creator, woInfo.modifier, woInfo.reason,a1.assign_no as assign_no, a1.id as relation_id,
    a1.time_interval as time_interval, a1.last_hand_time_interval as last_hand_time_interval, a1.urge_count as urge_count
  </sql>

  <resultMap id="WorkOrderReport" type="com.zhongan.lincoln.dal.domain.WorkOrderReportDO">
    <result column="wo_num" jdbcType="INTEGER" property="woNum"/>
    <result column="wo_date" jdbcType="VARCHAR" property="woDate"/>
  </resultMap>

  <sql id="doQueryParams">
    <if test="assignNo != null and assignNo != ''">
      and a1.assign_no = #{assignNo}
    </if>
    <if test="productId != null">
      and a1.product_id = #{productId}
    </if>
    <if test="assignUrgency != null">
      and a1.assign_urgency = #{assignUrgency}
    </if>
    <if test="isUrge != null">
      and a1.is_urge = #{isUrge}
    </if>
    <if test="isTimeout != null">
      and a1.is_timeout = #{isTimeout}
    </if>
    <if test="urgeCountGte != null">
      <![CDATA[
        and a1.urge_count >= #{urgeCountGte}
       ]]>
    </if>
    <if test="urgeCountLte != null">
      <![CDATA[
        and a1.urge_count <= #{urgeCountLte}
       ]]>
    </if>
    <if test="woNo != null and woNo != ''">
      and woInfo.wo_no = #{woNo}
    </if>
    <if test="woType != null and woType != ''">
      and woInfo.wo_type = #{woType}
    </if>
    <if test="woStatus != null">
      and woInfo.wo_status = #{woStatus}
    </if>
    <if test="reportNo != null and reportNo != ''">
      and woInfo.report_no = #{reportNo}
    </if>
    <if test="policyNo != null and policyNo != ''">
      and woInfo.policy_no = #{policyNo}
    </if>
    <if test="gmtCreatedBegin != null">
      and woInfo.gmt_created >= #{gmtCreatedBegin}
    </if>
    <if test="gmtCreatedEnd != null">
      <![CDATA[
      and woInfo.gmt_created < #{gmtCreatedEnd}
       ]]>
    </if>
    <if test="woStatusIn != null">
      and woInfo.wo_status in
      <foreach item='status' collection='woStatusIn' open='(' separator=',' close=')'>
        #{status}
      </foreach>
    </if>
    <if test="woStatusNotIn != null">
      and woInfo.wo_status not in
      <foreach item='status' collection='woStatusNotIn' open='(' separator=',' close=')'>
        #{status}
      </foreach>
    </if>
    <if test="woDataLike != null">
      <![CDATA[
            and woInfo.wo_data like concat('%', #{woDataLike}, '%')
          ]]>
    </if>
  </sql>

  <sql id="doUserQueryParams">
    <if test="assignNo != null and assignNo != ''">
      and a1.assign_no = #{assignNo}
    </if>
    <if test="productId != null">
      and a1.product_id = #{productId}
    </if>
    <if test="assignUrgency != null">
      and a1.assign_urgency = #{assignUrgency}
    </if>
    <if test="isUrge != null">
      and a1.is_urge = #{isUrge}
    </if>
    <if test="isTimeout != null">
      and a1.is_timeout = #{isTimeout}
    </if>
    <if test="urgeCountGte != null">
      <![CDATA[
        and a1.urge_count >= #{urgeCountGte}
       ]]>
    </if>
    <if test="urgeCountLte != null">
      <![CDATA[
        and a1.urge_count <= #{urgeCountLte}
       ]]>
    </if>
    <if test="woNo != null and woNo != ''">
      and woInfo.wo_no = #{woNo}
    </if>
    <if test="woType != null and woType != ''">
      and woInfo.wo_type = #{woType}
    </if>
    <if test="woStatus != null">
      and woInfo.wo_status = #{woStatus}
    </if>
    <if test="reportNo != null and reportNo != ''">
      and woInfo.report_no = #{reportNo}
    </if>
    <if test="policyNo != null and policyNo != ''">
      and woInfo.policy_no = #{policyNo}
    </if>
    <if test="gmtCreatedBegin != null">
      and woInfo.gmt_created >= #{gmtCreatedBegin}
    </if>
    <if test="gmtCreatedEnd != null">
      <![CDATA[
      and woInfo.gmt_created < #{gmtCreatedEnd}
       ]]>
    </if>
    <if test="woStatusIn != null">
      and woInfo.wo_status in
      <foreach item='status' collection='woStatusIn' open='(' separator=',' close=')'>
        #{status}
      </foreach>
    </if>
    <if test="woStatusNotIn != null">
      and woInfo.wo_status not in
      <foreach item='status' collection='woStatusNotIn' open='(' separator=',' close=')'>
        #{status}
      </foreach>
    </if>
    <if test="woDataLike != null">
      <![CDATA[
            and woInfo.wo_data like concat('%', #{woDataLike}, '%')
          ]]>
    </if>
    <if test="userId != null">
      and woUser.user_id = #{userId}
    </if>
    <if test="tpaUserId != null">
      and woUser.tpa_user_id = #{tpaUserId}
    </if>
  </sql>

  <sql id="workReportParams">
    <if test="startTime != null">
      and a1.gmt_created >= #{startTime}
    </if>
    <if test="endTime != null">
      <![CDATA[
      and a1.gmt_created < #{endTime}
      ]]>
    </if>
  </sql>

  <select id="listByQuery" parameterType="com.zhongan.hacksaw.model.workOrder.query.WoInfoQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM wo_ark_relation a1 left join wo_info woInfo on a1.wo_info_id = woInfo.id and
    woInfo.is_deleted = 'N'
    WHERE a1.is_deleted = 'N'
    <include refid="doQueryParams"/>
    order by woInfo.gmt_modified desc limit #{start},#{limit}
  </select>

  <select id="countByQuery" parameterType="com.zhongan.hacksaw.model.workOrder.query.WoInfoQuery"
    resultType="java.lang.Integer">
    SELECT count(*)
    FROM wo_ark_relation a1 left join wo_info woInfo on a1.wo_info_id = woInfo.id and
    woInfo.is_deleted = 'N'
    WHERE a1.is_deleted = 'N'
    <include refid="doQueryParams"/>
  </select>

  <select id="listUserByQuery" parameterType="com.zhongan.hacksaw.model.workOrder.query.WoInfoQuery"
    resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM wo_ark_relation a1 left join wo_info woInfo on a1.wo_info_id = woInfo.id and
    woInfo.is_deleted = 'N' left join wo_user_relation woUser on a1.wo_info_id = woUser.wo_info_id and
    woUser.is_deleted = 'N'
    WHERE a1.is_deleted = 'N'
    <include refid="doUserQueryParams" />
    ORDER BY woInfo.gmt_modified desc limit #{start},#{limit}
  </select>

  <select id="countUserByQuery" parameterType="com.zhongan.hacksaw.model.workOrder.query.WoInfoQuery"
    resultType="java.lang.Integer">
    SELECT count(*)
    FROM wo_ark_relation a1 left join wo_info woInfo on a1.wo_info_id = woInfo.id and
    woInfo.is_deleted = 'N' left join wo_user_relation woUser on a1.wo_info_id = woUser.wo_info_id and
    woUser.is_deleted = 'N'
    WHERE a1.is_deleted = 'N'
    <include refid="doUserQueryParams"/>
  </select>

  <select id="arkWorkReportByDay" parameterType="com.zhongan.lincoln.model.query.WorkOrderReportQuery" resultMap="WorkOrderReport">
    select count(distinct (a1.assign_no)) as wo_num, date_format(a1.gmt_created, '%Y-%m-%d') as wo_date
    from wo_ark_relation a1 left join wo_info wi on a1.wo_info_id=wi.id
    where wi.is_deleted = 'N'
    <include refid="workReportParams"/>
    group by date_format(a1.gmt_created, '%Y-%m-%d');
  </select>

  <select id="arkWorkReportByMonth" parameterType="com.zhongan.lincoln.model.query.WorkOrderReportQuery" resultMap="WorkOrderReport">
    select count(distinct (a1.assign_no)) as wo_num, date_format(a1.gmt_created, '%Y-%m') as wo_date
    from wo_ark_relation a1 left join wo_info wi on a1.wo_info_id=wi.id
    where wi.is_deleted = 'N'
    <include refid="workReportParams"/>
    group by date_format(a1.gmt_created, '%Y-%m');
  </select>

</mapper>