<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.AicHitRuleDetailDAO">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AicHitRuleDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_detail" jdbcType="VARCHAR" property="projectDetail" />
    <result column="hit_rule_name" jdbcType="VARCHAR" property="hitRuleName" />
    <result column="hit_rule_code" jdbcType="VARCHAR" property="hitRuleCode" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="claim_status" jdbcType="INTEGER" property="claimStatus" />
    <result column="paid_amount" jdbcType="VARCHAR" property="paidAmount" />
    <result column="report_date" jdbcType="TIMESTAMP" property="reportDate" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>

  <sql id="Base_Column_List">
    id, project_detail, hit_rule_name, hit_rule_code, report_no, report_date, extra_info,
    gmt_created, gmt_modified, creator, modifier, is_deleted
  </sql>

  <resultMap id="AicHitRuleProjectDetailDO" type="com.zhongan.lincoln.dal.domain.AicHitRuleProjectDetailDO">
    <result column="count" jdbcType="INTEGER" property="count"/>
    <result column="project_detail" jdbcType="VARCHAR" property="projectDetail"/>
  </resultMap>

  <sql id="doQueryParams">
    where is_deleted = 'N'
    <if test="ruleCode != null and ruleCode != ''">
      and hit_rule_code =#{ruleCode}
    </if>
    <if test="reportDateBegin != null">
      <![CDATA[
      and report_date > #{reportDateBegin}
       ]]>
    </if>
    <if test="reportDateEnd != null">
      <![CDATA[
      and report_date < #{reportDateEnd}
       ]]>
    </if>
  </sql>


  <select id="selectCount" parameterType="com.zhongan.hacksaw.model.aic.query.AicHitRuleDetailQuery" resultMap="AicHitRuleProjectDetailDO">
    select project_detail,count(1) from aic_hit_rule_detail
    <include refid="doQueryParams"/>
    GROUP BY project_detail
  </select>

  <select id="selectGroupBy" parameterType="com.zhongan.hacksaw.model.aic.query.AicHitRuleDetailQuery" resultMap="AicHitRuleProjectDetailDO">
    select project_detail,count(1) as count from aic_hit_rule_detail
    <include refid="doQueryParams"/>
    GROUP BY project_detail order by count desc limit #{offset},#{limit}
  </select>

  <select id="selectByProjectDetailIn" parameterType="java.lang.String" resultMap="BaseResultMap">
    select * from aic_hit_rule_detail where is_deleted = 'N' and project_detail in (${projectDetail})
    <![CDATA[
      and report_date > #{reportDateBegin} and report_date < #{reportDateEnd} and hit_rule_code =#{ruleCode}
       ]]>
  </select>


  <!-- hitRuleCode、reportDateBegin、reportDateEnd参数必传 -->
  <select id="listGroupByProjectDetail" resultMap="AicHitRuleProjectDetailDO">
      SELECT project_detail,COUNT(1) AS count
      FROM aic_hit_rule_detail
      WHERE is_deleted = 'N'
      AND hit_rule_code = #{hitRuleCode}
      AND report_date BETWEEN #{reportDateBegin} AND #{reportDateEnd}
      <if test="claimStatus != null">
          AND claim_status = #{claimStatus}
      </if>
      GROUP BY project_detail
  </select>
</mapper>