<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.AutoSettlementRiskRuleDAO">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AutoSettlementRiskRuleExtraDO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId"/>
    <result column="product_name" jdbcType="VARCHAR" property="productName"/>
    <result column="liability_name" jdbcType="VARCHAR" property="liabilityName"/>
    <result column="liability_code" jdbcType="VARCHAR" property="liabilityCode"/>
    <result column="common_rule_type" jdbcType="INTEGER" property="commonRuleType"/>
    <result column="common_rule_id" jdbcType="BIGINT" property="commonRuleId"/>
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated"/>
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
    <result column="creator" jdbcType="VARCHAR" property="creator"/>
    <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
    <result column="enable" jdbcType="VARCHAR" property="enable"/>
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName"/>
    <result column="rule_code" jdbcType="VARCHAR" property="ruleCode"/>
  </resultMap>

  <sql id="Base_Column_List">
    a1.id, a1.package_def_id, a1.product_name, a1.liability_name, a1.liability_code, a1.common_rule_type,
    a1.common_rule_id,
     a1.extra_info, a1.remark, a1.is_deleted, a1.gmt_created, a1.gmt_modified, a1.creator, a1.modifier,
      a1.enable
  </sql>

  <sql id="doQueryParams">
    <if test="packageDefId != null">
      and a1.package_def_id = #{packageDefId}
    </if>
    <if test="commonRuleType != null">
      and a1.common_rule_type = #{commonRuleType}
    </if>
    <if test="liabilityCode != null and liabilityCode != ''">
      and a1.liability_code = #{liabilityCode}
    </if>
  </sql>

  <select id="countByQueryJoin" resultType="java.lang.Long">
    select count(*)
    from auto_settlement_risk_rule a1 left join auto_settlement_common_rule a2 on a1.common_rule_id
    = a2.id
    where a1.is_deleted = 'N'
    <include refid="doQueryParams"/>
    <if test="commonRuleName != null and commonRuleName != ''">
      and a2.rule_name = #{commonRuleName}
    </if>
  </select>

  <select id="pageByQueryJoin" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>, a2.rule_name, a2.rule_code
    from auto_settlement_risk_rule a1 left join auto_settlement_common_rule a2 on a1.common_rule_id
    = a2.id
    where a1.is_deleted = 'N'
    <include refid="doQueryParams"/>
    <if test="commonRuleName != null and commonRuleName != ''">
      and a2.rule_name = #{commonRuleName}
    </if>
    limit #{offset,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
  </select>
</mapper>