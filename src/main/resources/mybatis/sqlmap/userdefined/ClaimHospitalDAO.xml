<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.dao.ClaimHospitalDAO">

    <select id="currentPageBillSummer" resultType="java.util.HashMap">
        SELECT 	0+CAST( CAST( SUM(bill_amount) AS DECIMAL (19, 2) ) AS  CHAR) billAmount ,
        0+CAST( CAST( SUM(medical_insurance_pay) AS DECIMAL (19, 2) ) AS  CHAR) medicalInsurancePay,
        0+CAST( CAST( SUM(non_responsible_cost) AS DECIMAL (19, 2) ) AS  CHAR) nonResponsibleCost,
        0+CAST( CAST( SUM(other_pay) AS DECIMAL (19, 2) ) AS  CHAR) otherPay,
        0+CAST( CAST( SUM(reasonable_pay) AS DECIMAL (19, 2) ) AS  CHAR) reasonablePay,
        0+CAST( CAST( SUM(self_expense) AS DECIMAL (19, 2) ) AS  CHAR) selfExpense,
        0+CAST( CAST( SUM(self_expense_category) AS DECIMAL (19, 2) ) AS  CHAR) selfExpenseCategory
        FROM  (select  * from claim_hospital where is_deleted = 'N'
        and report_no = #{reportNo}
        and claim_hospital_type is null
        order by ${orderByClause} limit #{start},#{limit}) as o
    </select>

    <select id="billSummer" resultType="java.util.HashMap" parameterType="java.lang.String">
        SELECT 	0+CAST( CAST( SUM(bill_amount) AS DECIMAL (19, 2) ) AS  CHAR) billAmount ,
        0+CAST( CAST( SUM(medical_insurance_pay) AS DECIMAL (19, 2) ) AS  CHAR) medicalInsurancePay,
        0+CAST( CAST( SUM(non_responsible_cost) AS DECIMAL (19, 2) ) AS  CHAR) nonResponsibleCost,
        0+CAST( CAST( SUM(other_pay) AS DECIMAL (19, 2) ) AS  CHAR) otherPay,
        0+CAST( CAST( SUM(reasonable_pay) AS DECIMAL (19, 2) ) AS  CHAR) reasonablePay,
        0+CAST( CAST( SUM(self_expense) AS DECIMAL (19, 2) ) AS  CHAR) selfExpense,
        0+CAST( CAST( SUM(self_expense_category) AS DECIMAL (19, 2) ) AS  CHAR) selfExpenseCategory
        FROM claim_hospital where is_deleted = 'N'
        and report_no = #{reportNo}
    </select>

    <select id="currentPageBillSummerBatch" resultType="java.util.HashMap">
        SELECT 	0+CAST( CAST( SUM(bill_amount) AS DECIMAL (19, 2) ) AS  CHAR) billAmount ,
        0+CAST( CAST( SUM(medical_insurance_pay) AS DECIMAL (19, 2) ) AS  CHAR) medicalInsurancePay,
        0+CAST( CAST( SUM(non_responsible_cost) AS DECIMAL (19, 2) ) AS  CHAR) nonResponsibleCost,
        0+CAST( CAST( SUM(other_pay) AS DECIMAL (19, 2) ) AS  CHAR) otherPay,
        0+CAST( CAST( SUM(reasonable_pay) AS DECIMAL (19, 2) ) AS  CHAR) reasonablePay,
        0+CAST( CAST( SUM(self_expense) AS DECIMAL (19, 2) ) AS  CHAR) selfExpense,
        0+CAST( CAST( SUM(self_expense_category) AS DECIMAL (19, 2) ) AS  CHAR) selfExpenseCategory
        FROM  (select  * from claim_hospital where is_deleted = 'N'
        and batch_claim_bill_no = #{batchNo}
        and claim_hospital_type is null
        order by gmt_created desc limit #{start},#{limit}) as o
    </select>

    <select id="billSummerBatch" resultType="java.util.HashMap" parameterType="java.lang.String">
        SELECT 	0+CAST( CAST( SUM(bill_amount) AS DECIMAL (19, 2) ) AS  CHAR) billAmount ,
        0+CAST( CAST( SUM(medical_insurance_pay) AS DECIMAL (19, 2) ) AS  CHAR) medicalInsurancePay,
        0+CAST( CAST( SUM(non_responsible_cost) AS DECIMAL (19, 2) ) AS  CHAR) nonResponsibleCost,
        0+CAST( CAST( SUM(other_pay) AS DECIMAL (19, 2) ) AS  CHAR) otherPay,
        0+CAST( CAST( SUM(reasonable_pay) AS DECIMAL (19, 2) ) AS  CHAR) reasonablePay,
        0+CAST( CAST( SUM(self_expense) AS DECIMAL (19, 2) ) AS  CHAR) selfExpense,
        0+CAST( CAST( SUM(self_expense_category) AS DECIMAL (19, 2) ) AS  CHAR) selfExpenseCategory
        FROM claim_hospital where is_deleted = 'N'
        and batch_claim_bill_no = #{batchNo}
    </select>
</mapper>
