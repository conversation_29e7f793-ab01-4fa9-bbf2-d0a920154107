<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.dao.FollowTaskDAO">

    <select id="queryFollowTaskAuditors" resultType="java.lang.String">
        select distinct(auditor) from follow_task_record where is_deleted = 'N'
        and task_status IN(0,3) AND auditor IS NOT null;
    </select>

</mapper>
