<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zhongan.lincoln.dal.dao.TpaCostDAO">

    <resultMap id="TpaCostFlowResultMap" type="com.zhongan.hacksaw.model.tpa.response.TpaCostFlow">
        <result property="companyId" javaType="java.lang.Long" column="companyId"/>
        <result property="companyName" javaType="java.lang.String" column="companyName"/>
        <result property="claimNum" javaType="java.lang.Integer" column="claimNum"/>
        <result property="billNum" javaType="java.lang.Integer" column="billNum"/>
        <result property="cost" javaType="java.lang.String" column="cost"/>
    </resultMap>

    <sql id="queryParams">
        <if test="companyId != null">
            and company_id = #{companyId}
        </if>
        <if test="submitDateBegin != null">
            and gmt_created > #{submitDateBegin}
        </if>
        <if test="submitDateEnd != null">
            <![CDATA[
            and gmt_created <= #{submitDateEnd}
            ]]>
        </if>
    </sql>

    <select id="sumByQuery" resultMap="TpaCostFlowResultMap"
            parameterType="com.zhongan.hacksaw.model.tpa.query.TpaCostQuery">
        select company_id as companyId, company_name as companyName,
        count(id) as claimNum, sum(hos_invoice_num) + sum(no_hos_invoice_num) as billNum, cast(sum(amount) as decimal(16,2)) cost
        from tpa_cost
        where is_deleted = 'N'
        <include refid="queryParams"></include>
        group by company_id
    </select>
</mapper>