<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimAppointmentCallbackMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimAppointmentCallbackDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="appointment_no" jdbcType="VARCHAR" property="appointmentNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="wo_no" jdbcType="VARCHAR" property="woNo" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="agent_name" jdbcType="VARCHAR" property="agentName" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_phone" jdbcType="VARCHAR" property="customerPhone" />
    <result column="appointment_status" jdbcType="VARCHAR" property="appointmentStatus" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="appointment_date" jdbcType="DATE" property="appointmentDate" />
    <result column="appointment_period" jdbcType="VARCHAR" property="appointmentPeriod" />
    <result column="callback_status" jdbcType="VARCHAR" property="callbackStatus" />
    <result column="callback_time" jdbcType="TIMESTAMP" property="callbackTime" />
    <result column="call_end_time" jdbcType="TIMESTAMP" property="callEndTime" />
    <result column="call_duration" jdbcType="INTEGER" property="callDuration" />
    <result column="call_count" jdbcType="INTEGER" property="callCount" />
    <result column="is_unprocessed" jdbcType="BIT" property="isUnprocessed" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, appointment_no, report_no, policy_no, wo_no, apply_time, agent_name, customer_name, 
    customer_phone, appointment_status, submit_time, appointment_date, appointment_period, 
    callback_status, callback_time, call_end_time, call_duration, call_count, is_unprocessed, 
    remark, extra_info, creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimAppointmentCallbackExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_appointment_callback
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_appointment_callback
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimAppointmentCallbackDO">
    insert into claim_appointment_callback (id, appointment_no, report_no, 
      policy_no, wo_no, apply_time, 
      agent_name, customer_name, customer_phone, 
      appointment_status, submit_time, appointment_date, 
      appointment_period, callback_status, callback_time, 
      call_end_time, call_duration, call_count, 
      is_unprocessed, remark, extra_info, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{appointmentNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{policyNo,jdbcType=VARCHAR}, #{woNo,jdbcType=VARCHAR}, #{applyTime,jdbcType=TIMESTAMP}, 
      #{agentName,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{customerPhone,jdbcType=VARCHAR}, 
      #{appointmentStatus,jdbcType=VARCHAR}, #{submitTime,jdbcType=TIMESTAMP}, #{appointmentDate,jdbcType=DATE}, 
      #{appointmentPeriod,jdbcType=VARCHAR}, #{callbackStatus,jdbcType=VARCHAR}, #{callbackTime,jdbcType=TIMESTAMP}, 
      #{callEndTime,jdbcType=TIMESTAMP}, #{callDuration,jdbcType=INTEGER}, #{callCount,jdbcType=INTEGER}, 
      #{isUnprocessed,jdbcType=BIT}, #{remark,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimAppointmentCallbackDO">
    insert into claim_appointment_callback
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appointmentNo != null">
        appointment_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="woNo != null">
        wo_no,
      </if>
      <if test="applyTime != null">
        apply_time,
      </if>
      <if test="agentName != null">
        agent_name,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerPhone != null">
        customer_phone,
      </if>
      <if test="appointmentStatus != null">
        appointment_status,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="appointmentDate != null">
        appointment_date,
      </if>
      <if test="appointmentPeriod != null">
        appointment_period,
      </if>
      <if test="callbackStatus != null">
        callback_status,
      </if>
      <if test="callbackTime != null">
        callback_time,
      </if>
      <if test="callEndTime != null">
        call_end_time,
      </if>
      <if test="callDuration != null">
        call_duration,
      </if>
      <if test="callCount != null">
        call_count,
      </if>
      <if test="isUnprocessed != null">
        is_unprocessed,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="appointmentNo != null">
        #{appointmentNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="woNo != null">
        #{woNo,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="agentName != null">
        #{agentName,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerPhone != null">
        #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="appointmentStatus != null">
        #{appointmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appointmentDate != null">
        #{appointmentDate,jdbcType=DATE},
      </if>
      <if test="appointmentPeriod != null">
        #{appointmentPeriod,jdbcType=VARCHAR},
      </if>
      <if test="callbackStatus != null">
        #{callbackStatus,jdbcType=VARCHAR},
      </if>
      <if test="callbackTime != null">
        #{callbackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="callEndTime != null">
        #{callEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="callDuration != null">
        #{callDuration,jdbcType=INTEGER},
      </if>
      <if test="callCount != null">
        #{callCount,jdbcType=INTEGER},
      </if>
      <if test="isUnprocessed != null">
        #{isUnprocessed,jdbcType=BIT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimAppointmentCallbackExample" resultType="java.lang.Long">
    select count(*) from claim_appointment_callback
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_appointment_callback
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appointmentNo != null">
        appointment_no = #{record.appointmentNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.woNo != null">
        wo_no = #{record.woNo,jdbcType=VARCHAR},
      </if>
      <if test="record.applyTime != null">
        apply_time = #{record.applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.agentName != null">
        agent_name = #{record.agentName,jdbcType=VARCHAR},
      </if>
      <if test="record.customerName != null">
        customer_name = #{record.customerName,jdbcType=VARCHAR},
      </if>
      <if test="record.customerPhone != null">
        customer_phone = #{record.customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.appointmentStatus != null">
        appointment_status = #{record.appointmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.submitTime != null">
        submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.appointmentDate != null">
        appointment_date = #{record.appointmentDate,jdbcType=DATE},
      </if>
      <if test="record.appointmentPeriod != null">
        appointment_period = #{record.appointmentPeriod,jdbcType=VARCHAR},
      </if>
      <if test="record.callbackStatus != null">
        callback_status = #{record.callbackStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.callbackTime != null">
        callback_time = #{record.callbackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.callEndTime != null">
        call_end_time = #{record.callEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.callDuration != null">
        call_duration = #{record.callDuration,jdbcType=INTEGER},
      </if>
      <if test="record.callCount != null">
        call_count = #{record.callCount,jdbcType=INTEGER},
      </if>
      <if test="record.isUnprocessed != null">
        is_unprocessed = #{record.isUnprocessed,jdbcType=BIT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_appointment_callback
    set id = #{record.id,jdbcType=BIGINT},
      appointment_no = #{record.appointmentNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      wo_no = #{record.woNo,jdbcType=VARCHAR},
      apply_time = #{record.applyTime,jdbcType=TIMESTAMP},
      agent_name = #{record.agentName,jdbcType=VARCHAR},
      customer_name = #{record.customerName,jdbcType=VARCHAR},
      customer_phone = #{record.customerPhone,jdbcType=VARCHAR},
      appointment_status = #{record.appointmentStatus,jdbcType=VARCHAR},
      submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      appointment_date = #{record.appointmentDate,jdbcType=DATE},
      appointment_period = #{record.appointmentPeriod,jdbcType=VARCHAR},
      callback_status = #{record.callbackStatus,jdbcType=VARCHAR},
      callback_time = #{record.callbackTime,jdbcType=TIMESTAMP},
      call_end_time = #{record.callEndTime,jdbcType=TIMESTAMP},
      call_duration = #{record.callDuration,jdbcType=INTEGER},
      call_count = #{record.callCount,jdbcType=INTEGER},
      is_unprocessed = #{record.isUnprocessed,jdbcType=BIT},
      remark = #{record.remark,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimAppointmentCallbackDO">
    update claim_appointment_callback
    <set>
      <if test="appointmentNo != null">
        appointment_no = #{appointmentNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="woNo != null">
        wo_no = #{woNo,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="agentName != null">
        agent_name = #{agentName,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerPhone != null">
        customer_phone = #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="appointmentStatus != null">
        appointment_status = #{appointmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appointmentDate != null">
        appointment_date = #{appointmentDate,jdbcType=DATE},
      </if>
      <if test="appointmentPeriod != null">
        appointment_period = #{appointmentPeriod,jdbcType=VARCHAR},
      </if>
      <if test="callbackStatus != null">
        callback_status = #{callbackStatus,jdbcType=VARCHAR},
      </if>
      <if test="callbackTime != null">
        callback_time = #{callbackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="callEndTime != null">
        call_end_time = #{callEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="callDuration != null">
        call_duration = #{callDuration,jdbcType=INTEGER},
      </if>
      <if test="callCount != null">
        call_count = #{callCount,jdbcType=INTEGER},
      </if>
      <if test="isUnprocessed != null">
        is_unprocessed = #{isUnprocessed,jdbcType=BIT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimAppointmentCallbackDO">
    update claim_appointment_callback
    set appointment_no = #{appointmentNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      wo_no = #{woNo,jdbcType=VARCHAR},
      apply_time = #{applyTime,jdbcType=TIMESTAMP},
      agent_name = #{agentName,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_phone = #{customerPhone,jdbcType=VARCHAR},
      appointment_status = #{appointmentStatus,jdbcType=VARCHAR},
      submit_time = #{submitTime,jdbcType=TIMESTAMP},
      appointment_date = #{appointmentDate,jdbcType=DATE},
      appointment_period = #{appointmentPeriod,jdbcType=VARCHAR},
      callback_status = #{callbackStatus,jdbcType=VARCHAR},
      callback_time = #{callbackTime,jdbcType=TIMESTAMP},
      call_end_time = #{callEndTime,jdbcType=TIMESTAMP},
      call_duration = #{callDuration,jdbcType=INTEGER},
      call_count = #{callCount,jdbcType=INTEGER},
      is_unprocessed = #{isUnprocessed,jdbcType=BIT},
      remark = #{remark,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_appointment_callback
    (id, appointment_no, report_no, policy_no, wo_no, apply_time, agent_name, customer_name, 
      customer_phone, appointment_status, submit_time, appointment_date, appointment_period, 
      callback_status, callback_time, call_end_time, call_duration, call_count, is_unprocessed, 
      remark, extra_info, creator, modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.appointmentNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.policyNo,jdbcType=VARCHAR}, #{item.woNo,jdbcType=VARCHAR}, #{item.applyTime,jdbcType=TIMESTAMP}, 
        #{item.agentName,jdbcType=VARCHAR}, #{item.customerName,jdbcType=VARCHAR}, #{item.customerPhone,jdbcType=VARCHAR}, 
        #{item.appointmentStatus,jdbcType=VARCHAR}, #{item.submitTime,jdbcType=TIMESTAMP}, 
        #{item.appointmentDate,jdbcType=DATE}, #{item.appointmentPeriod,jdbcType=VARCHAR}, 
        #{item.callbackStatus,jdbcType=VARCHAR}, #{item.callbackTime,jdbcType=TIMESTAMP}, 
        #{item.callEndTime,jdbcType=TIMESTAMP}, #{item.callDuration,jdbcType=INTEGER}, 
        #{item.callCount,jdbcType=INTEGER}, #{item.isUnprocessed,jdbcType=BIT}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_appointment_callback (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'appointment_no'.toString() == column.value">
          #{item.appointmentNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'wo_no'.toString() == column.value">
          #{item.woNo,jdbcType=VARCHAR}
        </if>
        <if test="'apply_time'.toString() == column.value">
          #{item.applyTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'agent_name'.toString() == column.value">
          #{item.agentName,jdbcType=VARCHAR}
        </if>
        <if test="'customer_name'.toString() == column.value">
          #{item.customerName,jdbcType=VARCHAR}
        </if>
        <if test="'customer_phone'.toString() == column.value">
          #{item.customerPhone,jdbcType=VARCHAR}
        </if>
        <if test="'appointment_status'.toString() == column.value">
          #{item.appointmentStatus,jdbcType=VARCHAR}
        </if>
        <if test="'submit_time'.toString() == column.value">
          #{item.submitTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'appointment_date'.toString() == column.value">
          #{item.appointmentDate,jdbcType=DATE}
        </if>
        <if test="'appointment_period'.toString() == column.value">
          #{item.appointmentPeriod,jdbcType=VARCHAR}
        </if>
        <if test="'callback_status'.toString() == column.value">
          #{item.callbackStatus,jdbcType=VARCHAR}
        </if>
        <if test="'callback_time'.toString() == column.value">
          #{item.callbackTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'call_end_time'.toString() == column.value">
          #{item.callEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'call_duration'.toString() == column.value">
          #{item.callDuration,jdbcType=INTEGER}
        </if>
        <if test="'call_count'.toString() == column.value">
          #{item.callCount,jdbcType=INTEGER}
        </if>
        <if test="'is_unprocessed'.toString() == column.value">
          #{item.isUnprocessed,jdbcType=BIT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>