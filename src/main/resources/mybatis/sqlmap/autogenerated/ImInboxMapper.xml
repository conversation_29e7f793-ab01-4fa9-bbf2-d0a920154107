<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ImInboxMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ImInboxDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="msg_abstract" jdbcType="VARCHAR" property="msgAbstract" />
    <result column="read_over" jdbcType="CHAR" property="readOver" />
    <result column="star" jdbcType="CHAR" property="star" />
    <result column="msg_type" jdbcType="TINYINT" property="msgType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="sender_name" jdbcType="VARCHAR" property="senderName" />
    <result column="msg_title" jdbcType="VARCHAR" property="msgTitle" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="msg_cast_type" jdbcType="TINYINT" property="msgCastType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, receiver, msg_id, msg_abstract, read_over, star, msg_type, remark, is_deleted, 
    gmt_created, gmt_modified, creator, modifier, sender_name, msg_title, send_time, 
    msg_cast_type
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ImInboxExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from im_inbox
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from im_inbox
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ImInboxDO">
    insert into im_inbox (id, receiver, msg_id, 
      msg_abstract, read_over, star, 
      msg_type, remark, is_deleted, 
      gmt_created, gmt_modified, creator, 
      modifier, sender_name, msg_title, 
      send_time, msg_cast_type)
    values (#{id,jdbcType=BIGINT}, #{receiver,jdbcType=VARCHAR}, #{msgId,jdbcType=VARCHAR}, 
      #{msgAbstract,jdbcType=VARCHAR}, #{readOver,jdbcType=CHAR}, #{star,jdbcType=CHAR}, 
      #{msgType,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{senderName,jdbcType=VARCHAR}, #{msgTitle,jdbcType=VARCHAR}, 
      #{sendTime,jdbcType=TIMESTAMP}, #{msgCastType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ImInboxDO">
    insert into im_inbox
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="receiver != null">
        receiver,
      </if>
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="msgAbstract != null">
        msg_abstract,
      </if>
      <if test="readOver != null">
        read_over,
      </if>
      <if test="star != null">
        star,
      </if>
      <if test="msgType != null">
        msg_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="senderName != null">
        sender_name,
      </if>
      <if test="msgTitle != null">
        msg_title,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="msgCastType != null">
        msg_cast_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="receiver != null">
        #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="msgId != null">
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="msgAbstract != null">
        #{msgAbstract,jdbcType=VARCHAR},
      </if>
      <if test="readOver != null">
        #{readOver,jdbcType=CHAR},
      </if>
      <if test="star != null">
        #{star,jdbcType=CHAR},
      </if>
      <if test="msgType != null">
        #{msgType,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="senderName != null">
        #{senderName,jdbcType=VARCHAR},
      </if>
      <if test="msgTitle != null">
        #{msgTitle,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="msgCastType != null">
        #{msgCastType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ImInboxExample" resultType="java.lang.Long">
    select count(*) from im_inbox
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update im_inbox
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.receiver != null">
        receiver = #{record.receiver,jdbcType=VARCHAR},
      </if>
      <if test="record.msgId != null">
        msg_id = #{record.msgId,jdbcType=VARCHAR},
      </if>
      <if test="record.msgAbstract != null">
        msg_abstract = #{record.msgAbstract,jdbcType=VARCHAR},
      </if>
      <if test="record.readOver != null">
        read_over = #{record.readOver,jdbcType=CHAR},
      </if>
      <if test="record.star != null">
        star = #{record.star,jdbcType=CHAR},
      </if>
      <if test="record.msgType != null">
        msg_type = #{record.msgType,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.senderName != null">
        sender_name = #{record.senderName,jdbcType=VARCHAR},
      </if>
      <if test="record.msgTitle != null">
        msg_title = #{record.msgTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.sendTime != null">
        send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.msgCastType != null">
        msg_cast_type = #{record.msgCastType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update im_inbox
    set id = #{record.id,jdbcType=BIGINT},
      receiver = #{record.receiver,jdbcType=VARCHAR},
      msg_id = #{record.msgId,jdbcType=VARCHAR},
      msg_abstract = #{record.msgAbstract,jdbcType=VARCHAR},
      read_over = #{record.readOver,jdbcType=CHAR},
      star = #{record.star,jdbcType=CHAR},
      msg_type = #{record.msgType,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      sender_name = #{record.senderName,jdbcType=VARCHAR},
      msg_title = #{record.msgTitle,jdbcType=VARCHAR},
      send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      msg_cast_type = #{record.msgCastType,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ImInboxDO">
    update im_inbox
    <set>
      <if test="receiver != null">
        receiver = #{receiver,jdbcType=VARCHAR},
      </if>
      <if test="msgId != null">
        msg_id = #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="msgAbstract != null">
        msg_abstract = #{msgAbstract,jdbcType=VARCHAR},
      </if>
      <if test="readOver != null">
        read_over = #{readOver,jdbcType=CHAR},
      </if>
      <if test="star != null">
        star = #{star,jdbcType=CHAR},
      </if>
      <if test="msgType != null">
        msg_type = #{msgType,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="senderName != null">
        sender_name = #{senderName,jdbcType=VARCHAR},
      </if>
      <if test="msgTitle != null">
        msg_title = #{msgTitle,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="msgCastType != null">
        msg_cast_type = #{msgCastType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ImInboxDO">
    update im_inbox
    set receiver = #{receiver,jdbcType=VARCHAR},
      msg_id = #{msgId,jdbcType=VARCHAR},
      msg_abstract = #{msgAbstract,jdbcType=VARCHAR},
      read_over = #{readOver,jdbcType=CHAR},
      star = #{star,jdbcType=CHAR},
      msg_type = #{msgType,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      sender_name = #{senderName,jdbcType=VARCHAR},
      msg_title = #{msgTitle,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      msg_cast_type = #{msgCastType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into im_inbox
    (id, receiver, msg_id, msg_abstract, read_over, star, msg_type, remark, is_deleted, 
      gmt_created, gmt_modified, creator, modifier, sender_name, msg_title, send_time, 
      msg_cast_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.receiver,jdbcType=VARCHAR}, #{item.msgId,jdbcType=VARCHAR}, 
        #{item.msgAbstract,jdbcType=VARCHAR}, #{item.readOver,jdbcType=CHAR}, #{item.star,jdbcType=CHAR}, 
        #{item.msgType,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.senderName,jdbcType=VARCHAR}, 
        #{item.msgTitle,jdbcType=VARCHAR}, #{item.sendTime,jdbcType=TIMESTAMP}, #{item.msgCastType,jdbcType=TINYINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into im_inbox (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'receiver'.toString() == column.value">
          #{item.receiver,jdbcType=VARCHAR}
        </if>
        <if test="'msg_id'.toString() == column.value">
          #{item.msgId,jdbcType=VARCHAR}
        </if>
        <if test="'msg_abstract'.toString() == column.value">
          #{item.msgAbstract,jdbcType=VARCHAR}
        </if>
        <if test="'read_over'.toString() == column.value">
          #{item.readOver,jdbcType=CHAR}
        </if>
        <if test="'star'.toString() == column.value">
          #{item.star,jdbcType=CHAR}
        </if>
        <if test="'msg_type'.toString() == column.value">
          #{item.msgType,jdbcType=TINYINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'sender_name'.toString() == column.value">
          #{item.senderName,jdbcType=VARCHAR}
        </if>
        <if test="'msg_title'.toString() == column.value">
          #{item.msgTitle,jdbcType=VARCHAR}
        </if>
        <if test="'send_time'.toString() == column.value">
          #{item.sendTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'msg_cast_type'.toString() == column.value">
          #{item.msgCastType,jdbcType=TINYINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>