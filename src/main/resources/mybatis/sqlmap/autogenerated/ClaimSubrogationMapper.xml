<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimSubrogationMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimSubrogationDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_id" jdbcType="BIGINT" property="policyId" />
    <result column="claim_id" jdbcType="BIGINT" property="claimId" />
    <result column="claim_bank_info_id" jdbcType="BIGINT" property="claimBankInfoId" />
    <result column="subrogation_no" jdbcType="VARCHAR" property="subrogationNo" />
    <result column="subrogation_status" jdbcType="VARCHAR" property="subrogationStatus" />
    <result column="subrogation_amount" jdbcType="DECIMAL" property="subrogationAmount" />
    <result column="subrogation_reason" jdbcType="VARCHAR" property="subrogationReason" />
    <result column="trans_account_name" jdbcType="VARCHAR" property="transAccountName" />
    <result column="trans_account" jdbcType="VARCHAR" property="transAccount" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, policy_id, claim_id, claim_bank_info_id, subrogation_no, subrogation_status, 
    subrogation_amount, subrogation_reason, trans_account_name, trans_account, trade_no, 
    creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimSubrogationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_subrogation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_subrogation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimSubrogationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_subrogation (report_no, policy_id, claim_id, 
      claim_bank_info_id, subrogation_no, subrogation_status, 
      subrogation_amount, subrogation_reason, trans_account_name, 
      trans_account, trade_no, creator, 
      modifier, gmt_created, gmt_modified, 
      is_deleted)
    values (#{reportNo,jdbcType=VARCHAR}, #{policyId,jdbcType=BIGINT}, #{claimId,jdbcType=BIGINT}, 
      #{claimBankInfoId,jdbcType=BIGINT}, #{subrogationNo,jdbcType=VARCHAR}, #{subrogationStatus,jdbcType=VARCHAR}, 
      #{subrogationAmount,jdbcType=DECIMAL}, #{subrogationReason,jdbcType=VARCHAR}, #{transAccountName,jdbcType=VARCHAR}, 
      #{transAccount,jdbcType=VARCHAR}, #{tradeNo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate(), 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimSubrogationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_subrogation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyId != null">
        policy_id,
      </if>
      <if test="claimId != null">
        claim_id,
      </if>
      <if test="claimBankInfoId != null">
        claim_bank_info_id,
      </if>
      <if test="subrogationNo != null">
        subrogation_no,
      </if>
      <if test="subrogationStatus != null">
        subrogation_status,
      </if>
      <if test="subrogationAmount != null">
        subrogation_amount,
      </if>
      <if test="subrogationReason != null">
        subrogation_reason,
      </if>
      <if test="transAccountName != null">
        trans_account_name,
      </if>
      <if test="transAccount != null">
        trans_account,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        #{policyId,jdbcType=BIGINT},
      </if>
      <if test="claimId != null">
        #{claimId,jdbcType=BIGINT},
      </if>
      <if test="claimBankInfoId != null">
        #{claimBankInfoId,jdbcType=BIGINT},
      </if>
      <if test="subrogationNo != null">
        #{subrogationNo,jdbcType=VARCHAR},
      </if>
      <if test="subrogationStatus != null">
        #{subrogationStatus,jdbcType=VARCHAR},
      </if>
      <if test="subrogationAmount != null">
        #{subrogationAmount,jdbcType=DECIMAL},
      </if>
      <if test="subrogationReason != null">
        #{subrogationReason,jdbcType=VARCHAR},
      </if>
      <if test="transAccountName != null">
        #{transAccountName,jdbcType=VARCHAR},
      </if>
      <if test="transAccount != null">
        #{transAccount,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimSubrogationExample" resultType="java.lang.Long">
    select count(*) from claim_subrogation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_subrogation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyId != null">
        policy_id = #{record.policyId,jdbcType=BIGINT},
      </if>
      <if test="record.claimId != null">
        claim_id = #{record.claimId,jdbcType=BIGINT},
      </if>
      <if test="record.claimBankInfoId != null">
        claim_bank_info_id = #{record.claimBankInfoId,jdbcType=BIGINT},
      </if>
      <if test="record.subrogationNo != null">
        subrogation_no = #{record.subrogationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.subrogationStatus != null">
        subrogation_status = #{record.subrogationStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.subrogationAmount != null">
        subrogation_amount = #{record.subrogationAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.subrogationReason != null">
        subrogation_reason = #{record.subrogationReason,jdbcType=VARCHAR},
      </if>
      <if test="record.transAccountName != null">
        trans_account_name = #{record.transAccountName,jdbcType=VARCHAR},
      </if>
      <if test="record.transAccount != null">
        trans_account = #{record.transAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeNo != null">
        trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_subrogation
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_id = #{record.policyId,jdbcType=BIGINT},
      claim_id = #{record.claimId,jdbcType=BIGINT},
      claim_bank_info_id = #{record.claimBankInfoId,jdbcType=BIGINT},
      subrogation_no = #{record.subrogationNo,jdbcType=VARCHAR},
      subrogation_status = #{record.subrogationStatus,jdbcType=VARCHAR},
      subrogation_amount = #{record.subrogationAmount,jdbcType=DECIMAL},
      subrogation_reason = #{record.subrogationReason,jdbcType=VARCHAR},
      trans_account_name = #{record.transAccountName,jdbcType=VARCHAR},
      trans_account = #{record.transAccount,jdbcType=VARCHAR},
      trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimSubrogationDO">
    update claim_subrogation
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        policy_id = #{policyId,jdbcType=BIGINT},
      </if>
      <if test="claimId != null">
        claim_id = #{claimId,jdbcType=BIGINT},
      </if>
      <if test="claimBankInfoId != null">
        claim_bank_info_id = #{claimBankInfoId,jdbcType=BIGINT},
      </if>
      <if test="subrogationNo != null">
        subrogation_no = #{subrogationNo,jdbcType=VARCHAR},
      </if>
      <if test="subrogationStatus != null">
        subrogation_status = #{subrogationStatus,jdbcType=VARCHAR},
      </if>
      <if test="subrogationAmount != null">
        subrogation_amount = #{subrogationAmount,jdbcType=DECIMAL},
      </if>
      <if test="subrogationReason != null">
        subrogation_reason = #{subrogationReason,jdbcType=VARCHAR},
      </if>
      <if test="transAccountName != null">
        trans_account_name = #{transAccountName,jdbcType=VARCHAR},
      </if>
      <if test="transAccount != null">
        trans_account = #{transAccount,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimSubrogationDO">
    update claim_subrogation
    set report_no = #{reportNo,jdbcType=VARCHAR},
      policy_id = #{policyId,jdbcType=BIGINT},
      claim_id = #{claimId,jdbcType=BIGINT},
      claim_bank_info_id = #{claimBankInfoId,jdbcType=BIGINT},
      subrogation_no = #{subrogationNo,jdbcType=VARCHAR},
      subrogation_status = #{subrogationStatus,jdbcType=VARCHAR},
      subrogation_amount = #{subrogationAmount,jdbcType=DECIMAL},
      subrogation_reason = #{subrogationReason,jdbcType=VARCHAR},
      trans_account_name = #{transAccountName,jdbcType=VARCHAR},
      trans_account = #{transAccount,jdbcType=VARCHAR},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into claim_subrogation
    (report_no, policy_id, claim_id, claim_bank_info_id, subrogation_no, subrogation_status, 
      subrogation_amount, subrogation_reason, trans_account_name, trans_account, trade_no, 
      creator, modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reportNo,jdbcType=VARCHAR}, #{item.policyId,jdbcType=BIGINT}, #{item.claimId,jdbcType=BIGINT}, 
        #{item.claimBankInfoId,jdbcType=BIGINT}, #{item.subrogationNo,jdbcType=VARCHAR}, 
        #{item.subrogationStatus,jdbcType=VARCHAR}, #{item.subrogationAmount,jdbcType=DECIMAL}, 
        #{item.subrogationReason,jdbcType=VARCHAR}, #{item.transAccountName,jdbcType=VARCHAR}, 
        #{item.transAccount,jdbcType=VARCHAR}, #{item.tradeNo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into claim_subrogation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_id'.toString() == column.value">
          #{item.policyId,jdbcType=BIGINT}
        </if>
        <if test="'claim_id'.toString() == column.value">
          #{item.claimId,jdbcType=BIGINT}
        </if>
        <if test="'claim_bank_info_id'.toString() == column.value">
          #{item.claimBankInfoId,jdbcType=BIGINT}
        </if>
        <if test="'subrogation_no'.toString() == column.value">
          #{item.subrogationNo,jdbcType=VARCHAR}
        </if>
        <if test="'subrogation_status'.toString() == column.value">
          #{item.subrogationStatus,jdbcType=VARCHAR}
        </if>
        <if test="'subrogation_amount'.toString() == column.value">
          #{item.subrogationAmount,jdbcType=DECIMAL}
        </if>
        <if test="'subrogation_reason'.toString() == column.value">
          #{item.subrogationReason,jdbcType=VARCHAR}
        </if>
        <if test="'trans_account_name'.toString() == column.value">
          #{item.transAccountName,jdbcType=VARCHAR}
        </if>
        <if test="'trans_account'.toString() == column.value">
          #{item.transAccount,jdbcType=VARCHAR}
        </if>
        <if test="'trade_no'.toString() == column.value">
          #{item.tradeNo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>