<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimProductAttributeConfigMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimProductAttributeConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="prod_no" jdbcType="VARCHAR" property="prodNo" />
    <result column="pipeline" jdbcType="VARCHAR" property="pipeline" />
    <result column="default_reserve_amount" jdbcType="DECIMAL" property="defaultReserveAmount" />
    <result column="default_liability_code" jdbcType="VARCHAR" property="defaultLiabilityCode" />
    <result column="adjust_report_amount" jdbcType="VARCHAR" property="adjustReportAmount" />
    <result column="adjust_claimant" jdbcType="VARCHAR" property="adjustClaimant" />
    <result column="adjust_policy_no" jdbcType="VARCHAR" property="adjustPolicyNo" />
    <result column="adjust_channel_report_no" jdbcType="VARCHAR" property="adjustChannelReportNo" />
    <result column="extended_tags" jdbcType="BIGINT" property="extendedTags" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, product_id, prod_no, pipeline, default_reserve_amount, default_liability_code, 
    adjust_report_amount, adjust_claimant, adjust_policy_no, adjust_channel_report_no, 
    extended_tags, creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductAttributeConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_product_attribute_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_product_attribute_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductAttributeConfigDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_product_attribute_config (product_id, prod_no, pipeline, 
      default_reserve_amount, default_liability_code, 
      adjust_report_amount, adjust_claimant, adjust_policy_no, 
      adjust_channel_report_no, extended_tags, creator, 
      modifier, gmt_created, gmt_modified, 
      is_deleted)
    values (#{productId,jdbcType=BIGINT}, #{prodNo,jdbcType=VARCHAR}, #{pipeline,jdbcType=VARCHAR}, 
      #{defaultReserveAmount,jdbcType=DECIMAL}, #{defaultLiabilityCode,jdbcType=VARCHAR}, 
      #{adjustReportAmount,jdbcType=VARCHAR}, #{adjustClaimant,jdbcType=VARCHAR}, #{adjustPolicyNo,jdbcType=VARCHAR}, 
      #{adjustChannelReportNo,jdbcType=VARCHAR}, #{extendedTags,jdbcType=BIGINT}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate(), 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductAttributeConfigDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_product_attribute_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productId != null">
        product_id,
      </if>
      <if test="prodNo != null">
        prod_no,
      </if>
      <if test="pipeline != null">
        pipeline,
      </if>
      <if test="defaultReserveAmount != null">
        default_reserve_amount,
      </if>
      <if test="defaultLiabilityCode != null">
        default_liability_code,
      </if>
      <if test="adjustReportAmount != null">
        adjust_report_amount,
      </if>
      <if test="adjustClaimant != null">
        adjust_claimant,
      </if>
      <if test="adjustPolicyNo != null">
        adjust_policy_no,
      </if>
      <if test="adjustChannelReportNo != null">
        adjust_channel_report_no,
      </if>
      <if test="extendedTags != null">
        extended_tags,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="prodNo != null">
        #{prodNo,jdbcType=VARCHAR},
      </if>
      <if test="pipeline != null">
        #{pipeline,jdbcType=VARCHAR},
      </if>
      <if test="defaultReserveAmount != null">
        #{defaultReserveAmount,jdbcType=DECIMAL},
      </if>
      <if test="defaultLiabilityCode != null">
        #{defaultLiabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="adjustReportAmount != null">
        #{adjustReportAmount,jdbcType=VARCHAR},
      </if>
      <if test="adjustClaimant != null">
        #{adjustClaimant,jdbcType=VARCHAR},
      </if>
      <if test="adjustPolicyNo != null">
        #{adjustPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="adjustChannelReportNo != null">
        #{adjustChannelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="extendedTags != null">
        #{extendedTags,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductAttributeConfigExample" resultType="java.lang.Long">
    select count(*) from claim_product_attribute_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_product_attribute_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.prodNo != null">
        prod_no = #{record.prodNo,jdbcType=VARCHAR},
      </if>
      <if test="record.pipeline != null">
        pipeline = #{record.pipeline,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultReserveAmount != null">
        default_reserve_amount = #{record.defaultReserveAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.defaultLiabilityCode != null">
        default_liability_code = #{record.defaultLiabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustReportAmount != null">
        adjust_report_amount = #{record.adjustReportAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustClaimant != null">
        adjust_claimant = #{record.adjustClaimant,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustPolicyNo != null">
        adjust_policy_no = #{record.adjustPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustChannelReportNo != null">
        adjust_channel_report_no = #{record.adjustChannelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.extendedTags != null">
        extended_tags = #{record.extendedTags,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_product_attribute_config
    set id = #{record.id,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=BIGINT},
      prod_no = #{record.prodNo,jdbcType=VARCHAR},
      pipeline = #{record.pipeline,jdbcType=VARCHAR},
      default_reserve_amount = #{record.defaultReserveAmount,jdbcType=DECIMAL},
      default_liability_code = #{record.defaultLiabilityCode,jdbcType=VARCHAR},
      adjust_report_amount = #{record.adjustReportAmount,jdbcType=VARCHAR},
      adjust_claimant = #{record.adjustClaimant,jdbcType=VARCHAR},
      adjust_policy_no = #{record.adjustPolicyNo,jdbcType=VARCHAR},
      adjust_channel_report_no = #{record.adjustChannelReportNo,jdbcType=VARCHAR},
      extended_tags = #{record.extendedTags,jdbcType=BIGINT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductAttributeConfigDO">
    update claim_product_attribute_config
    <set>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="prodNo != null">
        prod_no = #{prodNo,jdbcType=VARCHAR},
      </if>
      <if test="pipeline != null">
        pipeline = #{pipeline,jdbcType=VARCHAR},
      </if>
      <if test="defaultReserveAmount != null">
        default_reserve_amount = #{defaultReserveAmount,jdbcType=DECIMAL},
      </if>
      <if test="defaultLiabilityCode != null">
        default_liability_code = #{defaultLiabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="adjustReportAmount != null">
        adjust_report_amount = #{adjustReportAmount,jdbcType=VARCHAR},
      </if>
      <if test="adjustClaimant != null">
        adjust_claimant = #{adjustClaimant,jdbcType=VARCHAR},
      </if>
      <if test="adjustPolicyNo != null">
        adjust_policy_no = #{adjustPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="adjustChannelReportNo != null">
        adjust_channel_report_no = #{adjustChannelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="extendedTags != null">
        extended_tags = #{extendedTags,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductAttributeConfigDO">
    update claim_product_attribute_config
    set product_id = #{productId,jdbcType=BIGINT},
      prod_no = #{prodNo,jdbcType=VARCHAR},
      pipeline = #{pipeline,jdbcType=VARCHAR},
      default_reserve_amount = #{defaultReserveAmount,jdbcType=DECIMAL},
      default_liability_code = #{defaultLiabilityCode,jdbcType=VARCHAR},
      adjust_report_amount = #{adjustReportAmount,jdbcType=VARCHAR},
      adjust_claimant = #{adjustClaimant,jdbcType=VARCHAR},
      adjust_policy_no = #{adjustPolicyNo,jdbcType=VARCHAR},
      adjust_channel_report_no = #{adjustChannelReportNo,jdbcType=VARCHAR},
      extended_tags = #{extendedTags,jdbcType=BIGINT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into claim_product_attribute_config
    (product_id, prod_no, pipeline, default_reserve_amount, default_liability_code, adjust_report_amount, 
      adjust_claimant, adjust_policy_no, adjust_channel_report_no, extended_tags, creator, 
      modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.productId,jdbcType=BIGINT}, #{item.prodNo,jdbcType=VARCHAR}, #{item.pipeline,jdbcType=VARCHAR}, 
        #{item.defaultReserveAmount,jdbcType=DECIMAL}, #{item.defaultLiabilityCode,jdbcType=VARCHAR}, 
        #{item.adjustReportAmount,jdbcType=VARCHAR}, #{item.adjustClaimant,jdbcType=VARCHAR}, 
        #{item.adjustPolicyNo,jdbcType=VARCHAR}, #{item.adjustChannelReportNo,jdbcType=VARCHAR}, 
        #{item.extendedTags,jdbcType=BIGINT}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into claim_product_attribute_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'product_id'.toString() == column.value">
          #{item.productId,jdbcType=BIGINT}
        </if>
        <if test="'prod_no'.toString() == column.value">
          #{item.prodNo,jdbcType=VARCHAR}
        </if>
        <if test="'pipeline'.toString() == column.value">
          #{item.pipeline,jdbcType=VARCHAR}
        </if>
        <if test="'default_reserve_amount'.toString() == column.value">
          #{item.defaultReserveAmount,jdbcType=DECIMAL}
        </if>
        <if test="'default_liability_code'.toString() == column.value">
          #{item.defaultLiabilityCode,jdbcType=VARCHAR}
        </if>
        <if test="'adjust_report_amount'.toString() == column.value">
          #{item.adjustReportAmount,jdbcType=VARCHAR}
        </if>
        <if test="'adjust_claimant'.toString() == column.value">
          #{item.adjustClaimant,jdbcType=VARCHAR}
        </if>
        <if test="'adjust_policy_no'.toString() == column.value">
          #{item.adjustPolicyNo,jdbcType=VARCHAR}
        </if>
        <if test="'adjust_channel_report_no'.toString() == column.value">
          #{item.adjustChannelReportNo,jdbcType=VARCHAR}
        </if>
        <if test="'extended_tags'.toString() == column.value">
          #{item.extendedTags,jdbcType=BIGINT}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>