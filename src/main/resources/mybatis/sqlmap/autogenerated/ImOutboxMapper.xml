<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ImOutboxMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ImOutboxDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="msg_title" jdbcType="VARCHAR" property="msgTitle" />
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="msg_abstract" jdbcType="VARCHAR" property="msgAbstract" />
    <result column="msg_type" jdbcType="TINYINT" property="msgType" />
    <result column="msg_state" jdbcType="TINYINT" property="msgState" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="msg_cast_type" jdbcType="TINYINT" property="msgCastType" />
    <result column="receivers" jdbcType="VARCHAR" property="receivers" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, msg_title, msg_id, msg_abstract, msg_type, msg_state, remark, is_deleted, gmt_created, 
    gmt_modified, creator, modifier, send_time, msg_cast_type, receivers
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ImOutboxExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from im_outbox
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from im_outbox
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ImOutboxDO">
    insert into im_outbox (id, msg_title, msg_id, 
      msg_abstract, msg_type, msg_state, 
      remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, 
      send_time, msg_cast_type, receivers
      )
    values (#{id,jdbcType=BIGINT}, #{msgTitle,jdbcType=VARCHAR}, #{msgId,jdbcType=VARCHAR}, 
      #{msgAbstract,jdbcType=VARCHAR}, #{msgType,jdbcType=TINYINT}, #{msgState,jdbcType=TINYINT}, 
      #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{sendTime,jdbcType=TIMESTAMP}, #{msgCastType,jdbcType=TINYINT}, #{receivers,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ImOutboxDO">
    insert into im_outbox
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="msgTitle != null">
        msg_title,
      </if>
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="msgAbstract != null">
        msg_abstract,
      </if>
      <if test="msgType != null">
        msg_type,
      </if>
      <if test="msgState != null">
        msg_state,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="msgCastType != null">
        msg_cast_type,
      </if>
      <if test="receivers != null">
        receivers,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="msgTitle != null">
        #{msgTitle,jdbcType=VARCHAR},
      </if>
      <if test="msgId != null">
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="msgAbstract != null">
        #{msgAbstract,jdbcType=VARCHAR},
      </if>
      <if test="msgType != null">
        #{msgType,jdbcType=TINYINT},
      </if>
      <if test="msgState != null">
        #{msgState,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="msgCastType != null">
        #{msgCastType,jdbcType=TINYINT},
      </if>
      <if test="receivers != null">
        #{receivers,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ImOutboxExample" resultType="java.lang.Long">
    select count(*) from im_outbox
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update im_outbox
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.msgTitle != null">
        msg_title = #{record.msgTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.msgId != null">
        msg_id = #{record.msgId,jdbcType=VARCHAR},
      </if>
      <if test="record.msgAbstract != null">
        msg_abstract = #{record.msgAbstract,jdbcType=VARCHAR},
      </if>
      <if test="record.msgType != null">
        msg_type = #{record.msgType,jdbcType=TINYINT},
      </if>
      <if test="record.msgState != null">
        msg_state = #{record.msgState,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.sendTime != null">
        send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.msgCastType != null">
        msg_cast_type = #{record.msgCastType,jdbcType=TINYINT},
      </if>
      <if test="record.receivers != null">
        receivers = #{record.receivers,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update im_outbox
    set id = #{record.id,jdbcType=BIGINT},
      msg_title = #{record.msgTitle,jdbcType=VARCHAR},
      msg_id = #{record.msgId,jdbcType=VARCHAR},
      msg_abstract = #{record.msgAbstract,jdbcType=VARCHAR},
      msg_type = #{record.msgType,jdbcType=TINYINT},
      msg_state = #{record.msgState,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      send_time = #{record.sendTime,jdbcType=TIMESTAMP},
      msg_cast_type = #{record.msgCastType,jdbcType=TINYINT},
      receivers = #{record.receivers,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ImOutboxDO">
    update im_outbox
    <set>
      <if test="msgTitle != null">
        msg_title = #{msgTitle,jdbcType=VARCHAR},
      </if>
      <if test="msgId != null">
        msg_id = #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="msgAbstract != null">
        msg_abstract = #{msgAbstract,jdbcType=VARCHAR},
      </if>
      <if test="msgType != null">
        msg_type = #{msgType,jdbcType=TINYINT},
      </if>
      <if test="msgState != null">
        msg_state = #{msgState,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="msgCastType != null">
        msg_cast_type = #{msgCastType,jdbcType=TINYINT},
      </if>
      <if test="receivers != null">
        receivers = #{receivers,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ImOutboxDO">
    update im_outbox
    set msg_title = #{msgTitle,jdbcType=VARCHAR},
      msg_id = #{msgId,jdbcType=VARCHAR},
      msg_abstract = #{msgAbstract,jdbcType=VARCHAR},
      msg_type = #{msgType,jdbcType=TINYINT},
      msg_state = #{msgState,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      msg_cast_type = #{msgCastType,jdbcType=TINYINT},
      receivers = #{receivers,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into im_outbox
    (id, msg_title, msg_id, msg_abstract, msg_type, msg_state, remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, send_time, msg_cast_type, receivers)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.msgTitle,jdbcType=VARCHAR}, #{item.msgId,jdbcType=VARCHAR}, 
        #{item.msgAbstract,jdbcType=VARCHAR}, #{item.msgType,jdbcType=TINYINT}, #{item.msgState,jdbcType=TINYINT}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.sendTime,jdbcType=TIMESTAMP}, #{item.msgCastType,jdbcType=TINYINT}, #{item.receivers,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into im_outbox (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'msg_title'.toString() == column.value">
          #{item.msgTitle,jdbcType=VARCHAR}
        </if>
        <if test="'msg_id'.toString() == column.value">
          #{item.msgId,jdbcType=VARCHAR}
        </if>
        <if test="'msg_abstract'.toString() == column.value">
          #{item.msgAbstract,jdbcType=VARCHAR}
        </if>
        <if test="'msg_type'.toString() == column.value">
          #{item.msgType,jdbcType=TINYINT}
        </if>
        <if test="'msg_state'.toString() == column.value">
          #{item.msgState,jdbcType=TINYINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'send_time'.toString() == column.value">
          #{item.sendTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'msg_cast_type'.toString() == column.value">
          #{item.msgCastType,jdbcType=TINYINT}
        </if>
        <if test="'receivers'.toString() == column.value">
          #{item.receivers,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>