<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimHospitalMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimHospitalDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="batch_claim_bill_no" jdbcType="VARCHAR" property="batchClaimBillNo" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="visit_hospital" jdbcType="VARCHAR" property="visitHospital" />
    <result column="bill_number" jdbcType="VARCHAR" property="billNumber" />
    <result column="treatment_type" jdbcType="TINYINT" property="treatmentType" />
    <result column="visit_start" jdbcType="DATE" property="visitStart" />
    <result column="visit_end" jdbcType="DATE" property="visitEnd" />
    <result column="valid_days" jdbcType="INTEGER" property="validDays" />
    <result column="bill_type" jdbcType="TINYINT" property="billType" />
    <result column="tpa_bill_status" jdbcType="TINYINT" property="tpaBillStatus" />
    <result column="attachment_id" jdbcType="VARCHAR" property="attachmentId" />
    <result column="problem_desc" jdbcType="VARCHAR" property="problemDesc" />
    <result column="medical_insurance_type" jdbcType="TINYINT" property="medicalInsuranceType" />
    <result column="material_type" jdbcType="TINYINT" property="materialType" />
    <result column="hospital_type" jdbcType="VARCHAR" property="hospitalType" />
    <result column="hospital_level" jdbcType="VARCHAR" property="hospitalLevel" />
    <result column="hospital_grade" jdbcType="VARCHAR" property="hospitalGrade" />
    <result column="hospital_nature" jdbcType="VARCHAR" property="hospitalNature" />
    <result column="hospital_approve" jdbcType="VARCHAR" property="hospitalApprove" />
    <result column="operation_info_list" jdbcType="VARCHAR" property="operationInfoList" />
    <result column="social_designated_hospital" jdbcType="VARCHAR" property="socialDesignatedHospital" />
    <result column="bill_amount" jdbcType="VARCHAR" property="billAmount" />
    <result column="self_expense" jdbcType="VARCHAR" property="selfExpense" />
    <result column="self_expense_category" jdbcType="VARCHAR" property="selfExpenseCategory" />
    <result column="non_responsible_cost" jdbcType="VARCHAR" property="nonResponsibleCost" />
    <result column="medical_insurance_pay" jdbcType="VARCHAR" property="medicalInsurancePay" />
    <result column="other_pay" jdbcType="VARCHAR" property="otherPay" />
    <result column="other_pay_detail" jdbcType="VARCHAR" property="otherPayDetail" />
    <result column="reasonable_pay" jdbcType="VARCHAR" property="reasonablePay" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="liability_id" jdbcType="BIGINT" property="liabilityId" />
    <result column="claim_hospital_type" jdbcType="CHAR" property="claimHospitalType" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, batch_claim_bill_no, hospital_code, visit_hospital, bill_number, treatment_type, 
    visit_start, visit_end, valid_days, bill_type, tpa_bill_status, attachment_id, problem_desc, 
    medical_insurance_type, material_type, hospital_type, hospital_level, hospital_grade, 
    hospital_nature, hospital_approve, operation_info_list, social_designated_hospital, 
    bill_amount, self_expense, self_expense_category, non_responsible_cost, medical_insurance_pay, 
    other_pay, other_pay_detail, reasonable_pay, policy_no, liability_id, claim_hospital_type, 
    gmt_created, gmt_modified, creator, modifier, is_deleted, patient_name
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_hospital
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_hospital
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalDO">
    insert into claim_hospital (id, report_no, batch_claim_bill_no, 
      hospital_code, visit_hospital, bill_number, 
      treatment_type, visit_start, visit_end, 
      valid_days, bill_type, tpa_bill_status, 
      attachment_id, problem_desc, medical_insurance_type, 
      material_type, hospital_type, hospital_level, 
      hospital_grade, hospital_nature, hospital_approve, 
      operation_info_list, social_designated_hospital, 
      bill_amount, self_expense, self_expense_category, 
      non_responsible_cost, medical_insurance_pay, 
      other_pay, other_pay_detail, reasonable_pay,
      policy_no, liability_id, claim_hospital_type, 
      gmt_created, gmt_modified, creator, 
      modifier, is_deleted, patient_name
      )
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{batchClaimBillNo,jdbcType=VARCHAR},
      #{hospitalCode,jdbcType=VARCHAR}, #{visitHospital,jdbcType=VARCHAR}, #{billNumber,jdbcType=VARCHAR}, 
      #{treatmentType,jdbcType=TINYINT}, #{visitStart,jdbcType=DATE}, #{visitEnd,jdbcType=DATE}, 
      #{validDays,jdbcType=INTEGER}, #{billType,jdbcType=TINYINT}, #{tpaBillStatus,jdbcType=TINYINT}, 
      #{attachmentId,jdbcType=VARCHAR}, #{problemDesc,jdbcType=VARCHAR}, #{medicalInsuranceType,jdbcType=TINYINT}, 
      #{materialType,jdbcType=TINYINT}, #{hospitalType,jdbcType=VARCHAR}, #{hospitalLevel,jdbcType=VARCHAR}, 
      #{hospitalGrade,jdbcType=VARCHAR}, #{hospitalNature,jdbcType=VARCHAR}, #{hospitalApprove,jdbcType=VARCHAR}, 
      #{operationInfoList,jdbcType=VARCHAR}, #{socialDesignatedHospital,jdbcType=VARCHAR}, 
      #{billAmount,jdbcType=VARCHAR}, #{selfExpense,jdbcType=VARCHAR}, #{selfExpenseCategory,jdbcType=VARCHAR}, 
      #{nonResponsibleCost,jdbcType=VARCHAR}, #{medicalInsurancePay,jdbcType=VARCHAR}, 
      #{otherPay,jdbcType=VARCHAR}, #{otherPayDetail,jdbcType=VARCHAR}, #{reasonablePay,jdbcType=VARCHAR},
      #{policyNo,jdbcType=VARCHAR}, #{liabilityId,jdbcType=BIGINT}, #{claimHospitalType,jdbcType=CHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, #{patientName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalDO">
    insert into claim_hospital
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no,
      </if>
      <if test="hospitalCode != null">
        hospital_code,
      </if>
      <if test="visitHospital != null">
        visit_hospital,
      </if>
      <if test="billNumber != null">
        bill_number,
      </if>
      <if test="treatmentType != null">
        treatment_type,
      </if>
      <if test="visitStart != null">
        visit_start,
      </if>
      <if test="visitEnd != null">
        visit_end,
      </if>
      <if test="validDays != null">
        valid_days,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="tpaBillStatus != null">
        tpa_bill_status,
      </if>
      <if test="attachmentId != null">
        attachment_id,
      </if>
      <if test="problemDesc != null">
        problem_desc,
      </if>
      <if test="medicalInsuranceType != null">
        medical_insurance_type,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="hospitalType != null">
        hospital_type,
      </if>
      <if test="hospitalLevel != null">
        hospital_level,
      </if>
      <if test="hospitalGrade != null">
        hospital_grade,
      </if>
      <if test="hospitalNature != null">
        hospital_nature,
      </if>
      <if test="hospitalApprove != null">
        hospital_approve,
      </if>
      <if test="operationInfoList != null">
        operation_info_list,
      </if>
      <if test="socialDesignatedHospital != null">
        social_designated_hospital,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="selfExpense != null">
        self_expense,
      </if>
      <if test="selfExpenseCategory != null">
        self_expense_category,
      </if>
      <if test="nonResponsibleCost != null">
        non_responsible_cost,
      </if>
      <if test="medicalInsurancePay != null">
        medical_insurance_pay,
      </if>
      <if test="otherPay != null">
        other_pay,
      </if>
      <if test="otherPayDetail != null">
        other_pay_detail,
      </if>
      <if test="reasonablePay != null">
        reasonable_pay,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="liabilityId != null">
        liability_id,
      </if>
      <if test="claimHospitalType != null">
        claim_hospital_type,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="patientName != null">
        patient_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillNo != null">
        #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="visitHospital != null">
        #{visitHospital,jdbcType=VARCHAR},
      </if>
      <if test="billNumber != null">
        #{billNumber,jdbcType=VARCHAR},
      </if>
      <if test="treatmentType != null">
        #{treatmentType,jdbcType=TINYINT},
      </if>
      <if test="visitStart != null">
        #{visitStart,jdbcType=DATE},
      </if>
      <if test="visitEnd != null">
        #{visitEnd,jdbcType=DATE},
      </if>
      <if test="validDays != null">
        #{validDays,jdbcType=INTEGER},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=TINYINT},
      </if>
      <if test="tpaBillStatus != null">
        #{tpaBillStatus,jdbcType=TINYINT},
      </if>
      <if test="attachmentId != null">
        #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="problemDesc != null">
        #{problemDesc,jdbcType=VARCHAR},
      </if>
      <if test="medicalInsuranceType != null">
        #{medicalInsuranceType,jdbcType=TINYINT},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=TINYINT},
      </if>
      <if test="hospitalType != null">
        #{hospitalType,jdbcType=VARCHAR},
      </if>
      <if test="hospitalLevel != null">
        #{hospitalLevel,jdbcType=VARCHAR},
      </if>
      <if test="hospitalGrade != null">
        #{hospitalGrade,jdbcType=VARCHAR},
      </if>
      <if test="hospitalNature != null">
        #{hospitalNature,jdbcType=VARCHAR},
      </if>
      <if test="hospitalApprove != null">
        #{hospitalApprove,jdbcType=VARCHAR},
      </if>
      <if test="operationInfoList != null">
        #{operationInfoList,jdbcType=VARCHAR},
      </if>
      <if test="socialDesignatedHospital != null">
        #{socialDesignatedHospital,jdbcType=VARCHAR},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=VARCHAR},
      </if>
      <if test="selfExpense != null">
        #{selfExpense,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseCategory != null">
        #{selfExpenseCategory,jdbcType=VARCHAR},
      </if>
      <if test="nonResponsibleCost != null">
        #{nonResponsibleCost,jdbcType=VARCHAR},
      </if>
      <if test="medicalInsurancePay != null">
        #{medicalInsurancePay,jdbcType=VARCHAR},
      </if>
      <if test="otherPay != null">
        #{otherPay,jdbcType=VARCHAR},
      </if>
      <if test="otherPayDetail != null">
        #{otherPayDetail,jdbcType=VARCHAR},
      </if>
      <if test="reasonablePay != null">
        #{reasonablePay,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="liabilityId != null">
        #{liabilityId,jdbcType=BIGINT},
      </if>
      <if test="claimHospitalType != null">
        #{claimHospitalType,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="patientName != null">
        #{patientName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalExample" resultType="java.lang.Long">
    select count(*) from claim_hospital
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_hospital
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchClaimBillNo != null">
        batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalCode != null">
        hospital_code = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.visitHospital != null">
        visit_hospital = #{record.visitHospital,jdbcType=VARCHAR},
      </if>
      <if test="record.billNumber != null">
        bill_number = #{record.billNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.treatmentType != null">
        treatment_type = #{record.treatmentType,jdbcType=TINYINT},
      </if>
      <if test="record.visitStart != null">
        visit_start = #{record.visitStart,jdbcType=DATE},
      </if>
      <if test="record.visitEnd != null">
        visit_end = #{record.visitEnd,jdbcType=DATE},
      </if>
      <if test="record.validDays != null">
        valid_days = #{record.validDays,jdbcType=INTEGER},
      </if>
      <if test="record.billType != null">
        bill_type = #{record.billType,jdbcType=TINYINT},
      </if>
      <if test="record.tpaBillStatus != null">
        tpa_bill_status = #{record.tpaBillStatus,jdbcType=TINYINT},
      </if>
      <if test="record.attachmentId != null">
        attachment_id = #{record.attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="record.problemDesc != null">
        problem_desc = #{record.problemDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.medicalInsuranceType != null">
        medical_insurance_type = #{record.medicalInsuranceType,jdbcType=TINYINT},
      </if>
      <if test="record.materialType != null">
        material_type = #{record.materialType,jdbcType=TINYINT},
      </if>
      <if test="record.hospitalType != null">
        hospital_type = #{record.hospitalType,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalLevel != null">
        hospital_level = #{record.hospitalLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalGrade != null">
        hospital_grade = #{record.hospitalGrade,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalNature != null">
        hospital_nature = #{record.hospitalNature,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalApprove != null">
        hospital_approve = #{record.hospitalApprove,jdbcType=VARCHAR},
      </if>
      <if test="record.operationInfoList != null">
        operation_info_list = #{record.operationInfoList,jdbcType=VARCHAR},
      </if>
      <if test="record.socialDesignatedHospital != null">
        social_designated_hospital = #{record.socialDesignatedHospital,jdbcType=VARCHAR},
      </if>
      <if test="record.billAmount != null">
        bill_amount = #{record.billAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.selfExpense != null">
        self_expense = #{record.selfExpense,jdbcType=VARCHAR},
      </if>
      <if test="record.selfExpenseCategory != null">
        self_expense_category = #{record.selfExpenseCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.nonResponsibleCost != null">
        non_responsible_cost = #{record.nonResponsibleCost,jdbcType=VARCHAR},
      </if>
      <if test="record.medicalInsurancePay != null">
        medical_insurance_pay = #{record.medicalInsurancePay,jdbcType=VARCHAR},
      </if>
      <if test="record.otherPay != null">
        other_pay = #{record.otherPay,jdbcType=VARCHAR},
      </if>
      <if test="record.otherPayDetail != null">
        other_pay_detail = #{record.otherPayDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonablePay != null">
        reasonable_pay = #{record.reasonablePay,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityId != null">
        liability_id = #{record.liabilityId,jdbcType=BIGINT},
      </if>
      <if test="record.claimHospitalType != null">
        claim_hospital_type = #{record.claimHospitalType,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.patientName != null">
        patient_name = #{record.patientName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_hospital
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      hospital_code = #{record.hospitalCode,jdbcType=VARCHAR},
      visit_hospital = #{record.visitHospital,jdbcType=VARCHAR},
      bill_number = #{record.billNumber,jdbcType=VARCHAR},
      treatment_type = #{record.treatmentType,jdbcType=TINYINT},
      visit_start = #{record.visitStart,jdbcType=DATE},
      visit_end = #{record.visitEnd,jdbcType=DATE},
      valid_days = #{record.validDays,jdbcType=INTEGER},
      bill_type = #{record.billType,jdbcType=TINYINT},
      tpa_bill_status = #{record.tpaBillStatus,jdbcType=TINYINT},
      attachment_id = #{record.attachmentId,jdbcType=VARCHAR},
      problem_desc = #{record.problemDesc,jdbcType=VARCHAR},
      medical_insurance_type = #{record.medicalInsuranceType,jdbcType=TINYINT},
      material_type = #{record.materialType,jdbcType=TINYINT},
      hospital_type = #{record.hospitalType,jdbcType=VARCHAR},
      hospital_level = #{record.hospitalLevel,jdbcType=VARCHAR},
      hospital_grade = #{record.hospitalGrade,jdbcType=VARCHAR},
      hospital_nature = #{record.hospitalNature,jdbcType=VARCHAR},
      hospital_approve = #{record.hospitalApprove,jdbcType=VARCHAR},
      operation_info_list = #{record.operationInfoList,jdbcType=VARCHAR},
      social_designated_hospital = #{record.socialDesignatedHospital,jdbcType=VARCHAR},
      bill_amount = #{record.billAmount,jdbcType=VARCHAR},
      self_expense = #{record.selfExpense,jdbcType=VARCHAR},
      self_expense_category = #{record.selfExpenseCategory,jdbcType=VARCHAR},
      non_responsible_cost = #{record.nonResponsibleCost,jdbcType=VARCHAR},
      medical_insurance_pay = #{record.medicalInsurancePay,jdbcType=VARCHAR},
      other_pay = #{record.otherPay,jdbcType=VARCHAR},
      other_pay_detail = #{record.otherPayDetail,jdbcType=VARCHAR},
      reasonable_pay = #{record.reasonablePay,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      liability_id = #{record.liabilityId,jdbcType=BIGINT},
      claim_hospital_type = #{record.claimHospitalType,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      patient_name = #{record.patientName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalDO">
    update claim_hospital
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="hospitalCode != null">
        hospital_code = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="visitHospital != null">
        visit_hospital = #{visitHospital,jdbcType=VARCHAR},
      </if>
      <if test="billNumber != null">
        bill_number = #{billNumber,jdbcType=VARCHAR},
      </if>
      <if test="treatmentType != null">
        treatment_type = #{treatmentType,jdbcType=TINYINT},
      </if>
      <if test="visitStart != null">
        visit_start = #{visitStart,jdbcType=DATE},
      </if>
      <if test="visitEnd != null">
        visit_end = #{visitEnd,jdbcType=DATE},
      </if>
      <if test="validDays != null">
        valid_days = #{validDays,jdbcType=INTEGER},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=TINYINT},
      </if>
      <if test="tpaBillStatus != null">
        tpa_bill_status = #{tpaBillStatus,jdbcType=TINYINT},
      </if>
      <if test="attachmentId != null">
        attachment_id = #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="problemDesc != null">
        problem_desc = #{problemDesc,jdbcType=VARCHAR},
      </if>
      <if test="medicalInsuranceType != null">
        medical_insurance_type = #{medicalInsuranceType,jdbcType=TINYINT},
      </if>
      <if test="materialType != null">
        material_type = #{materialType,jdbcType=TINYINT},
      </if>
      <if test="hospitalType != null">
        hospital_type = #{hospitalType,jdbcType=VARCHAR},
      </if>
      <if test="hospitalLevel != null">
        hospital_level = #{hospitalLevel,jdbcType=VARCHAR},
      </if>
      <if test="hospitalGrade != null">
        hospital_grade = #{hospitalGrade,jdbcType=VARCHAR},
      </if>
      <if test="hospitalNature != null">
        hospital_nature = #{hospitalNature,jdbcType=VARCHAR},
      </if>
      <if test="hospitalApprove != null">
        hospital_approve = #{hospitalApprove,jdbcType=VARCHAR},
      </if>
      <if test="operationInfoList != null">
        operation_info_list = #{operationInfoList,jdbcType=VARCHAR},
      </if>
      <if test="socialDesignatedHospital != null">
        social_designated_hospital = #{socialDesignatedHospital,jdbcType=VARCHAR},
      </if>
      <if test="billAmount != null">
        bill_amount = #{billAmount,jdbcType=VARCHAR},
      </if>
      <if test="selfExpense != null">
        self_expense = #{selfExpense,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseCategory != null">
        self_expense_category = #{selfExpenseCategory,jdbcType=VARCHAR},
      </if>
      <if test="nonResponsibleCost != null">
        non_responsible_cost = #{nonResponsibleCost,jdbcType=VARCHAR},
      </if>
      <if test="medicalInsurancePay != null">
        medical_insurance_pay = #{medicalInsurancePay,jdbcType=VARCHAR},
      </if>
      <if test="otherPay != null">
        other_pay = #{otherPay,jdbcType=VARCHAR},
      </if>
      <if test="otherPayDetail != null">
        other_pay_detail = #{otherPayDetail,jdbcType=VARCHAR},
      </if>
      <if test="reasonablePay != null">
        reasonable_pay = #{reasonablePay,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="liabilityId != null">
        liability_id = #{liabilityId,jdbcType=BIGINT},
      </if>
      <if test="claimHospitalType != null">
        claim_hospital_type = #{claimHospitalType,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="patientName != null">
        patient_name = #{patientName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalDO">
    update claim_hospital
    set report_no = #{reportNo,jdbcType=VARCHAR},
      batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      hospital_code = #{hospitalCode,jdbcType=VARCHAR},
      visit_hospital = #{visitHospital,jdbcType=VARCHAR},
      bill_number = #{billNumber,jdbcType=VARCHAR},
      treatment_type = #{treatmentType,jdbcType=TINYINT},
      visit_start = #{visitStart,jdbcType=DATE},
      visit_end = #{visitEnd,jdbcType=DATE},
      valid_days = #{validDays,jdbcType=INTEGER},
      bill_type = #{billType,jdbcType=TINYINT},
      tpa_bill_status = #{tpaBillStatus,jdbcType=TINYINT},
      attachment_id = #{attachmentId,jdbcType=VARCHAR},
      problem_desc = #{problemDesc,jdbcType=VARCHAR},
      medical_insurance_type = #{medicalInsuranceType,jdbcType=TINYINT},
      material_type = #{materialType,jdbcType=TINYINT},
      hospital_type = #{hospitalType,jdbcType=VARCHAR},
      hospital_level = #{hospitalLevel,jdbcType=VARCHAR},
      hospital_grade = #{hospitalGrade,jdbcType=VARCHAR},
      hospital_nature = #{hospitalNature,jdbcType=VARCHAR},
      hospital_approve = #{hospitalApprove,jdbcType=VARCHAR},
      operation_info_list = #{operationInfoList,jdbcType=VARCHAR},
      social_designated_hospital = #{socialDesignatedHospital,jdbcType=VARCHAR},
      bill_amount = #{billAmount,jdbcType=VARCHAR},
      self_expense = #{selfExpense,jdbcType=VARCHAR},
      self_expense_category = #{selfExpenseCategory,jdbcType=VARCHAR},
      non_responsible_cost = #{nonResponsibleCost,jdbcType=VARCHAR},
      medical_insurance_pay = #{medicalInsurancePay,jdbcType=VARCHAR},
      other_pay = #{otherPay,jdbcType=VARCHAR},
      other_pay_detail = #{otherPayDetail,jdbcType=VARCHAR},
      reasonable_pay = #{reasonablePay,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      liability_id = #{liabilityId,jdbcType=BIGINT},
      claim_hospital_type = #{claimHospitalType,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      patient_name = #{patientName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_hospital
    (id, report_no, batch_claim_bill_no, hospital_code, visit_hospital, bill_number, 
      treatment_type, visit_start, visit_end, valid_days, bill_type, tpa_bill_status, 
      attachment_id, problem_desc, medical_insurance_type, material_type, hospital_type, 
      hospital_level, hospital_grade, hospital_nature, hospital_approve, operation_info_list, 
      social_designated_hospital, bill_amount, self_expense, self_expense_category, non_responsible_cost, 
      medical_insurance_pay, other_pay, other_pay_detail, reasonable_pay, policy_no, 
      liability_id, claim_hospital_type, gmt_created, gmt_modified, creator, modifier, 
      is_deleted, patient_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.batchClaimBillNo,jdbcType=VARCHAR}, 
        #{item.hospitalCode,jdbcType=VARCHAR}, #{item.visitHospital,jdbcType=VARCHAR}, 
        #{item.billNumber,jdbcType=VARCHAR}, #{item.treatmentType,jdbcType=TINYINT}, #{item.visitStart,jdbcType=DATE}, 
        #{item.visitEnd,jdbcType=DATE}, #{item.validDays,jdbcType=INTEGER}, #{item.billType,jdbcType=TINYINT}, 
        #{item.tpaBillStatus,jdbcType=TINYINT}, #{item.attachmentId,jdbcType=VARCHAR}, 
        #{item.problemDesc,jdbcType=VARCHAR}, #{item.medicalInsuranceType,jdbcType=TINYINT}, 
        #{item.materialType,jdbcType=TINYINT}, #{item.hospitalType,jdbcType=VARCHAR}, #{item.hospitalLevel,jdbcType=VARCHAR}, 
        #{item.hospitalGrade,jdbcType=VARCHAR}, #{item.hospitalNature,jdbcType=VARCHAR}, 
        #{item.hospitalApprove,jdbcType=VARCHAR}, #{item.operationInfoList,jdbcType=VARCHAR}, 
        #{item.socialDesignatedHospital,jdbcType=VARCHAR}, #{item.billAmount,jdbcType=VARCHAR}, 
        #{item.selfExpense,jdbcType=VARCHAR}, #{item.selfExpenseCategory,jdbcType=VARCHAR}, 
        #{item.nonResponsibleCost,jdbcType=VARCHAR}, #{item.medicalInsurancePay,jdbcType=VARCHAR}, 
        #{item.otherPay,jdbcType=VARCHAR}, #{item.otherPayDetail,jdbcType=VARCHAR}, #{item.reasonablePay,jdbcType=VARCHAR}, 
        #{item.policyNo,jdbcType=VARCHAR}, #{item.liabilityId,jdbcType=BIGINT}, #{item.claimHospitalType,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.patientName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_hospital (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'batch_claim_bill_no'.toString() == column.value">
          #{item.batchClaimBillNo,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_code'.toString() == column.value">
          #{item.hospitalCode,jdbcType=VARCHAR}
        </if>
        <if test="'visit_hospital'.toString() == column.value">
          #{item.visitHospital,jdbcType=VARCHAR}
        </if>
        <if test="'bill_number'.toString() == column.value">
          #{item.billNumber,jdbcType=VARCHAR}
        </if>
        <if test="'treatment_type'.toString() == column.value">
          #{item.treatmentType,jdbcType=TINYINT}
        </if>
        <if test="'visit_start'.toString() == column.value">
          #{item.visitStart,jdbcType=DATE}
        </if>
        <if test="'visit_end'.toString() == column.value">
          #{item.visitEnd,jdbcType=DATE}
        </if>
        <if test="'valid_days'.toString() == column.value">
          #{item.validDays,jdbcType=INTEGER}
        </if>
        <if test="'bill_type'.toString() == column.value">
          #{item.billType,jdbcType=TINYINT}
        </if>
        <if test="'tpa_bill_status'.toString() == column.value">
          #{item.tpaBillStatus,jdbcType=TINYINT}
        </if>
        <if test="'attachment_id'.toString() == column.value">
          #{item.attachmentId,jdbcType=VARCHAR}
        </if>
        <if test="'problem_desc'.toString() == column.value">
          #{item.problemDesc,jdbcType=VARCHAR}
        </if>
        <if test="'medical_insurance_type'.toString() == column.value">
          #{item.medicalInsuranceType,jdbcType=TINYINT}
        </if>
        <if test="'material_type'.toString() == column.value">
          #{item.materialType,jdbcType=TINYINT}
        </if>
        <if test="'hospital_type'.toString() == column.value">
          #{item.hospitalType,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_level'.toString() == column.value">
          #{item.hospitalLevel,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_grade'.toString() == column.value">
          #{item.hospitalGrade,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_nature'.toString() == column.value">
          #{item.hospitalNature,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_approve'.toString() == column.value">
          #{item.hospitalApprove,jdbcType=VARCHAR}
        </if>
        <if test="'operation_info_list'.toString() == column.value">
          #{item.operationInfoList,jdbcType=VARCHAR}
        </if>
        <if test="'social_designated_hospital'.toString() == column.value">
          #{item.socialDesignatedHospital,jdbcType=VARCHAR}
        </if>
        <if test="'bill_amount'.toString() == column.value">
          #{item.billAmount,jdbcType=VARCHAR}
        </if>
        <if test="'self_expense'.toString() == column.value">
          #{item.selfExpense,jdbcType=VARCHAR}
        </if>
        <if test="'self_expense_category'.toString() == column.value">
          #{item.selfExpenseCategory,jdbcType=VARCHAR}
        </if>
        <if test="'non_responsible_cost'.toString() == column.value">
          #{item.nonResponsibleCost,jdbcType=VARCHAR}
        </if>
        <if test="'medical_insurance_pay'.toString() == column.value">
          #{item.medicalInsurancePay,jdbcType=VARCHAR}
        </if>
        <if test="'other_pay'.toString() == column.value">
          #{item.otherPay,jdbcType=VARCHAR}
        </if>
        <if test="'other_pay_detail'.toString() == column.value">
          #{item.otherPayDetail,jdbcType=VARCHAR}
        </if>
        <if test="'reasonable_pay'.toString() == column.value">
          #{item.reasonablePay,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'liability_id'.toString() == column.value">
          #{item.liabilityId,jdbcType=BIGINT}
        </if>
        <if test="'claim_hospital_type'.toString() == column.value">
          #{item.claimHospitalType,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'patient_name'.toString() == column.value">
          #{item.patientName,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>