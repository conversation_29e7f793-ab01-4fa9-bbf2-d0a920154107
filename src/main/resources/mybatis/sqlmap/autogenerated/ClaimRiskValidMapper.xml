<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimRiskValidMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimRiskValidDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="positive_label" jdbcType="VARCHAR" property="positiveLabel" />
    <result column="is_valid" jdbcType="CHAR" property="isValid" />
    <result column="loss_amount" jdbcType="VARCHAR" property="lossAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_reopen" jdbcType="CHAR" property="isReopen" />
    <result column="positive_label_valid" jdbcType="VARCHAR" property="positiveLabelValid" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_no, report_no, positive_label, is_valid, loss_amount, remark, is_reopen, 
    positive_label_valid, is_deleted, gmt_created, gmt_modified, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskValidExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_risk_valid
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_risk_valid
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskValidDO">
    insert into claim_risk_valid (id, policy_no, report_no, 
      positive_label, is_valid, loss_amount, 
      remark, is_reopen, positive_label_valid, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier)
    values (#{id,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{positiveLabel,jdbcType=VARCHAR}, #{isValid,jdbcType=CHAR}, #{lossAmount,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{isReopen,jdbcType=CHAR}, #{positiveLabelValid,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskValidDO">
    insert into claim_risk_valid
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="positiveLabel != null">
        positive_label,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="lossAmount != null">
        loss_amount,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isReopen != null">
        is_reopen,
      </if>
      <if test="positiveLabelValid != null">
        positive_label_valid,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="positiveLabel != null">
        #{positiveLabel,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=CHAR},
      </if>
      <if test="lossAmount != null">
        #{lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isReopen != null">
        #{isReopen,jdbcType=CHAR},
      </if>
      <if test="positiveLabelValid != null">
        #{positiveLabelValid,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskValidExample" resultType="java.lang.Long">
    select count(*) from claim_risk_valid
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_risk_valid
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.positiveLabel != null">
        positive_label = #{record.positiveLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.isValid != null">
        is_valid = #{record.isValid,jdbcType=CHAR},
      </if>
      <if test="record.lossAmount != null">
        loss_amount = #{record.lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isReopen != null">
        is_reopen = #{record.isReopen,jdbcType=CHAR},
      </if>
      <if test="record.positiveLabelValid != null">
        positive_label_valid = #{record.positiveLabelValid,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_risk_valid
    set id = #{record.id,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      positive_label = #{record.positiveLabel,jdbcType=VARCHAR},
      is_valid = #{record.isValid,jdbcType=CHAR},
      loss_amount = #{record.lossAmount,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_reopen = #{record.isReopen,jdbcType=CHAR},
      positive_label_valid = #{record.positiveLabelValid,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskValidDO">
    update claim_risk_valid
    <set>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="positiveLabel != null">
        positive_label = #{positiveLabel,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=CHAR},
      </if>
      <if test="lossAmount != null">
        loss_amount = #{lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isReopen != null">
        is_reopen = #{isReopen,jdbcType=CHAR},
      </if>
      <if test="positiveLabelValid != null">
        positive_label_valid = #{positiveLabelValid,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskValidDO">
    update claim_risk_valid
    set policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      positive_label = #{positiveLabel,jdbcType=VARCHAR},
      is_valid = #{isValid,jdbcType=CHAR},
      loss_amount = #{lossAmount,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_reopen = #{isReopen,jdbcType=CHAR},
      positive_label_valid = #{positiveLabelValid,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_risk_valid
    (id, policy_no, report_no, positive_label, is_valid, loss_amount, remark, is_reopen, 
      positive_label_valid, is_deleted, gmt_created, gmt_modified, creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.positiveLabel,jdbcType=VARCHAR}, #{item.isValid,jdbcType=CHAR}, #{item.lossAmount,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.isReopen,jdbcType=CHAR}, #{item.positiveLabelValid,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_risk_valid (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'positive_label'.toString() == column.value">
          #{item.positiveLabel,jdbcType=VARCHAR}
        </if>
        <if test="'is_valid'.toString() == column.value">
          #{item.isValid,jdbcType=CHAR}
        </if>
        <if test="'loss_amount'.toString() == column.value">
          #{item.lossAmount,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_reopen'.toString() == column.value">
          #{item.isReopen,jdbcType=CHAR}
        </if>
        <if test="'positive_label_valid'.toString() == column.value">
          #{item.positiveLabelValid,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>