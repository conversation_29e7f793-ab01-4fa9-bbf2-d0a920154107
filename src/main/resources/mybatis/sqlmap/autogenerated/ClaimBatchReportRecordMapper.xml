<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimBatchReportRecordMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimBatchReportRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="report_batch_no" jdbcType="VARCHAR" property="reportBatchNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="parent_policy_no" jdbcType="VARCHAR" property="parentPolicyNo" />
    <result column="policy_nos" jdbcType="VARCHAR" property="policyNos" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="deal_result" jdbcType="CHAR" property="dealResult" />
    <result column="deal_remark" jdbcType="VARCHAR" property="dealRemark" />
    <result column="origin_type" jdbcType="VARCHAR" property="originType" />
    <result column="origin_id" jdbcType="BIGINT" property="originId" />
    <result column="sync_email_flag" jdbcType="CHAR" property="syncEmailFlag" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_type, report_batch_no, policy_no, parent_policy_no, policy_nos, report_no, 
    deal_result, deal_remark, origin_type, origin_id, sync_email_flag, is_deleted, gmt_created, 
    gmt_modified, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchReportRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_batch_report_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_batch_report_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchReportRecordDO">
    insert into claim_batch_report_record (id, biz_type, report_batch_no, 
      policy_no, parent_policy_no, policy_nos, 
      report_no, deal_result, deal_remark, 
      origin_type, origin_id, sync_email_flag, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier)
    values (#{id,jdbcType=BIGINT}, #{bizType,jdbcType=TINYINT}, #{reportBatchNo,jdbcType=VARCHAR}, 
      #{policyNo,jdbcType=VARCHAR}, #{parentPolicyNo,jdbcType=VARCHAR}, #{policyNos,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{dealResult,jdbcType=CHAR}, #{dealRemark,jdbcType=VARCHAR}, 
      #{originType,jdbcType=VARCHAR}, #{originId,jdbcType=BIGINT}, #{syncEmailFlag,jdbcType=CHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchReportRecordDO">
    insert into claim_batch_report_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="reportBatchNo != null">
        report_batch_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="parentPolicyNo != null">
        parent_policy_no,
      </if>
      <if test="policyNos != null">
        policy_nos,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="dealResult != null">
        deal_result,
      </if>
      <if test="dealRemark != null">
        deal_remark,
      </if>
      <if test="originType != null">
        origin_type,
      </if>
      <if test="originId != null">
        origin_id,
      </if>
      <if test="syncEmailFlag != null">
        sync_email_flag,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="reportBatchNo != null">
        #{reportBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="parentPolicyNo != null">
        #{parentPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNos != null">
        #{policyNos,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="dealResult != null">
        #{dealResult,jdbcType=CHAR},
      </if>
      <if test="dealRemark != null">
        #{dealRemark,jdbcType=VARCHAR},
      </if>
      <if test="originType != null">
        #{originType,jdbcType=VARCHAR},
      </if>
      <if test="originId != null">
        #{originId,jdbcType=BIGINT},
      </if>
      <if test="syncEmailFlag != null">
        #{syncEmailFlag,jdbcType=CHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchReportRecordExample" resultType="java.lang.Long">
    select count(*) from claim_batch_report_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_batch_report_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizType != null">
        biz_type = #{record.bizType,jdbcType=TINYINT},
      </if>
      <if test="record.reportBatchNo != null">
        report_batch_no = #{record.reportBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.parentPolicyNo != null">
        parent_policy_no = #{record.parentPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNos != null">
        policy_nos = #{record.policyNos,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.dealResult != null">
        deal_result = #{record.dealResult,jdbcType=CHAR},
      </if>
      <if test="record.dealRemark != null">
        deal_remark = #{record.dealRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.originType != null">
        origin_type = #{record.originType,jdbcType=VARCHAR},
      </if>
      <if test="record.originId != null">
        origin_id = #{record.originId,jdbcType=BIGINT},
      </if>
      <if test="record.syncEmailFlag != null">
        sync_email_flag = #{record.syncEmailFlag,jdbcType=CHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_batch_report_record
    set id = #{record.id,jdbcType=BIGINT},
      biz_type = #{record.bizType,jdbcType=TINYINT},
      report_batch_no = #{record.reportBatchNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      parent_policy_no = #{record.parentPolicyNo,jdbcType=VARCHAR},
      policy_nos = #{record.policyNos,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      deal_result = #{record.dealResult,jdbcType=CHAR},
      deal_remark = #{record.dealRemark,jdbcType=VARCHAR},
      origin_type = #{record.originType,jdbcType=VARCHAR},
      origin_id = #{record.originId,jdbcType=BIGINT},
      sync_email_flag = #{record.syncEmailFlag,jdbcType=CHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchReportRecordDO">
    update claim_batch_report_record
    <set>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="reportBatchNo != null">
        report_batch_no = #{reportBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="parentPolicyNo != null">
        parent_policy_no = #{parentPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNos != null">
        policy_nos = #{policyNos,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="dealResult != null">
        deal_result = #{dealResult,jdbcType=CHAR},
      </if>
      <if test="dealRemark != null">
        deal_remark = #{dealRemark,jdbcType=VARCHAR},
      </if>
      <if test="originType != null">
        origin_type = #{originType,jdbcType=VARCHAR},
      </if>
      <if test="originId != null">
        origin_id = #{originId,jdbcType=BIGINT},
      </if>
      <if test="syncEmailFlag != null">
        sync_email_flag = #{syncEmailFlag,jdbcType=CHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchReportRecordDO">
    update claim_batch_report_record
    set biz_type = #{bizType,jdbcType=TINYINT},
      report_batch_no = #{reportBatchNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      parent_policy_no = #{parentPolicyNo,jdbcType=VARCHAR},
      policy_nos = #{policyNos,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      deal_result = #{dealResult,jdbcType=CHAR},
      deal_remark = #{dealRemark,jdbcType=VARCHAR},
      origin_type = #{originType,jdbcType=VARCHAR},
      origin_id = #{originId,jdbcType=BIGINT},
      sync_email_flag = #{syncEmailFlag,jdbcType=CHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_batch_report_record
    (id, biz_type, report_batch_no, policy_no, parent_policy_no, policy_nos, report_no, 
      deal_result, deal_remark, origin_type, origin_id, sync_email_flag, is_deleted, 
      gmt_created, gmt_modified, creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.bizType,jdbcType=TINYINT}, #{item.reportBatchNo,jdbcType=VARCHAR}, 
        #{item.policyNo,jdbcType=VARCHAR}, #{item.parentPolicyNo,jdbcType=VARCHAR}, #{item.policyNos,jdbcType=VARCHAR}, 
        #{item.reportNo,jdbcType=VARCHAR}, #{item.dealResult,jdbcType=CHAR}, #{item.dealRemark,jdbcType=VARCHAR}, 
        #{item.originType,jdbcType=VARCHAR}, #{item.originId,jdbcType=BIGINT}, #{item.syncEmailFlag,jdbcType=CHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_batch_report_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'biz_type'.toString() == column.value">
          #{item.bizType,jdbcType=TINYINT}
        </if>
        <if test="'report_batch_no'.toString() == column.value">
          #{item.reportBatchNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'parent_policy_no'.toString() == column.value">
          #{item.parentPolicyNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_nos'.toString() == column.value">
          #{item.policyNos,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'deal_result'.toString() == column.value">
          #{item.dealResult,jdbcType=CHAR}
        </if>
        <if test="'deal_remark'.toString() == column.value">
          #{item.dealRemark,jdbcType=VARCHAR}
        </if>
        <if test="'origin_type'.toString() == column.value">
          #{item.originType,jdbcType=VARCHAR}
        </if>
        <if test="'origin_id'.toString() == column.value">
          #{item.originId,jdbcType=BIGINT}
        </if>
        <if test="'sync_email_flag'.toString() == column.value">
          #{item.syncEmailFlag,jdbcType=CHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>