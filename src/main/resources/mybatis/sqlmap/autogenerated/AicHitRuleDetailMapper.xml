<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AicHitRuleDetailMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AicHitRuleDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_detail" jdbcType="VARCHAR" property="projectDetail" />
    <result column="hit_rule_name" jdbcType="VARCHAR" property="hitRuleName" />
    <result column="hit_rule_code" jdbcType="VARCHAR" property="hitRuleCode" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="claim_status" jdbcType="INTEGER" property="claimStatus" />
    <result column="paid_amount" jdbcType="VARCHAR" property="paidAmount" />
    <result column="report_date" jdbcType="TIMESTAMP" property="reportDate" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_detail, hit_rule_name, hit_rule_code, report_no, claim_status, paid_amount, 
    report_date, extra_info, gmt_created, gmt_modified, creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AicHitRuleDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aic_hit_rule_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aic_hit_rule_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AicHitRuleDetailDO">
    insert into aic_hit_rule_detail (id, project_detail, hit_rule_name, 
      hit_rule_code, report_no, claim_status, 
      paid_amount, report_date, extra_info, 
      gmt_created, gmt_modified, creator, 
      modifier, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{projectDetail,jdbcType=VARCHAR}, #{hitRuleName,jdbcType=VARCHAR}, 
      #{hitRuleCode,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{claimStatus,jdbcType=INTEGER}, 
      #{paidAmount,jdbcType=VARCHAR}, #{reportDate,jdbcType=TIMESTAMP}, #{extraInfo,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AicHitRuleDetailDO">
    insert into aic_hit_rule_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectDetail != null">
        project_detail,
      </if>
      <if test="hitRuleName != null">
        hit_rule_name,
      </if>
      <if test="hitRuleCode != null">
        hit_rule_code,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="claimStatus != null">
        claim_status,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectDetail != null">
        #{projectDetail,jdbcType=VARCHAR},
      </if>
      <if test="hitRuleName != null">
        #{hitRuleName,jdbcType=VARCHAR},
      </if>
      <if test="hitRuleCode != null">
        #{hitRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        #{claimStatus,jdbcType=INTEGER},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AicHitRuleDetailExample" resultType="java.lang.Long">
    select count(*) from aic_hit_rule_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update aic_hit_rule_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectDetail != null">
        project_detail = #{record.projectDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.hitRuleName != null">
        hit_rule_name = #{record.hitRuleName,jdbcType=VARCHAR},
      </if>
      <if test="record.hitRuleCode != null">
        hit_rule_code = #{record.hitRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimStatus != null">
        claim_status = #{record.claimStatus,jdbcType=INTEGER},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDate != null">
        report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update aic_hit_rule_detail
    set id = #{record.id,jdbcType=BIGINT},
      project_detail = #{record.projectDetail,jdbcType=VARCHAR},
      hit_rule_name = #{record.hitRuleName,jdbcType=VARCHAR},
      hit_rule_code = #{record.hitRuleCode,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      claim_status = #{record.claimStatus,jdbcType=INTEGER},
      paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AicHitRuleDetailDO">
    update aic_hit_rule_detail
    <set>
      <if test="projectDetail != null">
        project_detail = #{projectDetail,jdbcType=VARCHAR},
      </if>
      <if test="hitRuleName != null">
        hit_rule_name = #{hitRuleName,jdbcType=VARCHAR},
      </if>
      <if test="hitRuleCode != null">
        hit_rule_code = #{hitRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        claim_status = #{claimStatus,jdbcType=INTEGER},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AicHitRuleDetailDO">
    update aic_hit_rule_detail
    set project_detail = #{projectDetail,jdbcType=VARCHAR},
      hit_rule_name = #{hitRuleName,jdbcType=VARCHAR},
      hit_rule_code = #{hitRuleCode,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      claim_status = #{claimStatus,jdbcType=INTEGER},
      paid_amount = #{paidAmount,jdbcType=VARCHAR},
      report_date = #{reportDate,jdbcType=TIMESTAMP},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into aic_hit_rule_detail
    (id, project_detail, hit_rule_name, hit_rule_code, report_no, claim_status, paid_amount, 
      report_date, extra_info, gmt_created, gmt_modified, creator, modifier, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.projectDetail,jdbcType=VARCHAR}, #{item.hitRuleName,jdbcType=VARCHAR}, 
        #{item.hitRuleCode,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.claimStatus,jdbcType=INTEGER}, 
        #{item.paidAmount,jdbcType=VARCHAR}, #{item.reportDate,jdbcType=TIMESTAMP}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into aic_hit_rule_detail (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'project_detail'.toString() == column.value">
          #{item.projectDetail,jdbcType=VARCHAR}
        </if>
        <if test="'hit_rule_name'.toString() == column.value">
          #{item.hitRuleName,jdbcType=VARCHAR}
        </if>
        <if test="'hit_rule_code'.toString() == column.value">
          #{item.hitRuleCode,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'claim_status'.toString() == column.value">
          #{item.claimStatus,jdbcType=INTEGER}
        </if>
        <if test="'paid_amount'.toString() == column.value">
          #{item.paidAmount,jdbcType=VARCHAR}
        </if>
        <if test="'report_date'.toString() == column.value">
          #{item.reportDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>