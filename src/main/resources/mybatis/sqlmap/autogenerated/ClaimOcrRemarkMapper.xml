<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimOcrRemarkMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimOcrRemarkDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="flight_no" jdbcType="VARCHAR" property="flightNo" />
    <result column="flight_dep_time_plan" jdbcType="VARCHAR" property="flightDepTimePlan" />
    <result column="aboard_time" jdbcType="VARCHAR" property="aboardTime" />
    <result column="delay_time" jdbcType="VARCHAR" property="delayTime" />
    <result column="flight_dep_code" jdbcType="VARCHAR" property="flightDepCode" />
    <result column="flight_arr_code" jdbcType="VARCHAR" property="flightArrCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, attachment_url, name, flight_no, flight_dep_time_plan, aboard_time, 
    delay_time, flight_dep_code, flight_arr_code, remark, extra_info, is_deleted, gmt_created, 
    gmt_modified, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimOcrRemarkExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_ocr_remark
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_ocr_remark
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimOcrRemarkDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_ocr_remark (report_no, attachment_url, name, 
      flight_no, flight_dep_time_plan, aboard_time, 
      delay_time, flight_dep_code, flight_arr_code, 
      remark, extra_info, is_deleted, 
      gmt_created, gmt_modified, creator, 
      modifier)
    values (#{reportNo,jdbcType=VARCHAR}, #{attachmentUrl,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{flightNo,jdbcType=VARCHAR}, #{flightDepTimePlan,jdbcType=VARCHAR}, #{aboardTime,jdbcType=VARCHAR}, 
      #{delayTime,jdbcType=VARCHAR}, #{flightDepCode,jdbcType=VARCHAR}, #{flightArrCode,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimOcrRemarkDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_ocr_remark
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="attachmentUrl != null">
        attachment_url,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="flightNo != null">
        flight_no,
      </if>
      <if test="flightDepTimePlan != null">
        flight_dep_time_plan,
      </if>
      <if test="aboardTime != null">
        aboard_time,
      </if>
      <if test="delayTime != null">
        delay_time,
      </if>
      <if test="flightDepCode != null">
        flight_dep_code,
      </if>
      <if test="flightArrCode != null">
        flight_arr_code,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null">
        #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="flightNo != null">
        #{flightNo,jdbcType=VARCHAR},
      </if>
      <if test="flightDepTimePlan != null">
        #{flightDepTimePlan,jdbcType=VARCHAR},
      </if>
      <if test="aboardTime != null">
        #{aboardTime,jdbcType=VARCHAR},
      </if>
      <if test="delayTime != null">
        #{delayTime,jdbcType=VARCHAR},
      </if>
      <if test="flightDepCode != null">
        #{flightDepCode,jdbcType=VARCHAR},
      </if>
      <if test="flightArrCode != null">
        #{flightArrCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimOcrRemarkExample" resultType="java.lang.Long">
    select count(*) from claim_ocr_remark
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_ocr_remark
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentUrl != null">
        attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.flightNo != null">
        flight_no = #{record.flightNo,jdbcType=VARCHAR},
      </if>
      <if test="record.flightDepTimePlan != null">
        flight_dep_time_plan = #{record.flightDepTimePlan,jdbcType=VARCHAR},
      </if>
      <if test="record.aboardTime != null">
        aboard_time = #{record.aboardTime,jdbcType=VARCHAR},
      </if>
      <if test="record.delayTime != null">
        delay_time = #{record.delayTime,jdbcType=VARCHAR},
      </if>
      <if test="record.flightDepCode != null">
        flight_dep_code = #{record.flightDepCode,jdbcType=VARCHAR},
      </if>
      <if test="record.flightArrCode != null">
        flight_arr_code = #{record.flightArrCode,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_ocr_remark
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      flight_no = #{record.flightNo,jdbcType=VARCHAR},
      flight_dep_time_plan = #{record.flightDepTimePlan,jdbcType=VARCHAR},
      aboard_time = #{record.aboardTime,jdbcType=VARCHAR},
      delay_time = #{record.delayTime,jdbcType=VARCHAR},
      flight_dep_code = #{record.flightDepCode,jdbcType=VARCHAR},
      flight_arr_code = #{record.flightArrCode,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimOcrRemarkDO">
    update claim_ocr_remark
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null">
        attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="flightNo != null">
        flight_no = #{flightNo,jdbcType=VARCHAR},
      </if>
      <if test="flightDepTimePlan != null">
        flight_dep_time_plan = #{flightDepTimePlan,jdbcType=VARCHAR},
      </if>
      <if test="aboardTime != null">
        aboard_time = #{aboardTime,jdbcType=VARCHAR},
      </if>
      <if test="delayTime != null">
        delay_time = #{delayTime,jdbcType=VARCHAR},
      </if>
      <if test="flightDepCode != null">
        flight_dep_code = #{flightDepCode,jdbcType=VARCHAR},
      </if>
      <if test="flightArrCode != null">
        flight_arr_code = #{flightArrCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimOcrRemarkDO">
    update claim_ocr_remark
    set report_no = #{reportNo,jdbcType=VARCHAR},
      attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      flight_no = #{flightNo,jdbcType=VARCHAR},
      flight_dep_time_plan = #{flightDepTimePlan,jdbcType=VARCHAR},
      aboard_time = #{aboardTime,jdbcType=VARCHAR},
      delay_time = #{delayTime,jdbcType=VARCHAR},
      flight_dep_code = #{flightDepCode,jdbcType=VARCHAR},
      flight_arr_code = #{flightArrCode,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into claim_ocr_remark
    (report_no, attachment_url, name, flight_no, flight_dep_time_plan, aboard_time, delay_time, 
      flight_dep_code, flight_arr_code, remark, extra_info, is_deleted, gmt_created, 
      gmt_modified, creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reportNo,jdbcType=VARCHAR}, #{item.attachmentUrl,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.flightNo,jdbcType=VARCHAR}, #{item.flightDepTimePlan,jdbcType=VARCHAR}, 
        #{item.aboardTime,jdbcType=VARCHAR}, #{item.delayTime,jdbcType=VARCHAR}, #{item.flightDepCode,jdbcType=VARCHAR}, 
        #{item.flightArrCode,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into claim_ocr_remark (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_url'.toString() == column.value">
          #{item.attachmentUrl,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'flight_no'.toString() == column.value">
          #{item.flightNo,jdbcType=VARCHAR}
        </if>
        <if test="'flight_dep_time_plan'.toString() == column.value">
          #{item.flightDepTimePlan,jdbcType=VARCHAR}
        </if>
        <if test="'aboard_time'.toString() == column.value">
          #{item.aboardTime,jdbcType=VARCHAR}
        </if>
        <if test="'delay_time'.toString() == column.value">
          #{item.delayTime,jdbcType=VARCHAR}
        </if>
        <if test="'flight_dep_code'.toString() == column.value">
          #{item.flightDepCode,jdbcType=VARCHAR}
        </if>
        <if test="'flight_arr_code'.toString() == column.value">
          #{item.flightArrCode,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>