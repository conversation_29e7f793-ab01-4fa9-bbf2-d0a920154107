<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimTaskTriggerMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimTaskTriggerDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="task_type" jdbcType="INTEGER" property="taskType" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="trigger_date_plan" jdbcType="TIMESTAMP" property="triggerDatePlan" />
    <result column="data" jdbcType="VARCHAR" property="data" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_trigger" jdbcType="VARCHAR" property="isTrigger" />
    <result column="trigger_date" jdbcType="TIMESTAMP" property="triggerDate" />
    <result column="is_active" jdbcType="VARCHAR" property="isActive" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="batch_claim_bill_no" jdbcType="VARCHAR" property="batchClaimBillNo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, is_deleted, creator, modifier, gmt_created, gmt_modified, task_type, task_name, 
    trigger_date_plan, data, remark, is_trigger, trigger_date, is_active, report_no, 
    batch_claim_bill_no
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimTaskTriggerExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_task_trigger
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_task_trigger
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimTaskTriggerDO">
    insert into claim_task_trigger (id, is_deleted, creator, 
      modifier, gmt_created, gmt_modified, 
      task_type, task_name, trigger_date_plan, 
      data, remark, is_trigger, 
      trigger_date, is_active, report_no, 
      batch_claim_bill_no)
    values (#{id,jdbcType=BIGINT}, #{isDeleted,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate(), 
      #{taskType,jdbcType=INTEGER}, #{taskName,jdbcType=VARCHAR}, #{triggerDatePlan,jdbcType=TIMESTAMP}, 
      #{data,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isTrigger,jdbcType=VARCHAR}, 
      #{triggerDate,jdbcType=TIMESTAMP}, #{isActive,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{batchClaimBillNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimTaskTriggerDO">
    insert into claim_task_trigger
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="triggerDatePlan != null">
        trigger_date_plan,
      </if>
      <if test="data != null">
        data,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isTrigger != null">
        is_trigger,
      </if>
      <if test="triggerDate != null">
        trigger_date,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=INTEGER},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="triggerDatePlan != null">
        #{triggerDatePlan,jdbcType=TIMESTAMP},
      </if>
      <if test="data != null">
        #{data,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isTrigger != null">
        #{isTrigger,jdbcType=VARCHAR},
      </if>
      <if test="triggerDate != null">
        #{triggerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillNo != null">
        #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimTaskTriggerExample" resultType="java.lang.Long">
    select count(*) from claim_task_trigger
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_task_trigger
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=INTEGER},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.triggerDatePlan != null">
        trigger_date_plan = #{record.triggerDatePlan,jdbcType=TIMESTAMP},
      </if>
      <if test="record.data != null">
        data = #{record.data,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isTrigger != null">
        is_trigger = #{record.isTrigger,jdbcType=VARCHAR},
      </if>
      <if test="record.triggerDate != null">
        trigger_date = #{record.triggerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isActive != null">
        is_active = #{record.isActive,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchClaimBillNo != null">
        batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_task_trigger
    set id = #{record.id,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      task_type = #{record.taskType,jdbcType=INTEGER},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      trigger_date_plan = #{record.triggerDatePlan,jdbcType=TIMESTAMP},
      data = #{record.data,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_trigger = #{record.isTrigger,jdbcType=VARCHAR},
      trigger_date = #{record.triggerDate,jdbcType=TIMESTAMP},
      is_active = #{record.isActive,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimTaskTriggerDO">
    update claim_task_trigger
    <set>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=INTEGER},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="triggerDatePlan != null">
        trigger_date_plan = #{triggerDatePlan,jdbcType=TIMESTAMP},
      </if>
      <if test="data != null">
        data = #{data,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isTrigger != null">
        is_trigger = #{isTrigger,jdbcType=VARCHAR},
      </if>
      <if test="triggerDate != null">
        trigger_date = #{triggerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimTaskTriggerDO">
    update claim_task_trigger
    set is_deleted = #{isDeleted,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      task_type = #{taskType,jdbcType=INTEGER},
      task_name = #{taskName,jdbcType=VARCHAR},
      trigger_date_plan = #{triggerDatePlan,jdbcType=TIMESTAMP},
      data = #{data,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_trigger = #{isTrigger,jdbcType=VARCHAR},
      trigger_date = #{triggerDate,jdbcType=TIMESTAMP},
      is_active = #{isActive,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_task_trigger
    (id, is_deleted, creator, modifier, gmt_created, gmt_modified, task_type, task_name, 
      trigger_date_plan, data, remark, is_trigger, trigger_date, is_active, report_no, 
      batch_claim_bill_no)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.isDeleted,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.taskType,jdbcType=INTEGER}, #{item.taskName,jdbcType=VARCHAR}, #{item.triggerDatePlan,jdbcType=TIMESTAMP}, 
        #{item.data,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.isTrigger,jdbcType=VARCHAR}, 
        #{item.triggerDate,jdbcType=TIMESTAMP}, #{item.isActive,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.batchClaimBillNo,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_task_trigger (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'task_type'.toString() == column.value">
          #{item.taskType,jdbcType=INTEGER}
        </if>
        <if test="'task_name'.toString() == column.value">
          #{item.taskName,jdbcType=VARCHAR}
        </if>
        <if test="'trigger_date_plan'.toString() == column.value">
          #{item.triggerDatePlan,jdbcType=TIMESTAMP}
        </if>
        <if test="'data'.toString() == column.value">
          #{item.data,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_trigger'.toString() == column.value">
          #{item.isTrigger,jdbcType=VARCHAR}
        </if>
        <if test="'trigger_date'.toString() == column.value">
          #{item.triggerDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_active'.toString() == column.value">
          #{item.isActive,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'batch_claim_bill_no'.toString() == column.value">
          #{item.batchClaimBillNo,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>