<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.CouponInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.CouponInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="coupon_code" jdbcType="VARCHAR" property="couponCode" />
    <result column="coupon_id" jdbcType="VARCHAR" property="couponId" />
    <result column="coupon_name" jdbcType="VARCHAR" property="couponName" />
    <result column="coupon_amount" jdbcType="DECIMAL" property="couponAmount" />
    <result column="coupon_begin" jdbcType="DATE" property="couponBegin" />
    <result column="coupon_end" jdbcType="DATE" property="couponEnd" />
    <result column="reamrk" jdbcType="VARCHAR" property="reamrk" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_no, report_no, source, coupon_code, coupon_id, coupon_name, coupon_amount, 
    coupon_begin, coupon_end, reamrk, extra_info, creator, modifier, gmt_created, gmt_modified, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.CouponInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from coupon_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from coupon_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.CouponInfoDO">
    insert into coupon_info (id, policy_no, report_no, 
      source, coupon_code, coupon_id, 
      coupon_name, coupon_amount, coupon_begin, 
      coupon_end, reamrk, extra_info, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{source,jdbcType=VARCHAR}, #{couponCode,jdbcType=VARCHAR}, #{couponId,jdbcType=VARCHAR}, 
      #{couponName,jdbcType=VARCHAR}, #{couponAmount,jdbcType=DECIMAL}, #{couponBegin,jdbcType=DATE}, 
      #{couponEnd,jdbcType=DATE}, #{reamrk,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.CouponInfoDO">
    insert into coupon_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="couponCode != null">
        coupon_code,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="couponName != null">
        coupon_name,
      </if>
      <if test="couponAmount != null">
        coupon_amount,
      </if>
      <if test="couponBegin != null">
        coupon_begin,
      </if>
      <if test="couponEnd != null">
        coupon_end,
      </if>
      <if test="reamrk != null">
        reamrk,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="couponCode != null">
        #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="couponName != null">
        #{couponName,jdbcType=VARCHAR},
      </if>
      <if test="couponAmount != null">
        #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponBegin != null">
        #{couponBegin,jdbcType=DATE},
      </if>
      <if test="couponEnd != null">
        #{couponEnd,jdbcType=DATE},
      </if>
      <if test="reamrk != null">
        #{reamrk,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.CouponInfoExample" resultType="java.lang.Long">
    select count(*) from coupon_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update coupon_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.couponCode != null">
        coupon_code = #{record.couponCode,jdbcType=VARCHAR},
      </if>
      <if test="record.couponId != null">
        coupon_id = #{record.couponId,jdbcType=VARCHAR},
      </if>
      <if test="record.couponName != null">
        coupon_name = #{record.couponName,jdbcType=VARCHAR},
      </if>
      <if test="record.couponAmount != null">
        coupon_amount = #{record.couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.couponBegin != null">
        coupon_begin = #{record.couponBegin,jdbcType=DATE},
      </if>
      <if test="record.couponEnd != null">
        coupon_end = #{record.couponEnd,jdbcType=DATE},
      </if>
      <if test="record.reamrk != null">
        reamrk = #{record.reamrk,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update coupon_info
    set id = #{record.id,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      source = #{record.source,jdbcType=VARCHAR},
      coupon_code = #{record.couponCode,jdbcType=VARCHAR},
      coupon_id = #{record.couponId,jdbcType=VARCHAR},
      coupon_name = #{record.couponName,jdbcType=VARCHAR},
      coupon_amount = #{record.couponAmount,jdbcType=DECIMAL},
      coupon_begin = #{record.couponBegin,jdbcType=DATE},
      coupon_end = #{record.couponEnd,jdbcType=DATE},
      reamrk = #{record.reamrk,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.CouponInfoDO">
    update coupon_info
    <set>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="couponCode != null">
        coupon_code = #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="couponName != null">
        coupon_name = #{couponName,jdbcType=VARCHAR},
      </if>
      <if test="couponAmount != null">
        coupon_amount = #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponBegin != null">
        coupon_begin = #{couponBegin,jdbcType=DATE},
      </if>
      <if test="couponEnd != null">
        coupon_end = #{couponEnd,jdbcType=DATE},
      </if>
      <if test="reamrk != null">
        reamrk = #{reamrk,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.CouponInfoDO">
    update coupon_info
    set policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      coupon_code = #{couponCode,jdbcType=VARCHAR},
      coupon_id = #{couponId,jdbcType=VARCHAR},
      coupon_name = #{couponName,jdbcType=VARCHAR},
      coupon_amount = #{couponAmount,jdbcType=DECIMAL},
      coupon_begin = #{couponBegin,jdbcType=DATE},
      coupon_end = #{couponEnd,jdbcType=DATE},
      reamrk = #{reamrk,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into coupon_info
    (id, policy_no, report_no, source, coupon_code, coupon_id, coupon_name, coupon_amount, 
      coupon_begin, coupon_end, reamrk, extra_info, creator, modifier, gmt_created, gmt_modified, 
      is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.source,jdbcType=VARCHAR}, #{item.couponCode,jdbcType=VARCHAR}, #{item.couponId,jdbcType=VARCHAR}, 
        #{item.couponName,jdbcType=VARCHAR}, #{item.couponAmount,jdbcType=DECIMAL}, #{item.couponBegin,jdbcType=DATE}, 
        #{item.couponEnd,jdbcType=DATE}, #{item.reamrk,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into coupon_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=VARCHAR}
        </if>
        <if test="'coupon_code'.toString() == column.value">
          #{item.couponCode,jdbcType=VARCHAR}
        </if>
        <if test="'coupon_id'.toString() == column.value">
          #{item.couponId,jdbcType=VARCHAR}
        </if>
        <if test="'coupon_name'.toString() == column.value">
          #{item.couponName,jdbcType=VARCHAR}
        </if>
        <if test="'coupon_amount'.toString() == column.value">
          #{item.couponAmount,jdbcType=DECIMAL}
        </if>
        <if test="'coupon_begin'.toString() == column.value">
          #{item.couponBegin,jdbcType=DATE}
        </if>
        <if test="'coupon_end'.toString() == column.value">
          #{item.couponEnd,jdbcType=DATE}
        </if>
        <if test="'reamrk'.toString() == column.value">
          #{item.reamrk,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>