<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ThirdPartyLiabilityLossMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityLossDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_certificate_type" jdbcType="VARCHAR" property="userCertificateType" />
    <result column="user_certi_no" jdbcType="VARCHAR" property="userCertiNo" />
    <result column="duty_type" jdbcType="VARCHAR" property="dutyType" />
    <result column="loss_amount" jdbcType="VARCHAR" property="lossAmount" />
    <result column="paid_amount" jdbcType="VARCHAR" property="paidAmount" />
    <result column="paid_ratio" jdbcType="VARCHAR" property="paidRatio" />
    <result column="item_specification" jdbcType="VARCHAR" property="itemSpecification" />
    <result column="item_type" jdbcType="VARCHAR" property="itemType" />
    <result column="coverage_type" jdbcType="VARCHAR" property="coverageType" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="user_phone" jdbcType="VARCHAR" property="userPhone" />
    <result column="user_sex" jdbcType="VARCHAR" property="userSex" />
    <result column="user_age" jdbcType="TINYINT" property="userAge" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="plate_number" jdbcType="VARCHAR" property="plateNumber" />
    <result column="repair_factory" jdbcType="VARCHAR" property="repairFactory" />
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="loss_assessment_status" jdbcType="TINYINT" property="lossAssessmentStatus" />
    <result column="loss_assessment_timeliness" jdbcType="VARCHAR" property="lossAssessmentTimeliness" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_name, user_certificate_type, user_certi_no, duty_type, loss_amount, paid_amount, 
    paid_ratio, item_specification, item_type, coverage_type, report_no, user_phone, 
    user_sex, user_age, remark, plate_number, repair_factory, invoice_no, loss_assessment_status, 
    loss_assessment_timeliness, creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityLossExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_claim_insurance_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_claim_insurance_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityLossDO">
    insert into cargo_claim_insurance_info (id, user_name, user_certificate_type, 
      user_certi_no, duty_type, loss_amount, 
      paid_amount, paid_ratio, item_specification, 
      item_type, coverage_type, report_no, 
      user_phone, user_sex, user_age, 
      remark, plate_number, repair_factory, 
      invoice_no, loss_assessment_status, loss_assessment_timeliness, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR}, #{userCertificateType,jdbcType=VARCHAR}, 
      #{userCertiNo,jdbcType=VARCHAR}, #{dutyType,jdbcType=VARCHAR}, #{lossAmount,jdbcType=VARCHAR}, 
      #{paidAmount,jdbcType=VARCHAR}, #{paidRatio,jdbcType=VARCHAR}, #{itemSpecification,jdbcType=VARCHAR}, 
      #{itemType,jdbcType=VARCHAR}, #{coverageType,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{userPhone,jdbcType=VARCHAR}, #{userSex,jdbcType=VARCHAR}, #{userAge,jdbcType=TINYINT}, 
      #{remark,jdbcType=VARCHAR}, #{plateNumber,jdbcType=VARCHAR}, #{repairFactory,jdbcType=VARCHAR}, 
      #{invoiceNo,jdbcType=VARCHAR}, #{lossAssessmentStatus,jdbcType=TINYINT}, #{lossAssessmentTimeliness,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityLossDO">
    insert into cargo_claim_insurance_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="userCertificateType != null">
        user_certificate_type,
      </if>
      <if test="userCertiNo != null">
        user_certi_no,
      </if>
      <if test="dutyType != null">
        duty_type,
      </if>
      <if test="lossAmount != null">
        loss_amount,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="paidRatio != null">
        paid_ratio,
      </if>
      <if test="itemSpecification != null">
        item_specification,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="coverageType != null">
        coverage_type,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="userPhone != null">
        user_phone,
      </if>
      <if test="userSex != null">
        user_sex,
      </if>
      <if test="userAge != null">
        user_age,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="plateNumber != null">
        plate_number,
      </if>
      <if test="repairFactory != null">
        repair_factory,
      </if>
      <if test="invoiceNo != null">
        invoice_no,
      </if>
      <if test="lossAssessmentStatus != null">
        loss_assessment_status,
      </if>
      <if test="lossAssessmentTimeliness != null">
        loss_assessment_timeliness,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userCertificateType != null">
        #{userCertificateType,jdbcType=VARCHAR},
      </if>
      <if test="userCertiNo != null">
        #{userCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="dutyType != null">
        #{dutyType,jdbcType=VARCHAR},
      </if>
      <if test="lossAmount != null">
        #{lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="paidRatio != null">
        #{paidRatio,jdbcType=VARCHAR},
      </if>
      <if test="itemSpecification != null">
        #{itemSpecification,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=VARCHAR},
      </if>
      <if test="coverageType != null">
        #{coverageType,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="userSex != null">
        #{userSex,jdbcType=VARCHAR},
      </if>
      <if test="userAge != null">
        #{userAge,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="plateNumber != null">
        #{plateNumber,jdbcType=VARCHAR},
      </if>
      <if test="repairFactory != null">
        #{repairFactory,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="lossAssessmentStatus != null">
        #{lossAssessmentStatus,jdbcType=TINYINT},
      </if>
      <if test="lossAssessmentTimeliness != null">
        #{lossAssessmentTimeliness,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityLossExample" resultType="java.lang.Long">
    select count(*) from cargo_claim_insurance_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_claim_insurance_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.userCertificateType != null">
        user_certificate_type = #{record.userCertificateType,jdbcType=VARCHAR},
      </if>
      <if test="record.userCertiNo != null">
        user_certi_no = #{record.userCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="record.dutyType != null">
        duty_type = #{record.dutyType,jdbcType=VARCHAR},
      </if>
      <if test="record.lossAmount != null">
        loss_amount = #{record.lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.paidRatio != null">
        paid_ratio = #{record.paidRatio,jdbcType=VARCHAR},
      </if>
      <if test="record.itemSpecification != null">
        item_specification = #{record.itemSpecification,jdbcType=VARCHAR},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=VARCHAR},
      </if>
      <if test="record.coverageType != null">
        coverage_type = #{record.coverageType,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.userPhone != null">
        user_phone = #{record.userPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.userSex != null">
        user_sex = #{record.userSex,jdbcType=VARCHAR},
      </if>
      <if test="record.userAge != null">
        user_age = #{record.userAge,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.plateNumber != null">
        plate_number = #{record.plateNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.repairFactory != null">
        repair_factory = #{record.repairFactory,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceNo != null">
        invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.lossAssessmentStatus != null">
        loss_assessment_status = #{record.lossAssessmentStatus,jdbcType=TINYINT},
      </if>
      <if test="record.lossAssessmentTimeliness != null">
        loss_assessment_timeliness = #{record.lossAssessmentTimeliness,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_claim_insurance_info
    set id = #{record.id,jdbcType=BIGINT},
      user_name = #{record.userName,jdbcType=VARCHAR},
      user_certificate_type = #{record.userCertificateType,jdbcType=VARCHAR},
      user_certi_no = #{record.userCertiNo,jdbcType=VARCHAR},
      duty_type = #{record.dutyType,jdbcType=VARCHAR},
      loss_amount = #{record.lossAmount,jdbcType=VARCHAR},
      paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      paid_ratio = #{record.paidRatio,jdbcType=VARCHAR},
      item_specification = #{record.itemSpecification,jdbcType=VARCHAR},
      item_type = #{record.itemType,jdbcType=VARCHAR},
      coverage_type = #{record.coverageType,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      user_phone = #{record.userPhone,jdbcType=VARCHAR},
      user_sex = #{record.userSex,jdbcType=VARCHAR},
      user_age = #{record.userAge,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      plate_number = #{record.plateNumber,jdbcType=VARCHAR},
      repair_factory = #{record.repairFactory,jdbcType=VARCHAR},
      invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      loss_assessment_status = #{record.lossAssessmentStatus,jdbcType=TINYINT},
      loss_assessment_timeliness = #{record.lossAssessmentTimeliness,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityLossDO">
    update cargo_claim_insurance_info
    <set>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userCertificateType != null">
        user_certificate_type = #{userCertificateType,jdbcType=VARCHAR},
      </if>
      <if test="userCertiNo != null">
        user_certi_no = #{userCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="dutyType != null">
        duty_type = #{dutyType,jdbcType=VARCHAR},
      </if>
      <if test="lossAmount != null">
        loss_amount = #{lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="paidRatio != null">
        paid_ratio = #{paidRatio,jdbcType=VARCHAR},
      </if>
      <if test="itemSpecification != null">
        item_specification = #{itemSpecification,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=VARCHAR},
      </if>
      <if test="coverageType != null">
        coverage_type = #{coverageType,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        user_phone = #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="userSex != null">
        user_sex = #{userSex,jdbcType=VARCHAR},
      </if>
      <if test="userAge != null">
        user_age = #{userAge,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="plateNumber != null">
        plate_number = #{plateNumber,jdbcType=VARCHAR},
      </if>
      <if test="repairFactory != null">
        repair_factory = #{repairFactory,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="lossAssessmentStatus != null">
        loss_assessment_status = #{lossAssessmentStatus,jdbcType=TINYINT},
      </if>
      <if test="lossAssessmentTimeliness != null">
        loss_assessment_timeliness = #{lossAssessmentTimeliness,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityLossDO">
    update cargo_claim_insurance_info
    set user_name = #{userName,jdbcType=VARCHAR},
      user_certificate_type = #{userCertificateType,jdbcType=VARCHAR},
      user_certi_no = #{userCertiNo,jdbcType=VARCHAR},
      duty_type = #{dutyType,jdbcType=VARCHAR},
      loss_amount = #{lossAmount,jdbcType=VARCHAR},
      paid_amount = #{paidAmount,jdbcType=VARCHAR},
      paid_ratio = #{paidRatio,jdbcType=VARCHAR},
      item_specification = #{itemSpecification,jdbcType=VARCHAR},
      item_type = #{itemType,jdbcType=VARCHAR},
      coverage_type = #{coverageType,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      user_phone = #{userPhone,jdbcType=VARCHAR},
      user_sex = #{userSex,jdbcType=VARCHAR},
      user_age = #{userAge,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      plate_number = #{plateNumber,jdbcType=VARCHAR},
      repair_factory = #{repairFactory,jdbcType=VARCHAR},
      invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      loss_assessment_status = #{lossAssessmentStatus,jdbcType=TINYINT},
      loss_assessment_timeliness = #{lossAssessmentTimeliness,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_claim_insurance_info
    (id, user_name, user_certificate_type, user_certi_no, duty_type, loss_amount, paid_amount, 
      paid_ratio, item_specification, item_type, coverage_type, report_no, user_phone, 
      user_sex, user_age, remark, plate_number, repair_factory, invoice_no, loss_assessment_status, 
      loss_assessment_timeliness, creator, modifier, gmt_created, gmt_modified, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.userName,jdbcType=VARCHAR}, #{item.userCertificateType,jdbcType=VARCHAR}, 
        #{item.userCertiNo,jdbcType=VARCHAR}, #{item.dutyType,jdbcType=VARCHAR}, #{item.lossAmount,jdbcType=VARCHAR}, 
        #{item.paidAmount,jdbcType=VARCHAR}, #{item.paidRatio,jdbcType=VARCHAR}, #{item.itemSpecification,jdbcType=VARCHAR}, 
        #{item.itemType,jdbcType=VARCHAR}, #{item.coverageType,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.userPhone,jdbcType=VARCHAR}, #{item.userSex,jdbcType=VARCHAR}, #{item.userAge,jdbcType=TINYINT}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.plateNumber,jdbcType=VARCHAR}, #{item.repairFactory,jdbcType=VARCHAR}, 
        #{item.invoiceNo,jdbcType=VARCHAR}, #{item.lossAssessmentStatus,jdbcType=TINYINT}, 
        #{item.lossAssessmentTimeliness,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_claim_insurance_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'user_name'.toString() == column.value">
          #{item.userName,jdbcType=VARCHAR}
        </if>
        <if test="'user_certificate_type'.toString() == column.value">
          #{item.userCertificateType,jdbcType=VARCHAR}
        </if>
        <if test="'user_certi_no'.toString() == column.value">
          #{item.userCertiNo,jdbcType=VARCHAR}
        </if>
        <if test="'duty_type'.toString() == column.value">
          #{item.dutyType,jdbcType=VARCHAR}
        </if>
        <if test="'loss_amount'.toString() == column.value">
          #{item.lossAmount,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount'.toString() == column.value">
          #{item.paidAmount,jdbcType=VARCHAR}
        </if>
        <if test="'paid_ratio'.toString() == column.value">
          #{item.paidRatio,jdbcType=VARCHAR}
        </if>
        <if test="'item_specification'.toString() == column.value">
          #{item.itemSpecification,jdbcType=VARCHAR}
        </if>
        <if test="'item_type'.toString() == column.value">
          #{item.itemType,jdbcType=VARCHAR}
        </if>
        <if test="'coverage_type'.toString() == column.value">
          #{item.coverageType,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'user_phone'.toString() == column.value">
          #{item.userPhone,jdbcType=VARCHAR}
        </if>
        <if test="'user_sex'.toString() == column.value">
          #{item.userSex,jdbcType=VARCHAR}
        </if>
        <if test="'user_age'.toString() == column.value">
          #{item.userAge,jdbcType=TINYINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'plate_number'.toString() == column.value">
          #{item.plateNumber,jdbcType=VARCHAR}
        </if>
        <if test="'repair_factory'.toString() == column.value">
          #{item.repairFactory,jdbcType=VARCHAR}
        </if>
        <if test="'invoice_no'.toString() == column.value">
          #{item.invoiceNo,jdbcType=VARCHAR}
        </if>
        <if test="'loss_assessment_status'.toString() == column.value">
          #{item.lossAssessmentStatus,jdbcType=TINYINT}
        </if>
        <if test="'loss_assessment_timeliness'.toString() == column.value">
          #{item.lossAssessmentTimeliness,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>