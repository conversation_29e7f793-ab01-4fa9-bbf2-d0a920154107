<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.PetClaimAssistantRecordMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.PetClaimAssistantRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="apply_source" jdbcType="VARCHAR" property="applySource" />
    <result column="apply_page" jdbcType="VARCHAR" property="applyPage" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="policy_id" jdbcType="BIGINT" property="policyId" />
    <result column="channel_policy_no" jdbcType="VARCHAR" property="channelPolicyNo" />
    <result column="cust_name" jdbcType="VARCHAR" property="custName" />
    <result column="cust_mobile" jdbcType="VARCHAR" property="custMobile" />
    <result column="pet_name" jdbcType="VARCHAR" property="petName" />
    <result column="visit_time" jdbcType="TIMESTAMP" property="visitTime" />
    <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
    <result column="visit_hospital_id" jdbcType="VARCHAR" property="visitHospitalId" />
    <result column="visit_hospital_name" jdbcType="VARCHAR" property="visitHospitalName" />
    <result column="disease_diagnosis" jdbcType="VARCHAR" property="diseaseDiagnosis" />
    <result column="invoice_amount" jdbcType="DECIMAL" property="invoiceAmount" />
    <result column="cancel_source" jdbcType="VARCHAR" property="cancelSource" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="upload_time" jdbcType="TIMESTAMP" property="uploadTime" />
    <result column="report_time" jdbcType="TIMESTAMP" property="reportTime" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, apply_no, apply_source, apply_page, apply_time, status, policy_no, policy_id, 
    channel_policy_no, cust_name, cust_mobile, pet_name, visit_time, visit_type, visit_hospital_id, 
    visit_hospital_name, disease_diagnosis, invoice_amount, cancel_source, cancel_time, 
    upload_time, report_time, report_no, extra_info, creator, modifier, gmt_created, 
    gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.PetClaimAssistantRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pet_claim_assistant_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pet_claim_assistant_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.PetClaimAssistantRecordDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pet_claim_assistant_record (apply_no, apply_source, apply_page, 
      apply_time, status, policy_no, 
      policy_id, channel_policy_no, cust_name, 
      cust_mobile, pet_name, visit_time, 
      visit_type, visit_hospital_id, visit_hospital_name, 
      disease_diagnosis, invoice_amount, cancel_source, 
      cancel_time, upload_time, report_time, 
      report_no, extra_info, creator, 
      modifier, gmt_created, gmt_modified, 
      is_deleted)
    values (#{applyNo,jdbcType=VARCHAR}, #{applySource,jdbcType=VARCHAR}, #{applyPage,jdbcType=VARCHAR}, 
      #{applyTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{policyNo,jdbcType=VARCHAR}, 
      #{policyId,jdbcType=BIGINT}, #{channelPolicyNo,jdbcType=VARCHAR}, #{custName,jdbcType=VARCHAR}, 
      #{custMobile,jdbcType=VARCHAR}, #{petName,jdbcType=VARCHAR}, #{visitTime,jdbcType=TIMESTAMP}, 
      #{visitType,jdbcType=VARCHAR}, #{visitHospitalId,jdbcType=VARCHAR}, #{visitHospitalName,jdbcType=VARCHAR}, 
      #{diseaseDiagnosis,jdbcType=VARCHAR}, #{invoiceAmount,jdbcType=DECIMAL}, #{cancelSource,jdbcType=VARCHAR}, 
      #{cancelTime,jdbcType=TIMESTAMP}, #{uploadTime,jdbcType=TIMESTAMP}, #{reportTime,jdbcType=TIMESTAMP}, 
      #{reportNo,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate(), 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.PetClaimAssistantRecordDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pet_claim_assistant_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        apply_no,
      </if>
      <if test="applySource != null">
        apply_source,
      </if>
      <if test="applyPage != null">
        apply_page,
      </if>
      <if test="applyTime != null">
        apply_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="policyId != null">
        policy_id,
      </if>
      <if test="channelPolicyNo != null">
        channel_policy_no,
      </if>
      <if test="custName != null">
        cust_name,
      </if>
      <if test="custMobile != null">
        cust_mobile,
      </if>
      <if test="petName != null">
        pet_name,
      </if>
      <if test="visitTime != null">
        visit_time,
      </if>
      <if test="visitType != null">
        visit_type,
      </if>
      <if test="visitHospitalId != null">
        visit_hospital_id,
      </if>
      <if test="visitHospitalName != null">
        visit_hospital_name,
      </if>
      <if test="diseaseDiagnosis != null">
        disease_diagnosis,
      </if>
      <if test="invoiceAmount != null">
        invoice_amount,
      </if>
      <if test="cancelSource != null">
        cancel_source,
      </if>
      <if test="cancelTime != null">
        cancel_time,
      </if>
      <if test="uploadTime != null">
        upload_time,
      </if>
      <if test="reportTime != null">
        report_time,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyNo != null">
        #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="applySource != null">
        #{applySource,jdbcType=VARCHAR},
      </if>
      <if test="applyPage != null">
        #{applyPage,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        #{policyId,jdbcType=BIGINT},
      </if>
      <if test="channelPolicyNo != null">
        #{channelPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        #{custName,jdbcType=VARCHAR},
      </if>
      <if test="custMobile != null">
        #{custMobile,jdbcType=VARCHAR},
      </if>
      <if test="petName != null">
        #{petName,jdbcType=VARCHAR},
      </if>
      <if test="visitTime != null">
        #{visitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="visitType != null">
        #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="visitHospitalId != null">
        #{visitHospitalId,jdbcType=VARCHAR},
      </if>
      <if test="visitHospitalName != null">
        #{visitHospitalName,jdbcType=VARCHAR},
      </if>
      <if test="diseaseDiagnosis != null">
        #{diseaseDiagnosis,jdbcType=VARCHAR},
      </if>
      <if test="invoiceAmount != null">
        #{invoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="cancelSource != null">
        #{cancelSource,jdbcType=VARCHAR},
      </if>
      <if test="cancelTime != null">
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="uploadTime != null">
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportTime != null">
        #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.PetClaimAssistantRecordExample" resultType="java.lang.Long">
    select count(*) from pet_claim_assistant_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pet_claim_assistant_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.applyNo != null">
        apply_no = #{record.applyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.applySource != null">
        apply_source = #{record.applySource,jdbcType=VARCHAR},
      </if>
      <if test="record.applyPage != null">
        apply_page = #{record.applyPage,jdbcType=VARCHAR},
      </if>
      <if test="record.applyTime != null">
        apply_time = #{record.applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyId != null">
        policy_id = #{record.policyId,jdbcType=BIGINT},
      </if>
      <if test="record.channelPolicyNo != null">
        channel_policy_no = #{record.channelPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.custName != null">
        cust_name = #{record.custName,jdbcType=VARCHAR},
      </if>
      <if test="record.custMobile != null">
        cust_mobile = #{record.custMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.petName != null">
        pet_name = #{record.petName,jdbcType=VARCHAR},
      </if>
      <if test="record.visitTime != null">
        visit_time = #{record.visitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.visitType != null">
        visit_type = #{record.visitType,jdbcType=VARCHAR},
      </if>
      <if test="record.visitHospitalId != null">
        visit_hospital_id = #{record.visitHospitalId,jdbcType=VARCHAR},
      </if>
      <if test="record.visitHospitalName != null">
        visit_hospital_name = #{record.visitHospitalName,jdbcType=VARCHAR},
      </if>
      <if test="record.diseaseDiagnosis != null">
        disease_diagnosis = #{record.diseaseDiagnosis,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceAmount != null">
        invoice_amount = #{record.invoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.cancelSource != null">
        cancel_source = #{record.cancelSource,jdbcType=VARCHAR},
      </if>
      <if test="record.cancelTime != null">
        cancel_time = #{record.cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.uploadTime != null">
        upload_time = #{record.uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportTime != null">
        report_time = #{record.reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pet_claim_assistant_record
    set id = #{record.id,jdbcType=BIGINT},
      apply_no = #{record.applyNo,jdbcType=VARCHAR},
      apply_source = #{record.applySource,jdbcType=VARCHAR},
      apply_page = #{record.applyPage,jdbcType=VARCHAR},
      apply_time = #{record.applyTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=INTEGER},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      policy_id = #{record.policyId,jdbcType=BIGINT},
      channel_policy_no = #{record.channelPolicyNo,jdbcType=VARCHAR},
      cust_name = #{record.custName,jdbcType=VARCHAR},
      cust_mobile = #{record.custMobile,jdbcType=VARCHAR},
      pet_name = #{record.petName,jdbcType=VARCHAR},
      visit_time = #{record.visitTime,jdbcType=TIMESTAMP},
      visit_type = #{record.visitType,jdbcType=VARCHAR},
      visit_hospital_id = #{record.visitHospitalId,jdbcType=VARCHAR},
      visit_hospital_name = #{record.visitHospitalName,jdbcType=VARCHAR},
      disease_diagnosis = #{record.diseaseDiagnosis,jdbcType=VARCHAR},
      invoice_amount = #{record.invoiceAmount,jdbcType=DECIMAL},
      cancel_source = #{record.cancelSource,jdbcType=VARCHAR},
      cancel_time = #{record.cancelTime,jdbcType=TIMESTAMP},
      upload_time = #{record.uploadTime,jdbcType=TIMESTAMP},
      report_time = #{record.reportTime,jdbcType=TIMESTAMP},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.PetClaimAssistantRecordDO">
    update pet_claim_assistant_record
    <set>
      <if test="applyNo != null">
        apply_no = #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="applySource != null">
        apply_source = #{applySource,jdbcType=VARCHAR},
      </if>
      <if test="applyPage != null">
        apply_page = #{applyPage,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        policy_id = #{policyId,jdbcType=BIGINT},
      </if>
      <if test="channelPolicyNo != null">
        channel_policy_no = #{channelPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        cust_name = #{custName,jdbcType=VARCHAR},
      </if>
      <if test="custMobile != null">
        cust_mobile = #{custMobile,jdbcType=VARCHAR},
      </if>
      <if test="petName != null">
        pet_name = #{petName,jdbcType=VARCHAR},
      </if>
      <if test="visitTime != null">
        visit_time = #{visitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="visitType != null">
        visit_type = #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="visitHospitalId != null">
        visit_hospital_id = #{visitHospitalId,jdbcType=VARCHAR},
      </if>
      <if test="visitHospitalName != null">
        visit_hospital_name = #{visitHospitalName,jdbcType=VARCHAR},
      </if>
      <if test="diseaseDiagnosis != null">
        disease_diagnosis = #{diseaseDiagnosis,jdbcType=VARCHAR},
      </if>
      <if test="invoiceAmount != null">
        invoice_amount = #{invoiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="cancelSource != null">
        cancel_source = #{cancelSource,jdbcType=VARCHAR},
      </if>
      <if test="cancelTime != null">
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="uploadTime != null">
        upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportTime != null">
        report_time = #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.PetClaimAssistantRecordDO">
    update pet_claim_assistant_record
    set apply_no = #{applyNo,jdbcType=VARCHAR},
      apply_source = #{applySource,jdbcType=VARCHAR},
      apply_page = #{applyPage,jdbcType=VARCHAR},
      apply_time = #{applyTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      policy_id = #{policyId,jdbcType=BIGINT},
      channel_policy_no = #{channelPolicyNo,jdbcType=VARCHAR},
      cust_name = #{custName,jdbcType=VARCHAR},
      cust_mobile = #{custMobile,jdbcType=VARCHAR},
      pet_name = #{petName,jdbcType=VARCHAR},
      visit_time = #{visitTime,jdbcType=TIMESTAMP},
      visit_type = #{visitType,jdbcType=VARCHAR},
      visit_hospital_id = #{visitHospitalId,jdbcType=VARCHAR},
      visit_hospital_name = #{visitHospitalName,jdbcType=VARCHAR},
      disease_diagnosis = #{diseaseDiagnosis,jdbcType=VARCHAR},
      invoice_amount = #{invoiceAmount,jdbcType=DECIMAL},
      cancel_source = #{cancelSource,jdbcType=VARCHAR},
      cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      report_time = #{reportTime,jdbcType=TIMESTAMP},
      report_no = #{reportNo,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into pet_claim_assistant_record
    (apply_no, apply_source, apply_page, apply_time, status, policy_no, policy_id, channel_policy_no, 
      cust_name, cust_mobile, pet_name, visit_time, visit_type, visit_hospital_id, visit_hospital_name, 
      disease_diagnosis, invoice_amount, cancel_source, cancel_time, upload_time, report_time, 
      report_no, extra_info, creator, modifier, gmt_created, gmt_modified, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.applyNo,jdbcType=VARCHAR}, #{item.applySource,jdbcType=VARCHAR}, #{item.applyPage,jdbcType=VARCHAR}, 
        #{item.applyTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=INTEGER}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.policyId,jdbcType=BIGINT}, #{item.channelPolicyNo,jdbcType=VARCHAR}, #{item.custName,jdbcType=VARCHAR}, 
        #{item.custMobile,jdbcType=VARCHAR}, #{item.petName,jdbcType=VARCHAR}, #{item.visitTime,jdbcType=TIMESTAMP}, 
        #{item.visitType,jdbcType=VARCHAR}, #{item.visitHospitalId,jdbcType=VARCHAR}, #{item.visitHospitalName,jdbcType=VARCHAR}, 
        #{item.diseaseDiagnosis,jdbcType=VARCHAR}, #{item.invoiceAmount,jdbcType=DECIMAL}, 
        #{item.cancelSource,jdbcType=VARCHAR}, #{item.cancelTime,jdbcType=TIMESTAMP}, #{item.uploadTime,jdbcType=TIMESTAMP}, 
        #{item.reportTime,jdbcType=TIMESTAMP}, #{item.reportNo,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into pet_claim_assistant_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'apply_no'.toString() == column.value">
          #{item.applyNo,jdbcType=VARCHAR}
        </if>
        <if test="'apply_source'.toString() == column.value">
          #{item.applySource,jdbcType=VARCHAR}
        </if>
        <if test="'apply_page'.toString() == column.value">
          #{item.applyPage,jdbcType=VARCHAR}
        </if>
        <if test="'apply_time'.toString() == column.value">
          #{item.applyTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_id'.toString() == column.value">
          #{item.policyId,jdbcType=BIGINT}
        </if>
        <if test="'channel_policy_no'.toString() == column.value">
          #{item.channelPolicyNo,jdbcType=VARCHAR}
        </if>
        <if test="'cust_name'.toString() == column.value">
          #{item.custName,jdbcType=VARCHAR}
        </if>
        <if test="'cust_mobile'.toString() == column.value">
          #{item.custMobile,jdbcType=VARCHAR}
        </if>
        <if test="'pet_name'.toString() == column.value">
          #{item.petName,jdbcType=VARCHAR}
        </if>
        <if test="'visit_time'.toString() == column.value">
          #{item.visitTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'visit_type'.toString() == column.value">
          #{item.visitType,jdbcType=VARCHAR}
        </if>
        <if test="'visit_hospital_id'.toString() == column.value">
          #{item.visitHospitalId,jdbcType=VARCHAR}
        </if>
        <if test="'visit_hospital_name'.toString() == column.value">
          #{item.visitHospitalName,jdbcType=VARCHAR}
        </if>
        <if test="'disease_diagnosis'.toString() == column.value">
          #{item.diseaseDiagnosis,jdbcType=VARCHAR}
        </if>
        <if test="'invoice_amount'.toString() == column.value">
          #{item.invoiceAmount,jdbcType=DECIMAL}
        </if>
        <if test="'cancel_source'.toString() == column.value">
          #{item.cancelSource,jdbcType=VARCHAR}
        </if>
        <if test="'cancel_time'.toString() == column.value">
          #{item.cancelTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'upload_time'.toString() == column.value">
          #{item.uploadTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'report_time'.toString() == column.value">
          #{item.reportTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>