<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.DeliveryClaimMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.DeliveryClaimDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="paid_amount" jdbcType="DECIMAL" property="paidAmount" />
    <result column="accident_reason" jdbcType="VARCHAR" property="accidentReason" />
    <result column="cus_id" jdbcType="VARCHAR" property="cusId" />
    <result column="cus_name" jdbcType="VARCHAR" property="cusName" />
    <result column="cus_cert_no" jdbcType="VARCHAR" property="cusCertNo" />
    <result column="cus_cert_type" jdbcType="TINYINT" property="cusCertType" />
    <result column="cus_phone" jdbcType="VARCHAR" property="cusPhone" />
    <result column="cus_account_no" jdbcType="VARCHAR" property="cusAccountNo" />
    <result column="shop_id" jdbcType="VARCHAR" property="shopId" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="accident_addr_detail" jdbcType="VARCHAR" property="accidentAddrDetail" />
    <result column="order_detail" jdbcType="VARCHAR" property="orderDetail" />
    <result column="attachment_detail" jdbcType="VARCHAR" property="attachmentDetail" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="cus_account_id" jdbcType="VARCHAR" property="cusAccountId" />
    <result column="out_request_no" jdbcType="VARCHAR" property="outRequestNo" />
    <result column="cus_cert_real_name" jdbcType="VARCHAR" property="cusCertRealName" />
    <result column="pay_state" jdbcType="TINYINT" property="payState" />
    <result column="addr" jdbcType="VARCHAR" property="addr" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_no, report_no, paid_amount, accident_reason, cus_id, cus_name, cus_cert_no, 
    cus_cert_type, cus_phone, cus_account_no, shop_id, shop_name, accident_addr_detail, 
    order_detail, attachment_detail, remark, extra_info, creator, modifier, gmt_created, 
    gmt_modified, is_deleted, order_no, cus_account_id, out_request_no, cus_cert_real_name, 
    pay_state, addr
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.DeliveryClaimExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from delivery_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from delivery_claim
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.DeliveryClaimDO">
    insert into delivery_claim (id, policy_no, report_no, 
      paid_amount, accident_reason, cus_id, 
      cus_name, cus_cert_no, cus_cert_type, 
      cus_phone, cus_account_no, shop_id, 
      shop_name, accident_addr_detail, order_detail, 
      attachment_detail, remark, extra_info, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted, order_no, 
      cus_account_id, out_request_no, cus_cert_real_name, 
      pay_state, addr)
    values (#{id,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{paidAmount,jdbcType=DECIMAL}, #{accidentReason,jdbcType=VARCHAR}, #{cusId,jdbcType=VARCHAR}, 
      #{cusName,jdbcType=VARCHAR}, #{cusCertNo,jdbcType=VARCHAR}, #{cusCertType,jdbcType=TINYINT}, 
      #{cusPhone,jdbcType=VARCHAR}, #{cusAccountNo,jdbcType=VARCHAR}, #{shopId,jdbcType=VARCHAR}, 
      #{shopName,jdbcType=VARCHAR}, #{accidentAddrDetail,jdbcType=VARCHAR}, #{orderDetail,jdbcType=VARCHAR}, 
      #{attachmentDetail,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR}, #{orderNo,jdbcType=VARCHAR}, 
      #{cusAccountId,jdbcType=VARCHAR}, #{outRequestNo,jdbcType=VARCHAR}, #{cusCertRealName,jdbcType=VARCHAR}, 
      #{payState,jdbcType=TINYINT}, #{addr,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.DeliveryClaimDO">
    insert into delivery_claim
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="accidentReason != null">
        accident_reason,
      </if>
      <if test="cusId != null">
        cus_id,
      </if>
      <if test="cusName != null">
        cus_name,
      </if>
      <if test="cusCertNo != null">
        cus_cert_no,
      </if>
      <if test="cusCertType != null">
        cus_cert_type,
      </if>
      <if test="cusPhone != null">
        cus_phone,
      </if>
      <if test="cusAccountNo != null">
        cus_account_no,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="shopName != null">
        shop_name,
      </if>
      <if test="accidentAddrDetail != null">
        accident_addr_detail,
      </if>
      <if test="orderDetail != null">
        order_detail,
      </if>
      <if test="attachmentDetail != null">
        attachment_detail,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="cusAccountId != null">
        cus_account_id,
      </if>
      <if test="outRequestNo != null">
        out_request_no,
      </if>
      <if test="cusCertRealName != null">
        cus_cert_real_name,
      </if>
      <if test="payState != null">
        pay_state,
      </if>
      <if test="addr != null">
        addr,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accidentReason != null">
        #{accidentReason,jdbcType=VARCHAR},
      </if>
      <if test="cusId != null">
        #{cusId,jdbcType=VARCHAR},
      </if>
      <if test="cusName != null">
        #{cusName,jdbcType=VARCHAR},
      </if>
      <if test="cusCertNo != null">
        #{cusCertNo,jdbcType=VARCHAR},
      </if>
      <if test="cusCertType != null">
        #{cusCertType,jdbcType=TINYINT},
      </if>
      <if test="cusPhone != null">
        #{cusPhone,jdbcType=VARCHAR},
      </if>
      <if test="cusAccountNo != null">
        #{cusAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="accidentAddrDetail != null">
        #{accidentAddrDetail,jdbcType=VARCHAR},
      </if>
      <if test="orderDetail != null">
        #{orderDetail,jdbcType=VARCHAR},
      </if>
      <if test="attachmentDetail != null">
        #{attachmentDetail,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="cusAccountId != null">
        #{cusAccountId,jdbcType=VARCHAR},
      </if>
      <if test="outRequestNo != null">
        #{outRequestNo,jdbcType=VARCHAR},
      </if>
      <if test="cusCertRealName != null">
        #{cusCertRealName,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        #{payState,jdbcType=TINYINT},
      </if>
      <if test="addr != null">
        #{addr,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.DeliveryClaimExample" resultType="java.lang.Long">
    select count(*) from delivery_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update delivery_claim
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.accidentReason != null">
        accident_reason = #{record.accidentReason,jdbcType=VARCHAR},
      </if>
      <if test="record.cusId != null">
        cus_id = #{record.cusId,jdbcType=VARCHAR},
      </if>
      <if test="record.cusName != null">
        cus_name = #{record.cusName,jdbcType=VARCHAR},
      </if>
      <if test="record.cusCertNo != null">
        cus_cert_no = #{record.cusCertNo,jdbcType=VARCHAR},
      </if>
      <if test="record.cusCertType != null">
        cus_cert_type = #{record.cusCertType,jdbcType=TINYINT},
      </if>
      <if test="record.cusPhone != null">
        cus_phone = #{record.cusPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.cusAccountNo != null">
        cus_account_no = #{record.cusAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=VARCHAR},
      </if>
      <if test="record.shopName != null">
        shop_name = #{record.shopName,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentAddrDetail != null">
        accident_addr_detail = #{record.accidentAddrDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.orderDetail != null">
        order_detail = #{record.orderDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentDetail != null">
        attachment_detail = #{record.attachmentDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.cusAccountId != null">
        cus_account_id = #{record.cusAccountId,jdbcType=VARCHAR},
      </if>
      <if test="record.outRequestNo != null">
        out_request_no = #{record.outRequestNo,jdbcType=VARCHAR},
      </if>
      <if test="record.cusCertRealName != null">
        cus_cert_real_name = #{record.cusCertRealName,jdbcType=VARCHAR},
      </if>
      <if test="record.payState != null">
        pay_state = #{record.payState,jdbcType=TINYINT},
      </if>
      <if test="record.addr != null">
        addr = #{record.addr,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update delivery_claim
    set id = #{record.id,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      paid_amount = #{record.paidAmount,jdbcType=DECIMAL},
      accident_reason = #{record.accidentReason,jdbcType=VARCHAR},
      cus_id = #{record.cusId,jdbcType=VARCHAR},
      cus_name = #{record.cusName,jdbcType=VARCHAR},
      cus_cert_no = #{record.cusCertNo,jdbcType=VARCHAR},
      cus_cert_type = #{record.cusCertType,jdbcType=TINYINT},
      cus_phone = #{record.cusPhone,jdbcType=VARCHAR},
      cus_account_no = #{record.cusAccountNo,jdbcType=VARCHAR},
      shop_id = #{record.shopId,jdbcType=VARCHAR},
      shop_name = #{record.shopName,jdbcType=VARCHAR},
      accident_addr_detail = #{record.accidentAddrDetail,jdbcType=VARCHAR},
      order_detail = #{record.orderDetail,jdbcType=VARCHAR},
      attachment_detail = #{record.attachmentDetail,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      cus_account_id = #{record.cusAccountId,jdbcType=VARCHAR},
      out_request_no = #{record.outRequestNo,jdbcType=VARCHAR},
      cus_cert_real_name = #{record.cusCertRealName,jdbcType=VARCHAR},
      pay_state = #{record.payState,jdbcType=TINYINT},
      addr = #{record.addr,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.DeliveryClaimDO">
    update delivery_claim
    <set>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accidentReason != null">
        accident_reason = #{accidentReason,jdbcType=VARCHAR},
      </if>
      <if test="cusId != null">
        cus_id = #{cusId,jdbcType=VARCHAR},
      </if>
      <if test="cusName != null">
        cus_name = #{cusName,jdbcType=VARCHAR},
      </if>
      <if test="cusCertNo != null">
        cus_cert_no = #{cusCertNo,jdbcType=VARCHAR},
      </if>
      <if test="cusCertType != null">
        cus_cert_type = #{cusCertType,jdbcType=TINYINT},
      </if>
      <if test="cusPhone != null">
        cus_phone = #{cusPhone,jdbcType=VARCHAR},
      </if>
      <if test="cusAccountNo != null">
        cus_account_no = #{cusAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        shop_name = #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="accidentAddrDetail != null">
        accident_addr_detail = #{accidentAddrDetail,jdbcType=VARCHAR},
      </if>
      <if test="orderDetail != null">
        order_detail = #{orderDetail,jdbcType=VARCHAR},
      </if>
      <if test="attachmentDetail != null">
        attachment_detail = #{attachmentDetail,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="cusAccountId != null">
        cus_account_id = #{cusAccountId,jdbcType=VARCHAR},
      </if>
      <if test="outRequestNo != null">
        out_request_no = #{outRequestNo,jdbcType=VARCHAR},
      </if>
      <if test="cusCertRealName != null">
        cus_cert_real_name = #{cusCertRealName,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        pay_state = #{payState,jdbcType=TINYINT},
      </if>
      <if test="addr != null">
        addr = #{addr,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.DeliveryClaimDO">
    update delivery_claim
    set policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      paid_amount = #{paidAmount,jdbcType=DECIMAL},
      accident_reason = #{accidentReason,jdbcType=VARCHAR},
      cus_id = #{cusId,jdbcType=VARCHAR},
      cus_name = #{cusName,jdbcType=VARCHAR},
      cus_cert_no = #{cusCertNo,jdbcType=VARCHAR},
      cus_cert_type = #{cusCertType,jdbcType=TINYINT},
      cus_phone = #{cusPhone,jdbcType=VARCHAR},
      cus_account_no = #{cusAccountNo,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=VARCHAR},
      shop_name = #{shopName,jdbcType=VARCHAR},
      accident_addr_detail = #{accidentAddrDetail,jdbcType=VARCHAR},
      order_detail = #{orderDetail,jdbcType=VARCHAR},
      attachment_detail = #{attachmentDetail,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      cus_account_id = #{cusAccountId,jdbcType=VARCHAR},
      out_request_no = #{outRequestNo,jdbcType=VARCHAR},
      cus_cert_real_name = #{cusCertRealName,jdbcType=VARCHAR},
      pay_state = #{payState,jdbcType=TINYINT},
      addr = #{addr,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into delivery_claim
    (id, policy_no, report_no, paid_amount, accident_reason, cus_id, cus_name, cus_cert_no, 
      cus_cert_type, cus_phone, cus_account_no, shop_id, shop_name, accident_addr_detail, 
      order_detail, attachment_detail, remark, extra_info, creator, modifier, gmt_created, 
      gmt_modified, is_deleted, order_no, cus_account_id, out_request_no, cus_cert_real_name, 
      pay_state, addr)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.paidAmount,jdbcType=DECIMAL}, #{item.accidentReason,jdbcType=VARCHAR}, #{item.cusId,jdbcType=VARCHAR}, 
        #{item.cusName,jdbcType=VARCHAR}, #{item.cusCertNo,jdbcType=VARCHAR}, #{item.cusCertType,jdbcType=TINYINT}, 
        #{item.cusPhone,jdbcType=VARCHAR}, #{item.cusAccountNo,jdbcType=VARCHAR}, #{item.shopId,jdbcType=VARCHAR}, 
        #{item.shopName,jdbcType=VARCHAR}, #{item.accidentAddrDetail,jdbcType=VARCHAR}, 
        #{item.orderDetail,jdbcType=VARCHAR}, #{item.attachmentDetail,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.orderNo,jdbcType=VARCHAR}, #{item.cusAccountId,jdbcType=VARCHAR}, 
        #{item.outRequestNo,jdbcType=VARCHAR}, #{item.cusCertRealName,jdbcType=VARCHAR}, 
        #{item.payState,jdbcType=TINYINT}, #{item.addr,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into delivery_claim (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount'.toString() == column.value">
          #{item.paidAmount,jdbcType=DECIMAL}
        </if>
        <if test="'accident_reason'.toString() == column.value">
          #{item.accidentReason,jdbcType=VARCHAR}
        </if>
        <if test="'cus_id'.toString() == column.value">
          #{item.cusId,jdbcType=VARCHAR}
        </if>
        <if test="'cus_name'.toString() == column.value">
          #{item.cusName,jdbcType=VARCHAR}
        </if>
        <if test="'cus_cert_no'.toString() == column.value">
          #{item.cusCertNo,jdbcType=VARCHAR}
        </if>
        <if test="'cus_cert_type'.toString() == column.value">
          #{item.cusCertType,jdbcType=TINYINT}
        </if>
        <if test="'cus_phone'.toString() == column.value">
          #{item.cusPhone,jdbcType=VARCHAR}
        </if>
        <if test="'cus_account_no'.toString() == column.value">
          #{item.cusAccountNo,jdbcType=VARCHAR}
        </if>
        <if test="'shop_id'.toString() == column.value">
          #{item.shopId,jdbcType=VARCHAR}
        </if>
        <if test="'shop_name'.toString() == column.value">
          #{item.shopName,jdbcType=VARCHAR}
        </if>
        <if test="'accident_addr_detail'.toString() == column.value">
          #{item.accidentAddrDetail,jdbcType=VARCHAR}
        </if>
        <if test="'order_detail'.toString() == column.value">
          #{item.orderDetail,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_detail'.toString() == column.value">
          #{item.attachmentDetail,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'order_no'.toString() == column.value">
          #{item.orderNo,jdbcType=VARCHAR}
        </if>
        <if test="'cus_account_id'.toString() == column.value">
          #{item.cusAccountId,jdbcType=VARCHAR}
        </if>
        <if test="'out_request_no'.toString() == column.value">
          #{item.outRequestNo,jdbcType=VARCHAR}
        </if>
        <if test="'cus_cert_real_name'.toString() == column.value">
          #{item.cusCertRealName,jdbcType=VARCHAR}
        </if>
        <if test="'pay_state'.toString() == column.value">
          #{item.payState,jdbcType=TINYINT}
        </if>
        <if test="'addr'.toString() == column.value">
          #{item.addr,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>