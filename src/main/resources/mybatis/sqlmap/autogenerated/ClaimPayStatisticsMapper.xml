<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimPayStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimPayStatisticsDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="campaign_def_id" jdbcType="BIGINT" property="campaignDefId" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="paid_closed_count" jdbcType="INTEGER" property="paidClosedCount" />
    <result column="success_pay_count" jdbcType="INTEGER" property="successPayCount" />
    <result column="fail_pay_count" jdbcType="INTEGER" property="failPayCount" />
    <result column="pay_price" jdbcType="DECIMAL" property="payPrice" />
    <result column="stat_type" jdbcType="VARCHAR" property="statType" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, campaign_def_id, package_def_id, paid_closed_count, success_pay_count, fail_pay_count, 
    pay_price, stat_type, start_time, end_time, creator, modifier, gmt_created, gmt_modified, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayStatisticsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_pay_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_pay_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayStatisticsDO">
    insert into claim_pay_statistics (id, campaign_def_id, package_def_id, 
      paid_closed_count, success_pay_count, fail_pay_count, 
      pay_price, stat_type, start_time, 
      end_time, creator, modifier, 
      gmt_created, gmt_modified, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{campaignDefId,jdbcType=BIGINT}, #{packageDefId,jdbcType=BIGINT}, 
      #{paidClosedCount,jdbcType=INTEGER}, #{successPayCount,jdbcType=INTEGER}, #{failPayCount,jdbcType=INTEGER}, 
      #{payPrice,jdbcType=DECIMAL}, #{statType,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayStatisticsDO">
    insert into claim_pay_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="paidClosedCount != null">
        paid_closed_count,
      </if>
      <if test="successPayCount != null">
        success_pay_count,
      </if>
      <if test="failPayCount != null">
        fail_pay_count,
      </if>
      <if test="payPrice != null">
        pay_price,
      </if>
      <if test="statType != null">
        stat_type,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="paidClosedCount != null">
        #{paidClosedCount,jdbcType=INTEGER},
      </if>
      <if test="successPayCount != null">
        #{successPayCount,jdbcType=INTEGER},
      </if>
      <if test="failPayCount != null">
        #{failPayCount,jdbcType=INTEGER},
      </if>
      <if test="payPrice != null">
        #{payPrice,jdbcType=DECIMAL},
      </if>
      <if test="statType != null">
        #{statType,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayStatisticsExample" resultType="java.lang.Long">
    select count(*) from claim_pay_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_pay_statistics
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.paidClosedCount != null">
        paid_closed_count = #{record.paidClosedCount,jdbcType=INTEGER},
      </if>
      <if test="record.successPayCount != null">
        success_pay_count = #{record.successPayCount,jdbcType=INTEGER},
      </if>
      <if test="record.failPayCount != null">
        fail_pay_count = #{record.failPayCount,jdbcType=INTEGER},
      </if>
      <if test="record.payPrice != null">
        pay_price = #{record.payPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.statType != null">
        stat_type = #{record.statType,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_pay_statistics
    set id = #{record.id,jdbcType=BIGINT},
      campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      paid_closed_count = #{record.paidClosedCount,jdbcType=INTEGER},
      success_pay_count = #{record.successPayCount,jdbcType=INTEGER},
      fail_pay_count = #{record.failPayCount,jdbcType=INTEGER},
      pay_price = #{record.payPrice,jdbcType=DECIMAL},
      stat_type = #{record.statType,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayStatisticsDO">
    update claim_pay_statistics
    <set>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="paidClosedCount != null">
        paid_closed_count = #{paidClosedCount,jdbcType=INTEGER},
      </if>
      <if test="successPayCount != null">
        success_pay_count = #{successPayCount,jdbcType=INTEGER},
      </if>
      <if test="failPayCount != null">
        fail_pay_count = #{failPayCount,jdbcType=INTEGER},
      </if>
      <if test="payPrice != null">
        pay_price = #{payPrice,jdbcType=DECIMAL},
      </if>
      <if test="statType != null">
        stat_type = #{statType,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayStatisticsDO">
    update claim_pay_statistics
    set campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      package_def_id = #{packageDefId,jdbcType=BIGINT},
      paid_closed_count = #{paidClosedCount,jdbcType=INTEGER},
      success_pay_count = #{successPayCount,jdbcType=INTEGER},
      fail_pay_count = #{failPayCount,jdbcType=INTEGER},
      pay_price = #{payPrice,jdbcType=DECIMAL},
      stat_type = #{statType,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_pay_statistics
    (id, campaign_def_id, package_def_id, paid_closed_count, success_pay_count, fail_pay_count, 
      pay_price, stat_type, start_time, end_time, creator, modifier, gmt_created, gmt_modified, 
      is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.campaignDefId,jdbcType=BIGINT}, #{item.packageDefId,jdbcType=BIGINT}, 
        #{item.paidClosedCount,jdbcType=INTEGER}, #{item.successPayCount,jdbcType=INTEGER}, 
        #{item.failPayCount,jdbcType=INTEGER}, #{item.payPrice,jdbcType=DECIMAL}, #{item.statType,jdbcType=VARCHAR}, 
        #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_pay_statistics (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=BIGINT}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'paid_closed_count'.toString() == column.value">
          #{item.paidClosedCount,jdbcType=INTEGER}
        </if>
        <if test="'success_pay_count'.toString() == column.value">
          #{item.successPayCount,jdbcType=INTEGER}
        </if>
        <if test="'fail_pay_count'.toString() == column.value">
          #{item.failPayCount,jdbcType=INTEGER}
        </if>
        <if test="'pay_price'.toString() == column.value">
          #{item.payPrice,jdbcType=DECIMAL}
        </if>
        <if test="'stat_type'.toString() == column.value">
          #{item.statType,jdbcType=VARCHAR}
        </if>
        <if test="'start_time'.toString() == column.value">
          #{item.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>