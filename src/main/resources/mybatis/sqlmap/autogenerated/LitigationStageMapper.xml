<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.LitigationStageMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.LitigationStageDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="litigation_no" jdbcType="VARCHAR" property="litigationNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="stage" jdbcType="TINYINT" property="stage" />
    <result column="is_mediation_success" jdbcType="BIT" property="isMediationSuccess" />
    <result column="mediation_amount" jdbcType="DECIMAL" property="mediationAmount" />
    <result column="mediation_proportion" jdbcType="DECIMAL" property="mediationProportion" />
    <result column="mediation_person" jdbcType="VARCHAR" property="mediationPerson" />
    <result column="mediation_time" jdbcType="TIMESTAMP" property="mediationTime" />
    <result column="mediation_result" jdbcType="VARCHAR" property="mediationResult" />
    <result column="first_audit_result" jdbcType="VARCHAR" property="firstAuditResult" />
    <result column="second_audit_result" jdbcType="VARCHAR" property="secondAuditResult" />
    <result column="final_audit_result" jdbcType="VARCHAR" property="finalAuditResult" />
    <result column="is_appeal" jdbcType="BIT" property="isAppeal" />
    <result column="is_exist_fee" jdbcType="BIT" property="isExistFee" />
    <result column="legal_fee" jdbcType="DECIMAL" property="legalFee" />
    <result column="lawyer" jdbcType="VARCHAR" property="lawyer" />
    <result column="law_firm" jdbcType="VARCHAR" property="lawFirm" />
    <result column="judge_name" jdbcType="VARCHAR" property="judgeName" />
    <result column="compensation_amount" jdbcType="DECIMAL" property="compensationAmount" />
    <result column="litigation_fee" jdbcType="DECIMAL" property="litigationFee" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="risk_points" jdbcType="VARCHAR" property="riskPoints" />
    <result column="judgment_approach" jdbcType="VARCHAR" property="judgmentApproach" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, litigation_no, policy_no, report_no, stage, is_mediation_success, mediation_amount, 
    mediation_proportion, mediation_person, mediation_time, mediation_result, first_audit_result, 
    second_audit_result, final_audit_result, is_appeal, is_exist_fee, legal_fee, lawyer, 
    law_firm, judge_name, compensation_amount, litigation_fee, remark, risk_points, judgment_approach, 
    creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.LitigationStageExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from litigation_stage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from litigation_stage
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.LitigationStageDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into litigation_stage (litigation_no, policy_no, report_no, 
      stage, is_mediation_success, mediation_amount, 
      mediation_proportion, mediation_person, mediation_time, 
      mediation_result, first_audit_result, second_audit_result, 
      final_audit_result, is_appeal, is_exist_fee, 
      legal_fee, lawyer, law_firm, 
      judge_name, compensation_amount, litigation_fee, 
      remark, risk_points, judgment_approach, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{litigationNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{stage,jdbcType=TINYINT}, #{isMediationSuccess,jdbcType=BIT}, #{mediationAmount,jdbcType=DECIMAL}, 
      #{mediationProportion,jdbcType=DECIMAL}, #{mediationPerson,jdbcType=VARCHAR}, #{mediationTime,jdbcType=TIMESTAMP}, 
      #{mediationResult,jdbcType=VARCHAR}, #{firstAuditResult,jdbcType=VARCHAR}, #{secondAuditResult,jdbcType=VARCHAR}, 
      #{finalAuditResult,jdbcType=VARCHAR}, #{isAppeal,jdbcType=BIT}, #{isExistFee,jdbcType=BIT}, 
      #{legalFee,jdbcType=DECIMAL}, #{lawyer,jdbcType=VARCHAR}, #{lawFirm,jdbcType=VARCHAR}, 
      #{judgeName,jdbcType=VARCHAR}, #{compensationAmount,jdbcType=DECIMAL}, #{litigationFee,jdbcType=DECIMAL}, 
      #{remark,jdbcType=VARCHAR}, #{riskPoints,jdbcType=VARCHAR}, #{judgmentApproach,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.LitigationStageDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into litigation_stage
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="litigationNo != null">
        litigation_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="stage != null">
        stage,
      </if>
      <if test="isMediationSuccess != null">
        is_mediation_success,
      </if>
      <if test="mediationAmount != null">
        mediation_amount,
      </if>
      <if test="mediationProportion != null">
        mediation_proportion,
      </if>
      <if test="mediationPerson != null">
        mediation_person,
      </if>
      <if test="mediationTime != null">
        mediation_time,
      </if>
      <if test="mediationResult != null">
        mediation_result,
      </if>
      <if test="firstAuditResult != null">
        first_audit_result,
      </if>
      <if test="secondAuditResult != null">
        second_audit_result,
      </if>
      <if test="finalAuditResult != null">
        final_audit_result,
      </if>
      <if test="isAppeal != null">
        is_appeal,
      </if>
      <if test="isExistFee != null">
        is_exist_fee,
      </if>
      <if test="legalFee != null">
        legal_fee,
      </if>
      <if test="lawyer != null">
        lawyer,
      </if>
      <if test="lawFirm != null">
        law_firm,
      </if>
      <if test="judgeName != null">
        judge_name,
      </if>
      <if test="compensationAmount != null">
        compensation_amount,
      </if>
      <if test="litigationFee != null">
        litigation_fee,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="riskPoints != null">
        risk_points,
      </if>
      <if test="judgmentApproach != null">
        judgment_approach,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="litigationNo != null">
        #{litigationNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="stage != null">
        #{stage,jdbcType=TINYINT},
      </if>
      <if test="isMediationSuccess != null">
        #{isMediationSuccess,jdbcType=BIT},
      </if>
      <if test="mediationAmount != null">
        #{mediationAmount,jdbcType=DECIMAL},
      </if>
      <if test="mediationProportion != null">
        #{mediationProportion,jdbcType=DECIMAL},
      </if>
      <if test="mediationPerson != null">
        #{mediationPerson,jdbcType=VARCHAR},
      </if>
      <if test="mediationTime != null">
        #{mediationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mediationResult != null">
        #{mediationResult,jdbcType=VARCHAR},
      </if>
      <if test="firstAuditResult != null">
        #{firstAuditResult,jdbcType=VARCHAR},
      </if>
      <if test="secondAuditResult != null">
        #{secondAuditResult,jdbcType=VARCHAR},
      </if>
      <if test="finalAuditResult != null">
        #{finalAuditResult,jdbcType=VARCHAR},
      </if>
      <if test="isAppeal != null">
        #{isAppeal,jdbcType=BIT},
      </if>
      <if test="isExistFee != null">
        #{isExistFee,jdbcType=BIT},
      </if>
      <if test="legalFee != null">
        #{legalFee,jdbcType=DECIMAL},
      </if>
      <if test="lawyer != null">
        #{lawyer,jdbcType=VARCHAR},
      </if>
      <if test="lawFirm != null">
        #{lawFirm,jdbcType=VARCHAR},
      </if>
      <if test="judgeName != null">
        #{judgeName,jdbcType=VARCHAR},
      </if>
      <if test="compensationAmount != null">
        #{compensationAmount,jdbcType=DECIMAL},
      </if>
      <if test="litigationFee != null">
        #{litigationFee,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="riskPoints != null">
        #{riskPoints,jdbcType=VARCHAR},
      </if>
      <if test="judgmentApproach != null">
        #{judgmentApproach,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.LitigationStageExample" resultType="java.lang.Long">
    select count(*) from litigation_stage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update litigation_stage
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.litigationNo != null">
        litigation_no = #{record.litigationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.stage != null">
        stage = #{record.stage,jdbcType=TINYINT},
      </if>
      <if test="record.isMediationSuccess != null">
        is_mediation_success = #{record.isMediationSuccess,jdbcType=BIT},
      </if>
      <if test="record.mediationAmount != null">
        mediation_amount = #{record.mediationAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.mediationProportion != null">
        mediation_proportion = #{record.mediationProportion,jdbcType=DECIMAL},
      </if>
      <if test="record.mediationPerson != null">
        mediation_person = #{record.mediationPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.mediationTime != null">
        mediation_time = #{record.mediationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mediationResult != null">
        mediation_result = #{record.mediationResult,jdbcType=VARCHAR},
      </if>
      <if test="record.firstAuditResult != null">
        first_audit_result = #{record.firstAuditResult,jdbcType=VARCHAR},
      </if>
      <if test="record.secondAuditResult != null">
        second_audit_result = #{record.secondAuditResult,jdbcType=VARCHAR},
      </if>
      <if test="record.finalAuditResult != null">
        final_audit_result = #{record.finalAuditResult,jdbcType=VARCHAR},
      </if>
      <if test="record.isAppeal != null">
        is_appeal = #{record.isAppeal,jdbcType=BIT},
      </if>
      <if test="record.isExistFee != null">
        is_exist_fee = #{record.isExistFee,jdbcType=BIT},
      </if>
      <if test="record.legalFee != null">
        legal_fee = #{record.legalFee,jdbcType=DECIMAL},
      </if>
      <if test="record.lawyer != null">
        lawyer = #{record.lawyer,jdbcType=VARCHAR},
      </if>
      <if test="record.lawFirm != null">
        law_firm = #{record.lawFirm,jdbcType=VARCHAR},
      </if>
      <if test="record.judgeName != null">
        judge_name = #{record.judgeName,jdbcType=VARCHAR},
      </if>
      <if test="record.compensationAmount != null">
        compensation_amount = #{record.compensationAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.litigationFee != null">
        litigation_fee = #{record.litigationFee,jdbcType=DECIMAL},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.riskPoints != null">
        risk_points = #{record.riskPoints,jdbcType=VARCHAR},
      </if>
      <if test="record.judgmentApproach != null">
        judgment_approach = #{record.judgmentApproach,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update litigation_stage
    set id = #{record.id,jdbcType=BIGINT},
      litigation_no = #{record.litigationNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      stage = #{record.stage,jdbcType=TINYINT},
      is_mediation_success = #{record.isMediationSuccess,jdbcType=BIT},
      mediation_amount = #{record.mediationAmount,jdbcType=DECIMAL},
      mediation_proportion = #{record.mediationProportion,jdbcType=DECIMAL},
      mediation_person = #{record.mediationPerson,jdbcType=VARCHAR},
      mediation_time = #{record.mediationTime,jdbcType=TIMESTAMP},
      mediation_result = #{record.mediationResult,jdbcType=VARCHAR},
      first_audit_result = #{record.firstAuditResult,jdbcType=VARCHAR},
      second_audit_result = #{record.secondAuditResult,jdbcType=VARCHAR},
      final_audit_result = #{record.finalAuditResult,jdbcType=VARCHAR},
      is_appeal = #{record.isAppeal,jdbcType=BIT},
      is_exist_fee = #{record.isExistFee,jdbcType=BIT},
      legal_fee = #{record.legalFee,jdbcType=DECIMAL},
      lawyer = #{record.lawyer,jdbcType=VARCHAR},
      law_firm = #{record.lawFirm,jdbcType=VARCHAR},
      judge_name = #{record.judgeName,jdbcType=VARCHAR},
      compensation_amount = #{record.compensationAmount,jdbcType=DECIMAL},
      litigation_fee = #{record.litigationFee,jdbcType=DECIMAL},
      remark = #{record.remark,jdbcType=VARCHAR},
      risk_points = #{record.riskPoints,jdbcType=VARCHAR},
      judgment_approach = #{record.judgmentApproach,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.LitigationStageDO">
    update litigation_stage
    <set>
      <if test="litigationNo != null">
        litigation_no = #{litigationNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="stage != null">
        stage = #{stage,jdbcType=TINYINT},
      </if>
      <if test="isMediationSuccess != null">
        is_mediation_success = #{isMediationSuccess,jdbcType=BIT},
      </if>
      <if test="mediationAmount != null">
        mediation_amount = #{mediationAmount,jdbcType=DECIMAL},
      </if>
      <if test="mediationProportion != null">
        mediation_proportion = #{mediationProportion,jdbcType=DECIMAL},
      </if>
      <if test="mediationPerson != null">
        mediation_person = #{mediationPerson,jdbcType=VARCHAR},
      </if>
      <if test="mediationTime != null">
        mediation_time = #{mediationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mediationResult != null">
        mediation_result = #{mediationResult,jdbcType=VARCHAR},
      </if>
      <if test="firstAuditResult != null">
        first_audit_result = #{firstAuditResult,jdbcType=VARCHAR},
      </if>
      <if test="secondAuditResult != null">
        second_audit_result = #{secondAuditResult,jdbcType=VARCHAR},
      </if>
      <if test="finalAuditResult != null">
        final_audit_result = #{finalAuditResult,jdbcType=VARCHAR},
      </if>
      <if test="isAppeal != null">
        is_appeal = #{isAppeal,jdbcType=BIT},
      </if>
      <if test="isExistFee != null">
        is_exist_fee = #{isExistFee,jdbcType=BIT},
      </if>
      <if test="legalFee != null">
        legal_fee = #{legalFee,jdbcType=DECIMAL},
      </if>
      <if test="lawyer != null">
        lawyer = #{lawyer,jdbcType=VARCHAR},
      </if>
      <if test="lawFirm != null">
        law_firm = #{lawFirm,jdbcType=VARCHAR},
      </if>
      <if test="judgeName != null">
        judge_name = #{judgeName,jdbcType=VARCHAR},
      </if>
      <if test="compensationAmount != null">
        compensation_amount = #{compensationAmount,jdbcType=DECIMAL},
      </if>
      <if test="litigationFee != null">
        litigation_fee = #{litigationFee,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="riskPoints != null">
        risk_points = #{riskPoints,jdbcType=VARCHAR},
      </if>
      <if test="judgmentApproach != null">
        judgment_approach = #{judgmentApproach,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.LitigationStageDO">
    update litigation_stage
    set litigation_no = #{litigationNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      stage = #{stage,jdbcType=TINYINT},
      is_mediation_success = #{isMediationSuccess,jdbcType=BIT},
      mediation_amount = #{mediationAmount,jdbcType=DECIMAL},
      mediation_proportion = #{mediationProportion,jdbcType=DECIMAL},
      mediation_person = #{mediationPerson,jdbcType=VARCHAR},
      mediation_time = #{mediationTime,jdbcType=TIMESTAMP},
      mediation_result = #{mediationResult,jdbcType=VARCHAR},
      first_audit_result = #{firstAuditResult,jdbcType=VARCHAR},
      second_audit_result = #{secondAuditResult,jdbcType=VARCHAR},
      final_audit_result = #{finalAuditResult,jdbcType=VARCHAR},
      is_appeal = #{isAppeal,jdbcType=BIT},
      is_exist_fee = #{isExistFee,jdbcType=BIT},
      legal_fee = #{legalFee,jdbcType=DECIMAL},
      lawyer = #{lawyer,jdbcType=VARCHAR},
      law_firm = #{lawFirm,jdbcType=VARCHAR},
      judge_name = #{judgeName,jdbcType=VARCHAR},
      compensation_amount = #{compensationAmount,jdbcType=DECIMAL},
      litigation_fee = #{litigationFee,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      risk_points = #{riskPoints,jdbcType=VARCHAR},
      judgment_approach = #{judgmentApproach,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into litigation_stage
    (litigation_no, policy_no, report_no, stage, is_mediation_success, mediation_amount, 
      mediation_proportion, mediation_person, mediation_time, mediation_result, first_audit_result, 
      second_audit_result, final_audit_result, is_appeal, is_exist_fee, legal_fee, lawyer, 
      law_firm, judge_name, compensation_amount, litigation_fee, remark, risk_points, 
      judgment_approach, creator, modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.litigationNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.stage,jdbcType=TINYINT}, #{item.isMediationSuccess,jdbcType=BIT}, #{item.mediationAmount,jdbcType=DECIMAL}, 
        #{item.mediationProportion,jdbcType=DECIMAL}, #{item.mediationPerson,jdbcType=VARCHAR}, 
        #{item.mediationTime,jdbcType=TIMESTAMP}, #{item.mediationResult,jdbcType=VARCHAR}, 
        #{item.firstAuditResult,jdbcType=VARCHAR}, #{item.secondAuditResult,jdbcType=VARCHAR}, 
        #{item.finalAuditResult,jdbcType=VARCHAR}, #{item.isAppeal,jdbcType=BIT}, #{item.isExistFee,jdbcType=BIT}, 
        #{item.legalFee,jdbcType=DECIMAL}, #{item.lawyer,jdbcType=VARCHAR}, #{item.lawFirm,jdbcType=VARCHAR}, 
        #{item.judgeName,jdbcType=VARCHAR}, #{item.compensationAmount,jdbcType=DECIMAL}, 
        #{item.litigationFee,jdbcType=DECIMAL}, #{item.remark,jdbcType=VARCHAR}, #{item.riskPoints,jdbcType=VARCHAR}, 
        #{item.judgmentApproach,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into litigation_stage (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'litigation_no'.toString() == column.value">
          #{item.litigationNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'stage'.toString() == column.value">
          #{item.stage,jdbcType=TINYINT}
        </if>
        <if test="'is_mediation_success'.toString() == column.value">
          #{item.isMediationSuccess,jdbcType=BIT}
        </if>
        <if test="'mediation_amount'.toString() == column.value">
          #{item.mediationAmount,jdbcType=DECIMAL}
        </if>
        <if test="'mediation_proportion'.toString() == column.value">
          #{item.mediationProportion,jdbcType=DECIMAL}
        </if>
        <if test="'mediation_person'.toString() == column.value">
          #{item.mediationPerson,jdbcType=VARCHAR}
        </if>
        <if test="'mediation_time'.toString() == column.value">
          #{item.mediationTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'mediation_result'.toString() == column.value">
          #{item.mediationResult,jdbcType=VARCHAR}
        </if>
        <if test="'first_audit_result'.toString() == column.value">
          #{item.firstAuditResult,jdbcType=VARCHAR}
        </if>
        <if test="'second_audit_result'.toString() == column.value">
          #{item.secondAuditResult,jdbcType=VARCHAR}
        </if>
        <if test="'final_audit_result'.toString() == column.value">
          #{item.finalAuditResult,jdbcType=VARCHAR}
        </if>
        <if test="'is_appeal'.toString() == column.value">
          #{item.isAppeal,jdbcType=BIT}
        </if>
        <if test="'is_exist_fee'.toString() == column.value">
          #{item.isExistFee,jdbcType=BIT}
        </if>
        <if test="'legal_fee'.toString() == column.value">
          #{item.legalFee,jdbcType=DECIMAL}
        </if>
        <if test="'lawyer'.toString() == column.value">
          #{item.lawyer,jdbcType=VARCHAR}
        </if>
        <if test="'law_firm'.toString() == column.value">
          #{item.lawFirm,jdbcType=VARCHAR}
        </if>
        <if test="'judge_name'.toString() == column.value">
          #{item.judgeName,jdbcType=VARCHAR}
        </if>
        <if test="'compensation_amount'.toString() == column.value">
          #{item.compensationAmount,jdbcType=DECIMAL}
        </if>
        <if test="'litigation_fee'.toString() == column.value">
          #{item.litigationFee,jdbcType=DECIMAL}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'risk_points'.toString() == column.value">
          #{item.riskPoints,jdbcType=VARCHAR}
        </if>
        <if test="'judgment_approach'.toString() == column.value">
          #{item.judgmentApproach,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>