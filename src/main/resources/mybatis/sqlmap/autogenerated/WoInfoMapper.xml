<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.WoInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.WoInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="wo_no" jdbcType="VARCHAR" property="woNo" />
    <result column="wo_type" jdbcType="VARCHAR" property="woType" />
    <result column="wo_status" jdbcType="TINYINT" property="woStatus" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="dispatcher_no" jdbcType="VARCHAR" property="dispatcherNo" />
    <result column="inquisition_point" jdbcType="VARCHAR" property="inquisitionPoint" />
    <result column="attachment_id" jdbcType="VARCHAR" property="attachmentId" />
    <result column="wo_data" jdbcType="VARCHAR" property="woData" />
    <result column="executor" jdbcType="VARCHAR" property="executor" />
    <result column="execute_time" jdbcType="TIMESTAMP" property="executeTime" />
    <result column="execute_remark" jdbcType="VARCHAR" property="executeRemark" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, wo_no, wo_type, wo_status, report_no, policy_no, dispatcher_no, inquisition_point, 
    attachment_id, wo_data, executor, execute_time, execute_remark, remark, is_deleted, 
    gmt_created, gmt_modified, creator, modifier, reason
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.WoInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wo_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wo_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.WoInfoDO">
    insert into wo_info (id, wo_no, wo_type, 
      wo_status, report_no, policy_no, 
      dispatcher_no, inquisition_point, attachment_id, 
      wo_data, executor, execute_time, 
      execute_remark, remark, is_deleted, 
      gmt_created, gmt_modified, creator, 
      modifier, reason)
    values (#{id,jdbcType=BIGINT}, #{woNo,jdbcType=VARCHAR}, #{woType,jdbcType=VARCHAR}, 
      #{woStatus,jdbcType=TINYINT}, #{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, 
      #{dispatcherNo,jdbcType=VARCHAR}, #{inquisitionPoint,jdbcType=VARCHAR}, #{attachmentId,jdbcType=VARCHAR}, 
      #{woData,jdbcType=VARCHAR}, #{executor,jdbcType=VARCHAR}, #{executeTime,jdbcType=TIMESTAMP}, 
      #{executeRemark,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.WoInfoDO">
    insert into wo_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="woNo != null">
        wo_no,
      </if>
      <if test="woType != null">
        wo_type,
      </if>
      <if test="woStatus != null">
        wo_status,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no,
      </if>
      <if test="inquisitionPoint != null">
        inquisition_point,
      </if>
      <if test="attachmentId != null">
        attachment_id,
      </if>
      <if test="woData != null">
        wo_data,
      </if>
      <if test="executor != null">
        executor,
      </if>
      <if test="executeTime != null">
        execute_time,
      </if>
      <if test="executeRemark != null">
        execute_remark,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="reason != null">
        reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="woNo != null">
        #{woNo,jdbcType=VARCHAR},
      </if>
      <if test="woType != null">
        #{woType,jdbcType=VARCHAR},
      </if>
      <if test="woStatus != null">
        #{woStatus,jdbcType=TINYINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherNo != null">
        #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="inquisitionPoint != null">
        #{inquisitionPoint,jdbcType=VARCHAR},
      </if>
      <if test="attachmentId != null">
        #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="woData != null">
        #{woData,jdbcType=VARCHAR},
      </if>
      <if test="executor != null">
        #{executor,jdbcType=VARCHAR},
      </if>
      <if test="executeTime != null">
        #{executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="executeRemark != null">
        #{executeRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.WoInfoExample" resultType="java.lang.Long">
    select count(*) from wo_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wo_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.woNo != null">
        wo_no = #{record.woNo,jdbcType=VARCHAR},
      </if>
      <if test="record.woType != null">
        wo_type = #{record.woType,jdbcType=VARCHAR},
      </if>
      <if test="record.woStatus != null">
        wo_status = #{record.woStatus,jdbcType=TINYINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.dispatcherNo != null">
        dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="record.inquisitionPoint != null">
        inquisition_point = #{record.inquisitionPoint,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentId != null">
        attachment_id = #{record.attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="record.woData != null">
        wo_data = #{record.woData,jdbcType=VARCHAR},
      </if>
      <if test="record.executor != null">
        executor = #{record.executor,jdbcType=VARCHAR},
      </if>
      <if test="record.executeTime != null">
        execute_time = #{record.executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.executeRemark != null">
        execute_remark = #{record.executeRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wo_info
    set id = #{record.id,jdbcType=BIGINT},
      wo_no = #{record.woNo,jdbcType=VARCHAR},
      wo_type = #{record.woType,jdbcType=VARCHAR},
      wo_status = #{record.woStatus,jdbcType=TINYINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      inquisition_point = #{record.inquisitionPoint,jdbcType=VARCHAR},
      attachment_id = #{record.attachmentId,jdbcType=VARCHAR},
      wo_data = #{record.woData,jdbcType=VARCHAR},
      executor = #{record.executor,jdbcType=VARCHAR},
      execute_time = #{record.executeTime,jdbcType=TIMESTAMP},
      execute_remark = #{record.executeRemark,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.WoInfoDO">
    update wo_info
    <set>
      <if test="woNo != null">
        wo_no = #{woNo,jdbcType=VARCHAR},
      </if>
      <if test="woType != null">
        wo_type = #{woType,jdbcType=VARCHAR},
      </if>
      <if test="woStatus != null">
        wo_status = #{woStatus,jdbcType=TINYINT},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="inquisitionPoint != null">
        inquisition_point = #{inquisitionPoint,jdbcType=VARCHAR},
      </if>
      <if test="attachmentId != null">
        attachment_id = #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="woData != null">
        wo_data = #{woData,jdbcType=VARCHAR},
      </if>
      <if test="executor != null">
        executor = #{executor,jdbcType=VARCHAR},
      </if>
      <if test="executeTime != null">
        execute_time = #{executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="executeRemark != null">
        execute_remark = #{executeRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.WoInfoDO">
    update wo_info
    set wo_no = #{woNo,jdbcType=VARCHAR},
      wo_type = #{woType,jdbcType=VARCHAR},
      wo_status = #{woStatus,jdbcType=TINYINT},
      report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      inquisition_point = #{inquisitionPoint,jdbcType=VARCHAR},
      attachment_id = #{attachmentId,jdbcType=VARCHAR},
      wo_data = #{woData,jdbcType=VARCHAR},
      executor = #{executor,jdbcType=VARCHAR},
      execute_time = #{executeTime,jdbcType=TIMESTAMP},
      execute_remark = #{executeRemark,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into wo_info
    (id, wo_no, wo_type, wo_status, report_no, policy_no, dispatcher_no, inquisition_point, 
      attachment_id, wo_data, executor, execute_time, execute_remark, remark, is_deleted, 
      gmt_created, gmt_modified, creator, modifier, reason)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.woNo,jdbcType=VARCHAR}, #{item.woType,jdbcType=VARCHAR}, 
        #{item.woStatus,jdbcType=TINYINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.dispatcherNo,jdbcType=VARCHAR}, #{item.inquisitionPoint,jdbcType=VARCHAR}, 
        #{item.attachmentId,jdbcType=VARCHAR}, #{item.woData,jdbcType=VARCHAR}, #{item.executor,jdbcType=VARCHAR}, 
        #{item.executeTime,jdbcType=TIMESTAMP}, #{item.executeRemark,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.reason,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into wo_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'wo_no'.toString() == column.value">
          #{item.woNo,jdbcType=VARCHAR}
        </if>
        <if test="'wo_type'.toString() == column.value">
          #{item.woType,jdbcType=VARCHAR}
        </if>
        <if test="'wo_status'.toString() == column.value">
          #{item.woStatus,jdbcType=TINYINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'dispatcher_no'.toString() == column.value">
          #{item.dispatcherNo,jdbcType=VARCHAR}
        </if>
        <if test="'inquisition_point'.toString() == column.value">
          #{item.inquisitionPoint,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_id'.toString() == column.value">
          #{item.attachmentId,jdbcType=VARCHAR}
        </if>
        <if test="'wo_data'.toString() == column.value">
          #{item.woData,jdbcType=VARCHAR}
        </if>
        <if test="'executor'.toString() == column.value">
          #{item.executor,jdbcType=VARCHAR}
        </if>
        <if test="'execute_time'.toString() == column.value">
          #{item.executeTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'execute_remark'.toString() == column.value">
          #{item.executeRemark,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'reason'.toString() == column.value">
          #{item.reason,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>