<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.DirectSettlementMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.DirectSettlementDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="pet_name" jdbcType="VARCHAR" property="petName" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
    <result column="hospital_id" jdbcType="VARCHAR" property="hospitalId" />
    <result column="hospital_yc_id" jdbcType="VARCHAR" property="hospitalYcId" />
    <result column="compensation_ratio" jdbcType="VARCHAR" property="compensationRatio" />
    <result column="once_paid_amount" jdbcType="DECIMAL" property="oncePaidAmount" />
    <result column="once_max_compensation_amount" jdbcType="DECIMAL" property="onceMaxCompensationAmount" />
    <result column="customer_settlement_amount" jdbcType="DECIMAL" property="customerSettlementAmount" />
    <result column="supplier_settlement_amount" jdbcType="DECIMAL" property="supplierSettlementAmount" />
    <result column="settlement_conclusion" jdbcType="VARCHAR" property="settlementConclusion" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_no, report_no, pet_name, hospital_name, hospital_id, hospital_yc_id, compensation_ratio, 
    once_paid_amount, once_max_compensation_amount, customer_settlement_amount, supplier_settlement_amount, 
    settlement_conclusion, status, extra_info, creator, modifier, gmt_created, gmt_modified, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.DirectSettlementExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pet_direct_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pet_direct_settlement
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.DirectSettlementDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pet_direct_settlement (policy_no, report_no, pet_name, 
      hospital_name, hospital_id, hospital_yc_id, 
      compensation_ratio, once_paid_amount, once_max_compensation_amount, 
      customer_settlement_amount, supplier_settlement_amount, 
      settlement_conclusion, status, extra_info, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{petName,jdbcType=VARCHAR}, 
      #{hospitalName,jdbcType=VARCHAR}, #{hospitalId,jdbcType=VARCHAR}, #{hospitalYcId,jdbcType=VARCHAR}, 
      #{compensationRatio,jdbcType=VARCHAR}, #{oncePaidAmount,jdbcType=DECIMAL}, #{onceMaxCompensationAmount,jdbcType=DECIMAL}, 
      #{customerSettlementAmount,jdbcType=DECIMAL}, #{supplierSettlementAmount,jdbcType=DECIMAL}, 
      #{settlementConclusion,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{extraInfo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.DirectSettlementDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pet_direct_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="petName != null">
        pet_name,
      </if>
      <if test="hospitalName != null">
        hospital_name,
      </if>
      <if test="hospitalId != null">
        hospital_id,
      </if>
      <if test="hospitalYcId != null">
        hospital_yc_id,
      </if>
      <if test="compensationRatio != null">
        compensation_ratio,
      </if>
      <if test="oncePaidAmount != null">
        once_paid_amount,
      </if>
      <if test="onceMaxCompensationAmount != null">
        once_max_compensation_amount,
      </if>
      <if test="customerSettlementAmount != null">
        customer_settlement_amount,
      </if>
      <if test="supplierSettlementAmount != null">
        supplier_settlement_amount,
      </if>
      <if test="settlementConclusion != null">
        settlement_conclusion,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="petName != null">
        #{petName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalId != null">
        #{hospitalId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalYcId != null">
        #{hospitalYcId,jdbcType=VARCHAR},
      </if>
      <if test="compensationRatio != null">
        #{compensationRatio,jdbcType=VARCHAR},
      </if>
      <if test="oncePaidAmount != null">
        #{oncePaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="onceMaxCompensationAmount != null">
        #{onceMaxCompensationAmount,jdbcType=DECIMAL},
      </if>
      <if test="customerSettlementAmount != null">
        #{customerSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierSettlementAmount != null">
        #{supplierSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementConclusion != null">
        #{settlementConclusion,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.DirectSettlementExample" resultType="java.lang.Long">
    select count(*) from pet_direct_settlement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pet_direct_settlement
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.petName != null">
        pet_name = #{record.petName,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalName != null">
        hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalId != null">
        hospital_id = #{record.hospitalId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalYcId != null">
        hospital_yc_id = #{record.hospitalYcId,jdbcType=VARCHAR},
      </if>
      <if test="record.compensationRatio != null">
        compensation_ratio = #{record.compensationRatio,jdbcType=VARCHAR},
      </if>
      <if test="record.oncePaidAmount != null">
        once_paid_amount = #{record.oncePaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.onceMaxCompensationAmount != null">
        once_max_compensation_amount = #{record.onceMaxCompensationAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.customerSettlementAmount != null">
        customer_settlement_amount = #{record.customerSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.supplierSettlementAmount != null">
        supplier_settlement_amount = #{record.supplierSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.settlementConclusion != null">
        settlement_conclusion = #{record.settlementConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pet_direct_settlement
    set id = #{record.id,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      pet_name = #{record.petName,jdbcType=VARCHAR},
      hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      hospital_id = #{record.hospitalId,jdbcType=VARCHAR},
      hospital_yc_id = #{record.hospitalYcId,jdbcType=VARCHAR},
      compensation_ratio = #{record.compensationRatio,jdbcType=VARCHAR},
      once_paid_amount = #{record.oncePaidAmount,jdbcType=DECIMAL},
      once_max_compensation_amount = #{record.onceMaxCompensationAmount,jdbcType=DECIMAL},
      customer_settlement_amount = #{record.customerSettlementAmount,jdbcType=DECIMAL},
      supplier_settlement_amount = #{record.supplierSettlementAmount,jdbcType=DECIMAL},
      settlement_conclusion = #{record.settlementConclusion,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.DirectSettlementDO">
    update pet_direct_settlement
    <set>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="petName != null">
        pet_name = #{petName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        hospital_name = #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalYcId != null">
        hospital_yc_id = #{hospitalYcId,jdbcType=VARCHAR},
      </if>
      <if test="compensationRatio != null">
        compensation_ratio = #{compensationRatio,jdbcType=VARCHAR},
      </if>
      <if test="oncePaidAmount != null">
        once_paid_amount = #{oncePaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="onceMaxCompensationAmount != null">
        once_max_compensation_amount = #{onceMaxCompensationAmount,jdbcType=DECIMAL},
      </if>
      <if test="customerSettlementAmount != null">
        customer_settlement_amount = #{customerSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierSettlementAmount != null">
        supplier_settlement_amount = #{supplierSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementConclusion != null">
        settlement_conclusion = #{settlementConclusion,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.DirectSettlementDO">
    update pet_direct_settlement
    set policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      pet_name = #{petName,jdbcType=VARCHAR},
      hospital_name = #{hospitalName,jdbcType=VARCHAR},
      hospital_id = #{hospitalId,jdbcType=VARCHAR},
      hospital_yc_id = #{hospitalYcId,jdbcType=VARCHAR},
      compensation_ratio = #{compensationRatio,jdbcType=VARCHAR},
      once_paid_amount = #{oncePaidAmount,jdbcType=DECIMAL},
      once_max_compensation_amount = #{onceMaxCompensationAmount,jdbcType=DECIMAL},
      customer_settlement_amount = #{customerSettlementAmount,jdbcType=DECIMAL},
      supplier_settlement_amount = #{supplierSettlementAmount,jdbcType=DECIMAL},
      settlement_conclusion = #{settlementConclusion,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into pet_direct_settlement
    (policy_no, report_no, pet_name, hospital_name, hospital_id, hospital_yc_id, compensation_ratio, 
      once_paid_amount, once_max_compensation_amount, customer_settlement_amount, supplier_settlement_amount, 
      settlement_conclusion, status, extra_info, creator, modifier, gmt_created, gmt_modified, 
      is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.petName,jdbcType=VARCHAR}, 
        #{item.hospitalName,jdbcType=VARCHAR}, #{item.hospitalId,jdbcType=VARCHAR}, #{item.hospitalYcId,jdbcType=VARCHAR}, 
        #{item.compensationRatio,jdbcType=VARCHAR}, #{item.oncePaidAmount,jdbcType=DECIMAL}, 
        #{item.onceMaxCompensationAmount,jdbcType=DECIMAL}, #{item.customerSettlementAmount,jdbcType=DECIMAL}, 
        #{item.supplierSettlementAmount,jdbcType=DECIMAL}, #{item.settlementConclusion,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.extraInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into pet_direct_settlement (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'pet_name'.toString() == column.value">
          #{item.petName,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_name'.toString() == column.value">
          #{item.hospitalName,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_id'.toString() == column.value">
          #{item.hospitalId,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_yc_id'.toString() == column.value">
          #{item.hospitalYcId,jdbcType=VARCHAR}
        </if>
        <if test="'compensation_ratio'.toString() == column.value">
          #{item.compensationRatio,jdbcType=VARCHAR}
        </if>
        <if test="'once_paid_amount'.toString() == column.value">
          #{item.oncePaidAmount,jdbcType=DECIMAL}
        </if>
        <if test="'once_max_compensation_amount'.toString() == column.value">
          #{item.onceMaxCompensationAmount,jdbcType=DECIMAL}
        </if>
        <if test="'customer_settlement_amount'.toString() == column.value">
          #{item.customerSettlementAmount,jdbcType=DECIMAL}
        </if>
        <if test="'supplier_settlement_amount'.toString() == column.value">
          #{item.supplierSettlementAmount,jdbcType=DECIMAL}
        </if>
        <if test="'settlement_conclusion'.toString() == column.value">
          #{item.settlementConclusion,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>