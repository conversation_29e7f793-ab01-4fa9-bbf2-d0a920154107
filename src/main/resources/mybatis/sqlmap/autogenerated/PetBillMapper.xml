<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.PetBillMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.PetBillDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="attachment_id" jdbcType="BIGINT" property="attachmentId" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="bill_date" jdbcType="VARCHAR" property="billDate" />
    <result column="bill_amount" jdbcType="DECIMAL" property="billAmount" />
    <result column="bill_excluded_amount" jdbcType="DECIMAL" property="billExcludedAmount" />
    <result column="bill_reasonable_amount" jdbcType="DECIMAL" property="billReasonableAmount" />
    <result column="latest" jdbcType="BIT" property="latest" />
    <result column="modify_flag" jdbcType="BIGINT" property="modifyFlag" />
    <result column="modify_operation" jdbcType="VARCHAR" property="modifyOperation" />
    <result column="automatic" jdbcType="BIT" property="automatic" />
    <result column="item_count" jdbcType="INTEGER" property="itemCount" />
    <result column="matched_item_count" jdbcType="INTEGER" property="matchedItemCount" />
    <result column="claim_report_source" jdbcType="VARCHAR" property="claimReportSource" />
    <result column="source_model" jdbcType="VARCHAR" property="sourceModel" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, attachment_id, bill_no, bill_date, bill_amount, bill_excluded_amount, 
    bill_reasonable_amount, latest, modify_flag, modify_operation, automatic, item_count, 
    matched_item_count, claim_report_source, source_model, extra_info, creator, modifier, 
    gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.PetBillExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pet_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pet_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.PetBillDO">
    insert into pet_bill (id, report_no, attachment_id, 
      bill_no, bill_date, bill_amount, 
      bill_excluded_amount, bill_reasonable_amount, 
      latest, modify_flag, modify_operation, 
      automatic, item_count, matched_item_count, 
      claim_report_source, source_model, extra_info, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{attachmentId,jdbcType=BIGINT}, 
      #{billNo,jdbcType=VARCHAR}, #{billDate,jdbcType=VARCHAR}, #{billAmount,jdbcType=DECIMAL}, 
      #{billExcludedAmount,jdbcType=DECIMAL}, #{billReasonableAmount,jdbcType=DECIMAL}, 
      #{latest,jdbcType=BIT}, #{modifyFlag,jdbcType=BIGINT}, #{modifyOperation,jdbcType=VARCHAR}, 
      #{automatic,jdbcType=BIT}, #{itemCount,jdbcType=INTEGER}, #{matchedItemCount,jdbcType=INTEGER}, 
      #{claimReportSource,jdbcType=VARCHAR}, #{sourceModel,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.PetBillDO">
    insert into pet_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="attachmentId != null">
        attachment_id,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="billDate != null">
        bill_date,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="billExcludedAmount != null">
        bill_excluded_amount,
      </if>
      <if test="billReasonableAmount != null">
        bill_reasonable_amount,
      </if>
      <if test="latest != null">
        latest,
      </if>
      <if test="modifyFlag != null">
        modify_flag,
      </if>
      <if test="modifyOperation != null">
        modify_operation,
      </if>
      <if test="automatic != null">
        automatic,
      </if>
      <if test="itemCount != null">
        item_count,
      </if>
      <if test="matchedItemCount != null">
        matched_item_count,
      </if>
      <if test="claimReportSource != null">
        claim_report_source,
      </if>
      <if test="sourceModel != null">
        source_model,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="attachmentId != null">
        #{attachmentId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="billDate != null">
        #{billDate,jdbcType=VARCHAR},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="billExcludedAmount != null">
        #{billExcludedAmount,jdbcType=DECIMAL},
      </if>
      <if test="billReasonableAmount != null">
        #{billReasonableAmount,jdbcType=DECIMAL},
      </if>
      <if test="latest != null">
        #{latest,jdbcType=BIT},
      </if>
      <if test="modifyFlag != null">
        #{modifyFlag,jdbcType=BIGINT},
      </if>
      <if test="modifyOperation != null">
        #{modifyOperation,jdbcType=VARCHAR},
      </if>
      <if test="automatic != null">
        #{automatic,jdbcType=BIT},
      </if>
      <if test="itemCount != null">
        #{itemCount,jdbcType=INTEGER},
      </if>
      <if test="matchedItemCount != null">
        #{matchedItemCount,jdbcType=INTEGER},
      </if>
      <if test="claimReportSource != null">
        #{claimReportSource,jdbcType=VARCHAR},
      </if>
      <if test="sourceModel != null">
        #{sourceModel,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.PetBillExample" resultType="java.lang.Long">
    select count(*) from pet_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pet_bill
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentId != null">
        attachment_id = #{record.attachmentId,jdbcType=BIGINT},
      </if>
      <if test="record.billNo != null">
        bill_no = #{record.billNo,jdbcType=VARCHAR},
      </if>
      <if test="record.billDate != null">
        bill_date = #{record.billDate,jdbcType=VARCHAR},
      </if>
      <if test="record.billAmount != null">
        bill_amount = #{record.billAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.billExcludedAmount != null">
        bill_excluded_amount = #{record.billExcludedAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.billReasonableAmount != null">
        bill_reasonable_amount = #{record.billReasonableAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.latest != null">
        latest = #{record.latest,jdbcType=BIT},
      </if>
      <if test="record.modifyFlag != null">
        modify_flag = #{record.modifyFlag,jdbcType=BIGINT},
      </if>
      <if test="record.modifyOperation != null">
        modify_operation = #{record.modifyOperation,jdbcType=VARCHAR},
      </if>
      <if test="record.automatic != null">
        automatic = #{record.automatic,jdbcType=BIT},
      </if>
      <if test="record.itemCount != null">
        item_count = #{record.itemCount,jdbcType=INTEGER},
      </if>
      <if test="record.matchedItemCount != null">
        matched_item_count = #{record.matchedItemCount,jdbcType=INTEGER},
      </if>
      <if test="record.claimReportSource != null">
        claim_report_source = #{record.claimReportSource,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceModel != null">
        source_model = #{record.sourceModel,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pet_bill
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      attachment_id = #{record.attachmentId,jdbcType=BIGINT},
      bill_no = #{record.billNo,jdbcType=VARCHAR},
      bill_date = #{record.billDate,jdbcType=VARCHAR},
      bill_amount = #{record.billAmount,jdbcType=DECIMAL},
      bill_excluded_amount = #{record.billExcludedAmount,jdbcType=DECIMAL},
      bill_reasonable_amount = #{record.billReasonableAmount,jdbcType=DECIMAL},
      latest = #{record.latest,jdbcType=BIT},
      modify_flag = #{record.modifyFlag,jdbcType=BIGINT},
      modify_operation = #{record.modifyOperation,jdbcType=VARCHAR},
      automatic = #{record.automatic,jdbcType=BIT},
      item_count = #{record.itemCount,jdbcType=INTEGER},
      matched_item_count = #{record.matchedItemCount,jdbcType=INTEGER},
      claim_report_source = #{record.claimReportSource,jdbcType=VARCHAR},
      source_model = #{record.sourceModel,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.PetBillDO">
    update pet_bill
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="attachmentId != null">
        attachment_id = #{attachmentId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="billDate != null">
        bill_date = #{billDate,jdbcType=VARCHAR},
      </if>
      <if test="billAmount != null">
        bill_amount = #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="billExcludedAmount != null">
        bill_excluded_amount = #{billExcludedAmount,jdbcType=DECIMAL},
      </if>
      <if test="billReasonableAmount != null">
        bill_reasonable_amount = #{billReasonableAmount,jdbcType=DECIMAL},
      </if>
      <if test="latest != null">
        latest = #{latest,jdbcType=BIT},
      </if>
      <if test="modifyFlag != null">
        modify_flag = #{modifyFlag,jdbcType=BIGINT},
      </if>
      <if test="modifyOperation != null">
        modify_operation = #{modifyOperation,jdbcType=VARCHAR},
      </if>
      <if test="automatic != null">
        automatic = #{automatic,jdbcType=BIT},
      </if>
      <if test="itemCount != null">
        item_count = #{itemCount,jdbcType=INTEGER},
      </if>
      <if test="matchedItemCount != null">
        matched_item_count = #{matchedItemCount,jdbcType=INTEGER},
      </if>
      <if test="claimReportSource != null">
        claim_report_source = #{claimReportSource,jdbcType=VARCHAR},
      </if>
      <if test="sourceModel != null">
        source_model = #{sourceModel,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.PetBillDO">
    update pet_bill
    set report_no = #{reportNo,jdbcType=VARCHAR},
      attachment_id = #{attachmentId,jdbcType=BIGINT},
      bill_no = #{billNo,jdbcType=VARCHAR},
      bill_date = #{billDate,jdbcType=VARCHAR},
      bill_amount = #{billAmount,jdbcType=DECIMAL},
      bill_excluded_amount = #{billExcludedAmount,jdbcType=DECIMAL},
      bill_reasonable_amount = #{billReasonableAmount,jdbcType=DECIMAL},
      latest = #{latest,jdbcType=BIT},
      modify_flag = #{modifyFlag,jdbcType=BIGINT},
      modify_operation = #{modifyOperation,jdbcType=VARCHAR},
      automatic = #{automatic,jdbcType=BIT},
      item_count = #{itemCount,jdbcType=INTEGER},
      matched_item_count = #{matchedItemCount,jdbcType=INTEGER},
      claim_report_source = #{claimReportSource,jdbcType=VARCHAR},
      source_model = #{sourceModel,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into pet_bill
    (id, report_no, attachment_id, bill_no, bill_date, bill_amount, bill_excluded_amount, 
      bill_reasonable_amount, latest, modify_flag, modify_operation, automatic, item_count, 
      matched_item_count, claim_report_source, source_model, extra_info, creator, modifier, 
      gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.attachmentId,jdbcType=BIGINT}, 
        #{item.billNo,jdbcType=VARCHAR}, #{item.billDate,jdbcType=VARCHAR}, #{item.billAmount,jdbcType=DECIMAL}, 
        #{item.billExcludedAmount,jdbcType=DECIMAL}, #{item.billReasonableAmount,jdbcType=DECIMAL}, 
        #{item.latest,jdbcType=BIT}, #{item.modifyFlag,jdbcType=BIGINT}, #{item.modifyOperation,jdbcType=VARCHAR}, 
        #{item.automatic,jdbcType=BIT}, #{item.itemCount,jdbcType=INTEGER}, #{item.matchedItemCount,jdbcType=INTEGER}, 
        #{item.claimReportSource,jdbcType=VARCHAR}, #{item.sourceModel,jdbcType=VARCHAR}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into pet_bill (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_id'.toString() == column.value">
          #{item.attachmentId,jdbcType=BIGINT}
        </if>
        <if test="'bill_no'.toString() == column.value">
          #{item.billNo,jdbcType=VARCHAR}
        </if>
        <if test="'bill_date'.toString() == column.value">
          #{item.billDate,jdbcType=VARCHAR}
        </if>
        <if test="'bill_amount'.toString() == column.value">
          #{item.billAmount,jdbcType=DECIMAL}
        </if>
        <if test="'bill_excluded_amount'.toString() == column.value">
          #{item.billExcludedAmount,jdbcType=DECIMAL}
        </if>
        <if test="'bill_reasonable_amount'.toString() == column.value">
          #{item.billReasonableAmount,jdbcType=DECIMAL}
        </if>
        <if test="'latest'.toString() == column.value">
          #{item.latest,jdbcType=BIT}
        </if>
        <if test="'modify_flag'.toString() == column.value">
          #{item.modifyFlag,jdbcType=BIGINT}
        </if>
        <if test="'modify_operation'.toString() == column.value">
          #{item.modifyOperation,jdbcType=VARCHAR}
        </if>
        <if test="'automatic'.toString() == column.value">
          #{item.automatic,jdbcType=BIT}
        </if>
        <if test="'item_count'.toString() == column.value">
          #{item.itemCount,jdbcType=INTEGER}
        </if>
        <if test="'matched_item_count'.toString() == column.value">
          #{item.matchedItemCount,jdbcType=INTEGER}
        </if>
        <if test="'claim_report_source'.toString() == column.value">
          #{item.claimReportSource,jdbcType=VARCHAR}
        </if>
        <if test="'source_model'.toString() == column.value">
          #{item.sourceModel,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>