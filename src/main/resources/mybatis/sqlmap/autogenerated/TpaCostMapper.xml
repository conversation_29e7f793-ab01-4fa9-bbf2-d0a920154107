<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.TpaCostMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.TpaCostDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="hos_invoice_num" jdbcType="INTEGER" property="hosInvoiceNum" />
    <result column="no_hos_invoice_num" jdbcType="INTEGER" property="noHosInvoiceNum" />
    <result column="cooperate_method" jdbcType="TINYINT" property="cooperateMethod" />
    <result column="pay_method" jdbcType="TINYINT" property="payMethod" />
    <result column="amount" jdbcType="VARCHAR" property="amount" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, policy_no, company_id, company_name, hos_invoice_num, no_hos_invoice_num, 
    cooperate_method, pay_method, amount, extra_info, remark, is_deleted, gmt_created, 
    gmt_modified, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.TpaCostExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpa_cost
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tpa_cost
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.TpaCostDO">
    insert into tpa_cost (id, report_no, policy_no, 
      company_id, company_name, hos_invoice_num, 
      no_hos_invoice_num, cooperate_method, pay_method, 
      amount, extra_info, remark, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier)
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=BIGINT}, #{companyName,jdbcType=VARCHAR}, #{hosInvoiceNum,jdbcType=INTEGER}, 
      #{noHosInvoiceNum,jdbcType=INTEGER}, #{cooperateMethod,jdbcType=TINYINT}, #{payMethod,jdbcType=TINYINT}, 
      #{amount,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.TpaCostDO">
    insert into tpa_cost
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="hosInvoiceNum != null">
        hos_invoice_num,
      </if>
      <if test="noHosInvoiceNum != null">
        no_hos_invoice_num,
      </if>
      <if test="cooperateMethod != null">
        cooperate_method,
      </if>
      <if test="payMethod != null">
        pay_method,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="hosInvoiceNum != null">
        #{hosInvoiceNum,jdbcType=INTEGER},
      </if>
      <if test="noHosInvoiceNum != null">
        #{noHosInvoiceNum,jdbcType=INTEGER},
      </if>
      <if test="cooperateMethod != null">
        #{cooperateMethod,jdbcType=TINYINT},
      </if>
      <if test="payMethod != null">
        #{payMethod,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.TpaCostExample" resultType="java.lang.Long">
    select count(*) from tpa_cost
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tpa_cost
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.hosInvoiceNum != null">
        hos_invoice_num = #{record.hosInvoiceNum,jdbcType=INTEGER},
      </if>
      <if test="record.noHosInvoiceNum != null">
        no_hos_invoice_num = #{record.noHosInvoiceNum,jdbcType=INTEGER},
      </if>
      <if test="record.cooperateMethod != null">
        cooperate_method = #{record.cooperateMethod,jdbcType=TINYINT},
      </if>
      <if test="record.payMethod != null">
        pay_method = #{record.payMethod,jdbcType=TINYINT},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tpa_cost
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=BIGINT},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      hos_invoice_num = #{record.hosInvoiceNum,jdbcType=INTEGER},
      no_hos_invoice_num = #{record.noHosInvoiceNum,jdbcType=INTEGER},
      cooperate_method = #{record.cooperateMethod,jdbcType=TINYINT},
      pay_method = #{record.payMethod,jdbcType=TINYINT},
      amount = #{record.amount,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.TpaCostDO">
    update tpa_cost
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="hosInvoiceNum != null">
        hos_invoice_num = #{hosInvoiceNum,jdbcType=INTEGER},
      </if>
      <if test="noHosInvoiceNum != null">
        no_hos_invoice_num = #{noHosInvoiceNum,jdbcType=INTEGER},
      </if>
      <if test="cooperateMethod != null">
        cooperate_method = #{cooperateMethod,jdbcType=TINYINT},
      </if>
      <if test="payMethod != null">
        pay_method = #{payMethod,jdbcType=TINYINT},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.TpaCostDO">
    update tpa_cost
    set report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT},
      company_name = #{companyName,jdbcType=VARCHAR},
      hos_invoice_num = #{hosInvoiceNum,jdbcType=INTEGER},
      no_hos_invoice_num = #{noHosInvoiceNum,jdbcType=INTEGER},
      cooperate_method = #{cooperateMethod,jdbcType=TINYINT},
      pay_method = #{payMethod,jdbcType=TINYINT},
      amount = #{amount,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into tpa_cost
    (id, report_no, policy_no, company_id, company_name, hos_invoice_num, no_hos_invoice_num, 
      cooperate_method, pay_method, amount, extra_info, remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.companyId,jdbcType=BIGINT}, #{item.companyName,jdbcType=VARCHAR}, #{item.hosInvoiceNum,jdbcType=INTEGER}, 
        #{item.noHosInvoiceNum,jdbcType=INTEGER}, #{item.cooperateMethod,jdbcType=TINYINT}, 
        #{item.payMethod,jdbcType=TINYINT}, #{item.amount,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into tpa_cost (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'company_id'.toString() == column.value">
          #{item.companyId,jdbcType=BIGINT}
        </if>
        <if test="'company_name'.toString() == column.value">
          #{item.companyName,jdbcType=VARCHAR}
        </if>
        <if test="'hos_invoice_num'.toString() == column.value">
          #{item.hosInvoiceNum,jdbcType=INTEGER}
        </if>
        <if test="'no_hos_invoice_num'.toString() == column.value">
          #{item.noHosInvoiceNum,jdbcType=INTEGER}
        </if>
        <if test="'cooperate_method'.toString() == column.value">
          #{item.cooperateMethod,jdbcType=TINYINT}
        </if>
        <if test="'pay_method'.toString() == column.value">
          #{item.payMethod,jdbcType=TINYINT}
        </if>
        <if test="'amount'.toString() == column.value">
          #{item.amount,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>