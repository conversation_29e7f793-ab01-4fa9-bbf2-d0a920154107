<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimRiskInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimRiskInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="claim_no" jdbcType="VARCHAR" property="claimNo" />
    <result column="policy_holder_certi_no" jdbcType="VARCHAR" property="policyHolderCertiNo" />
    <result column="insurant_certi_no" jdbcType="VARCHAR" property="insurantCertiNo" />
    <result column="loss_cause" jdbcType="VARCHAR" property="lossCause" />
    <result column="accident_date" jdbcType="TIMESTAMP" property="accidentDate" />
    <result column="accident_place" jdbcType="VARCHAR" property="accidentPlace" />
    <result column="is_dqcx" jdbcType="CHAR" property="isDqcx" />
    <result column="policy_insure_date" jdbcType="TIMESTAMP" property="policyInsureDate" />
    <result column="policy_effective_date" jdbcType="TIMESTAMP" property="policyEffectiveDate" />
    <result column="policy_expiry_date" jdbcType="TIMESTAMP" property="policyExpiryDate" />
    <result column="int_cx_tb" jdbcType="BIGINT" property="intCxTb" />
    <result column="int_cx_sx" jdbcType="BIGINT" property="intCxSx" />
    <result column="is_risk_area" jdbcType="CHAR" property="isRiskArea" />
    <result column="pay_type" jdbcType="VARCHAR" property="payType" />
    <result column="fee_datetime" jdbcType="TIMESTAMP" property="feeDatetime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.zhongan.lincoln.dal.domain.ClaimRiskInfoWithBLOBs">
    <result column="hmd_response_param" jdbcType="LONGVARCHAR" property="hmdResponseParam" />
    <result column="td_response_param" jdbcType="LONGVARCHAR" property="tdResponseParam" />
    <result column="zbx_response_param" jdbcType="LONGVARCHAR" property="zbxResponseParam" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, policy_no, claim_no, policy_holder_certi_no, insurant_certi_no, loss_cause, 
    accident_date, accident_place, is_dqcx, policy_insure_date, policy_effective_date, 
    policy_expiry_date, int_cx_tb, int_cx_sx, is_risk_area, pay_type, fee_datetime, creator, 
    gmt_created, modifier, gmt_modified, is_deleted
  </sql>
  <sql id="Blob_Column_List">
    hmd_response_param, td_response_param, zbx_response_param
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskInfoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from claim_risk_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_risk_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from claim_risk_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskInfoWithBLOBs">
    insert into claim_risk_info (id, report_no, policy_no, 
      claim_no, policy_holder_certi_no, insurant_certi_no, 
      loss_cause, accident_date, accident_place, 
      is_dqcx, policy_insure_date, policy_effective_date, 
      policy_expiry_date, int_cx_tb, int_cx_sx, 
      is_risk_area, pay_type, fee_datetime, 
      creator, gmt_created, modifier, 
      gmt_modified, is_deleted, hmd_response_param, 
      td_response_param, zbx_response_param
      )
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, 
      #{claimNo,jdbcType=VARCHAR}, #{policyHolderCertiNo,jdbcType=VARCHAR}, #{insurantCertiNo,jdbcType=VARCHAR}, 
      #{lossCause,jdbcType=VARCHAR}, #{accidentDate,jdbcType=TIMESTAMP}, #{accidentPlace,jdbcType=VARCHAR}, 
      #{isDqcx,jdbcType=CHAR}, #{policyInsureDate,jdbcType=TIMESTAMP}, #{policyEffectiveDate,jdbcType=TIMESTAMP}, 
      #{policyExpiryDate,jdbcType=TIMESTAMP}, #{intCxTb,jdbcType=BIGINT}, #{intCxSx,jdbcType=BIGINT}, 
      #{isRiskArea,jdbcType=CHAR}, #{payType,jdbcType=VARCHAR}, #{feeDatetime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, sysdate(), #{modifier,jdbcType=VARCHAR}, 
      sysdate(), #{isDeleted,jdbcType=CHAR}, #{hmdResponseParam,jdbcType=LONGVARCHAR}, 
      #{tdResponseParam,jdbcType=LONGVARCHAR}, #{zbxResponseParam,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskInfoWithBLOBs">
    insert into claim_risk_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="claimNo != null">
        claim_no,
      </if>
      <if test="policyHolderCertiNo != null">
        policy_holder_certi_no,
      </if>
      <if test="insurantCertiNo != null">
        insurant_certi_no,
      </if>
      <if test="lossCause != null">
        loss_cause,
      </if>
      <if test="accidentDate != null">
        accident_date,
      </if>
      <if test="accidentPlace != null">
        accident_place,
      </if>
      <if test="isDqcx != null">
        is_dqcx,
      </if>
      <if test="policyInsureDate != null">
        policy_insure_date,
      </if>
      <if test="policyEffectiveDate != null">
        policy_effective_date,
      </if>
      <if test="policyExpiryDate != null">
        policy_expiry_date,
      </if>
      <if test="intCxTb != null">
        int_cx_tb,
      </if>
      <if test="intCxSx != null">
        int_cx_sx,
      </if>
      <if test="isRiskArea != null">
        is_risk_area,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="feeDatetime != null">
        fee_datetime,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="hmdResponseParam != null">
        hmd_response_param,
      </if>
      <if test="tdResponseParam != null">
        td_response_param,
      </if>
      <if test="zbxResponseParam != null">
        zbx_response_param,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderCertiNo != null">
        #{policyHolderCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertiNo != null">
        #{insurantCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="lossCause != null">
        #{lossCause,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        #{accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="accidentPlace != null">
        #{accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="isDqcx != null">
        #{isDqcx,jdbcType=CHAR},
      </if>
      <if test="policyInsureDate != null">
        #{policyInsureDate,jdbcType=TIMESTAMP},
      </if>
      <if test="policyEffectiveDate != null">
        #{policyEffectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="policyExpiryDate != null">
        #{policyExpiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="intCxTb != null">
        #{intCxTb,jdbcType=BIGINT},
      </if>
      <if test="intCxSx != null">
        #{intCxSx,jdbcType=BIGINT},
      </if>
      <if test="isRiskArea != null">
        #{isRiskArea,jdbcType=CHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=VARCHAR},
      </if>
      <if test="feeDatetime != null">
        #{feeDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="hmdResponseParam != null">
        #{hmdResponseParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="tdResponseParam != null">
        #{tdResponseParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="zbxResponseParam != null">
        #{zbxResponseParam,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskInfoExample" resultType="java.lang.Long">
    select count(*) from claim_risk_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_risk_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimNo != null">
        claim_no = #{record.claimNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyHolderCertiNo != null">
        policy_holder_certi_no = #{record.policyHolderCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCertiNo != null">
        insurant_certi_no = #{record.insurantCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="record.lossCause != null">
        loss_cause = #{record.lossCause,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentDate != null">
        accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accidentPlace != null">
        accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="record.isDqcx != null">
        is_dqcx = #{record.isDqcx,jdbcType=CHAR},
      </if>
      <if test="record.policyInsureDate != null">
        policy_insure_date = #{record.policyInsureDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.policyEffectiveDate != null">
        policy_effective_date = #{record.policyEffectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.policyExpiryDate != null">
        policy_expiry_date = #{record.policyExpiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.intCxTb != null">
        int_cx_tb = #{record.intCxTb,jdbcType=BIGINT},
      </if>
      <if test="record.intCxSx != null">
        int_cx_sx = #{record.intCxSx,jdbcType=BIGINT},
      </if>
      <if test="record.isRiskArea != null">
        is_risk_area = #{record.isRiskArea,jdbcType=CHAR},
      </if>
      <if test="record.payType != null">
        pay_type = #{record.payType,jdbcType=VARCHAR},
      </if>
      <if test="record.feeDatetime != null">
        fee_datetime = #{record.feeDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.hmdResponseParam != null">
        hmd_response_param = #{record.hmdResponseParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.tdResponseParam != null">
        td_response_param = #{record.tdResponseParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.zbxResponseParam != null">
        zbx_response_param = #{record.zbxResponseParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update claim_risk_info
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      claim_no = #{record.claimNo,jdbcType=VARCHAR},
      policy_holder_certi_no = #{record.policyHolderCertiNo,jdbcType=VARCHAR},
      insurant_certi_no = #{record.insurantCertiNo,jdbcType=VARCHAR},
      loss_cause = #{record.lossCause,jdbcType=VARCHAR},
      accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      is_dqcx = #{record.isDqcx,jdbcType=CHAR},
      policy_insure_date = #{record.policyInsureDate,jdbcType=TIMESTAMP},
      policy_effective_date = #{record.policyEffectiveDate,jdbcType=TIMESTAMP},
      policy_expiry_date = #{record.policyExpiryDate,jdbcType=TIMESTAMP},
      int_cx_tb = #{record.intCxTb,jdbcType=BIGINT},
      int_cx_sx = #{record.intCxSx,jdbcType=BIGINT},
      is_risk_area = #{record.isRiskArea,jdbcType=CHAR},
      pay_type = #{record.payType,jdbcType=VARCHAR},
      fee_datetime = #{record.feeDatetime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      hmd_response_param = #{record.hmdResponseParam,jdbcType=LONGVARCHAR},
      td_response_param = #{record.tdResponseParam,jdbcType=LONGVARCHAR},
      zbx_response_param = #{record.zbxResponseParam,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_risk_info
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      claim_no = #{record.claimNo,jdbcType=VARCHAR},
      policy_holder_certi_no = #{record.policyHolderCertiNo,jdbcType=VARCHAR},
      insurant_certi_no = #{record.insurantCertiNo,jdbcType=VARCHAR},
      loss_cause = #{record.lossCause,jdbcType=VARCHAR},
      accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      is_dqcx = #{record.isDqcx,jdbcType=CHAR},
      policy_insure_date = #{record.policyInsureDate,jdbcType=TIMESTAMP},
      policy_effective_date = #{record.policyEffectiveDate,jdbcType=TIMESTAMP},
      policy_expiry_date = #{record.policyExpiryDate,jdbcType=TIMESTAMP},
      int_cx_tb = #{record.intCxTb,jdbcType=BIGINT},
      int_cx_sx = #{record.intCxSx,jdbcType=BIGINT},
      is_risk_area = #{record.isRiskArea,jdbcType=CHAR},
      pay_type = #{record.payType,jdbcType=VARCHAR},
      fee_datetime = #{record.feeDatetime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskInfoWithBLOBs">
    update claim_risk_info
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        claim_no = #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderCertiNo != null">
        policy_holder_certi_no = #{policyHolderCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertiNo != null">
        insurant_certi_no = #{insurantCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="lossCause != null">
        loss_cause = #{lossCause,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="accidentPlace != null">
        accident_place = #{accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="isDqcx != null">
        is_dqcx = #{isDqcx,jdbcType=CHAR},
      </if>
      <if test="policyInsureDate != null">
        policy_insure_date = #{policyInsureDate,jdbcType=TIMESTAMP},
      </if>
      <if test="policyEffectiveDate != null">
        policy_effective_date = #{policyEffectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="policyExpiryDate != null">
        policy_expiry_date = #{policyExpiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="intCxTb != null">
        int_cx_tb = #{intCxTb,jdbcType=BIGINT},
      </if>
      <if test="intCxSx != null">
        int_cx_sx = #{intCxSx,jdbcType=BIGINT},
      </if>
      <if test="isRiskArea != null">
        is_risk_area = #{isRiskArea,jdbcType=CHAR},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="feeDatetime != null">
        fee_datetime = #{feeDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="hmdResponseParam != null">
        hmd_response_param = #{hmdResponseParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="tdResponseParam != null">
        td_response_param = #{tdResponseParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="zbxResponseParam != null">
        zbx_response_param = #{zbxResponseParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskInfoWithBLOBs">
    update claim_risk_info
    set report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      claim_no = #{claimNo,jdbcType=VARCHAR},
      policy_holder_certi_no = #{policyHolderCertiNo,jdbcType=VARCHAR},
      insurant_certi_no = #{insurantCertiNo,jdbcType=VARCHAR},
      loss_cause = #{lossCause,jdbcType=VARCHAR},
      accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      accident_place = #{accidentPlace,jdbcType=VARCHAR},
      is_dqcx = #{isDqcx,jdbcType=CHAR},
      policy_insure_date = #{policyInsureDate,jdbcType=TIMESTAMP},
      policy_effective_date = #{policyEffectiveDate,jdbcType=TIMESTAMP},
      policy_expiry_date = #{policyExpiryDate,jdbcType=TIMESTAMP},
      int_cx_tb = #{intCxTb,jdbcType=BIGINT},
      int_cx_sx = #{intCxSx,jdbcType=BIGINT},
      is_risk_area = #{isRiskArea,jdbcType=CHAR},
      pay_type = #{payType,jdbcType=VARCHAR},
      fee_datetime = #{feeDatetime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR},
      hmd_response_param = #{hmdResponseParam,jdbcType=LONGVARCHAR},
      td_response_param = #{tdResponseParam,jdbcType=LONGVARCHAR},
      zbx_response_param = #{zbxResponseParam,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimRiskInfoDO">
    update claim_risk_info
    set report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      claim_no = #{claimNo,jdbcType=VARCHAR},
      policy_holder_certi_no = #{policyHolderCertiNo,jdbcType=VARCHAR},
      insurant_certi_no = #{insurantCertiNo,jdbcType=VARCHAR},
      loss_cause = #{lossCause,jdbcType=VARCHAR},
      accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      accident_place = #{accidentPlace,jdbcType=VARCHAR},
      is_dqcx = #{isDqcx,jdbcType=CHAR},
      policy_insure_date = #{policyInsureDate,jdbcType=TIMESTAMP},
      policy_effective_date = #{policyEffectiveDate,jdbcType=TIMESTAMP},
      policy_expiry_date = #{policyExpiryDate,jdbcType=TIMESTAMP},
      int_cx_tb = #{intCxTb,jdbcType=BIGINT},
      int_cx_sx = #{intCxSx,jdbcType=BIGINT},
      is_risk_area = #{isRiskArea,jdbcType=CHAR},
      pay_type = #{payType,jdbcType=VARCHAR},
      fee_datetime = #{feeDatetime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_risk_info
    (id, report_no, policy_no, claim_no, policy_holder_certi_no, insurant_certi_no, loss_cause, 
      accident_date, accident_place, is_dqcx, policy_insure_date, policy_effective_date, 
      policy_expiry_date, int_cx_tb, int_cx_sx, is_risk_area, pay_type, fee_datetime, 
      creator, gmt_created, modifier, gmt_modified, is_deleted, hmd_response_param, td_response_param, 
      zbx_response_param)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.claimNo,jdbcType=VARCHAR}, #{item.policyHolderCertiNo,jdbcType=VARCHAR}, 
        #{item.insurantCertiNo,jdbcType=VARCHAR}, #{item.lossCause,jdbcType=VARCHAR}, #{item.accidentDate,jdbcType=TIMESTAMP}, 
        #{item.accidentPlace,jdbcType=VARCHAR}, #{item.isDqcx,jdbcType=CHAR}, #{item.policyInsureDate,jdbcType=TIMESTAMP}, 
        #{item.policyEffectiveDate,jdbcType=TIMESTAMP}, #{item.policyExpiryDate,jdbcType=TIMESTAMP}, 
        #{item.intCxTb,jdbcType=BIGINT}, #{item.intCxSx,jdbcType=BIGINT}, #{item.isRiskArea,jdbcType=CHAR}, 
        #{item.payType,jdbcType=VARCHAR}, #{item.feeDatetime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.hmdResponseParam,jdbcType=LONGVARCHAR}, 
        #{item.tdResponseParam,jdbcType=LONGVARCHAR}, #{item.zbxResponseParam,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_risk_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'claim_no'.toString() == column.value">
          #{item.claimNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_holder_certi_no'.toString() == column.value">
          #{item.policyHolderCertiNo,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_certi_no'.toString() == column.value">
          #{item.insurantCertiNo,jdbcType=VARCHAR}
        </if>
        <if test="'loss_cause'.toString() == column.value">
          #{item.lossCause,jdbcType=VARCHAR}
        </if>
        <if test="'accident_date'.toString() == column.value">
          #{item.accidentDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'accident_place'.toString() == column.value">
          #{item.accidentPlace,jdbcType=VARCHAR}
        </if>
        <if test="'is_dqcx'.toString() == column.value">
          #{item.isDqcx,jdbcType=CHAR}
        </if>
        <if test="'policy_insure_date'.toString() == column.value">
          #{item.policyInsureDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'policy_effective_date'.toString() == column.value">
          #{item.policyEffectiveDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'policy_expiry_date'.toString() == column.value">
          #{item.policyExpiryDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'int_cx_tb'.toString() == column.value">
          #{item.intCxTb,jdbcType=BIGINT}
        </if>
        <if test="'int_cx_sx'.toString() == column.value">
          #{item.intCxSx,jdbcType=BIGINT}
        </if>
        <if test="'is_risk_area'.toString() == column.value">
          #{item.isRiskArea,jdbcType=CHAR}
        </if>
        <if test="'pay_type'.toString() == column.value">
          #{item.payType,jdbcType=VARCHAR}
        </if>
        <if test="'fee_datetime'.toString() == column.value">
          #{item.feeDatetime,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'hmd_response_param'.toString() == column.value">
          #{item.hmdResponseParam,jdbcType=LONGVARCHAR}
        </if>
        <if test="'td_response_param'.toString() == column.value">
          #{item.tdResponseParam,jdbcType=LONGVARCHAR}
        </if>
        <if test="'zbx_response_param'.toString() == column.value">
          #{item.zbxResponseParam,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>