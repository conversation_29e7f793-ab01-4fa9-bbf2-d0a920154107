<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AutoClaimMvcOverviewMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AutoClaimMvcOverviewDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="associate_batch_no" jdbcType="VARCHAR" property="associateBatchNo" />
    <result column="batch_start_time" jdbcType="TIMESTAMP" property="batchStartTime" />
    <result column="batch_end_time" jdbcType="TIMESTAMP" property="batchEndTime" />
    <result column="claim_count" jdbcType="INTEGER" property="claimCount" />
    <result column="settlement_info" jdbcType="VARCHAR" property="settlementInfo" />
    <result column="change_rule_info" jdbcType="VARCHAR" property="changeRuleInfo" />
    <result column="claim_data_info" jdbcType="VARCHAR" property="claimDataInfo" />
    <result column="full_link_auto_per" jdbcType="VARCHAR" property="fullLinkAutoPer" />
    <result column="auto_paid_close_per" jdbcType="VARCHAR" property="autoPaidClosePer" />
    <result column="average_paid_amount" jdbcType="VARCHAR" property="averagePaidAmount" />
    <result column="asset_loss" jdbcType="VARCHAR" property="assetLoss" />
    <result column="asset_loss_per" jdbcType="VARCHAR" property="assetLossPer" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, batch_no, associate_batch_no, batch_start_time, batch_end_time, claim_count, 
    settlement_info, change_rule_info, claim_data_info, full_link_auto_per, auto_paid_close_per, 
    average_paid_amount, asset_loss, asset_loss_per, extra_info, gmt_created, gmt_modified, 
    creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AutoClaimMvcOverviewExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auto_claim_mvc_overview
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auto_claim_mvc_overview
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AutoClaimMvcOverviewDO">
    insert into auto_claim_mvc_overview (id, batch_no, associate_batch_no, 
      batch_start_time, batch_end_time, claim_count, 
      settlement_info, change_rule_info, claim_data_info, 
      full_link_auto_per, auto_paid_close_per, average_paid_amount, 
      asset_loss, asset_loss_per, extra_info, 
      gmt_created, gmt_modified, creator, 
      modifier, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{batchNo,jdbcType=VARCHAR}, #{associateBatchNo,jdbcType=VARCHAR}, 
      #{batchStartTime,jdbcType=TIMESTAMP}, #{batchEndTime,jdbcType=TIMESTAMP}, #{claimCount,jdbcType=INTEGER}, 
      #{settlementInfo,jdbcType=VARCHAR}, #{changeRuleInfo,jdbcType=VARCHAR}, #{claimDataInfo,jdbcType=VARCHAR}, 
      #{fullLinkAutoPer,jdbcType=VARCHAR}, #{autoPaidClosePer,jdbcType=VARCHAR}, #{averagePaidAmount,jdbcType=VARCHAR}, 
      #{assetLoss,jdbcType=VARCHAR}, #{assetLossPer,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AutoClaimMvcOverviewDO">
    insert into auto_claim_mvc_overview
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="associateBatchNo != null">
        associate_batch_no,
      </if>
      <if test="batchStartTime != null">
        batch_start_time,
      </if>
      <if test="batchEndTime != null">
        batch_end_time,
      </if>
      <if test="claimCount != null">
        claim_count,
      </if>
      <if test="settlementInfo != null">
        settlement_info,
      </if>
      <if test="changeRuleInfo != null">
        change_rule_info,
      </if>
      <if test="claimDataInfo != null">
        claim_data_info,
      </if>
      <if test="fullLinkAutoPer != null">
        full_link_auto_per,
      </if>
      <if test="autoPaidClosePer != null">
        auto_paid_close_per,
      </if>
      <if test="averagePaidAmount != null">
        average_paid_amount,
      </if>
      <if test="assetLoss != null">
        asset_loss,
      </if>
      <if test="assetLossPer != null">
        asset_loss_per,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="associateBatchNo != null">
        #{associateBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="batchStartTime != null">
        #{batchStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchEndTime != null">
        #{batchEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="claimCount != null">
        #{claimCount,jdbcType=INTEGER},
      </if>
      <if test="settlementInfo != null">
        #{settlementInfo,jdbcType=VARCHAR},
      </if>
      <if test="changeRuleInfo != null">
        #{changeRuleInfo,jdbcType=VARCHAR},
      </if>
      <if test="claimDataInfo != null">
        #{claimDataInfo,jdbcType=VARCHAR},
      </if>
      <if test="fullLinkAutoPer != null">
        #{fullLinkAutoPer,jdbcType=VARCHAR},
      </if>
      <if test="autoPaidClosePer != null">
        #{autoPaidClosePer,jdbcType=VARCHAR},
      </if>
      <if test="averagePaidAmount != null">
        #{averagePaidAmount,jdbcType=VARCHAR},
      </if>
      <if test="assetLoss != null">
        #{assetLoss,jdbcType=VARCHAR},
      </if>
      <if test="assetLossPer != null">
        #{assetLossPer,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AutoClaimMvcOverviewExample" resultType="java.lang.Long">
    select count(*) from auto_claim_mvc_overview
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update auto_claim_mvc_overview
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.associateBatchNo != null">
        associate_batch_no = #{record.associateBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchStartTime != null">
        batch_start_time = #{record.batchStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.batchEndTime != null">
        batch_end_time = #{record.batchEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.claimCount != null">
        claim_count = #{record.claimCount,jdbcType=INTEGER},
      </if>
      <if test="record.settlementInfo != null">
        settlement_info = #{record.settlementInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.changeRuleInfo != null">
        change_rule_info = #{record.changeRuleInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimDataInfo != null">
        claim_data_info = #{record.claimDataInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.fullLinkAutoPer != null">
        full_link_auto_per = #{record.fullLinkAutoPer,jdbcType=VARCHAR},
      </if>
      <if test="record.autoPaidClosePer != null">
        auto_paid_close_per = #{record.autoPaidClosePer,jdbcType=VARCHAR},
      </if>
      <if test="record.averagePaidAmount != null">
        average_paid_amount = #{record.averagePaidAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.assetLoss != null">
        asset_loss = #{record.assetLoss,jdbcType=VARCHAR},
      </if>
      <if test="record.assetLossPer != null">
        asset_loss_per = #{record.assetLossPer,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update auto_claim_mvc_overview
    set id = #{record.id,jdbcType=BIGINT},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      associate_batch_no = #{record.associateBatchNo,jdbcType=VARCHAR},
      batch_start_time = #{record.batchStartTime,jdbcType=TIMESTAMP},
      batch_end_time = #{record.batchEndTime,jdbcType=TIMESTAMP},
      claim_count = #{record.claimCount,jdbcType=INTEGER},
      settlement_info = #{record.settlementInfo,jdbcType=VARCHAR},
      change_rule_info = #{record.changeRuleInfo,jdbcType=VARCHAR},
      claim_data_info = #{record.claimDataInfo,jdbcType=VARCHAR},
      full_link_auto_per = #{record.fullLinkAutoPer,jdbcType=VARCHAR},
      auto_paid_close_per = #{record.autoPaidClosePer,jdbcType=VARCHAR},
      average_paid_amount = #{record.averagePaidAmount,jdbcType=VARCHAR},
      asset_loss = #{record.assetLoss,jdbcType=VARCHAR},
      asset_loss_per = #{record.assetLossPer,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AutoClaimMvcOverviewDO">
    update auto_claim_mvc_overview
    <set>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="associateBatchNo != null">
        associate_batch_no = #{associateBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="batchStartTime != null">
        batch_start_time = #{batchStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="batchEndTime != null">
        batch_end_time = #{batchEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="claimCount != null">
        claim_count = #{claimCount,jdbcType=INTEGER},
      </if>
      <if test="settlementInfo != null">
        settlement_info = #{settlementInfo,jdbcType=VARCHAR},
      </if>
      <if test="changeRuleInfo != null">
        change_rule_info = #{changeRuleInfo,jdbcType=VARCHAR},
      </if>
      <if test="claimDataInfo != null">
        claim_data_info = #{claimDataInfo,jdbcType=VARCHAR},
      </if>
      <if test="fullLinkAutoPer != null">
        full_link_auto_per = #{fullLinkAutoPer,jdbcType=VARCHAR},
      </if>
      <if test="autoPaidClosePer != null">
        auto_paid_close_per = #{autoPaidClosePer,jdbcType=VARCHAR},
      </if>
      <if test="averagePaidAmount != null">
        average_paid_amount = #{averagePaidAmount,jdbcType=VARCHAR},
      </if>
      <if test="assetLoss != null">
        asset_loss = #{assetLoss,jdbcType=VARCHAR},
      </if>
      <if test="assetLossPer != null">
        asset_loss_per = #{assetLossPer,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AutoClaimMvcOverviewDO">
    update auto_claim_mvc_overview
    set batch_no = #{batchNo,jdbcType=VARCHAR},
      associate_batch_no = #{associateBatchNo,jdbcType=VARCHAR},
      batch_start_time = #{batchStartTime,jdbcType=TIMESTAMP},
      batch_end_time = #{batchEndTime,jdbcType=TIMESTAMP},
      claim_count = #{claimCount,jdbcType=INTEGER},
      settlement_info = #{settlementInfo,jdbcType=VARCHAR},
      change_rule_info = #{changeRuleInfo,jdbcType=VARCHAR},
      claim_data_info = #{claimDataInfo,jdbcType=VARCHAR},
      full_link_auto_per = #{fullLinkAutoPer,jdbcType=VARCHAR},
      auto_paid_close_per = #{autoPaidClosePer,jdbcType=VARCHAR},
      average_paid_amount = #{averagePaidAmount,jdbcType=VARCHAR},
      asset_loss = #{assetLoss,jdbcType=VARCHAR},
      asset_loss_per = #{assetLossPer,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into auto_claim_mvc_overview
    (id, batch_no, associate_batch_no, batch_start_time, batch_end_time, claim_count, 
      settlement_info, change_rule_info, claim_data_info, full_link_auto_per, auto_paid_close_per, 
      average_paid_amount, asset_loss, asset_loss_per, extra_info, gmt_created, gmt_modified, 
      creator, modifier, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.batchNo,jdbcType=VARCHAR}, #{item.associateBatchNo,jdbcType=VARCHAR}, 
        #{item.batchStartTime,jdbcType=TIMESTAMP}, #{item.batchEndTime,jdbcType=TIMESTAMP}, 
        #{item.claimCount,jdbcType=INTEGER}, #{item.settlementInfo,jdbcType=VARCHAR}, #{item.changeRuleInfo,jdbcType=VARCHAR}, 
        #{item.claimDataInfo,jdbcType=VARCHAR}, #{item.fullLinkAutoPer,jdbcType=VARCHAR}, 
        #{item.autoPaidClosePer,jdbcType=VARCHAR}, #{item.averagePaidAmount,jdbcType=VARCHAR}, 
        #{item.assetLoss,jdbcType=VARCHAR}, #{item.assetLossPer,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into auto_claim_mvc_overview (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'batch_no'.toString() == column.value">
          #{item.batchNo,jdbcType=VARCHAR}
        </if>
        <if test="'associate_batch_no'.toString() == column.value">
          #{item.associateBatchNo,jdbcType=VARCHAR}
        </if>
        <if test="'batch_start_time'.toString() == column.value">
          #{item.batchStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'batch_end_time'.toString() == column.value">
          #{item.batchEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'claim_count'.toString() == column.value">
          #{item.claimCount,jdbcType=INTEGER}
        </if>
        <if test="'settlement_info'.toString() == column.value">
          #{item.settlementInfo,jdbcType=VARCHAR}
        </if>
        <if test="'change_rule_info'.toString() == column.value">
          #{item.changeRuleInfo,jdbcType=VARCHAR}
        </if>
        <if test="'claim_data_info'.toString() == column.value">
          #{item.claimDataInfo,jdbcType=VARCHAR}
        </if>
        <if test="'full_link_auto_per'.toString() == column.value">
          #{item.fullLinkAutoPer,jdbcType=VARCHAR}
        </if>
        <if test="'auto_paid_close_per'.toString() == column.value">
          #{item.autoPaidClosePer,jdbcType=VARCHAR}
        </if>
        <if test="'average_paid_amount'.toString() == column.value">
          #{item.averagePaidAmount,jdbcType=VARCHAR}
        </if>
        <if test="'asset_loss'.toString() == column.value">
          #{item.assetLoss,jdbcType=VARCHAR}
        </if>
        <if test="'asset_loss_per'.toString() == column.value">
          #{item.assetLossPer,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>