<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimPayExecutionMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimPayExecutionDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="payment_id" jdbcType="BIGINT" property="paymentId" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="request_body" jdbcType="VARCHAR" property="requestBody" />
    <result column="response_body" jdbcType="VARCHAR" property="responseBody" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="pay_state" jdbcType="TINYINT" property="payState" />
    <result column="exec_reason" jdbcType="TINYINT" property="execReason" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, payment_id, biz_no, state, request_body, response_body, end_date, extra_info, 
    remark, is_deleted, gmt_created, gmt_modified, creator, modifier, pay_state, exec_reason
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayExecutionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_payment_execution
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_payment_execution
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayExecutionDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_payment_execution (payment_id, biz_no, state, 
      request_body, response_body, end_date, 
      extra_info, remark, is_deleted, 
      gmt_created, gmt_modified, creator, 
      modifier, pay_state, exec_reason
      )
    values (#{paymentId,jdbcType=BIGINT}, #{bizNo,jdbcType=VARCHAR}, #{state,jdbcType=TINYINT}, 
      #{requestBody,jdbcType=VARCHAR}, #{responseBody,jdbcType=VARCHAR}, #{endDate,jdbcType=TIMESTAMP}, 
      #{extraInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{payState,jdbcType=TINYINT}, #{execReason,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayExecutionDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_payment_execution
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="paymentId != null">
        payment_id,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="requestBody != null">
        request_body,
      </if>
      <if test="responseBody != null">
        response_body,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="payState != null">
        pay_state,
      </if>
      <if test="execReason != null">
        exec_reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="paymentId != null">
        #{paymentId,jdbcType=BIGINT},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=TINYINT},
      </if>
      <if test="requestBody != null">
        #{requestBody,jdbcType=VARCHAR},
      </if>
      <if test="responseBody != null">
        #{responseBody,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        #{payState,jdbcType=TINYINT},
      </if>
      <if test="execReason != null">
        #{execReason,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayExecutionExample" resultType="java.lang.Long">
    select count(*) from claim_payment_execution
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_payment_execution
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.paymentId != null">
        payment_id = #{record.paymentId,jdbcType=BIGINT},
      </if>
      <if test="record.bizNo != null">
        biz_no = #{record.bizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=TINYINT},
      </if>
      <if test="record.requestBody != null">
        request_body = #{record.requestBody,jdbcType=VARCHAR},
      </if>
      <if test="record.responseBody != null">
        response_body = #{record.responseBody,jdbcType=VARCHAR},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.payState != null">
        pay_state = #{record.payState,jdbcType=TINYINT},
      </if>
      <if test="record.execReason != null">
        exec_reason = #{record.execReason,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_payment_execution
    set id = #{record.id,jdbcType=BIGINT},
      payment_id = #{record.paymentId,jdbcType=BIGINT},
      biz_no = #{record.bizNo,jdbcType=VARCHAR},
      state = #{record.state,jdbcType=TINYINT},
      request_body = #{record.requestBody,jdbcType=VARCHAR},
      response_body = #{record.responseBody,jdbcType=VARCHAR},
      end_date = #{record.endDate,jdbcType=TIMESTAMP},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      pay_state = #{record.payState,jdbcType=TINYINT},
      exec_reason = #{record.execReason,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayExecutionDO">
    update claim_payment_execution
    <set>
      <if test="paymentId != null">
        payment_id = #{paymentId,jdbcType=BIGINT},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=TINYINT},
      </if>
      <if test="requestBody != null">
        request_body = #{requestBody,jdbcType=VARCHAR},
      </if>
      <if test="responseBody != null">
        response_body = #{responseBody,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        pay_state = #{payState,jdbcType=TINYINT},
      </if>
      <if test="execReason != null">
        exec_reason = #{execReason,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayExecutionDO">
    update claim_payment_execution
    set payment_id = #{paymentId,jdbcType=BIGINT},
      biz_no = #{bizNo,jdbcType=VARCHAR},
      state = #{state,jdbcType=TINYINT},
      request_body = #{requestBody,jdbcType=VARCHAR},
      response_body = #{responseBody,jdbcType=VARCHAR},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      pay_state = #{payState,jdbcType=TINYINT},
      exec_reason = #{execReason,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into claim_payment_execution
    (payment_id, biz_no, state, request_body, response_body, end_date, extra_info, remark, 
      is_deleted, gmt_created, gmt_modified, creator, modifier, pay_state, exec_reason
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.paymentId,jdbcType=BIGINT}, #{item.bizNo,jdbcType=VARCHAR}, #{item.state,jdbcType=TINYINT}, 
        #{item.requestBody,jdbcType=VARCHAR}, #{item.responseBody,jdbcType=VARCHAR}, #{item.endDate,jdbcType=TIMESTAMP}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.payState,jdbcType=TINYINT}, 
        #{item.execReason,jdbcType=TINYINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into claim_payment_execution (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'payment_id'.toString() == column.value">
          #{item.paymentId,jdbcType=BIGINT}
        </if>
        <if test="'biz_no'.toString() == column.value">
          #{item.bizNo,jdbcType=VARCHAR}
        </if>
        <if test="'state'.toString() == column.value">
          #{item.state,jdbcType=TINYINT}
        </if>
        <if test="'request_body'.toString() == column.value">
          #{item.requestBody,jdbcType=VARCHAR}
        </if>
        <if test="'response_body'.toString() == column.value">
          #{item.responseBody,jdbcType=VARCHAR}
        </if>
        <if test="'end_date'.toString() == column.value">
          #{item.endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'pay_state'.toString() == column.value">
          #{item.payState,jdbcType=TINYINT}
        </if>
        <if test="'exec_reason'.toString() == column.value">
          #{item.execReason,jdbcType=TINYINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>