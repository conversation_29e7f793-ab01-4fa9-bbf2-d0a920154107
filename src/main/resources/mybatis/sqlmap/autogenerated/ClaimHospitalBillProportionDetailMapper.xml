<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimHospitalBillProportionDetailMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimHospitalBillProportionDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cost_type" jdbcType="VARCHAR" property="costType" />
    <result column="total_amount" jdbcType="VARCHAR" property="totalAmount" />
    <result column="category_amount" jdbcType="VARCHAR" property="categoryAmount" />
    <result column="percentage" jdbcType="VARCHAR" property="percentage" />
    <result column="claim_hospital_id" jdbcType="BIGINT" property="claimHospitalId" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, cost_type, total_amount, category_amount, percentage, claim_hospital_id, report_no, 
    creator, gmt_created, modifier, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalBillProportionDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_hospital_bill_proportion_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_hospital_bill_proportion_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalBillProportionDetailDO">
    insert into claim_hospital_bill_proportion_detail (id, cost_type, total_amount, 
      category_amount, percentage, claim_hospital_id, 
      report_no, creator, gmt_created, 
      modifier, gmt_modified, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{costType,jdbcType=VARCHAR}, #{totalAmount,jdbcType=VARCHAR}, 
      #{categoryAmount,jdbcType=VARCHAR}, #{percentage,jdbcType=VARCHAR}, #{claimHospitalId,jdbcType=BIGINT}, 
      #{reportNo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, sysdate(), 
      #{modifier,jdbcType=VARCHAR}, sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalBillProportionDetailDO">
    insert into claim_hospital_bill_proportion_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="costType != null">
        cost_type,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="categoryAmount != null">
        category_amount,
      </if>
      <if test="percentage != null">
        percentage,
      </if>
      <if test="claimHospitalId != null">
        claim_hospital_id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="costType != null">
        #{costType,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=VARCHAR},
      </if>
      <if test="categoryAmount != null">
        #{categoryAmount,jdbcType=VARCHAR},
      </if>
      <if test="percentage != null">
        #{percentage,jdbcType=VARCHAR},
      </if>
      <if test="claimHospitalId != null">
        #{claimHospitalId,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalBillProportionDetailExample" resultType="java.lang.Long">
    select count(*) from claim_hospital_bill_proportion_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_hospital_bill_proportion_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.costType != null">
        cost_type = #{record.costType,jdbcType=VARCHAR},
      </if>
      <if test="record.totalAmount != null">
        total_amount = #{record.totalAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryAmount != null">
        category_amount = #{record.categoryAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.percentage != null">
        percentage = #{record.percentage,jdbcType=VARCHAR},
      </if>
      <if test="record.claimHospitalId != null">
        claim_hospital_id = #{record.claimHospitalId,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_hospital_bill_proportion_detail
    set id = #{record.id,jdbcType=BIGINT},
      cost_type = #{record.costType,jdbcType=VARCHAR},
      total_amount = #{record.totalAmount,jdbcType=VARCHAR},
      category_amount = #{record.categoryAmount,jdbcType=VARCHAR},
      percentage = #{record.percentage,jdbcType=VARCHAR},
      claim_hospital_id = #{record.claimHospitalId,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalBillProportionDetailDO">
    update claim_hospital_bill_proportion_detail
    <set>
      <if test="costType != null">
        cost_type = #{costType,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=VARCHAR},
      </if>
      <if test="categoryAmount != null">
        category_amount = #{categoryAmount,jdbcType=VARCHAR},
      </if>
      <if test="percentage != null">
        percentage = #{percentage,jdbcType=VARCHAR},
      </if>
      <if test="claimHospitalId != null">
        claim_hospital_id = #{claimHospitalId,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalBillProportionDetailDO">
    update claim_hospital_bill_proportion_detail
    set cost_type = #{costType,jdbcType=VARCHAR},
      total_amount = #{totalAmount,jdbcType=VARCHAR},
      category_amount = #{categoryAmount,jdbcType=VARCHAR},
      percentage = #{percentage,jdbcType=VARCHAR},
      claim_hospital_id = #{claimHospitalId,jdbcType=BIGINT},
      report_no = #{reportNo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_hospital_bill_proportion_detail
    (id, cost_type, total_amount, category_amount, percentage, claim_hospital_id, report_no, 
      creator, gmt_created, modifier, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.costType,jdbcType=VARCHAR}, #{item.totalAmount,jdbcType=VARCHAR}, 
        #{item.categoryAmount,jdbcType=VARCHAR}, #{item.percentage,jdbcType=VARCHAR}, #{item.claimHospitalId,jdbcType=BIGINT}, 
        #{item.reportNo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_hospital_bill_proportion_detail (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'cost_type'.toString() == column.value">
          #{item.costType,jdbcType=VARCHAR}
        </if>
        <if test="'total_amount'.toString() == column.value">
          #{item.totalAmount,jdbcType=VARCHAR}
        </if>
        <if test="'category_amount'.toString() == column.value">
          #{item.categoryAmount,jdbcType=VARCHAR}
        </if>
        <if test="'percentage'.toString() == column.value">
          #{item.percentage,jdbcType=VARCHAR}
        </if>
        <if test="'claim_hospital_id'.toString() == column.value">
          #{item.claimHospitalId,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>