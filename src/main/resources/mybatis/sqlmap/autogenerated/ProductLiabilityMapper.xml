<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ProductLiabilityMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ProductLiabilityDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="campaign_def_id" jdbcType="BIGINT" property="campaignDefId" />
    <result column="campaign_full_name" jdbcType="VARCHAR" property="campaignFullName" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="package_full_name" jdbcType="VARCHAR" property="packageFullName" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="liability_code" jdbcType="VARCHAR" property="liabilityCode" />
    <result column="liability_name" jdbcType="VARCHAR" property="liabilityName" />
    <result column="online_date" jdbcType="TIMESTAMP" property="onlineDate" />
    <result column="start_sale_date" jdbcType="TIMESTAMP" property="startSaleDate" />
    <result column="end_sale_date" jdbcType="TIMESTAMP" property="endSaleDate" />
    <result column="is_effective" jdbcType="VARCHAR" property="isEffective" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="config_value" jdbcType="VARCHAR" property="configValue" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, campaign_def_id, campaign_full_name, package_def_id, package_full_name, product_name, 
    liability_code, liability_name, online_date, start_sale_date, end_sale_date, is_effective, 
    is_deleted, gmt_created, gmt_modified, creator, modifier, config_value
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ProductLiabilityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_product_sync
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_product_sync
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ProductLiabilityDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cargo_product_sync (campaign_def_id, campaign_full_name, package_def_id, 
      package_full_name, product_name, liability_code, 
      liability_name, online_date, start_sale_date, 
      end_sale_date, is_effective, is_deleted, 
      gmt_created, gmt_modified, creator, 
      modifier, config_value)
    values (#{campaignDefId,jdbcType=BIGINT}, #{campaignFullName,jdbcType=VARCHAR}, #{packageDefId,jdbcType=BIGINT}, 
      #{packageFullName,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{liabilityCode,jdbcType=VARCHAR}, 
      #{liabilityName,jdbcType=VARCHAR}, #{onlineDate,jdbcType=TIMESTAMP}, #{startSaleDate,jdbcType=TIMESTAMP}, 
      #{endSaleDate,jdbcType=TIMESTAMP}, #{isEffective,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{configValue,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ProductLiabilityDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cargo_product_sync
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="campaignFullName != null">
        campaign_full_name,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="packageFullName != null">
        package_full_name,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="liabilityCode != null">
        liability_code,
      </if>
      <if test="liabilityName != null">
        liability_name,
      </if>
      <if test="onlineDate != null">
        online_date,
      </if>
      <if test="startSaleDate != null">
        start_sale_date,
      </if>
      <if test="endSaleDate != null">
        end_sale_date,
      </if>
      <if test="isEffective != null">
        is_effective,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="configValue != null">
        config_value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="campaignFullName != null">
        #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="packageFullName != null">
        #{packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityCode != null">
        #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="liabilityName != null">
        #{liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="onlineDate != null">
        #{onlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="startSaleDate != null">
        #{startSaleDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endSaleDate != null">
        #{endSaleDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isEffective != null">
        #{isEffective,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null">
        #{configValue,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ProductLiabilityExample" resultType="java.lang.Long">
    select count(*) from cargo_product_sync
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_product_sync
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="record.campaignFullName != null">
        campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.packageFullName != null">
        package_full_name = #{record.packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityCode != null">
        liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityName != null">
        liability_name = #{record.liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="record.onlineDate != null">
        online_date = #{record.onlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startSaleDate != null">
        start_sale_date = #{record.startSaleDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endSaleDate != null">
        end_sale_date = #{record.endSaleDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isEffective != null">
        is_effective = #{record.isEffective,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.configValue != null">
        config_value = #{record.configValue,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_product_sync
    set id = #{record.id,jdbcType=BIGINT},
      campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      package_full_name = #{record.packageFullName,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      liability_name = #{record.liabilityName,jdbcType=VARCHAR},
      online_date = #{record.onlineDate,jdbcType=TIMESTAMP},
      start_sale_date = #{record.startSaleDate,jdbcType=TIMESTAMP},
      end_sale_date = #{record.endSaleDate,jdbcType=TIMESTAMP},
      is_effective = #{record.isEffective,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      config_value = #{record.configValue,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ProductLiabilityDO">
    update cargo_product_sync
    <set>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="campaignFullName != null">
        campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="packageFullName != null">
        package_full_name = #{packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityCode != null">
        liability_code = #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="liabilityName != null">
        liability_name = #{liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="onlineDate != null">
        online_date = #{onlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="startSaleDate != null">
        start_sale_date = #{startSaleDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endSaleDate != null">
        end_sale_date = #{endSaleDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isEffective != null">
        is_effective = #{isEffective,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="configValue != null">
        config_value = #{configValue,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ProductLiabilityDO">
    update cargo_product_sync
    set campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      package_def_id = #{packageDefId,jdbcType=BIGINT},
      package_full_name = #{packageFullName,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      liability_code = #{liabilityCode,jdbcType=VARCHAR},
      liability_name = #{liabilityName,jdbcType=VARCHAR},
      online_date = #{onlineDate,jdbcType=TIMESTAMP},
      start_sale_date = #{startSaleDate,jdbcType=TIMESTAMP},
      end_sale_date = #{endSaleDate,jdbcType=TIMESTAMP},
      is_effective = #{isEffective,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      config_value = #{configValue,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into cargo_product_sync
    (campaign_def_id, campaign_full_name, package_def_id, package_full_name, product_name, 
      liability_code, liability_name, online_date, start_sale_date, end_sale_date, is_effective, 
      is_deleted, gmt_created, gmt_modified, creator, modifier, config_value)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.campaignDefId,jdbcType=BIGINT}, #{item.campaignFullName,jdbcType=VARCHAR}, 
        #{item.packageDefId,jdbcType=BIGINT}, #{item.packageFullName,jdbcType=VARCHAR}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.liabilityCode,jdbcType=VARCHAR}, #{item.liabilityName,jdbcType=VARCHAR}, 
        #{item.onlineDate,jdbcType=TIMESTAMP}, #{item.startSaleDate,jdbcType=TIMESTAMP}, 
        #{item.endSaleDate,jdbcType=TIMESTAMP}, #{item.isEffective,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.configValue,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into cargo_product_sync (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=BIGINT}
        </if>
        <if test="'campaign_full_name'.toString() == column.value">
          #{item.campaignFullName,jdbcType=VARCHAR}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'package_full_name'.toString() == column.value">
          #{item.packageFullName,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'liability_code'.toString() == column.value">
          #{item.liabilityCode,jdbcType=VARCHAR}
        </if>
        <if test="'liability_name'.toString() == column.value">
          #{item.liabilityName,jdbcType=VARCHAR}
        </if>
        <if test="'online_date'.toString() == column.value">
          #{item.onlineDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'start_sale_date'.toString() == column.value">
          #{item.startSaleDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'end_sale_date'.toString() == column.value">
          #{item.endSaleDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_effective'.toString() == column.value">
          #{item.isEffective,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'config_value'.toString() == column.value">
          #{item.configValue,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>