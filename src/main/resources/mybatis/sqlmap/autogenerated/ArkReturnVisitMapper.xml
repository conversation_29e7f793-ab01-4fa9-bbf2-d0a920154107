<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ArkReturnVisitMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ArkReturnVisitDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_insurant" jdbcType="VARCHAR" property="policyInsurant" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="visit_name" jdbcType="VARCHAR" property="visitName" />
    <result column="visit_phone" jdbcType="VARCHAR" property="visitPhone" />
    <result column="cargo_claim_bank_info_id" jdbcType="BIGINT" property="cargoClaimBankInfoId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="plan_time" jdbcType="TIMESTAMP" property="planTime" />
    <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
    <result column="visit_status" jdbcType="VARCHAR" property="visitStatus" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="tpa_user_id" jdbcType="BIGINT" property="tpaUserId" />
    <result column="ark_visit_id" jdbcType="BIGINT" property="arkVisitId" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_no, report_no, policy_insurant, product_name, visit_name, visit_phone, 
    cargo_claim_bank_info_id, remark, plan_time, visit_type, visit_status, auditor, tpa_user_id, 
    ark_visit_id, extra_info, creator, gmt_created, modifier, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ArkReturnVisitExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ark_return_visit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ark_return_visit
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ArkReturnVisitDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ark_return_visit (policy_no, report_no, policy_insurant, 
      product_name, visit_name, visit_phone, 
      cargo_claim_bank_info_id, remark, plan_time, 
      visit_type, visit_status, auditor, 
      tpa_user_id, ark_visit_id, extra_info, 
      creator, gmt_created, modifier, 
      gmt_modified, is_deleted)
    values (#{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{policyInsurant,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{visitName,jdbcType=VARCHAR}, #{visitPhone,jdbcType=VARCHAR}, 
      #{cargoClaimBankInfoId,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, #{planTime,jdbcType=TIMESTAMP}, 
      #{visitType,jdbcType=VARCHAR}, #{visitStatus,jdbcType=VARCHAR}, #{auditor,jdbcType=VARCHAR}, 
      #{tpaUserId,jdbcType=BIGINT}, #{arkVisitId,jdbcType=BIGINT}, #{extraInfo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, sysdate(), #{modifier,jdbcType=VARCHAR}, 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ArkReturnVisitDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ark_return_visit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyInsurant != null">
        policy_insurant,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="visitName != null">
        visit_name,
      </if>
      <if test="visitPhone != null">
        visit_phone,
      </if>
      <if test="cargoClaimBankInfoId != null">
        cargo_claim_bank_info_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="planTime != null">
        plan_time,
      </if>
      <if test="visitType != null">
        visit_type,
      </if>
      <if test="visitStatus != null">
        visit_status,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="tpaUserId != null">
        tpa_user_id,
      </if>
      <if test="arkVisitId != null">
        ark_visit_id,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyInsurant != null">
        #{policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="visitName != null">
        #{visitName,jdbcType=VARCHAR},
      </if>
      <if test="visitPhone != null">
        #{visitPhone,jdbcType=VARCHAR},
      </if>
      <if test="cargoClaimBankInfoId != null">
        #{cargoClaimBankInfoId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="planTime != null">
        #{planTime,jdbcType=TIMESTAMP},
      </if>
      <if test="visitType != null">
        #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="visitStatus != null">
        #{visitStatus,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="tpaUserId != null">
        #{tpaUserId,jdbcType=BIGINT},
      </if>
      <if test="arkVisitId != null">
        #{arkVisitId,jdbcType=BIGINT},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ArkReturnVisitExample" resultType="java.lang.Long">
    select count(*) from ark_return_visit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ark_return_visit
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyInsurant != null">
        policy_insurant = #{record.policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.visitName != null">
        visit_name = #{record.visitName,jdbcType=VARCHAR},
      </if>
      <if test="record.visitPhone != null">
        visit_phone = #{record.visitPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.cargoClaimBankInfoId != null">
        cargo_claim_bank_info_id = #{record.cargoClaimBankInfoId,jdbcType=BIGINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.planTime != null">
        plan_time = #{record.planTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.visitType != null">
        visit_type = #{record.visitType,jdbcType=VARCHAR},
      </if>
      <if test="record.visitStatus != null">
        visit_status = #{record.visitStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.auditor != null">
        auditor = #{record.auditor,jdbcType=VARCHAR},
      </if>
      <if test="record.tpaUserId != null">
        tpa_user_id = #{record.tpaUserId,jdbcType=BIGINT},
      </if>
      <if test="record.arkVisitId != null">
        ark_visit_id = #{record.arkVisitId,jdbcType=BIGINT},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ark_return_visit
    set id = #{record.id,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_insurant = #{record.policyInsurant,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      visit_name = #{record.visitName,jdbcType=VARCHAR},
      visit_phone = #{record.visitPhone,jdbcType=VARCHAR},
      cargo_claim_bank_info_id = #{record.cargoClaimBankInfoId,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      plan_time = #{record.planTime,jdbcType=TIMESTAMP},
      visit_type = #{record.visitType,jdbcType=VARCHAR},
      visit_status = #{record.visitStatus,jdbcType=VARCHAR},
      auditor = #{record.auditor,jdbcType=VARCHAR},
      tpa_user_id = #{record.tpaUserId,jdbcType=BIGINT},
      ark_visit_id = #{record.arkVisitId,jdbcType=BIGINT},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ArkReturnVisitDO">
    update ark_return_visit
    <set>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyInsurant != null">
        policy_insurant = #{policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="visitName != null">
        visit_name = #{visitName,jdbcType=VARCHAR},
      </if>
      <if test="visitPhone != null">
        visit_phone = #{visitPhone,jdbcType=VARCHAR},
      </if>
      <if test="cargoClaimBankInfoId != null">
        cargo_claim_bank_info_id = #{cargoClaimBankInfoId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="planTime != null">
        plan_time = #{planTime,jdbcType=TIMESTAMP},
      </if>
      <if test="visitType != null">
        visit_type = #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="visitStatus != null">
        visit_status = #{visitStatus,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="tpaUserId != null">
        tpa_user_id = #{tpaUserId,jdbcType=BIGINT},
      </if>
      <if test="arkVisitId != null">
        ark_visit_id = #{arkVisitId,jdbcType=BIGINT},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ArkReturnVisitDO">
    update ark_return_visit
    set policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      policy_insurant = #{policyInsurant,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      visit_name = #{visitName,jdbcType=VARCHAR},
      visit_phone = #{visitPhone,jdbcType=VARCHAR},
      cargo_claim_bank_info_id = #{cargoClaimBankInfoId,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      plan_time = #{planTime,jdbcType=TIMESTAMP},
      visit_type = #{visitType,jdbcType=VARCHAR},
      visit_status = #{visitStatus,jdbcType=VARCHAR},
      auditor = #{auditor,jdbcType=VARCHAR},
      tpa_user_id = #{tpaUserId,jdbcType=BIGINT},
      ark_visit_id = #{arkVisitId,jdbcType=BIGINT},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into ark_return_visit
    (policy_no, report_no, policy_insurant, product_name, visit_name, visit_phone, cargo_claim_bank_info_id, 
      remark, plan_time, visit_type, visit_status, auditor, tpa_user_id, ark_visit_id, 
      extra_info, creator, gmt_created, modifier, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.policyInsurant,jdbcType=VARCHAR}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.visitName,jdbcType=VARCHAR}, #{item.visitPhone,jdbcType=VARCHAR}, 
        #{item.cargoClaimBankInfoId,jdbcType=BIGINT}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.planTime,jdbcType=TIMESTAMP}, #{item.visitType,jdbcType=VARCHAR}, #{item.visitStatus,jdbcType=VARCHAR}, 
        #{item.auditor,jdbcType=VARCHAR}, #{item.tpaUserId,jdbcType=BIGINT}, #{item.arkVisitId,jdbcType=BIGINT}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into ark_return_visit (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_insurant'.toString() == column.value">
          #{item.policyInsurant,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'visit_name'.toString() == column.value">
          #{item.visitName,jdbcType=VARCHAR}
        </if>
        <if test="'visit_phone'.toString() == column.value">
          #{item.visitPhone,jdbcType=VARCHAR}
        </if>
        <if test="'cargo_claim_bank_info_id'.toString() == column.value">
          #{item.cargoClaimBankInfoId,jdbcType=BIGINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'plan_time'.toString() == column.value">
          #{item.planTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'visit_type'.toString() == column.value">
          #{item.visitType,jdbcType=VARCHAR}
        </if>
        <if test="'visit_status'.toString() == column.value">
          #{item.visitStatus,jdbcType=VARCHAR}
        </if>
        <if test="'auditor'.toString() == column.value">
          #{item.auditor,jdbcType=VARCHAR}
        </if>
        <if test="'tpa_user_id'.toString() == column.value">
          #{item.tpaUserId,jdbcType=BIGINT}
        </if>
        <if test="'ark_visit_id'.toString() == column.value">
          #{item.arkVisitId,jdbcType=BIGINT}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>