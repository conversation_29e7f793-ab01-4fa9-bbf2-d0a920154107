<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.TcReconciliationDataMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.TcReconciliationDataDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="insurant_user_name" jdbcType="VARCHAR" property="insurantUserName" />
    <result column="insurant_certi_no" jdbcType="VARCHAR" property="insurantCertiNo" />
    <result column="travel_no" jdbcType="VARCHAR" property="travelNo" />
    <result column="req_date" jdbcType="VARCHAR" property="reqDate" />
    <result column="claim_amount" jdbcType="VARCHAR" property="claimAmount" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_upload_date" jdbcType="VARCHAR" property="fileUploadDate" />
    <result column="file_upload_url" jdbcType="VARCHAR" property="fileUploadUrl" />
    <result column="plan_start_date" jdbcType="VARCHAR" property="planStartDate" />
    <result column="plan_end_date" jdbcType="VARCHAR" property="planEndDate" />
    <result column="end_date" jdbcType="VARCHAR" property="endDate" />
    <result column="start_date" jdbcType="VARCHAR" property="startDate" />
    <result column="departure_city" jdbcType="VARCHAR" property="departureCity" />
    <result column="arrival_city" jdbcType="VARCHAR" property="arrivalCity" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_no, insurant_user_name, insurant_certi_no, travel_no, req_date, claim_amount, 
    file_name, file_upload_date, file_upload_url, plan_start_date, plan_end_date, end_date, 
    start_date, departure_city, arrival_city, type, remark, creator, gmt_created, modifier, 
    gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.TcReconciliationDataExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tc_reconciliation_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tc_reconciliation_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.TcReconciliationDataDO">
    insert into tc_reconciliation_data (id, policy_no, insurant_user_name, 
      insurant_certi_no, travel_no, req_date, 
      claim_amount, file_name, file_upload_date, 
      file_upload_url, plan_start_date, plan_end_date, 
      end_date, start_date, departure_city, 
      arrival_city, type, remark, 
      creator, gmt_created, modifier, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, #{insurantUserName,jdbcType=VARCHAR}, 
      #{insurantCertiNo,jdbcType=VARCHAR}, #{travelNo,jdbcType=VARCHAR}, #{reqDate,jdbcType=VARCHAR}, 
      #{claimAmount,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{fileUploadDate,jdbcType=VARCHAR}, 
      #{fileUploadUrl,jdbcType=VARCHAR}, #{planStartDate,jdbcType=VARCHAR}, #{planEndDate,jdbcType=VARCHAR}, 
      #{endDate,jdbcType=VARCHAR}, #{startDate,jdbcType=VARCHAR}, #{departureCity,jdbcType=VARCHAR}, 
      #{arrivalCity,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, sysdate(), #{modifier,jdbcType=VARCHAR}, 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.TcReconciliationDataDO">
    insert into tc_reconciliation_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="insurantUserName != null">
        insurant_user_name,
      </if>
      <if test="insurantCertiNo != null">
        insurant_certi_no,
      </if>
      <if test="travelNo != null">
        travel_no,
      </if>
      <if test="reqDate != null">
        req_date,
      </if>
      <if test="claimAmount != null">
        claim_amount,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileUploadDate != null">
        file_upload_date,
      </if>
      <if test="fileUploadUrl != null">
        file_upload_url,
      </if>
      <if test="planStartDate != null">
        plan_start_date,
      </if>
      <if test="planEndDate != null">
        plan_end_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="departureCity != null">
        departure_city,
      </if>
      <if test="arrivalCity != null">
        arrival_city,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantUserName != null">
        #{insurantUserName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertiNo != null">
        #{insurantCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="travelNo != null">
        #{travelNo,jdbcType=VARCHAR},
      </if>
      <if test="reqDate != null">
        #{reqDate,jdbcType=VARCHAR},
      </if>
      <if test="claimAmount != null">
        #{claimAmount,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUploadDate != null">
        #{fileUploadDate,jdbcType=VARCHAR},
      </if>
      <if test="fileUploadUrl != null">
        #{fileUploadUrl,jdbcType=VARCHAR},
      </if>
      <if test="planStartDate != null">
        #{planStartDate,jdbcType=VARCHAR},
      </if>
      <if test="planEndDate != null">
        #{planEndDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="departureCity != null">
        #{departureCity,jdbcType=VARCHAR},
      </if>
      <if test="arrivalCity != null">
        #{arrivalCity,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.TcReconciliationDataExample" resultType="java.lang.Long">
    select count(*) from tc_reconciliation_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tc_reconciliation_data
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantUserName != null">
        insurant_user_name = #{record.insurantUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCertiNo != null">
        insurant_certi_no = #{record.insurantCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="record.travelNo != null">
        travel_no = #{record.travelNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reqDate != null">
        req_date = #{record.reqDate,jdbcType=VARCHAR},
      </if>
      <if test="record.claimAmount != null">
        claim_amount = #{record.claimAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileUploadDate != null">
        file_upload_date = #{record.fileUploadDate,jdbcType=VARCHAR},
      </if>
      <if test="record.fileUploadUrl != null">
        file_upload_url = #{record.fileUploadUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.planStartDate != null">
        plan_start_date = #{record.planStartDate,jdbcType=VARCHAR},
      </if>
      <if test="record.planEndDate != null">
        plan_end_date = #{record.planEndDate,jdbcType=VARCHAR},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=VARCHAR},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=VARCHAR},
      </if>
      <if test="record.departureCity != null">
        departure_city = #{record.departureCity,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivalCity != null">
        arrival_city = #{record.arrivalCity,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tc_reconciliation_data
    set id = #{record.id,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      insurant_user_name = #{record.insurantUserName,jdbcType=VARCHAR},
      insurant_certi_no = #{record.insurantCertiNo,jdbcType=VARCHAR},
      travel_no = #{record.travelNo,jdbcType=VARCHAR},
      req_date = #{record.reqDate,jdbcType=VARCHAR},
      claim_amount = #{record.claimAmount,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      file_upload_date = #{record.fileUploadDate,jdbcType=VARCHAR},
      file_upload_url = #{record.fileUploadUrl,jdbcType=VARCHAR},
      plan_start_date = #{record.planStartDate,jdbcType=VARCHAR},
      plan_end_date = #{record.planEndDate,jdbcType=VARCHAR},
      end_date = #{record.endDate,jdbcType=VARCHAR},
      start_date = #{record.startDate,jdbcType=VARCHAR},
      departure_city = #{record.departureCity,jdbcType=VARCHAR},
      arrival_city = #{record.arrivalCity,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.TcReconciliationDataDO">
    update tc_reconciliation_data
    <set>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantUserName != null">
        insurant_user_name = #{insurantUserName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertiNo != null">
        insurant_certi_no = #{insurantCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="travelNo != null">
        travel_no = #{travelNo,jdbcType=VARCHAR},
      </if>
      <if test="reqDate != null">
        req_date = #{reqDate,jdbcType=VARCHAR},
      </if>
      <if test="claimAmount != null">
        claim_amount = #{claimAmount,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUploadDate != null">
        file_upload_date = #{fileUploadDate,jdbcType=VARCHAR},
      </if>
      <if test="fileUploadUrl != null">
        file_upload_url = #{fileUploadUrl,jdbcType=VARCHAR},
      </if>
      <if test="planStartDate != null">
        plan_start_date = #{planStartDate,jdbcType=VARCHAR},
      </if>
      <if test="planEndDate != null">
        plan_end_date = #{planEndDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="departureCity != null">
        departure_city = #{departureCity,jdbcType=VARCHAR},
      </if>
      <if test="arrivalCity != null">
        arrival_city = #{arrivalCity,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.TcReconciliationDataDO">
    update tc_reconciliation_data
    set policy_no = #{policyNo,jdbcType=VARCHAR},
      insurant_user_name = #{insurantUserName,jdbcType=VARCHAR},
      insurant_certi_no = #{insurantCertiNo,jdbcType=VARCHAR},
      travel_no = #{travelNo,jdbcType=VARCHAR},
      req_date = #{reqDate,jdbcType=VARCHAR},
      claim_amount = #{claimAmount,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_upload_date = #{fileUploadDate,jdbcType=VARCHAR},
      file_upload_url = #{fileUploadUrl,jdbcType=VARCHAR},
      plan_start_date = #{planStartDate,jdbcType=VARCHAR},
      plan_end_date = #{planEndDate,jdbcType=VARCHAR},
      end_date = #{endDate,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=VARCHAR},
      departure_city = #{departureCity,jdbcType=VARCHAR},
      arrival_city = #{arrivalCity,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into tc_reconciliation_data
    (id, policy_no, insurant_user_name, insurant_certi_no, travel_no, req_date, claim_amount, 
      file_name, file_upload_date, file_upload_url, plan_start_date, plan_end_date, end_date, 
      start_date, departure_city, arrival_city, type, remark, creator, gmt_created, modifier, 
      gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, #{item.insurantUserName,jdbcType=VARCHAR}, 
        #{item.insurantCertiNo,jdbcType=VARCHAR}, #{item.travelNo,jdbcType=VARCHAR}, #{item.reqDate,jdbcType=VARCHAR}, 
        #{item.claimAmount,jdbcType=VARCHAR}, #{item.fileName,jdbcType=VARCHAR}, #{item.fileUploadDate,jdbcType=VARCHAR}, 
        #{item.fileUploadUrl,jdbcType=VARCHAR}, #{item.planStartDate,jdbcType=VARCHAR}, 
        #{item.planEndDate,jdbcType=VARCHAR}, #{item.endDate,jdbcType=VARCHAR}, #{item.startDate,jdbcType=VARCHAR}, 
        #{item.departureCity,jdbcType=VARCHAR}, #{item.arrivalCity,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into tc_reconciliation_data (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_user_name'.toString() == column.value">
          #{item.insurantUserName,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_certi_no'.toString() == column.value">
          #{item.insurantCertiNo,jdbcType=VARCHAR}
        </if>
        <if test="'travel_no'.toString() == column.value">
          #{item.travelNo,jdbcType=VARCHAR}
        </if>
        <if test="'req_date'.toString() == column.value">
          #{item.reqDate,jdbcType=VARCHAR}
        </if>
        <if test="'claim_amount'.toString() == column.value">
          #{item.claimAmount,jdbcType=VARCHAR}
        </if>
        <if test="'file_name'.toString() == column.value">
          #{item.fileName,jdbcType=VARCHAR}
        </if>
        <if test="'file_upload_date'.toString() == column.value">
          #{item.fileUploadDate,jdbcType=VARCHAR}
        </if>
        <if test="'file_upload_url'.toString() == column.value">
          #{item.fileUploadUrl,jdbcType=VARCHAR}
        </if>
        <if test="'plan_start_date'.toString() == column.value">
          #{item.planStartDate,jdbcType=VARCHAR}
        </if>
        <if test="'plan_end_date'.toString() == column.value">
          #{item.planEndDate,jdbcType=VARCHAR}
        </if>
        <if test="'end_date'.toString() == column.value">
          #{item.endDate,jdbcType=VARCHAR}
        </if>
        <if test="'start_date'.toString() == column.value">
          #{item.startDate,jdbcType=VARCHAR}
        </if>
        <if test="'departure_city'.toString() == column.value">
          #{item.departureCity,jdbcType=VARCHAR}
        </if>
        <if test="'arrival_city'.toString() == column.value">
          #{item.arrivalCity,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>