<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AutoSettlementRiskInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AutoSettlementRiskInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="risk_label" jdbcType="VARCHAR" property="riskLabel" />
    <result column="exclude_expense" jdbcType="VARCHAR" property="excludeExpense" />
    <result column="risk_level" jdbcType="INTEGER" property="riskLevel" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="suggestion" jdbcType="VARCHAR" property="suggestion" />
    <result column="is_right" jdbcType="TINYINT" property="isRight" />
    <result column="detract_amount" jdbcType="DECIMAL" property="detractAmount" />
    <result column="risk_name" jdbcType="VARCHAR" property="riskName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="rule_hit_status" jdbcType="VARCHAR" property="ruleHitStatus" />
    <result column="source" jdbcType="VARCHAR" property="source" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, risk_label, exclude_expense, risk_level, extra_info, remark, is_deleted, 
    gmt_created, gmt_modified, creator, modifier, suggestion, is_right, detract_amount, 
    risk_name, description, rule_hit_status, source
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementRiskInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auto_settlement_risk_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auto_settlement_risk_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementRiskInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into auto_settlement_risk_info (id, report_no, risk_label, 
      exclude_expense, risk_level, extra_info, 
      remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, 
      suggestion, is_right, detract_amount, 
      risk_name, description, rule_hit_status, 
      source)
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{riskLabel,jdbcType=VARCHAR}, 
      #{excludeExpense,jdbcType=VARCHAR}, #{riskLevel,jdbcType=INTEGER}, #{extraInfo,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{suggestion,jdbcType=VARCHAR}, #{isRight,jdbcType=TINYINT}, #{detractAmount,jdbcType=DECIMAL}, 
      #{riskName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{ruleHitStatus,jdbcType=VARCHAR}, 
      #{source,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementRiskInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into auto_settlement_risk_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="riskLabel != null">
        risk_label,
      </if>
      <if test="excludeExpense != null">
        exclude_expense,
      </if>
      <if test="riskLevel != null">
        risk_level,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="suggestion != null">
        suggestion,
      </if>
      <if test="isRight != null">
        is_right,
      </if>
      <if test="detractAmount != null">
        detract_amount,
      </if>
      <if test="riskName != null">
        risk_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="ruleHitStatus != null">
        rule_hit_status,
      </if>
      <if test="source != null">
        source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="riskLabel != null">
        #{riskLabel,jdbcType=VARCHAR},
      </if>
      <if test="excludeExpense != null">
        #{excludeExpense,jdbcType=VARCHAR},
      </if>
      <if test="riskLevel != null">
        #{riskLevel,jdbcType=INTEGER},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="suggestion != null">
        #{suggestion,jdbcType=VARCHAR},
      </if>
      <if test="isRight != null">
        #{isRight,jdbcType=TINYINT},
      </if>
      <if test="detractAmount != null">
        #{detractAmount,jdbcType=DECIMAL},
      </if>
      <if test="riskName != null">
        #{riskName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="ruleHitStatus != null">
        #{ruleHitStatus,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementRiskInfoExample" resultType="java.lang.Long">
    select count(*) from auto_settlement_risk_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update auto_settlement_risk_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.riskLabel != null">
        risk_label = #{record.riskLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.excludeExpense != null">
        exclude_expense = #{record.excludeExpense,jdbcType=VARCHAR},
      </if>
      <if test="record.riskLevel != null">
        risk_level = #{record.riskLevel,jdbcType=INTEGER},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.suggestion != null">
        suggestion = #{record.suggestion,jdbcType=VARCHAR},
      </if>
      <if test="record.isRight != null">
        is_right = #{record.isRight,jdbcType=TINYINT},
      </if>
      <if test="record.detractAmount != null">
        detract_amount = #{record.detractAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.riskName != null">
        risk_name = #{record.riskName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleHitStatus != null">
        rule_hit_status = #{record.ruleHitStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update auto_settlement_risk_info
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      risk_label = #{record.riskLabel,jdbcType=VARCHAR},
      exclude_expense = #{record.excludeExpense,jdbcType=VARCHAR},
      risk_level = #{record.riskLevel,jdbcType=INTEGER},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      suggestion = #{record.suggestion,jdbcType=VARCHAR},
      is_right = #{record.isRight,jdbcType=TINYINT},
      detract_amount = #{record.detractAmount,jdbcType=DECIMAL},
      risk_name = #{record.riskName,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      rule_hit_status = #{record.ruleHitStatus,jdbcType=VARCHAR},
      source = #{record.source,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementRiskInfoDO">
    update auto_settlement_risk_info
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="riskLabel != null">
        risk_label = #{riskLabel,jdbcType=VARCHAR},
      </if>
      <if test="excludeExpense != null">
        exclude_expense = #{excludeExpense,jdbcType=VARCHAR},
      </if>
      <if test="riskLevel != null">
        risk_level = #{riskLevel,jdbcType=INTEGER},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="suggestion != null">
        suggestion = #{suggestion,jdbcType=VARCHAR},
      </if>
      <if test="isRight != null">
        is_right = #{isRight,jdbcType=TINYINT},
      </if>
      <if test="detractAmount != null">
        detract_amount = #{detractAmount,jdbcType=DECIMAL},
      </if>
      <if test="riskName != null">
        risk_name = #{riskName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="ruleHitStatus != null">
        rule_hit_status = #{ruleHitStatus,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementRiskInfoDO">
    update auto_settlement_risk_info
    set report_no = #{reportNo,jdbcType=VARCHAR},
      risk_label = #{riskLabel,jdbcType=VARCHAR},
      exclude_expense = #{excludeExpense,jdbcType=VARCHAR},
      risk_level = #{riskLevel,jdbcType=INTEGER},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      suggestion = #{suggestion,jdbcType=VARCHAR},
      is_right = #{isRight,jdbcType=TINYINT},
      detract_amount = #{detractAmount,jdbcType=DECIMAL},
      risk_name = #{riskName,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      rule_hit_status = #{ruleHitStatus,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into auto_settlement_risk_info
    (id, report_no, risk_label, exclude_expense, risk_level, extra_info, remark, is_deleted, 
      gmt_created, gmt_modified, creator, modifier, suggestion, is_right, detract_amount, 
      risk_name, description, rule_hit_status, source)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.riskLabel,jdbcType=VARCHAR}, 
        #{item.excludeExpense,jdbcType=VARCHAR}, #{item.riskLevel,jdbcType=INTEGER}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.suggestion,jdbcType=VARCHAR}, #{item.isRight,jdbcType=TINYINT}, #{item.detractAmount,jdbcType=DECIMAL}, 
        #{item.riskName,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.ruleHitStatus,jdbcType=VARCHAR}, 
        #{item.source,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into auto_settlement_risk_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'risk_label'.toString() == column.value">
          #{item.riskLabel,jdbcType=VARCHAR}
        </if>
        <if test="'exclude_expense'.toString() == column.value">
          #{item.excludeExpense,jdbcType=VARCHAR}
        </if>
        <if test="'risk_level'.toString() == column.value">
          #{item.riskLevel,jdbcType=INTEGER}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'suggestion'.toString() == column.value">
          #{item.suggestion,jdbcType=VARCHAR}
        </if>
        <if test="'is_right'.toString() == column.value">
          #{item.isRight,jdbcType=TINYINT}
        </if>
        <if test="'detract_amount'.toString() == column.value">
          #{item.detractAmount,jdbcType=DECIMAL}
        </if>
        <if test="'risk_name'.toString() == column.value">
          #{item.riskName,jdbcType=VARCHAR}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'rule_hit_status'.toString() == column.value">
          #{item.ruleHitStatus,jdbcType=VARCHAR}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>