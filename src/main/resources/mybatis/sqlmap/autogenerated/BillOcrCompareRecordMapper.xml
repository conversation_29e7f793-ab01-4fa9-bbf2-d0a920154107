<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.BillOcrCompareRecordMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.BillOcrCompareRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="attachment_id" jdbcType="VARCHAR" property="attachmentId" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl" />
    <result column="ocr_json" jdbcType="VARCHAR" property="ocrJson" />
    <result column="correct_json" jdbcType="VARCHAR" property="correctJson" />
    <result column="hos_correct" jdbcType="TINYINT" property="hosCorrect" />
    <result column="amount_correct" jdbcType="TINYINT" property="amountCorrect" />
    <result column="bill_num_correct" jdbcType="TINYINT" property="billNumCorrect" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="invoice_pic_type" jdbcType="TINYINT" property="invoicePicType" />
    <result column="enter_end_time" jdbcType="TIMESTAMP" property="enterEndTime" />
    <result column="enter_start_time" jdbcType="TIMESTAMP" property="enterStartTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="has_detail" jdbcType="TINYINT" property="hasDetail" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="req_num" jdbcType="TINYINT" property="reqNum" />
    <result column="info_lack" jdbcType="CHAR" property="infoLack" />
    <result column="compare_json" jdbcType="VARCHAR" property="compareJson" />
    <result column="province" jdbcType="VARCHAR" property="province" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, attachment_id, report_no, attachment_url, ocr_json, correct_json, hos_correct, 
    amount_correct, bill_num_correct, source, is_deleted, gmt_created, gmt_modified, 
    creator, modifier, invoice_pic_type, enter_end_time, enter_start_time, remark, has_detail, 
    state, req_num, info_lack, compare_json, province
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bill_ocr_compare_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_ocr_compare_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareRecordDO">
    insert into bill_ocr_compare_record (id, attachment_id, report_no, 
      attachment_url, ocr_json, correct_json, 
      hos_correct, amount_correct, bill_num_correct, 
      source, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, 
      invoice_pic_type, enter_end_time, enter_start_time, 
      remark, has_detail, state, 
      req_num, info_lack, compare_json, 
      province)
    values (#{id,jdbcType=BIGINT}, #{attachmentId,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{attachmentUrl,jdbcType=VARCHAR}, #{ocrJson,jdbcType=VARCHAR}, #{correctJson,jdbcType=VARCHAR}, 
      #{hosCorrect,jdbcType=TINYINT}, #{amountCorrect,jdbcType=TINYINT}, #{billNumCorrect,jdbcType=TINYINT}, 
      #{source,jdbcType=TINYINT}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{invoicePicType,jdbcType=TINYINT}, #{enterEndTime,jdbcType=TIMESTAMP}, #{enterStartTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{hasDetail,jdbcType=TINYINT}, #{state,jdbcType=TINYINT}, 
      #{reqNum,jdbcType=TINYINT}, #{infoLack,jdbcType=CHAR}, #{compareJson,jdbcType=VARCHAR}, 
      #{province,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareRecordDO">
    insert into bill_ocr_compare_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="attachmentId != null">
        attachment_id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="attachmentUrl != null">
        attachment_url,
      </if>
      <if test="ocrJson != null">
        ocr_json,
      </if>
      <if test="correctJson != null">
        correct_json,
      </if>
      <if test="hosCorrect != null">
        hos_correct,
      </if>
      <if test="amountCorrect != null">
        amount_correct,
      </if>
      <if test="billNumCorrect != null">
        bill_num_correct,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="invoicePicType != null">
        invoice_pic_type,
      </if>
      <if test="enterEndTime != null">
        enter_end_time,
      </if>
      <if test="enterStartTime != null">
        enter_start_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="hasDetail != null">
        has_detail,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="reqNum != null">
        req_num,
      </if>
      <if test="infoLack != null">
        info_lack,
      </if>
      <if test="compareJson != null">
        compare_json,
      </if>
      <if test="province != null">
        province,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="attachmentId != null">
        #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null">
        #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="ocrJson != null">
        #{ocrJson,jdbcType=VARCHAR},
      </if>
      <if test="correctJson != null">
        #{correctJson,jdbcType=VARCHAR},
      </if>
      <if test="hosCorrect != null">
        #{hosCorrect,jdbcType=TINYINT},
      </if>
      <if test="amountCorrect != null">
        #{amountCorrect,jdbcType=TINYINT},
      </if>
      <if test="billNumCorrect != null">
        #{billNumCorrect,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="invoicePicType != null">
        #{invoicePicType,jdbcType=TINYINT},
      </if>
      <if test="enterEndTime != null">
        #{enterEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enterStartTime != null">
        #{enterStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="hasDetail != null">
        #{hasDetail,jdbcType=TINYINT},
      </if>
      <if test="state != null">
        #{state,jdbcType=TINYINT},
      </if>
      <if test="reqNum != null">
        #{reqNum,jdbcType=TINYINT},
      </if>
      <if test="infoLack != null">
        #{infoLack,jdbcType=CHAR},
      </if>
      <if test="compareJson != null">
        #{compareJson,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareRecordExample" resultType="java.lang.Long">
    select count(*) from bill_ocr_compare_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bill_ocr_compare_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.attachmentId != null">
        attachment_id = #{record.attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentUrl != null">
        attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.ocrJson != null">
        ocr_json = #{record.ocrJson,jdbcType=VARCHAR},
      </if>
      <if test="record.correctJson != null">
        correct_json = #{record.correctJson,jdbcType=VARCHAR},
      </if>
      <if test="record.hosCorrect != null">
        hos_correct = #{record.hosCorrect,jdbcType=TINYINT},
      </if>
      <if test="record.amountCorrect != null">
        amount_correct = #{record.amountCorrect,jdbcType=TINYINT},
      </if>
      <if test="record.billNumCorrect != null">
        bill_num_correct = #{record.billNumCorrect,jdbcType=TINYINT},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.invoicePicType != null">
        invoice_pic_type = #{record.invoicePicType,jdbcType=TINYINT},
      </if>
      <if test="record.enterEndTime != null">
        enter_end_time = #{record.enterEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.enterStartTime != null">
        enter_start_time = #{record.enterStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.hasDetail != null">
        has_detail = #{record.hasDetail,jdbcType=TINYINT},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=TINYINT},
      </if>
      <if test="record.reqNum != null">
        req_num = #{record.reqNum,jdbcType=TINYINT},
      </if>
      <if test="record.infoLack != null">
        info_lack = #{record.infoLack,jdbcType=CHAR},
      </if>
      <if test="record.compareJson != null">
        compare_json = #{record.compareJson,jdbcType=VARCHAR},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bill_ocr_compare_record
    set id = #{record.id,jdbcType=BIGINT},
      attachment_id = #{record.attachmentId,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      ocr_json = #{record.ocrJson,jdbcType=VARCHAR},
      correct_json = #{record.correctJson,jdbcType=VARCHAR},
      hos_correct = #{record.hosCorrect,jdbcType=TINYINT},
      amount_correct = #{record.amountCorrect,jdbcType=TINYINT},
      bill_num_correct = #{record.billNumCorrect,jdbcType=TINYINT},
      source = #{record.source,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      invoice_pic_type = #{record.invoicePicType,jdbcType=TINYINT},
      enter_end_time = #{record.enterEndTime,jdbcType=TIMESTAMP},
      enter_start_time = #{record.enterStartTime,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=VARCHAR},
      has_detail = #{record.hasDetail,jdbcType=TINYINT},
      state = #{record.state,jdbcType=TINYINT},
      req_num = #{record.reqNum,jdbcType=TINYINT},
      info_lack = #{record.infoLack,jdbcType=CHAR},
      compare_json = #{record.compareJson,jdbcType=VARCHAR},
      province = #{record.province,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareRecordDO">
    update bill_ocr_compare_record
    <set>
      <if test="attachmentId != null">
        attachment_id = #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null">
        attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="ocrJson != null">
        ocr_json = #{ocrJson,jdbcType=VARCHAR},
      </if>
      <if test="correctJson != null">
        correct_json = #{correctJson,jdbcType=VARCHAR},
      </if>
      <if test="hosCorrect != null">
        hos_correct = #{hosCorrect,jdbcType=TINYINT},
      </if>
      <if test="amountCorrect != null">
        amount_correct = #{amountCorrect,jdbcType=TINYINT},
      </if>
      <if test="billNumCorrect != null">
        bill_num_correct = #{billNumCorrect,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="invoicePicType != null">
        invoice_pic_type = #{invoicePicType,jdbcType=TINYINT},
      </if>
      <if test="enterEndTime != null">
        enter_end_time = #{enterEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enterStartTime != null">
        enter_start_time = #{enterStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="hasDetail != null">
        has_detail = #{hasDetail,jdbcType=TINYINT},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=TINYINT},
      </if>
      <if test="reqNum != null">
        req_num = #{reqNum,jdbcType=TINYINT},
      </if>
      <if test="infoLack != null">
        info_lack = #{infoLack,jdbcType=CHAR},
      </if>
      <if test="compareJson != null">
        compare_json = #{compareJson,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareRecordDO">
    update bill_ocr_compare_record
    set attachment_id = #{attachmentId,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      ocr_json = #{ocrJson,jdbcType=VARCHAR},
      correct_json = #{correctJson,jdbcType=VARCHAR},
      hos_correct = #{hosCorrect,jdbcType=TINYINT},
      amount_correct = #{amountCorrect,jdbcType=TINYINT},
      bill_num_correct = #{billNumCorrect,jdbcType=TINYINT},
      source = #{source,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      invoice_pic_type = #{invoicePicType,jdbcType=TINYINT},
      enter_end_time = #{enterEndTime,jdbcType=TIMESTAMP},
      enter_start_time = #{enterStartTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      has_detail = #{hasDetail,jdbcType=TINYINT},
      state = #{state,jdbcType=TINYINT},
      req_num = #{reqNum,jdbcType=TINYINT},
      info_lack = #{infoLack,jdbcType=CHAR},
      compare_json = #{compareJson,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into bill_ocr_compare_record
    (id, attachment_id, report_no, attachment_url, ocr_json, correct_json, hos_correct, 
      amount_correct, bill_num_correct, source, is_deleted, gmt_created, gmt_modified, 
      creator, modifier, invoice_pic_type, enter_end_time, enter_start_time, remark, 
      has_detail, state, req_num, info_lack, compare_json, province)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.attachmentId,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.attachmentUrl,jdbcType=VARCHAR}, #{item.ocrJson,jdbcType=VARCHAR}, #{item.correctJson,jdbcType=VARCHAR}, 
        #{item.hosCorrect,jdbcType=TINYINT}, #{item.amountCorrect,jdbcType=TINYINT}, #{item.billNumCorrect,jdbcType=TINYINT}, 
        #{item.source,jdbcType=TINYINT}, #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.invoicePicType,jdbcType=TINYINT}, #{item.enterEndTime,jdbcType=TIMESTAMP}, 
        #{item.enterStartTime,jdbcType=TIMESTAMP}, #{item.remark,jdbcType=VARCHAR}, #{item.hasDetail,jdbcType=TINYINT}, 
        #{item.state,jdbcType=TINYINT}, #{item.reqNum,jdbcType=TINYINT}, #{item.infoLack,jdbcType=CHAR}, 
        #{item.compareJson,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into bill_ocr_compare_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'attachment_id'.toString() == column.value">
          #{item.attachmentId,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_url'.toString() == column.value">
          #{item.attachmentUrl,jdbcType=VARCHAR}
        </if>
        <if test="'ocr_json'.toString() == column.value">
          #{item.ocrJson,jdbcType=VARCHAR}
        </if>
        <if test="'correct_json'.toString() == column.value">
          #{item.correctJson,jdbcType=VARCHAR}
        </if>
        <if test="'hos_correct'.toString() == column.value">
          #{item.hosCorrect,jdbcType=TINYINT}
        </if>
        <if test="'amount_correct'.toString() == column.value">
          #{item.amountCorrect,jdbcType=TINYINT}
        </if>
        <if test="'bill_num_correct'.toString() == column.value">
          #{item.billNumCorrect,jdbcType=TINYINT}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=TINYINT}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'invoice_pic_type'.toString() == column.value">
          #{item.invoicePicType,jdbcType=TINYINT}
        </if>
        <if test="'enter_end_time'.toString() == column.value">
          #{item.enterEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'enter_start_time'.toString() == column.value">
          #{item.enterStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'has_detail'.toString() == column.value">
          #{item.hasDetail,jdbcType=TINYINT}
        </if>
        <if test="'state'.toString() == column.value">
          #{item.state,jdbcType=TINYINT}
        </if>
        <if test="'req_num'.toString() == column.value">
          #{item.reqNum,jdbcType=TINYINT}
        </if>
        <if test="'info_lack'.toString() == column.value">
          #{item.infoLack,jdbcType=CHAR}
        </if>
        <if test="'compare_json'.toString() == column.value">
          #{item.compareJson,jdbcType=VARCHAR}
        </if>
        <if test="'province'.toString() == column.value">
          #{item.province,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>