<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.MaterialFeeEquConfigMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.MaterialFeeEquConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="equ_name" jdbcType="VARCHAR" property="equName" />
    <result column="mobile_brand" jdbcType="VARCHAR" property="mobileBrand" />
    <result column="mobile_type" jdbcType="VARCHAR" property="mobileType" />
    <result column="mobile_model" jdbcType="VARCHAR" property="mobileModel" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, product_code, equ_name, mobile_brand, mobile_type, mobile_model, creator, modifier, 
    gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.MaterialFeeEquConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from material_fee_equ_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from material_fee_equ_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.MaterialFeeEquConfigDO">
    insert into material_fee_equ_config (id, product_code, equ_name, 
      mobile_brand, mobile_type, mobile_model, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{productCode,jdbcType=VARCHAR}, #{equName,jdbcType=VARCHAR}, 
      #{mobileBrand,jdbcType=VARCHAR}, #{mobileType,jdbcType=VARCHAR}, #{mobileModel,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.MaterialFeeEquConfigDO">
    insert into material_fee_equ_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="productCode != null">
        product_code,
      </if>
      <if test="equName != null">
        equ_name,
      </if>
      <if test="mobileBrand != null">
        mobile_brand,
      </if>
      <if test="mobileType != null">
        mobile_type,
      </if>
      <if test="mobileModel != null">
        mobile_model,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="equName != null">
        #{equName,jdbcType=VARCHAR},
      </if>
      <if test="mobileBrand != null">
        #{mobileBrand,jdbcType=VARCHAR},
      </if>
      <if test="mobileType != null">
        #{mobileType,jdbcType=VARCHAR},
      </if>
      <if test="mobileModel != null">
        #{mobileModel,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.MaterialFeeEquConfigExample" resultType="java.lang.Long">
    select count(*) from material_fee_equ_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update material_fee_equ_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.productCode != null">
        product_code = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.equName != null">
        equ_name = #{record.equName,jdbcType=VARCHAR},
      </if>
      <if test="record.mobileBrand != null">
        mobile_brand = #{record.mobileBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.mobileType != null">
        mobile_type = #{record.mobileType,jdbcType=VARCHAR},
      </if>
      <if test="record.mobileModel != null">
        mobile_model = #{record.mobileModel,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update material_fee_equ_config
    set id = #{record.id,jdbcType=BIGINT},
      product_code = #{record.productCode,jdbcType=VARCHAR},
      equ_name = #{record.equName,jdbcType=VARCHAR},
      mobile_brand = #{record.mobileBrand,jdbcType=VARCHAR},
      mobile_type = #{record.mobileType,jdbcType=VARCHAR},
      mobile_model = #{record.mobileModel,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.MaterialFeeEquConfigDO">
    update material_fee_equ_config
    <set>
      <if test="productCode != null">
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="equName != null">
        equ_name = #{equName,jdbcType=VARCHAR},
      </if>
      <if test="mobileBrand != null">
        mobile_brand = #{mobileBrand,jdbcType=VARCHAR},
      </if>
      <if test="mobileType != null">
        mobile_type = #{mobileType,jdbcType=VARCHAR},
      </if>
      <if test="mobileModel != null">
        mobile_model = #{mobileModel,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.MaterialFeeEquConfigDO">
    update material_fee_equ_config
    set product_code = #{productCode,jdbcType=VARCHAR},
      equ_name = #{equName,jdbcType=VARCHAR},
      mobile_brand = #{mobileBrand,jdbcType=VARCHAR},
      mobile_type = #{mobileType,jdbcType=VARCHAR},
      mobile_model = #{mobileModel,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into material_fee_equ_config
    (id, product_code, equ_name, mobile_brand, mobile_type, mobile_model, creator, modifier, 
      gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.productCode,jdbcType=VARCHAR}, #{item.equName,jdbcType=VARCHAR}, 
        #{item.mobileBrand,jdbcType=VARCHAR}, #{item.mobileType,jdbcType=VARCHAR}, #{item.mobileModel,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into material_fee_equ_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'product_code'.toString() == column.value">
          #{item.productCode,jdbcType=VARCHAR}
        </if>
        <if test="'equ_name'.toString() == column.value">
          #{item.equName,jdbcType=VARCHAR}
        </if>
        <if test="'mobile_brand'.toString() == column.value">
          #{item.mobileBrand,jdbcType=VARCHAR}
        </if>
        <if test="'mobile_type'.toString() == column.value">
          #{item.mobileType,jdbcType=VARCHAR}
        </if>
        <if test="'mobile_model'.toString() == column.value">
          #{item.mobileModel,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>