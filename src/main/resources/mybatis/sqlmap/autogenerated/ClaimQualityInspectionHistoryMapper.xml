<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimQualityInspectionHistoryMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimQualityInspectionHistoryDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="main_id" jdbcType="BIGINT" property="mainId" />
    <result column="operate_type" jdbcType="VARCHAR" property="operateType" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="feedback_sources" jdbcType="VARCHAR" property="feedbackSources" />
    <result column="conclusion" jdbcType="VARCHAR" property="conclusion" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="registrant" jdbcType="VARCHAR" property="registrant" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="reviewer" jdbcType="VARCHAR" property="reviewer" />
    <result column="error_type" jdbcType="VARCHAR" property="errorType" />
    <result column="error_amount" jdbcType="DECIMAL" property="errorAmount" />
    <result column="inspect_time" jdbcType="TIMESTAMP" property="inspectTime" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, main_id, operate_type, report_no, policy_no, feedback_sources, conclusion, description, 
    registrant, auditor, reviewer, error_type, error_amount, inspect_time, gmt_created, 
    gmt_modified, creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimQualityInspectionHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_quality_inspection_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_quality_inspection_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimQualityInspectionHistoryDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_quality_inspection_history (main_id, operate_type, report_no, 
      policy_no, feedback_sources, conclusion, 
      description, registrant, auditor, 
      reviewer, error_type, error_amount, 
      inspect_time, gmt_created, gmt_modified, 
      creator, modifier, is_deleted
      )
    values (#{mainId,jdbcType=BIGINT}, #{operateType,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{policyNo,jdbcType=VARCHAR}, #{feedbackSources,jdbcType=VARCHAR}, #{conclusion,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{registrant,jdbcType=VARCHAR}, #{auditor,jdbcType=VARCHAR}, 
      #{reviewer,jdbcType=VARCHAR}, #{errorType,jdbcType=VARCHAR}, #{errorAmount,jdbcType=DECIMAL}, 
      #{inspectTime,jdbcType=TIMESTAMP}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimQualityInspectionHistoryDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_quality_inspection_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mainId != null">
        main_id,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="feedbackSources != null">
        feedback_sources,
      </if>
      <if test="conclusion != null">
        conclusion,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="registrant != null">
        registrant,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="reviewer != null">
        reviewer,
      </if>
      <if test="errorType != null">
        error_type,
      </if>
      <if test="errorAmount != null">
        error_amount,
      </if>
      <if test="inspectTime != null">
        inspect_time,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mainId != null">
        #{mainId,jdbcType=BIGINT},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="feedbackSources != null">
        #{feedbackSources,jdbcType=VARCHAR},
      </if>
      <if test="conclusion != null">
        #{conclusion,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="registrant != null">
        #{registrant,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="reviewer != null">
        #{reviewer,jdbcType=VARCHAR},
      </if>
      <if test="errorType != null">
        #{errorType,jdbcType=VARCHAR},
      </if>
      <if test="errorAmount != null">
        #{errorAmount,jdbcType=DECIMAL},
      </if>
      <if test="inspectTime != null">
        #{inspectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimQualityInspectionHistoryExample" resultType="java.lang.Long">
    select count(*) from claim_quality_inspection_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_quality_inspection_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mainId != null">
        main_id = #{record.mainId,jdbcType=BIGINT},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.feedbackSources != null">
        feedback_sources = #{record.feedbackSources,jdbcType=VARCHAR},
      </if>
      <if test="record.conclusion != null">
        conclusion = #{record.conclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.registrant != null">
        registrant = #{record.registrant,jdbcType=VARCHAR},
      </if>
      <if test="record.auditor != null">
        auditor = #{record.auditor,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewer != null">
        reviewer = #{record.reviewer,jdbcType=VARCHAR},
      </if>
      <if test="record.errorType != null">
        error_type = #{record.errorType,jdbcType=VARCHAR},
      </if>
      <if test="record.errorAmount != null">
        error_amount = #{record.errorAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.inspectTime != null">
        inspect_time = #{record.inspectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_quality_inspection_history
    set id = #{record.id,jdbcType=BIGINT},
      main_id = #{record.mainId,jdbcType=BIGINT},
      operate_type = #{record.operateType,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      feedback_sources = #{record.feedbackSources,jdbcType=VARCHAR},
      conclusion = #{record.conclusion,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      registrant = #{record.registrant,jdbcType=VARCHAR},
      auditor = #{record.auditor,jdbcType=VARCHAR},
      reviewer = #{record.reviewer,jdbcType=VARCHAR},
      error_type = #{record.errorType,jdbcType=VARCHAR},
      error_amount = #{record.errorAmount,jdbcType=DECIMAL},
      inspect_time = #{record.inspectTime,jdbcType=TIMESTAMP},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimQualityInspectionHistoryDO">
    update claim_quality_inspection_history
    <set>
      <if test="mainId != null">
        main_id = #{mainId,jdbcType=BIGINT},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="feedbackSources != null">
        feedback_sources = #{feedbackSources,jdbcType=VARCHAR},
      </if>
      <if test="conclusion != null">
        conclusion = #{conclusion,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="registrant != null">
        registrant = #{registrant,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="reviewer != null">
        reviewer = #{reviewer,jdbcType=VARCHAR},
      </if>
      <if test="errorType != null">
        error_type = #{errorType,jdbcType=VARCHAR},
      </if>
      <if test="errorAmount != null">
        error_amount = #{errorAmount,jdbcType=DECIMAL},
      </if>
      <if test="inspectTime != null">
        inspect_time = #{inspectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimQualityInspectionHistoryDO">
    update claim_quality_inspection_history
    set main_id = #{mainId,jdbcType=BIGINT},
      operate_type = #{operateType,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      feedback_sources = #{feedbackSources,jdbcType=VARCHAR},
      conclusion = #{conclusion,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      registrant = #{registrant,jdbcType=VARCHAR},
      auditor = #{auditor,jdbcType=VARCHAR},
      reviewer = #{reviewer,jdbcType=VARCHAR},
      error_type = #{errorType,jdbcType=VARCHAR},
      error_amount = #{errorAmount,jdbcType=DECIMAL},
      inspect_time = #{inspectTime,jdbcType=TIMESTAMP},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into claim_quality_inspection_history
    (main_id, operate_type, report_no, policy_no, feedback_sources, conclusion, description, 
      registrant, auditor, reviewer, error_type, error_amount, inspect_time, gmt_created, 
      gmt_modified, creator, modifier, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.mainId,jdbcType=BIGINT}, #{item.operateType,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.policyNo,jdbcType=VARCHAR}, #{item.feedbackSources,jdbcType=VARCHAR}, #{item.conclusion,jdbcType=VARCHAR}, 
        #{item.description,jdbcType=VARCHAR}, #{item.registrant,jdbcType=VARCHAR}, #{item.auditor,jdbcType=VARCHAR}, 
        #{item.reviewer,jdbcType=VARCHAR}, #{item.errorType,jdbcType=VARCHAR}, #{item.errorAmount,jdbcType=DECIMAL}, 
        #{item.inspectTime,jdbcType=TIMESTAMP}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into claim_quality_inspection_history (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'main_id'.toString() == column.value">
          #{item.mainId,jdbcType=BIGINT}
        </if>
        <if test="'operate_type'.toString() == column.value">
          #{item.operateType,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'feedback_sources'.toString() == column.value">
          #{item.feedbackSources,jdbcType=VARCHAR}
        </if>
        <if test="'conclusion'.toString() == column.value">
          #{item.conclusion,jdbcType=VARCHAR}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'registrant'.toString() == column.value">
          #{item.registrant,jdbcType=VARCHAR}
        </if>
        <if test="'auditor'.toString() == column.value">
          #{item.auditor,jdbcType=VARCHAR}
        </if>
        <if test="'reviewer'.toString() == column.value">
          #{item.reviewer,jdbcType=VARCHAR}
        </if>
        <if test="'error_type'.toString() == column.value">
          #{item.errorType,jdbcType=VARCHAR}
        </if>
        <if test="'error_amount'.toString() == column.value">
          #{item.errorAmount,jdbcType=DECIMAL}
        </if>
        <if test="'inspect_time'.toString() == column.value">
          #{item.inspectTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>