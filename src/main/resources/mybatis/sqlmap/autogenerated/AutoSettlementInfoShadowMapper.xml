<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AutoSettlementInfoShadowMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AutoSettlementInfoShadowDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="liability_id" jdbcType="BIGINT" property="liabilityId" />
    <result column="liability_code" jdbcType="VARCHAR" property="liabilityCode" />
    <result column="paid_amount" jdbcType="VARCHAR" property="paidAmount" />
    <result column="deduct_deductible" jdbcType="VARCHAR" property="deductDeductible" />
    <result column="deduct_out_deductible" jdbcType="VARCHAR" property="deductOutDeductible" />
    <result column="settlement_type" jdbcType="VARCHAR" property="settlementType" />
    <result column="audit_result_show" jdbcType="VARCHAR" property="auditResultShow" />
    <result column="refusal_reason" jdbcType="VARCHAR" property="refusalReason" />
    <result column="zero_paid_reason" jdbcType="VARCHAR" property="zeroPaidReason" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_same" jdbcType="TINYINT" property="isSame" />
    <result column="bill_amount_is_same" jdbcType="TINYINT" property="billAmountIsSame" />
    <result column="diff_rate" jdbcType="INTEGER" property="diffRate" />
    <result column="bill_amount_diff_rate" jdbcType="INTEGER" property="billAmountDiffRate" />
    <result column="settlement_type_is_same" jdbcType="TINYINT" property="settlementTypeIsSame" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, product_code, product_name, liability_id, liability_code, paid_amount, 
    deduct_deductible, deduct_out_deductible, settlement_type, audit_result_show, refusal_reason, 
    zero_paid_reason, extra_info, remark, is_deleted, gmt_created, gmt_modified, creator, 
    modifier, is_same, bill_amount_is_same, diff_rate, bill_amount_diff_rate, settlement_type_is_same
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementInfoShadowExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auto_settlement_info_shadow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auto_settlement_info_shadow
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementInfoShadowDO">
    insert into auto_settlement_info_shadow (id, report_no, product_code, 
      product_name, liability_id, liability_code, 
      paid_amount, deduct_deductible, deduct_out_deductible, 
      settlement_type, audit_result_show, refusal_reason, 
      zero_paid_reason, extra_info, remark, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier, is_same, 
      bill_amount_is_same, diff_rate, bill_amount_diff_rate, 
      settlement_type_is_same)
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{liabilityId,jdbcType=BIGINT}, #{liabilityCode,jdbcType=VARCHAR}, 
      #{paidAmount,jdbcType=VARCHAR}, #{deductDeductible,jdbcType=VARCHAR}, #{deductOutDeductible,jdbcType=VARCHAR}, 
      #{settlementType,jdbcType=VARCHAR}, #{auditResultShow,jdbcType=VARCHAR}, #{refusalReason,jdbcType=VARCHAR}, 
      #{zeroPaidReason,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{isSame,jdbcType=TINYINT}, 
      #{billAmountIsSame,jdbcType=TINYINT}, #{diffRate,jdbcType=INTEGER}, #{billAmountDiffRate,jdbcType=INTEGER}, 
      #{settlementTypeIsSame,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementInfoShadowDO">
    insert into auto_settlement_info_shadow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="productCode != null">
        product_code,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="liabilityId != null">
        liability_id,
      </if>
      <if test="liabilityCode != null">
        liability_code,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="deductDeductible != null">
        deduct_deductible,
      </if>
      <if test="deductOutDeductible != null">
        deduct_out_deductible,
      </if>
      <if test="settlementType != null">
        settlement_type,
      </if>
      <if test="auditResultShow != null">
        audit_result_show,
      </if>
      <if test="refusalReason != null">
        refusal_reason,
      </if>
      <if test="zeroPaidReason != null">
        zero_paid_reason,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isSame != null">
        is_same,
      </if>
      <if test="billAmountIsSame != null">
        bill_amount_is_same,
      </if>
      <if test="diffRate != null">
        diff_rate,
      </if>
      <if test="billAmountDiffRate != null">
        bill_amount_diff_rate,
      </if>
      <if test="settlementTypeIsSame != null">
        settlement_type_is_same,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityId != null">
        #{liabilityId,jdbcType=BIGINT},
      </if>
      <if test="liabilityCode != null">
        #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="deductDeductible != null">
        #{deductDeductible,jdbcType=VARCHAR},
      </if>
      <if test="deductOutDeductible != null">
        #{deductOutDeductible,jdbcType=VARCHAR},
      </if>
      <if test="settlementType != null">
        #{settlementType,jdbcType=VARCHAR},
      </if>
      <if test="auditResultShow != null">
        #{auditResultShow,jdbcType=VARCHAR},
      </if>
      <if test="refusalReason != null">
        #{refusalReason,jdbcType=VARCHAR},
      </if>
      <if test="zeroPaidReason != null">
        #{zeroPaidReason,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isSame != null">
        #{isSame,jdbcType=TINYINT},
      </if>
      <if test="billAmountIsSame != null">
        #{billAmountIsSame,jdbcType=TINYINT},
      </if>
      <if test="diffRate != null">
        #{diffRate,jdbcType=INTEGER},
      </if>
      <if test="billAmountDiffRate != null">
        #{billAmountDiffRate,jdbcType=INTEGER},
      </if>
      <if test="settlementTypeIsSame != null">
        #{settlementTypeIsSame,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementInfoShadowExample" resultType="java.lang.Long">
    select count(*) from auto_settlement_info_shadow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update auto_settlement_info_shadow
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.productCode != null">
        product_code = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityId != null">
        liability_id = #{record.liabilityId,jdbcType=BIGINT},
      </if>
      <if test="record.liabilityCode != null">
        liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.deductDeductible != null">
        deduct_deductible = #{record.deductDeductible,jdbcType=VARCHAR},
      </if>
      <if test="record.deductOutDeductible != null">
        deduct_out_deductible = #{record.deductOutDeductible,jdbcType=VARCHAR},
      </if>
      <if test="record.settlementType != null">
        settlement_type = #{record.settlementType,jdbcType=VARCHAR},
      </if>
      <if test="record.auditResultShow != null">
        audit_result_show = #{record.auditResultShow,jdbcType=VARCHAR},
      </if>
      <if test="record.refusalReason != null">
        refusal_reason = #{record.refusalReason,jdbcType=VARCHAR},
      </if>
      <if test="record.zeroPaidReason != null">
        zero_paid_reason = #{record.zeroPaidReason,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isSame != null">
        is_same = #{record.isSame,jdbcType=TINYINT},
      </if>
      <if test="record.billAmountIsSame != null">
        bill_amount_is_same = #{record.billAmountIsSame,jdbcType=TINYINT},
      </if>
      <if test="record.diffRate != null">
        diff_rate = #{record.diffRate,jdbcType=INTEGER},
      </if>
      <if test="record.billAmountDiffRate != null">
        bill_amount_diff_rate = #{record.billAmountDiffRate,jdbcType=INTEGER},
      </if>
      <if test="record.settlementTypeIsSame != null">
        settlement_type_is_same = #{record.settlementTypeIsSame,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update auto_settlement_info_shadow
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      product_code = #{record.productCode,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      liability_id = #{record.liabilityId,jdbcType=BIGINT},
      liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      deduct_deductible = #{record.deductDeductible,jdbcType=VARCHAR},
      deduct_out_deductible = #{record.deductOutDeductible,jdbcType=VARCHAR},
      settlement_type = #{record.settlementType,jdbcType=VARCHAR},
      audit_result_show = #{record.auditResultShow,jdbcType=VARCHAR},
      refusal_reason = #{record.refusalReason,jdbcType=VARCHAR},
      zero_paid_reason = #{record.zeroPaidReason,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_same = #{record.isSame,jdbcType=TINYINT},
      bill_amount_is_same = #{record.billAmountIsSame,jdbcType=TINYINT},
      diff_rate = #{record.diffRate,jdbcType=INTEGER},
      bill_amount_diff_rate = #{record.billAmountDiffRate,jdbcType=INTEGER},
      settlement_type_is_same = #{record.settlementTypeIsSame,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementInfoShadowDO">
    update auto_settlement_info_shadow
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityId != null">
        liability_id = #{liabilityId,jdbcType=BIGINT},
      </if>
      <if test="liabilityCode != null">
        liability_code = #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="deductDeductible != null">
        deduct_deductible = #{deductDeductible,jdbcType=VARCHAR},
      </if>
      <if test="deductOutDeductible != null">
        deduct_out_deductible = #{deductOutDeductible,jdbcType=VARCHAR},
      </if>
      <if test="settlementType != null">
        settlement_type = #{settlementType,jdbcType=VARCHAR},
      </if>
      <if test="auditResultShow != null">
        audit_result_show = #{auditResultShow,jdbcType=VARCHAR},
      </if>
      <if test="refusalReason != null">
        refusal_reason = #{refusalReason,jdbcType=VARCHAR},
      </if>
      <if test="zeroPaidReason != null">
        zero_paid_reason = #{zeroPaidReason,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isSame != null">
        is_same = #{isSame,jdbcType=TINYINT},
      </if>
      <if test="billAmountIsSame != null">
        bill_amount_is_same = #{billAmountIsSame,jdbcType=TINYINT},
      </if>
      <if test="diffRate != null">
        diff_rate = #{diffRate,jdbcType=INTEGER},
      </if>
      <if test="billAmountDiffRate != null">
        bill_amount_diff_rate = #{billAmountDiffRate,jdbcType=INTEGER},
      </if>
      <if test="settlementTypeIsSame != null">
        settlement_type_is_same = #{settlementTypeIsSame,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementInfoShadowDO">
    update auto_settlement_info_shadow
    set report_no = #{reportNo,jdbcType=VARCHAR},
      product_code = #{productCode,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      liability_id = #{liabilityId,jdbcType=BIGINT},
      liability_code = #{liabilityCode,jdbcType=VARCHAR},
      paid_amount = #{paidAmount,jdbcType=VARCHAR},
      deduct_deductible = #{deductDeductible,jdbcType=VARCHAR},
      deduct_out_deductible = #{deductOutDeductible,jdbcType=VARCHAR},
      settlement_type = #{settlementType,jdbcType=VARCHAR},
      audit_result_show = #{auditResultShow,jdbcType=VARCHAR},
      refusal_reason = #{refusalReason,jdbcType=VARCHAR},
      zero_paid_reason = #{zeroPaidReason,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_same = #{isSame,jdbcType=TINYINT},
      bill_amount_is_same = #{billAmountIsSame,jdbcType=TINYINT},
      diff_rate = #{diffRate,jdbcType=INTEGER},
      bill_amount_diff_rate = #{billAmountDiffRate,jdbcType=INTEGER},
      settlement_type_is_same = #{settlementTypeIsSame,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into auto_settlement_info_shadow
    (id, report_no, product_code, product_name, liability_id, liability_code, paid_amount, 
      deduct_deductible, deduct_out_deductible, settlement_type, audit_result_show, refusal_reason, 
      zero_paid_reason, extra_info, remark, is_deleted, gmt_created, gmt_modified, creator, 
      modifier, is_same, bill_amount_is_same, diff_rate, bill_amount_diff_rate, settlement_type_is_same
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.productCode,jdbcType=VARCHAR}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.liabilityId,jdbcType=BIGINT}, #{item.liabilityCode,jdbcType=VARCHAR}, 
        #{item.paidAmount,jdbcType=VARCHAR}, #{item.deductDeductible,jdbcType=VARCHAR}, 
        #{item.deductOutDeductible,jdbcType=VARCHAR}, #{item.settlementType,jdbcType=VARCHAR}, 
        #{item.auditResultShow,jdbcType=VARCHAR}, #{item.refusalReason,jdbcType=VARCHAR}, 
        #{item.zeroPaidReason,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.isSame,jdbcType=TINYINT}, 
        #{item.billAmountIsSame,jdbcType=TINYINT}, #{item.diffRate,jdbcType=INTEGER}, #{item.billAmountDiffRate,jdbcType=INTEGER}, 
        #{item.settlementTypeIsSame,jdbcType=TINYINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into auto_settlement_info_shadow (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'product_code'.toString() == column.value">
          #{item.productCode,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'liability_id'.toString() == column.value">
          #{item.liabilityId,jdbcType=BIGINT}
        </if>
        <if test="'liability_code'.toString() == column.value">
          #{item.liabilityCode,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount'.toString() == column.value">
          #{item.paidAmount,jdbcType=VARCHAR}
        </if>
        <if test="'deduct_deductible'.toString() == column.value">
          #{item.deductDeductible,jdbcType=VARCHAR}
        </if>
        <if test="'deduct_out_deductible'.toString() == column.value">
          #{item.deductOutDeductible,jdbcType=VARCHAR}
        </if>
        <if test="'settlement_type'.toString() == column.value">
          #{item.settlementType,jdbcType=VARCHAR}
        </if>
        <if test="'audit_result_show'.toString() == column.value">
          #{item.auditResultShow,jdbcType=VARCHAR}
        </if>
        <if test="'refusal_reason'.toString() == column.value">
          #{item.refusalReason,jdbcType=VARCHAR}
        </if>
        <if test="'zero_paid_reason'.toString() == column.value">
          #{item.zeroPaidReason,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_same'.toString() == column.value">
          #{item.isSame,jdbcType=TINYINT}
        </if>
        <if test="'bill_amount_is_same'.toString() == column.value">
          #{item.billAmountIsSame,jdbcType=TINYINT}
        </if>
        <if test="'diff_rate'.toString() == column.value">
          #{item.diffRate,jdbcType=INTEGER}
        </if>
        <if test="'bill_amount_diff_rate'.toString() == column.value">
          #{item.billAmountDiffRate,jdbcType=INTEGER}
        </if>
        <if test="'settlement_type_is_same'.toString() == column.value">
          #{item.settlementTypeIsSame,jdbcType=TINYINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>