<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.KneadBillConfigMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.KneadBillConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="campaign_def_id" jdbcType="VARCHAR" property="campaignDefId" />
    <result column="package_def_id" jdbcType="VARCHAR" property="packageDefId" />
    <result column="pay_period" jdbcType="INTEGER" property="payPeriod" />
    <result column="claim_report_source" jdbcType="VARCHAR" property="claimReportSource" />
    <result column="trans_account" jdbcType="VARCHAR" property="transAccount" />
    <result column="knead_rule" jdbcType="CHAR" property="kneadRule" />
    <result column="trans_name" jdbcType="VARCHAR" property="transName" />
    <result column="trans_bank_location" jdbcType="VARCHAR" property="transBankLocation" />
    <result column="to_mails" jdbcType="VARCHAR" property="toMails" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, campaign_def_id, package_def_id, pay_period, claim_report_source, trans_account, 
    knead_rule, trans_name, trans_bank_location, to_mails, creator, modifier, gmt_created, 
    gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.KneadBillConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from knead_bill_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from knead_bill_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.KneadBillConfigDO">
    insert into knead_bill_config (id, campaign_def_id, package_def_id, 
      pay_period, claim_report_source, trans_account, 
      knead_rule, trans_name, trans_bank_location, 
      to_mails, creator, modifier, 
      gmt_created, gmt_modified, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{campaignDefId,jdbcType=VARCHAR}, #{packageDefId,jdbcType=VARCHAR}, 
      #{payPeriod,jdbcType=INTEGER}, #{claimReportSource,jdbcType=VARCHAR}, #{transAccount,jdbcType=VARCHAR}, 
      #{kneadRule,jdbcType=CHAR}, #{transName,jdbcType=VARCHAR}, #{transBankLocation,jdbcType=VARCHAR}, 
      #{toMails,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.KneadBillConfigDO">
    insert into knead_bill_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="payPeriod != null">
        pay_period,
      </if>
      <if test="claimReportSource != null">
        claim_report_source,
      </if>
      <if test="transAccount != null">
        trans_account,
      </if>
      <if test="kneadRule != null">
        knead_rule,
      </if>
      <if test="transName != null">
        trans_name,
      </if>
      <if test="transBankLocation != null">
        trans_bank_location,
      </if>
      <if test="toMails != null">
        to_mails,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=VARCHAR},
      </if>
      <if test="payPeriod != null">
        #{payPeriod,jdbcType=INTEGER},
      </if>
      <if test="claimReportSource != null">
        #{claimReportSource,jdbcType=VARCHAR},
      </if>
      <if test="transAccount != null">
        #{transAccount,jdbcType=VARCHAR},
      </if>
      <if test="kneadRule != null">
        #{kneadRule,jdbcType=CHAR},
      </if>
      <if test="transName != null">
        #{transName,jdbcType=VARCHAR},
      </if>
      <if test="transBankLocation != null">
        #{transBankLocation,jdbcType=VARCHAR},
      </if>
      <if test="toMails != null">
        #{toMails,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.KneadBillConfigExample" resultType="java.lang.Long">
    select count(*) from knead_bill_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update knead_bill_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=VARCHAR},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=VARCHAR},
      </if>
      <if test="record.payPeriod != null">
        pay_period = #{record.payPeriod,jdbcType=INTEGER},
      </if>
      <if test="record.claimReportSource != null">
        claim_report_source = #{record.claimReportSource,jdbcType=VARCHAR},
      </if>
      <if test="record.transAccount != null">
        trans_account = #{record.transAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.kneadRule != null">
        knead_rule = #{record.kneadRule,jdbcType=CHAR},
      </if>
      <if test="record.transName != null">
        trans_name = #{record.transName,jdbcType=VARCHAR},
      </if>
      <if test="record.transBankLocation != null">
        trans_bank_location = #{record.transBankLocation,jdbcType=VARCHAR},
      </if>
      <if test="record.toMails != null">
        to_mails = #{record.toMails,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update knead_bill_config
    set id = #{record.id,jdbcType=BIGINT},
      campaign_def_id = #{record.campaignDefId,jdbcType=VARCHAR},
      package_def_id = #{record.packageDefId,jdbcType=VARCHAR},
      pay_period = #{record.payPeriod,jdbcType=INTEGER},
      claim_report_source = #{record.claimReportSource,jdbcType=VARCHAR},
      trans_account = #{record.transAccount,jdbcType=VARCHAR},
      knead_rule = #{record.kneadRule,jdbcType=CHAR},
      trans_name = #{record.transName,jdbcType=VARCHAR},
      trans_bank_location = #{record.transBankLocation,jdbcType=VARCHAR},
      to_mails = #{record.toMails,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.KneadBillConfigDO">
    update knead_bill_config
    <set>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=VARCHAR},
      </if>
      <if test="payPeriod != null">
        pay_period = #{payPeriod,jdbcType=INTEGER},
      </if>
      <if test="claimReportSource != null">
        claim_report_source = #{claimReportSource,jdbcType=VARCHAR},
      </if>
      <if test="transAccount != null">
        trans_account = #{transAccount,jdbcType=VARCHAR},
      </if>
      <if test="kneadRule != null">
        knead_rule = #{kneadRule,jdbcType=CHAR},
      </if>
      <if test="transName != null">
        trans_name = #{transName,jdbcType=VARCHAR},
      </if>
      <if test="transBankLocation != null">
        trans_bank_location = #{transBankLocation,jdbcType=VARCHAR},
      </if>
      <if test="toMails != null">
        to_mails = #{toMails,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.KneadBillConfigDO">
    update knead_bill_config
    set campaign_def_id = #{campaignDefId,jdbcType=VARCHAR},
      package_def_id = #{packageDefId,jdbcType=VARCHAR},
      pay_period = #{payPeriod,jdbcType=INTEGER},
      claim_report_source = #{claimReportSource,jdbcType=VARCHAR},
      trans_account = #{transAccount,jdbcType=VARCHAR},
      knead_rule = #{kneadRule,jdbcType=CHAR},
      trans_name = #{transName,jdbcType=VARCHAR},
      trans_bank_location = #{transBankLocation,jdbcType=VARCHAR},
      to_mails = #{toMails,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into knead_bill_config
    (id, campaign_def_id, package_def_id, pay_period, claim_report_source, trans_account, 
      knead_rule, trans_name, trans_bank_location, to_mails, creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.campaignDefId,jdbcType=VARCHAR}, #{item.packageDefId,jdbcType=VARCHAR}, 
        #{item.payPeriod,jdbcType=INTEGER}, #{item.claimReportSource,jdbcType=VARCHAR}, 
        #{item.transAccount,jdbcType=VARCHAR}, #{item.kneadRule,jdbcType=CHAR}, #{item.transName,jdbcType=VARCHAR}, 
        #{item.transBankLocation,jdbcType=VARCHAR}, #{item.toMails,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into knead_bill_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=VARCHAR}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=VARCHAR}
        </if>
        <if test="'pay_period'.toString() == column.value">
          #{item.payPeriod,jdbcType=INTEGER}
        </if>
        <if test="'claim_report_source'.toString() == column.value">
          #{item.claimReportSource,jdbcType=VARCHAR}
        </if>
        <if test="'trans_account'.toString() == column.value">
          #{item.transAccount,jdbcType=VARCHAR}
        </if>
        <if test="'knead_rule'.toString() == column.value">
          #{item.kneadRule,jdbcType=CHAR}
        </if>
        <if test="'trans_name'.toString() == column.value">
          #{item.transName,jdbcType=VARCHAR}
        </if>
        <if test="'trans_bank_location'.toString() == column.value">
          #{item.transBankLocation,jdbcType=VARCHAR}
        </if>
        <if test="'to_mails'.toString() == column.value">
          #{item.toMails,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>