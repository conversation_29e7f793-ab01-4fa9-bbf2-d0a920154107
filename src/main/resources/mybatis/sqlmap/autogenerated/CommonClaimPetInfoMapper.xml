<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.CommonClaimPetInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.CommonClaimPetInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel_report_no" jdbcType="VARCHAR" property="channelReportNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
    <result column="hospital_address" jdbcType="VARCHAR" property="hospitalAddress" />
    <result column="hospital_id" jdbcType="VARCHAR" property="hospitalId" />
    <result column="hospital_level" jdbcType="VARCHAR" property="hospitalLevel" />
    <result column="hospital_info" jdbcType="VARCHAR" property="hospitalInfo" />
    <result column="disease_info" jdbcType="VARCHAR" property="diseaseInfo" />
    <result column="include_surgery" jdbcType="VARCHAR" property="includeSurgery" />
    <result column="include_invoice" jdbcType="VARCHAR" property="includeInvoice" />
    <result column="need_auto_assess" jdbcType="CHAR" property="needAutoAssess" />
    <result column="is_affiliate_members" jdbcType="VARCHAR" property="isAffiliateMembers" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, channel_report_no, report_no, policy_no, hospital_name, hospital_address, hospital_id, 
    hospital_level, hospital_info, disease_info, include_surgery, include_invoice, need_auto_assess, 
    is_affiliate_members, is_deleted, gmt_created, gmt_modified, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.CommonClaimPetInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from common_claim_pet_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from common_claim_pet_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.CommonClaimPetInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into common_claim_pet_info (channel_report_no, report_no, policy_no, 
      hospital_name, hospital_address, hospital_id, 
      hospital_level, hospital_info, disease_info, 
      include_surgery, include_invoice, need_auto_assess, 
      is_affiliate_members, is_deleted, gmt_created, 
      gmt_modified, creator, modifier
      )
    values (#{channelReportNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, 
      #{hospitalName,jdbcType=VARCHAR}, #{hospitalAddress,jdbcType=VARCHAR}, #{hospitalId,jdbcType=VARCHAR}, 
      #{hospitalLevel,jdbcType=VARCHAR}, #{hospitalInfo,jdbcType=VARCHAR}, #{diseaseInfo,jdbcType=VARCHAR}, 
      #{includeSurgery,jdbcType=VARCHAR}, #{includeInvoice,jdbcType=VARCHAR}, #{needAutoAssess,jdbcType=CHAR}, 
      #{isAffiliateMembers,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.CommonClaimPetInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into common_claim_pet_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="channelReportNo != null">
        channel_report_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="hospitalName != null">
        hospital_name,
      </if>
      <if test="hospitalAddress != null">
        hospital_address,
      </if>
      <if test="hospitalId != null">
        hospital_id,
      </if>
      <if test="hospitalLevel != null">
        hospital_level,
      </if>
      <if test="hospitalInfo != null">
        hospital_info,
      </if>
      <if test="diseaseInfo != null">
        disease_info,
      </if>
      <if test="includeSurgery != null">
        include_surgery,
      </if>
      <if test="includeInvoice != null">
        include_invoice,
      </if>
      <if test="needAutoAssess != null">
        need_auto_assess,
      </if>
      <if test="isAffiliateMembers != null">
        is_affiliate_members,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="channelReportNo != null">
        #{channelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalAddress != null">
        #{hospitalAddress,jdbcType=VARCHAR},
      </if>
      <if test="hospitalId != null">
        #{hospitalId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalLevel != null">
        #{hospitalLevel,jdbcType=VARCHAR},
      </if>
      <if test="hospitalInfo != null">
        #{hospitalInfo,jdbcType=VARCHAR},
      </if>
      <if test="diseaseInfo != null">
        #{diseaseInfo,jdbcType=VARCHAR},
      </if>
      <if test="includeSurgery != null">
        #{includeSurgery,jdbcType=VARCHAR},
      </if>
      <if test="includeInvoice != null">
        #{includeInvoice,jdbcType=VARCHAR},
      </if>
      <if test="needAutoAssess != null">
        #{needAutoAssess,jdbcType=CHAR},
      </if>
      <if test="isAffiliateMembers != null">
        #{isAffiliateMembers,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.CommonClaimPetInfoExample" resultType="java.lang.Long">
    select count(*) from common_claim_pet_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update common_claim_pet_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.channelReportNo != null">
        channel_report_no = #{record.channelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalName != null">
        hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalAddress != null">
        hospital_address = #{record.hospitalAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalId != null">
        hospital_id = #{record.hospitalId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalLevel != null">
        hospital_level = #{record.hospitalLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalInfo != null">
        hospital_info = #{record.hospitalInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.diseaseInfo != null">
        disease_info = #{record.diseaseInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.includeSurgery != null">
        include_surgery = #{record.includeSurgery,jdbcType=VARCHAR},
      </if>
      <if test="record.includeInvoice != null">
        include_invoice = #{record.includeInvoice,jdbcType=VARCHAR},
      </if>
      <if test="record.needAutoAssess != null">
        need_auto_assess = #{record.needAutoAssess,jdbcType=CHAR},
      </if>
      <if test="record.isAffiliateMembers != null">
        is_affiliate_members = #{record.isAffiliateMembers,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update common_claim_pet_info
    set id = #{record.id,jdbcType=BIGINT},
      channel_report_no = #{record.channelReportNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      hospital_address = #{record.hospitalAddress,jdbcType=VARCHAR},
      hospital_id = #{record.hospitalId,jdbcType=VARCHAR},
      hospital_level = #{record.hospitalLevel,jdbcType=VARCHAR},
      hospital_info = #{record.hospitalInfo,jdbcType=VARCHAR},
      disease_info = #{record.diseaseInfo,jdbcType=VARCHAR},
      include_surgery = #{record.includeSurgery,jdbcType=VARCHAR},
      include_invoice = #{record.includeInvoice,jdbcType=VARCHAR},
      need_auto_assess = #{record.needAutoAssess,jdbcType=CHAR},
      is_affiliate_members = #{record.isAffiliateMembers,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.CommonClaimPetInfoDO">
    update common_claim_pet_info
    <set>
      <if test="channelReportNo != null">
        channel_report_no = #{channelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        hospital_name = #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalAddress != null">
        hospital_address = #{hospitalAddress,jdbcType=VARCHAR},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalLevel != null">
        hospital_level = #{hospitalLevel,jdbcType=VARCHAR},
      </if>
      <if test="hospitalInfo != null">
        hospital_info = #{hospitalInfo,jdbcType=VARCHAR},
      </if>
      <if test="diseaseInfo != null">
        disease_info = #{diseaseInfo,jdbcType=VARCHAR},
      </if>
      <if test="includeSurgery != null">
        include_surgery = #{includeSurgery,jdbcType=VARCHAR},
      </if>
      <if test="includeInvoice != null">
        include_invoice = #{includeInvoice,jdbcType=VARCHAR},
      </if>
      <if test="needAutoAssess != null">
        need_auto_assess = #{needAutoAssess,jdbcType=CHAR},
      </if>
      <if test="isAffiliateMembers != null">
        is_affiliate_members = #{isAffiliateMembers,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.CommonClaimPetInfoDO">
    update common_claim_pet_info
    set channel_report_no = #{channelReportNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      hospital_name = #{hospitalName,jdbcType=VARCHAR},
      hospital_address = #{hospitalAddress,jdbcType=VARCHAR},
      hospital_id = #{hospitalId,jdbcType=VARCHAR},
      hospital_level = #{hospitalLevel,jdbcType=VARCHAR},
      hospital_info = #{hospitalInfo,jdbcType=VARCHAR},
      disease_info = #{diseaseInfo,jdbcType=VARCHAR},
      include_surgery = #{includeSurgery,jdbcType=VARCHAR},
      include_invoice = #{includeInvoice,jdbcType=VARCHAR},
      need_auto_assess = #{needAutoAssess,jdbcType=CHAR},
      is_affiliate_members = #{isAffiliateMembers,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into common_claim_pet_info
    (channel_report_no, report_no, policy_no, hospital_name, hospital_address, hospital_id, 
      hospital_level, hospital_info, disease_info, include_surgery, include_invoice, 
      need_auto_assess, is_affiliate_members, is_deleted, gmt_created, gmt_modified, 
      creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.channelReportNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.hospitalName,jdbcType=VARCHAR}, #{item.hospitalAddress,jdbcType=VARCHAR}, 
        #{item.hospitalId,jdbcType=VARCHAR}, #{item.hospitalLevel,jdbcType=VARCHAR}, #{item.hospitalInfo,jdbcType=VARCHAR}, 
        #{item.diseaseInfo,jdbcType=VARCHAR}, #{item.includeSurgery,jdbcType=VARCHAR}, 
        #{item.includeInvoice,jdbcType=VARCHAR}, #{item.needAutoAssess,jdbcType=CHAR}, 
        #{item.isAffiliateMembers,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into common_claim_pet_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'channel_report_no'.toString() == column.value">
          #{item.channelReportNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_name'.toString() == column.value">
          #{item.hospitalName,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_address'.toString() == column.value">
          #{item.hospitalAddress,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_id'.toString() == column.value">
          #{item.hospitalId,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_level'.toString() == column.value">
          #{item.hospitalLevel,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_info'.toString() == column.value">
          #{item.hospitalInfo,jdbcType=VARCHAR}
        </if>
        <if test="'disease_info'.toString() == column.value">
          #{item.diseaseInfo,jdbcType=VARCHAR}
        </if>
        <if test="'include_surgery'.toString() == column.value">
          #{item.includeSurgery,jdbcType=VARCHAR}
        </if>
        <if test="'include_invoice'.toString() == column.value">
          #{item.includeInvoice,jdbcType=VARCHAR}
        </if>
        <if test="'need_auto_assess'.toString() == column.value">
          #{item.needAutoAssess,jdbcType=CHAR}
        </if>
        <if test="'is_affiliate_members'.toString() == column.value">
          #{item.isAffiliateMembers,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>