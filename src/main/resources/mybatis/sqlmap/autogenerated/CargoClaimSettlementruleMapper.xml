<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.CargoClaimSettlementruleMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.CargoClaimSettlementruleDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="package_full_name" jdbcType="VARCHAR" property="packageFullName" />
    <result column="liability_code" jdbcType="VARCHAR" property="liabilityCode" />
    <result column="liability_name" jdbcType="VARCHAR" property="liabilityName" />
    <result column="settlement_rule" jdbcType="VARCHAR" property="settlementRule" />
    <result column="share_deductible_type" jdbcType="VARCHAR" property="shareDeductibleType" />
    <result column="deductible" jdbcType="VARCHAR" property="deductible" />
    <result column="in_payment_proportion" jdbcType="VARCHAR" property="inPaymentProportion" />
    <result column="out_deductible" jdbcType="VARCHAR" property="outDeductible" />
    <result column="out_payment_proportion" jdbcType="VARCHAR" property="outPaymentProportion" />
    <result column="social_payment_proportion_is_same" jdbcType="VARCHAR" property="socialPaymentProportionIsSame" />
    <result column="social_dedutible_is_same" jdbcType="VARCHAR" property="socialDedutibleIsSame" />
    <result column="social_bill" jdbcType="VARCHAR" property="socialBill" />
    <result column="ordinary_bill" jdbcType="VARCHAR" property="ordinaryBill" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="configured" jdbcType="CHAR" property="configured" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, package_def_id, package_full_name, liability_code, liability_name, settlement_rule, 
    share_deductible_type, deductible, in_payment_proportion, out_deductible, out_payment_proportion, 
    social_payment_proportion_is_same, social_dedutible_is_same, social_bill, ordinary_bill, 
    creator, gmt_created, modifier, gmt_modified, is_deleted, configured
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.CargoClaimSettlementruleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_claim_settlementrule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_claim_settlementrule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.CargoClaimSettlementruleDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cargo_claim_settlementrule (package_def_id, package_full_name, liability_code, 
      liability_name, settlement_rule, share_deductible_type, 
      deductible, in_payment_proportion, out_deductible, 
      out_payment_proportion, social_payment_proportion_is_same, 
      social_dedutible_is_same, social_bill, ordinary_bill, 
      creator, gmt_created, modifier, 
      gmt_modified, is_deleted, configured
      )
    values (#{packageDefId,jdbcType=BIGINT}, #{packageFullName,jdbcType=VARCHAR}, #{liabilityCode,jdbcType=VARCHAR}, 
      #{liabilityName,jdbcType=VARCHAR}, #{settlementRule,jdbcType=VARCHAR}, #{shareDeductibleType,jdbcType=VARCHAR}, 
      #{deductible,jdbcType=VARCHAR}, #{inPaymentProportion,jdbcType=VARCHAR}, #{outDeductible,jdbcType=VARCHAR}, 
      #{outPaymentProportion,jdbcType=VARCHAR}, #{socialPaymentProportionIsSame,jdbcType=VARCHAR}, 
      #{socialDedutibleIsSame,jdbcType=VARCHAR}, #{socialBill,jdbcType=VARCHAR}, #{ordinaryBill,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, sysdate(), #{modifier,jdbcType=VARCHAR}, 
      sysdate(), #{isDeleted,jdbcType=CHAR}, #{configured,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.CargoClaimSettlementruleDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cargo_claim_settlementrule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="packageFullName != null">
        package_full_name,
      </if>
      <if test="liabilityCode != null">
        liability_code,
      </if>
      <if test="liabilityName != null">
        liability_name,
      </if>
      <if test="settlementRule != null">
        settlement_rule,
      </if>
      <if test="shareDeductibleType != null">
        share_deductible_type,
      </if>
      <if test="deductible != null">
        deductible,
      </if>
      <if test="inPaymentProportion != null">
        in_payment_proportion,
      </if>
      <if test="outDeductible != null">
        out_deductible,
      </if>
      <if test="outPaymentProportion != null">
        out_payment_proportion,
      </if>
      <if test="socialPaymentProportionIsSame != null">
        social_payment_proportion_is_same,
      </if>
      <if test="socialDedutibleIsSame != null">
        social_dedutible_is_same,
      </if>
      <if test="socialBill != null">
        social_bill,
      </if>
      <if test="ordinaryBill != null">
        ordinary_bill,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="configured != null">
        configured,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="packageFullName != null">
        #{packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityCode != null">
        #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="liabilityName != null">
        #{liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="settlementRule != null">
        #{settlementRule,jdbcType=VARCHAR},
      </if>
      <if test="shareDeductibleType != null">
        #{shareDeductibleType,jdbcType=VARCHAR},
      </if>
      <if test="deductible != null">
        #{deductible,jdbcType=VARCHAR},
      </if>
      <if test="inPaymentProportion != null">
        #{inPaymentProportion,jdbcType=VARCHAR},
      </if>
      <if test="outDeductible != null">
        #{outDeductible,jdbcType=VARCHAR},
      </if>
      <if test="outPaymentProportion != null">
        #{outPaymentProportion,jdbcType=VARCHAR},
      </if>
      <if test="socialPaymentProportionIsSame != null">
        #{socialPaymentProportionIsSame,jdbcType=VARCHAR},
      </if>
      <if test="socialDedutibleIsSame != null">
        #{socialDedutibleIsSame,jdbcType=VARCHAR},
      </if>
      <if test="socialBill != null">
        #{socialBill,jdbcType=VARCHAR},
      </if>
      <if test="ordinaryBill != null">
        #{ordinaryBill,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="configured != null">
        #{configured,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.CargoClaimSettlementruleExample" resultType="java.lang.Long">
    select count(*) from cargo_claim_settlementrule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_claim_settlementrule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.packageFullName != null">
        package_full_name = #{record.packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityCode != null">
        liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityName != null">
        liability_name = #{record.liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="record.settlementRule != null">
        settlement_rule = #{record.settlementRule,jdbcType=VARCHAR},
      </if>
      <if test="record.shareDeductibleType != null">
        share_deductible_type = #{record.shareDeductibleType,jdbcType=VARCHAR},
      </if>
      <if test="record.deductible != null">
        deductible = #{record.deductible,jdbcType=VARCHAR},
      </if>
      <if test="record.inPaymentProportion != null">
        in_payment_proportion = #{record.inPaymentProportion,jdbcType=VARCHAR},
      </if>
      <if test="record.outDeductible != null">
        out_deductible = #{record.outDeductible,jdbcType=VARCHAR},
      </if>
      <if test="record.outPaymentProportion != null">
        out_payment_proportion = #{record.outPaymentProportion,jdbcType=VARCHAR},
      </if>
      <if test="record.socialPaymentProportionIsSame != null">
        social_payment_proportion_is_same = #{record.socialPaymentProportionIsSame,jdbcType=VARCHAR},
      </if>
      <if test="record.socialDedutibleIsSame != null">
        social_dedutible_is_same = #{record.socialDedutibleIsSame,jdbcType=VARCHAR},
      </if>
      <if test="record.socialBill != null">
        social_bill = #{record.socialBill,jdbcType=VARCHAR},
      </if>
      <if test="record.ordinaryBill != null">
        ordinary_bill = #{record.ordinaryBill,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.configured != null">
        configured = #{record.configured,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_claim_settlementrule
    set id = #{record.id,jdbcType=BIGINT},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      package_full_name = #{record.packageFullName,jdbcType=VARCHAR},
      liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      liability_name = #{record.liabilityName,jdbcType=VARCHAR},
      settlement_rule = #{record.settlementRule,jdbcType=VARCHAR},
      share_deductible_type = #{record.shareDeductibleType,jdbcType=VARCHAR},
      deductible = #{record.deductible,jdbcType=VARCHAR},
      in_payment_proportion = #{record.inPaymentProportion,jdbcType=VARCHAR},
      out_deductible = #{record.outDeductible,jdbcType=VARCHAR},
      out_payment_proportion = #{record.outPaymentProportion,jdbcType=VARCHAR},
      social_payment_proportion_is_same = #{record.socialPaymentProportionIsSame,jdbcType=VARCHAR},
      social_dedutible_is_same = #{record.socialDedutibleIsSame,jdbcType=VARCHAR},
      social_bill = #{record.socialBill,jdbcType=VARCHAR},
      ordinary_bill = #{record.ordinaryBill,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      configured = #{record.configured,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.CargoClaimSettlementruleDO">
    update cargo_claim_settlementrule
    <set>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="packageFullName != null">
        package_full_name = #{packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityCode != null">
        liability_code = #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="liabilityName != null">
        liability_name = #{liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="settlementRule != null">
        settlement_rule = #{settlementRule,jdbcType=VARCHAR},
      </if>
      <if test="shareDeductibleType != null">
        share_deductible_type = #{shareDeductibleType,jdbcType=VARCHAR},
      </if>
      <if test="deductible != null">
        deductible = #{deductible,jdbcType=VARCHAR},
      </if>
      <if test="inPaymentProportion != null">
        in_payment_proportion = #{inPaymentProportion,jdbcType=VARCHAR},
      </if>
      <if test="outDeductible != null">
        out_deductible = #{outDeductible,jdbcType=VARCHAR},
      </if>
      <if test="outPaymentProportion != null">
        out_payment_proportion = #{outPaymentProportion,jdbcType=VARCHAR},
      </if>
      <if test="socialPaymentProportionIsSame != null">
        social_payment_proportion_is_same = #{socialPaymentProportionIsSame,jdbcType=VARCHAR},
      </if>
      <if test="socialDedutibleIsSame != null">
        social_dedutible_is_same = #{socialDedutibleIsSame,jdbcType=VARCHAR},
      </if>
      <if test="socialBill != null">
        social_bill = #{socialBill,jdbcType=VARCHAR},
      </if>
      <if test="ordinaryBill != null">
        ordinary_bill = #{ordinaryBill,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="configured != null">
        configured = #{configured,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.CargoClaimSettlementruleDO">
    update cargo_claim_settlementrule
    set package_def_id = #{packageDefId,jdbcType=BIGINT},
      package_full_name = #{packageFullName,jdbcType=VARCHAR},
      liability_code = #{liabilityCode,jdbcType=VARCHAR},
      liability_name = #{liabilityName,jdbcType=VARCHAR},
      settlement_rule = #{settlementRule,jdbcType=VARCHAR},
      share_deductible_type = #{shareDeductibleType,jdbcType=VARCHAR},
      deductible = #{deductible,jdbcType=VARCHAR},
      in_payment_proportion = #{inPaymentProportion,jdbcType=VARCHAR},
      out_deductible = #{outDeductible,jdbcType=VARCHAR},
      out_payment_proportion = #{outPaymentProportion,jdbcType=VARCHAR},
      social_payment_proportion_is_same = #{socialPaymentProportionIsSame,jdbcType=VARCHAR},
      social_dedutible_is_same = #{socialDedutibleIsSame,jdbcType=VARCHAR},
      social_bill = #{socialBill,jdbcType=VARCHAR},
      ordinary_bill = #{ordinaryBill,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR},
      configured = #{configured,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into cargo_claim_settlementrule
    (package_def_id, package_full_name, liability_code, liability_name, settlement_rule, 
      share_deductible_type, deductible, in_payment_proportion, out_deductible, out_payment_proportion, 
      social_payment_proportion_is_same, social_dedutible_is_same, social_bill, ordinary_bill, 
      creator, gmt_created, modifier, gmt_modified, is_deleted, configured)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.packageDefId,jdbcType=BIGINT}, #{item.packageFullName,jdbcType=VARCHAR}, 
        #{item.liabilityCode,jdbcType=VARCHAR}, #{item.liabilityName,jdbcType=VARCHAR}, 
        #{item.settlementRule,jdbcType=VARCHAR}, #{item.shareDeductibleType,jdbcType=VARCHAR}, 
        #{item.deductible,jdbcType=VARCHAR}, #{item.inPaymentProportion,jdbcType=VARCHAR}, 
        #{item.outDeductible,jdbcType=VARCHAR}, #{item.outPaymentProportion,jdbcType=VARCHAR}, 
        #{item.socialPaymentProportionIsSame,jdbcType=VARCHAR}, #{item.socialDedutibleIsSame,jdbcType=VARCHAR}, 
        #{item.socialBill,jdbcType=VARCHAR}, #{item.ordinaryBill,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.configured,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into cargo_claim_settlementrule (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'package_full_name'.toString() == column.value">
          #{item.packageFullName,jdbcType=VARCHAR}
        </if>
        <if test="'liability_code'.toString() == column.value">
          #{item.liabilityCode,jdbcType=VARCHAR}
        </if>
        <if test="'liability_name'.toString() == column.value">
          #{item.liabilityName,jdbcType=VARCHAR}
        </if>
        <if test="'settlement_rule'.toString() == column.value">
          #{item.settlementRule,jdbcType=VARCHAR}
        </if>
        <if test="'share_deductible_type'.toString() == column.value">
          #{item.shareDeductibleType,jdbcType=VARCHAR}
        </if>
        <if test="'deductible'.toString() == column.value">
          #{item.deductible,jdbcType=VARCHAR}
        </if>
        <if test="'in_payment_proportion'.toString() == column.value">
          #{item.inPaymentProportion,jdbcType=VARCHAR}
        </if>
        <if test="'out_deductible'.toString() == column.value">
          #{item.outDeductible,jdbcType=VARCHAR}
        </if>
        <if test="'out_payment_proportion'.toString() == column.value">
          #{item.outPaymentProportion,jdbcType=VARCHAR}
        </if>
        <if test="'social_payment_proportion_is_same'.toString() == column.value">
          #{item.socialPaymentProportionIsSame,jdbcType=VARCHAR}
        </if>
        <if test="'social_dedutible_is_same'.toString() == column.value">
          #{item.socialDedutibleIsSame,jdbcType=VARCHAR}
        </if>
        <if test="'social_bill'.toString() == column.value">
          #{item.socialBill,jdbcType=VARCHAR}
        </if>
        <if test="'ordinary_bill'.toString() == column.value">
          #{item.ordinaryBill,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'configured'.toString() == column.value">
          #{item.configured,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>