<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.CsCreedMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.CsCreedDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="traffic_insurance_type" jdbcType="VARCHAR" property="trafficInsuranceType" />
    <result column="traffic_insurance_type_name" jdbcType="VARCHAR" property="trafficInsuranceTypeName" />
    <result column="cs_creed" jdbcType="VARCHAR" property="csCreed" />
    <result column="loss_cause" jdbcType="VARCHAR" property="lossCause" />
    <result column="loss_cause_name" jdbcType="VARCHAR" property="lossCauseName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="accident_desc" jdbcType="VARCHAR" property="accidentDesc" />
    <result column="scirpt_type" jdbcType="CHAR" property="scirptType" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, traffic_insurance_type, traffic_insurance_type_name, cs_creed, loss_cause, loss_cause_name, 
    remark, accident_desc, scirpt_type, is_deleted, gmt_created, gmt_modified, creator, 
    modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.CsCreedExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cs_creed
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cs_creed
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.CsCreedDO">
    insert into cs_creed (id, traffic_insurance_type, traffic_insurance_type_name, 
      cs_creed, loss_cause, loss_cause_name, 
      remark, accident_desc, scirpt_type, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier)
    values (#{id,jdbcType=BIGINT}, #{trafficInsuranceType,jdbcType=VARCHAR}, #{trafficInsuranceTypeName,jdbcType=VARCHAR}, 
      #{csCreed,jdbcType=VARCHAR}, #{lossCause,jdbcType=VARCHAR}, #{lossCauseName,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{accidentDesc,jdbcType=VARCHAR}, #{scirptType,jdbcType=CHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.CsCreedDO">
    insert into cs_creed
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="trafficInsuranceType != null">
        traffic_insurance_type,
      </if>
      <if test="trafficInsuranceTypeName != null">
        traffic_insurance_type_name,
      </if>
      <if test="csCreed != null">
        cs_creed,
      </if>
      <if test="lossCause != null">
        loss_cause,
      </if>
      <if test="lossCauseName != null">
        loss_cause_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="accidentDesc != null">
        accident_desc,
      </if>
      <if test="scirptType != null">
        scirpt_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="trafficInsuranceType != null">
        #{trafficInsuranceType,jdbcType=VARCHAR},
      </if>
      <if test="trafficInsuranceTypeName != null">
        #{trafficInsuranceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="csCreed != null">
        #{csCreed,jdbcType=VARCHAR},
      </if>
      <if test="lossCause != null">
        #{lossCause,jdbcType=VARCHAR},
      </if>
      <if test="lossCauseName != null">
        #{lossCauseName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="accidentDesc != null">
        #{accidentDesc,jdbcType=VARCHAR},
      </if>
      <if test="scirptType != null">
        #{scirptType,jdbcType=CHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.CsCreedExample" resultType="java.lang.Long">
    select count(*) from cs_creed
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cs_creed
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.trafficInsuranceType != null">
        traffic_insurance_type = #{record.trafficInsuranceType,jdbcType=VARCHAR},
      </if>
      <if test="record.trafficInsuranceTypeName != null">
        traffic_insurance_type_name = #{record.trafficInsuranceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.csCreed != null">
        cs_creed = #{record.csCreed,jdbcType=VARCHAR},
      </if>
      <if test="record.lossCause != null">
        loss_cause = #{record.lossCause,jdbcType=VARCHAR},
      </if>
      <if test="record.lossCauseName != null">
        loss_cause_name = #{record.lossCauseName,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentDesc != null">
        accident_desc = #{record.accidentDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.scirptType != null">
        scirpt_type = #{record.scirptType,jdbcType=CHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cs_creed
    set id = #{record.id,jdbcType=BIGINT},
      traffic_insurance_type = #{record.trafficInsuranceType,jdbcType=VARCHAR},
      traffic_insurance_type_name = #{record.trafficInsuranceTypeName,jdbcType=VARCHAR},
      cs_creed = #{record.csCreed,jdbcType=VARCHAR},
      loss_cause = #{record.lossCause,jdbcType=VARCHAR},
      loss_cause_name = #{record.lossCauseName,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      accident_desc = #{record.accidentDesc,jdbcType=VARCHAR},
      scirpt_type = #{record.scirptType,jdbcType=CHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.CsCreedDO">
    update cs_creed
    <set>
      <if test="trafficInsuranceType != null">
        traffic_insurance_type = #{trafficInsuranceType,jdbcType=VARCHAR},
      </if>
      <if test="trafficInsuranceTypeName != null">
        traffic_insurance_type_name = #{trafficInsuranceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="csCreed != null">
        cs_creed = #{csCreed,jdbcType=VARCHAR},
      </if>
      <if test="lossCause != null">
        loss_cause = #{lossCause,jdbcType=VARCHAR},
      </if>
      <if test="lossCauseName != null">
        loss_cause_name = #{lossCauseName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="accidentDesc != null">
        accident_desc = #{accidentDesc,jdbcType=VARCHAR},
      </if>
      <if test="scirptType != null">
        scirpt_type = #{scirptType,jdbcType=CHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.CsCreedDO">
    update cs_creed
    set traffic_insurance_type = #{trafficInsuranceType,jdbcType=VARCHAR},
      traffic_insurance_type_name = #{trafficInsuranceTypeName,jdbcType=VARCHAR},
      cs_creed = #{csCreed,jdbcType=VARCHAR},
      loss_cause = #{lossCause,jdbcType=VARCHAR},
      loss_cause_name = #{lossCauseName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      accident_desc = #{accidentDesc,jdbcType=VARCHAR},
      scirpt_type = #{scirptType,jdbcType=CHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cs_creed
    (id, traffic_insurance_type, traffic_insurance_type_name, cs_creed, loss_cause, loss_cause_name, 
      remark, accident_desc, scirpt_type, is_deleted, gmt_created, gmt_modified, creator, 
      modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.trafficInsuranceType,jdbcType=VARCHAR}, #{item.trafficInsuranceTypeName,jdbcType=VARCHAR}, 
        #{item.csCreed,jdbcType=VARCHAR}, #{item.lossCause,jdbcType=VARCHAR}, #{item.lossCauseName,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.accidentDesc,jdbcType=VARCHAR}, #{item.scirptType,jdbcType=CHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cs_creed (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'traffic_insurance_type'.toString() == column.value">
          #{item.trafficInsuranceType,jdbcType=VARCHAR}
        </if>
        <if test="'traffic_insurance_type_name'.toString() == column.value">
          #{item.trafficInsuranceTypeName,jdbcType=VARCHAR}
        </if>
        <if test="'cs_creed'.toString() == column.value">
          #{item.csCreed,jdbcType=VARCHAR}
        </if>
        <if test="'loss_cause'.toString() == column.value">
          #{item.lossCause,jdbcType=VARCHAR}
        </if>
        <if test="'loss_cause_name'.toString() == column.value">
          #{item.lossCauseName,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'accident_desc'.toString() == column.value">
          #{item.accidentDesc,jdbcType=VARCHAR}
        </if>
        <if test="'scirpt_type'.toString() == column.value">
          #{item.scirptType,jdbcType=CHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>