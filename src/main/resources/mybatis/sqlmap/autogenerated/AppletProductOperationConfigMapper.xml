<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AppletProductOperationConfigMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AppletProductOperationConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="campaign_def_id" jdbcType="BIGINT" property="campaignDefId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="campaign_name" jdbcType="VARCHAR" property="campaignName" />
    <result column="renewal" jdbcType="BIT" property="renewal" />
    <result column="online_claim" jdbcType="BIT" property="onlineClaim" />
    <result column="online_refund" jdbcType="BIT" property="onlineRefund" />
    <result column="online_pre_claim" jdbcType="BIT" property="onlinePreClaim" />
    <result column="online_date_shift" jdbcType="BIT" property="onlineDateShift" />
    <result column="refund_type" jdbcType="VARCHAR" property="refundType" />
    <result column="product_property" jdbcType="VARCHAR" property="productProperty" />
    <result column="templates" jdbcType="VARCHAR" property="templates" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="channel_source" jdbcType="INTEGER" property="channelSource" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, package_def_id, campaign_def_id, product_name, package_name, campaign_name, renewal, 
    online_claim, online_refund, online_pre_claim, online_date_shift, refund_type, product_property, 
    templates, remark, extra_info, creator, gmt_created, modifier, gmt_modified, is_deleted, 
    channel_source
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AppletProductOperationConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from applet_product_operation_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from applet_product_operation_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AppletProductOperationConfigDO">
    insert into applet_product_operation_config (id, package_def_id, campaign_def_id, 
      product_name, package_name, campaign_name, 
      renewal, online_claim, online_refund, 
      online_pre_claim, online_date_shift, refund_type, 
      product_property, templates, remark, 
      extra_info, creator, gmt_created, 
      modifier, gmt_modified, is_deleted, 
      channel_source)
    values (#{id,jdbcType=BIGINT}, #{packageDefId,jdbcType=BIGINT}, #{campaignDefId,jdbcType=BIGINT}, 
      #{productName,jdbcType=VARCHAR}, #{packageName,jdbcType=VARCHAR}, #{campaignName,jdbcType=VARCHAR}, 
      #{renewal,jdbcType=BIT}, #{onlineClaim,jdbcType=BIT}, #{onlineRefund,jdbcType=BIT}, 
      #{onlinePreClaim,jdbcType=BIT}, #{onlineDateShift,jdbcType=BIT}, #{refundType,jdbcType=VARCHAR}, 
      #{productProperty,jdbcType=VARCHAR}, #{templates,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{extraInfo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, sysdate(), 
      #{modifier,jdbcType=VARCHAR}, sysdate(), #{isDeleted,jdbcType=CHAR}, 
      #{channelSource,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AppletProductOperationConfigDO">
    insert into applet_product_operation_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="packageName != null">
        package_name,
      </if>
      <if test="campaignName != null">
        campaign_name,
      </if>
      <if test="renewal != null">
        renewal,
      </if>
      <if test="onlineClaim != null">
        online_claim,
      </if>
      <if test="onlineRefund != null">
        online_refund,
      </if>
      <if test="onlinePreClaim != null">
        online_pre_claim,
      </if>
      <if test="onlineDateShift != null">
        online_date_shift,
      </if>
      <if test="refundType != null">
        refund_type,
      </if>
      <if test="productProperty != null">
        product_property,
      </if>
      <if test="templates != null">
        templates,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="channelSource != null">
        channel_source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="campaignName != null">
        #{campaignName,jdbcType=VARCHAR},
      </if>
      <if test="renewal != null">
        #{renewal,jdbcType=BIT},
      </if>
      <if test="onlineClaim != null">
        #{onlineClaim,jdbcType=BIT},
      </if>
      <if test="onlineRefund != null">
        #{onlineRefund,jdbcType=BIT},
      </if>
      <if test="onlinePreClaim != null">
        #{onlinePreClaim,jdbcType=BIT},
      </if>
      <if test="onlineDateShift != null">
        #{onlineDateShift,jdbcType=BIT},
      </if>
      <if test="refundType != null">
        #{refundType,jdbcType=VARCHAR},
      </if>
      <if test="productProperty != null">
        #{productProperty,jdbcType=VARCHAR},
      </if>
      <if test="templates != null">
        #{templates,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="channelSource != null">
        #{channelSource,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AppletProductOperationConfigExample" resultType="java.lang.Long">
    select count(*) from applet_product_operation_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update applet_product_operation_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageName != null">
        package_name = #{record.packageName,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignName != null">
        campaign_name = #{record.campaignName,jdbcType=VARCHAR},
      </if>
      <if test="record.renewal != null">
        renewal = #{record.renewal,jdbcType=BIT},
      </if>
      <if test="record.onlineClaim != null">
        online_claim = #{record.onlineClaim,jdbcType=BIT},
      </if>
      <if test="record.onlineRefund != null">
        online_refund = #{record.onlineRefund,jdbcType=BIT},
      </if>
      <if test="record.onlinePreClaim != null">
        online_pre_claim = #{record.onlinePreClaim,jdbcType=BIT},
      </if>
      <if test="record.onlineDateShift != null">
        online_date_shift = #{record.onlineDateShift,jdbcType=BIT},
      </if>
      <if test="record.refundType != null">
        refund_type = #{record.refundType,jdbcType=VARCHAR},
      </if>
      <if test="record.productProperty != null">
        product_property = #{record.productProperty,jdbcType=VARCHAR},
      </if>
      <if test="record.templates != null">
        templates = #{record.templates,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.channelSource != null">
        channel_source = #{record.channelSource,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update applet_product_operation_config
    set id = #{record.id,jdbcType=BIGINT},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      product_name = #{record.productName,jdbcType=VARCHAR},
      package_name = #{record.packageName,jdbcType=VARCHAR},
      campaign_name = #{record.campaignName,jdbcType=VARCHAR},
      renewal = #{record.renewal,jdbcType=BIT},
      online_claim = #{record.onlineClaim,jdbcType=BIT},
      online_refund = #{record.onlineRefund,jdbcType=BIT},
      online_pre_claim = #{record.onlinePreClaim,jdbcType=BIT},
      online_date_shift = #{record.onlineDateShift,jdbcType=BIT},
      refund_type = #{record.refundType,jdbcType=VARCHAR},
      product_property = #{record.productProperty,jdbcType=VARCHAR},
      templates = #{record.templates,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      channel_source = #{record.channelSource,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AppletProductOperationConfigDO">
    update applet_product_operation_config
    <set>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        package_name = #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="campaignName != null">
        campaign_name = #{campaignName,jdbcType=VARCHAR},
      </if>
      <if test="renewal != null">
        renewal = #{renewal,jdbcType=BIT},
      </if>
      <if test="onlineClaim != null">
        online_claim = #{onlineClaim,jdbcType=BIT},
      </if>
      <if test="onlineRefund != null">
        online_refund = #{onlineRefund,jdbcType=BIT},
      </if>
      <if test="onlinePreClaim != null">
        online_pre_claim = #{onlinePreClaim,jdbcType=BIT},
      </if>
      <if test="onlineDateShift != null">
        online_date_shift = #{onlineDateShift,jdbcType=BIT},
      </if>
      <if test="refundType != null">
        refund_type = #{refundType,jdbcType=VARCHAR},
      </if>
      <if test="productProperty != null">
        product_property = #{productProperty,jdbcType=VARCHAR},
      </if>
      <if test="templates != null">
        templates = #{templates,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="channelSource != null">
        channel_source = #{channelSource,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AppletProductOperationConfigDO">
    update applet_product_operation_config
    set package_def_id = #{packageDefId,jdbcType=BIGINT},
      campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      product_name = #{productName,jdbcType=VARCHAR},
      package_name = #{packageName,jdbcType=VARCHAR},
      campaign_name = #{campaignName,jdbcType=VARCHAR},
      renewal = #{renewal,jdbcType=BIT},
      online_claim = #{onlineClaim,jdbcType=BIT},
      online_refund = #{onlineRefund,jdbcType=BIT},
      online_pre_claim = #{onlinePreClaim,jdbcType=BIT},
      online_date_shift = #{onlineDateShift,jdbcType=BIT},
      refund_type = #{refundType,jdbcType=VARCHAR},
      product_property = #{productProperty,jdbcType=VARCHAR},
      templates = #{templates,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR},
      channel_source = #{channelSource,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into applet_product_operation_config
    (id, package_def_id, campaign_def_id, product_name, package_name, campaign_name, 
      renewal, online_claim, online_refund, online_pre_claim, online_date_shift, refund_type, 
      product_property, templates, remark, extra_info, creator, gmt_created, modifier, 
      gmt_modified, is_deleted, channel_source)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.packageDefId,jdbcType=BIGINT}, #{item.campaignDefId,jdbcType=BIGINT}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.packageName,jdbcType=VARCHAR}, #{item.campaignName,jdbcType=VARCHAR}, 
        #{item.renewal,jdbcType=BIT}, #{item.onlineClaim,jdbcType=BIT}, #{item.onlineRefund,jdbcType=BIT}, 
        #{item.onlinePreClaim,jdbcType=BIT}, #{item.onlineDateShift,jdbcType=BIT}, #{item.refundType,jdbcType=VARCHAR}, 
        #{item.productProperty,jdbcType=VARCHAR}, #{item.templates,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.channelSource,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into applet_product_operation_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=BIGINT}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'package_name'.toString() == column.value">
          #{item.packageName,jdbcType=VARCHAR}
        </if>
        <if test="'campaign_name'.toString() == column.value">
          #{item.campaignName,jdbcType=VARCHAR}
        </if>
        <if test="'renewal'.toString() == column.value">
          #{item.renewal,jdbcType=BIT}
        </if>
        <if test="'online_claim'.toString() == column.value">
          #{item.onlineClaim,jdbcType=BIT}
        </if>
        <if test="'online_refund'.toString() == column.value">
          #{item.onlineRefund,jdbcType=BIT}
        </if>
        <if test="'online_pre_claim'.toString() == column.value">
          #{item.onlinePreClaim,jdbcType=BIT}
        </if>
        <if test="'online_date_shift'.toString() == column.value">
          #{item.onlineDateShift,jdbcType=BIT}
        </if>
        <if test="'refund_type'.toString() == column.value">
          #{item.refundType,jdbcType=VARCHAR}
        </if>
        <if test="'product_property'.toString() == column.value">
          #{item.productProperty,jdbcType=VARCHAR}
        </if>
        <if test="'templates'.toString() == column.value">
          #{item.templates,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'channel_source'.toString() == column.value">
          #{item.channelSource,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>