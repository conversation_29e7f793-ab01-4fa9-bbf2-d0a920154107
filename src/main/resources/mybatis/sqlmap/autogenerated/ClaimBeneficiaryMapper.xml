<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimBeneficiaryMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimBeneficiaryDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="batch_claim_bill_id" jdbcType="BIGINT" property="batchClaimBillId" />
    <result column="cert_name" jdbcType="VARCHAR" property="certName" />
    <result column="cert_no" jdbcType="VARCHAR" property="certNo" />
    <result column="cert_type" jdbcType="VARCHAR" property="certType" />
    <result column="insured_relationship" jdbcType="VARCHAR" property="insuredRelationship" />
    <result column="policyholder_relationship" jdbcType="VARCHAR" property="policyholderRelationship" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="character_identity" jdbcType="VARCHAR" property="characterIdentity" />
    <result column="customer_type" jdbcType="VARCHAR" property="customerType" />
    <result column="cert_validity_start" jdbcType="VARCHAR" property="certValidityStart" />
    <result column="cert_validity_end" jdbcType="VARCHAR" property="certValidityEnd" />
    <result column="gender" jdbcType="CHAR" property="gender" />
    <result column="nationality" jdbcType="VARCHAR" property="nationality" />
    <result column="occupation" jdbcType="VARCHAR" property="occupation" />
    <result column="contact_tel" jdbcType="VARCHAR" property="contactTel" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="business_scope" jdbcType="VARCHAR" property="businessScope" />
    <result column="pay_bank_info_id" jdbcType="BIGINT" property="payBankInfoId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, batch_claim_bill_id, cert_name, cert_no, cert_type, insured_relationship, 
    policyholder_relationship, creator, modifier, gmt_created, gmt_modified, is_deleted, 
    character_identity, customer_type, cert_validity_start, cert_validity_end, gender, 
    nationality, occupation, contact_tel, address, business_scope, pay_bank_info_id
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBeneficiaryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_claim_beneficiary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_claim_beneficiary
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimBeneficiaryDO">
    insert into cargo_claim_beneficiary (id, report_no, batch_claim_bill_id, 
      cert_name, cert_no, cert_type, 
      insured_relationship, policyholder_relationship, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted, character_identity, 
      customer_type, cert_validity_start, cert_validity_end, 
      gender, nationality, occupation, 
      contact_tel, address, business_scope, 
      pay_bank_info_id)
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{batchClaimBillId,jdbcType=BIGINT}, 
      #{certName,jdbcType=VARCHAR}, #{certNo,jdbcType=VARCHAR}, #{certType,jdbcType=VARCHAR}, 
      #{insuredRelationship,jdbcType=VARCHAR}, #{policyholderRelationship,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR}, #{characterIdentity,jdbcType=VARCHAR}, 
      #{customerType,jdbcType=VARCHAR}, #{certValidityStart,jdbcType=VARCHAR}, #{certValidityEnd,jdbcType=VARCHAR}, 
      #{gender,jdbcType=CHAR}, #{nationality,jdbcType=VARCHAR}, #{occupation,jdbcType=VARCHAR}, 
      #{contactTel,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{businessScope,jdbcType=VARCHAR}, 
      #{payBankInfoId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBeneficiaryDO">
    insert into cargo_claim_beneficiary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="batchClaimBillId != null">
        batch_claim_bill_id,
      </if>
      <if test="certName != null">
        cert_name,
      </if>
      <if test="certNo != null">
        cert_no,
      </if>
      <if test="certType != null">
        cert_type,
      </if>
      <if test="insuredRelationship != null">
        insured_relationship,
      </if>
      <if test="policyholderRelationship != null">
        policyholder_relationship,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="characterIdentity != null">
        character_identity,
      </if>
      <if test="customerType != null">
        customer_type,
      </if>
      <if test="certValidityStart != null">
        cert_validity_start,
      </if>
      <if test="certValidityEnd != null">
        cert_validity_end,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="nationality != null">
        nationality,
      </if>
      <if test="occupation != null">
        occupation,
      </if>
      <if test="contactTel != null">
        contact_tel,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="businessScope != null">
        business_scope,
      </if>
      <if test="payBankInfoId != null">
        pay_bank_info_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillId != null">
        #{batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="certName != null">
        #{certName,jdbcType=VARCHAR},
      </if>
      <if test="certNo != null">
        #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="certType != null">
        #{certType,jdbcType=VARCHAR},
      </if>
      <if test="insuredRelationship != null">
        #{insuredRelationship,jdbcType=VARCHAR},
      </if>
      <if test="policyholderRelationship != null">
        #{policyholderRelationship,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="characterIdentity != null">
        #{characterIdentity,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null">
        #{customerType,jdbcType=VARCHAR},
      </if>
      <if test="certValidityStart != null">
        #{certValidityStart,jdbcType=VARCHAR},
      </if>
      <if test="certValidityEnd != null">
        #{certValidityEnd,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=CHAR},
      </if>
      <if test="nationality != null">
        #{nationality,jdbcType=VARCHAR},
      </if>
      <if test="occupation != null">
        #{occupation,jdbcType=VARCHAR},
      </if>
      <if test="contactTel != null">
        #{contactTel,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null">
        #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="payBankInfoId != null">
        #{payBankInfoId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBeneficiaryExample" resultType="java.lang.Long">
    select count(*) from cargo_claim_beneficiary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_claim_beneficiary
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchClaimBillId != null">
        batch_claim_bill_id = #{record.batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="record.certName != null">
        cert_name = #{record.certName,jdbcType=VARCHAR},
      </if>
      <if test="record.certNo != null">
        cert_no = #{record.certNo,jdbcType=VARCHAR},
      </if>
      <if test="record.certType != null">
        cert_type = #{record.certType,jdbcType=VARCHAR},
      </if>
      <if test="record.insuredRelationship != null">
        insured_relationship = #{record.insuredRelationship,jdbcType=VARCHAR},
      </if>
      <if test="record.policyholderRelationship != null">
        policyholder_relationship = #{record.policyholderRelationship,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.characterIdentity != null">
        character_identity = #{record.characterIdentity,jdbcType=VARCHAR},
      </if>
      <if test="record.customerType != null">
        customer_type = #{record.customerType,jdbcType=VARCHAR},
      </if>
      <if test="record.certValidityStart != null">
        cert_validity_start = #{record.certValidityStart,jdbcType=VARCHAR},
      </if>
      <if test="record.certValidityEnd != null">
        cert_validity_end = #{record.certValidityEnd,jdbcType=VARCHAR},
      </if>
      <if test="record.gender != null">
        gender = #{record.gender,jdbcType=CHAR},
      </if>
      <if test="record.nationality != null">
        nationality = #{record.nationality,jdbcType=VARCHAR},
      </if>
      <if test="record.occupation != null">
        occupation = #{record.occupation,jdbcType=VARCHAR},
      </if>
      <if test="record.contactTel != null">
        contact_tel = #{record.contactTel,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.businessScope != null">
        business_scope = #{record.businessScope,jdbcType=VARCHAR},
      </if>
      <if test="record.payBankInfoId != null">
        pay_bank_info_id = #{record.payBankInfoId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_claim_beneficiary
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      batch_claim_bill_id = #{record.batchClaimBillId,jdbcType=BIGINT},
      cert_name = #{record.certName,jdbcType=VARCHAR},
      cert_no = #{record.certNo,jdbcType=VARCHAR},
      cert_type = #{record.certType,jdbcType=VARCHAR},
      insured_relationship = #{record.insuredRelationship,jdbcType=VARCHAR},
      policyholder_relationship = #{record.policyholderRelationship,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      character_identity = #{record.characterIdentity,jdbcType=VARCHAR},
      customer_type = #{record.customerType,jdbcType=VARCHAR},
      cert_validity_start = #{record.certValidityStart,jdbcType=VARCHAR},
      cert_validity_end = #{record.certValidityEnd,jdbcType=VARCHAR},
      gender = #{record.gender,jdbcType=CHAR},
      nationality = #{record.nationality,jdbcType=VARCHAR},
      occupation = #{record.occupation,jdbcType=VARCHAR},
      contact_tel = #{record.contactTel,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      business_scope = #{record.businessScope,jdbcType=VARCHAR},
      pay_bank_info_id = #{record.payBankInfoId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBeneficiaryDO">
    update cargo_claim_beneficiary
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillId != null">
        batch_claim_bill_id = #{batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="certName != null">
        cert_name = #{certName,jdbcType=VARCHAR},
      </if>
      <if test="certNo != null">
        cert_no = #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="certType != null">
        cert_type = #{certType,jdbcType=VARCHAR},
      </if>
      <if test="insuredRelationship != null">
        insured_relationship = #{insuredRelationship,jdbcType=VARCHAR},
      </if>
      <if test="policyholderRelationship != null">
        policyholder_relationship = #{policyholderRelationship,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="characterIdentity != null">
        character_identity = #{characterIdentity,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null">
        customer_type = #{customerType,jdbcType=VARCHAR},
      </if>
      <if test="certValidityStart != null">
        cert_validity_start = #{certValidityStart,jdbcType=VARCHAR},
      </if>
      <if test="certValidityEnd != null">
        cert_validity_end = #{certValidityEnd,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=CHAR},
      </if>
      <if test="nationality != null">
        nationality = #{nationality,jdbcType=VARCHAR},
      </if>
      <if test="occupation != null">
        occupation = #{occupation,jdbcType=VARCHAR},
      </if>
      <if test="contactTel != null">
        contact_tel = #{contactTel,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null">
        business_scope = #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="payBankInfoId != null">
        pay_bank_info_id = #{payBankInfoId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimBeneficiaryDO">
    update cargo_claim_beneficiary
    set report_no = #{reportNo,jdbcType=VARCHAR},
      batch_claim_bill_id = #{batchClaimBillId,jdbcType=BIGINT},
      cert_name = #{certName,jdbcType=VARCHAR},
      cert_no = #{certNo,jdbcType=VARCHAR},
      cert_type = #{certType,jdbcType=VARCHAR},
      insured_relationship = #{insuredRelationship,jdbcType=VARCHAR},
      policyholder_relationship = #{policyholderRelationship,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR},
      character_identity = #{characterIdentity,jdbcType=VARCHAR},
      customer_type = #{customerType,jdbcType=VARCHAR},
      cert_validity_start = #{certValidityStart,jdbcType=VARCHAR},
      cert_validity_end = #{certValidityEnd,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=CHAR},
      nationality = #{nationality,jdbcType=VARCHAR},
      occupation = #{occupation,jdbcType=VARCHAR},
      contact_tel = #{contactTel,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      business_scope = #{businessScope,jdbcType=VARCHAR},
      pay_bank_info_id = #{payBankInfoId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_claim_beneficiary
    (id, report_no, batch_claim_bill_id, cert_name, cert_no, cert_type, insured_relationship, 
      policyholder_relationship, creator, modifier, gmt_created, gmt_modified, is_deleted, 
      character_identity, customer_type, cert_validity_start, cert_validity_end, gender, 
      nationality, occupation, contact_tel, address, business_scope, pay_bank_info_id
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.batchClaimBillId,jdbcType=BIGINT}, 
        #{item.certName,jdbcType=VARCHAR}, #{item.certNo,jdbcType=VARCHAR}, #{item.certType,jdbcType=VARCHAR}, 
        #{item.insuredRelationship,jdbcType=VARCHAR}, #{item.policyholderRelationship,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}, #{item.characterIdentity,jdbcType=VARCHAR}, 
        #{item.customerType,jdbcType=VARCHAR}, #{item.certValidityStart,jdbcType=VARCHAR}, 
        #{item.certValidityEnd,jdbcType=VARCHAR}, #{item.gender,jdbcType=CHAR}, #{item.nationality,jdbcType=VARCHAR}, 
        #{item.occupation,jdbcType=VARCHAR}, #{item.contactTel,jdbcType=VARCHAR}, #{item.address,jdbcType=VARCHAR}, 
        #{item.businessScope,jdbcType=VARCHAR}, #{item.payBankInfoId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_claim_beneficiary (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'batch_claim_bill_id'.toString() == column.value">
          #{item.batchClaimBillId,jdbcType=BIGINT}
        </if>
        <if test="'cert_name'.toString() == column.value">
          #{item.certName,jdbcType=VARCHAR}
        </if>
        <if test="'cert_no'.toString() == column.value">
          #{item.certNo,jdbcType=VARCHAR}
        </if>
        <if test="'cert_type'.toString() == column.value">
          #{item.certType,jdbcType=VARCHAR}
        </if>
        <if test="'insured_relationship'.toString() == column.value">
          #{item.insuredRelationship,jdbcType=VARCHAR}
        </if>
        <if test="'policyholder_relationship'.toString() == column.value">
          #{item.policyholderRelationship,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'character_identity'.toString() == column.value">
          #{item.characterIdentity,jdbcType=VARCHAR}
        </if>
        <if test="'customer_type'.toString() == column.value">
          #{item.customerType,jdbcType=VARCHAR}
        </if>
        <if test="'cert_validity_start'.toString() == column.value">
          #{item.certValidityStart,jdbcType=VARCHAR}
        </if>
        <if test="'cert_validity_end'.toString() == column.value">
          #{item.certValidityEnd,jdbcType=VARCHAR}
        </if>
        <if test="'gender'.toString() == column.value">
          #{item.gender,jdbcType=CHAR}
        </if>
        <if test="'nationality'.toString() == column.value">
          #{item.nationality,jdbcType=VARCHAR}
        </if>
        <if test="'occupation'.toString() == column.value">
          #{item.occupation,jdbcType=VARCHAR}
        </if>
        <if test="'contact_tel'.toString() == column.value">
          #{item.contactTel,jdbcType=VARCHAR}
        </if>
        <if test="'address'.toString() == column.value">
          #{item.address,jdbcType=VARCHAR}
        </if>
        <if test="'business_scope'.toString() == column.value">
          #{item.businessScope,jdbcType=VARCHAR}
        </if>
        <if test="'pay_bank_info_id'.toString() == column.value">
          #{item.payBankInfoId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>