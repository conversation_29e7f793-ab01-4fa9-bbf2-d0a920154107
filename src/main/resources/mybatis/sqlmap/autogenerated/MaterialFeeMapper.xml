<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.MaterialFeeMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.MaterialFeeDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_code" jdbcType="VARCHAR" property="merchantCode" />
    <result column="mobile_brand" jdbcType="VARCHAR" property="mobileBrand" />
    <result column="mobile_type" jdbcType="VARCHAR" property="mobileType" />
    <result column="mobile_model" jdbcType="VARCHAR" property="mobileModel" />
    <result column="material_fee" jdbcType="VARCHAR" property="materialFee" />
    <result column="repair_scope" jdbcType="VARCHAR" property="repairScope" />
    <result column="repair_channel" jdbcType="VARCHAR" property="repairChannel" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, merchant_code, mobile_brand, mobile_type, mobile_model, material_fee, repair_scope, 
    repair_channel, is_deleted, creator, modifier, gmt_created, gmt_modified, extra_info
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.MaterialFeeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from material_fee_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from material_fee_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.MaterialFeeDO">
    insert into material_fee_config (id, merchant_code, mobile_brand, 
      mobile_type, mobile_model, material_fee, 
      repair_scope, repair_channel, is_deleted, 
      creator, modifier, gmt_created, 
      gmt_modified, extra_info)
    values (#{id,jdbcType=BIGINT}, #{merchantCode,jdbcType=VARCHAR}, #{mobileBrand,jdbcType=VARCHAR}, 
      #{mobileType,jdbcType=VARCHAR}, #{mobileModel,jdbcType=VARCHAR}, #{materialFee,jdbcType=VARCHAR}, 
      #{repairScope,jdbcType=VARCHAR}, #{repairChannel,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{extraInfo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.MaterialFeeDO">
    insert into material_fee_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="merchantCode != null">
        merchant_code,
      </if>
      <if test="mobileBrand != null">
        mobile_brand,
      </if>
      <if test="mobileType != null">
        mobile_type,
      </if>
      <if test="mobileModel != null">
        mobile_model,
      </if>
      <if test="materialFee != null">
        material_fee,
      </if>
      <if test="repairScope != null">
        repair_scope,
      </if>
      <if test="repairChannel != null">
        repair_channel,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchantCode != null">
        #{merchantCode,jdbcType=VARCHAR},
      </if>
      <if test="mobileBrand != null">
        #{mobileBrand,jdbcType=VARCHAR},
      </if>
      <if test="mobileType != null">
        #{mobileType,jdbcType=VARCHAR},
      </if>
      <if test="mobileModel != null">
        #{mobileModel,jdbcType=VARCHAR},
      </if>
      <if test="materialFee != null">
        #{materialFee,jdbcType=VARCHAR},
      </if>
      <if test="repairScope != null">
        #{repairScope,jdbcType=VARCHAR},
      </if>
      <if test="repairChannel != null">
        #{repairChannel,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.MaterialFeeExample" resultType="java.lang.Long">
    select count(*) from material_fee_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update material_fee_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchantCode != null">
        merchant_code = #{record.merchantCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mobileBrand != null">
        mobile_brand = #{record.mobileBrand,jdbcType=VARCHAR},
      </if>
      <if test="record.mobileType != null">
        mobile_type = #{record.mobileType,jdbcType=VARCHAR},
      </if>
      <if test="record.mobileModel != null">
        mobile_model = #{record.mobileModel,jdbcType=VARCHAR},
      </if>
      <if test="record.materialFee != null">
        material_fee = #{record.materialFee,jdbcType=VARCHAR},
      </if>
      <if test="record.repairScope != null">
        repair_scope = #{record.repairScope,jdbcType=VARCHAR},
      </if>
      <if test="record.repairChannel != null">
        repair_channel = #{record.repairChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update material_fee_config
    set id = #{record.id,jdbcType=BIGINT},
      merchant_code = #{record.merchantCode,jdbcType=VARCHAR},
      mobile_brand = #{record.mobileBrand,jdbcType=VARCHAR},
      mobile_type = #{record.mobileType,jdbcType=VARCHAR},
      mobile_model = #{record.mobileModel,jdbcType=VARCHAR},
      material_fee = #{record.materialFee,jdbcType=VARCHAR},
      repair_scope = #{record.repairScope,jdbcType=VARCHAR},
      repair_channel = #{record.repairChannel,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      extra_info = #{record.extraInfo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.MaterialFeeDO">
    update material_fee_config
    <set>
      <if test="merchantCode != null">
        merchant_code = #{merchantCode,jdbcType=VARCHAR},
      </if>
      <if test="mobileBrand != null">
        mobile_brand = #{mobileBrand,jdbcType=VARCHAR},
      </if>
      <if test="mobileType != null">
        mobile_type = #{mobileType,jdbcType=VARCHAR},
      </if>
      <if test="mobileModel != null">
        mobile_model = #{mobileModel,jdbcType=VARCHAR},
      </if>
      <if test="materialFee != null">
        material_fee = #{materialFee,jdbcType=VARCHAR},
      </if>
      <if test="repairScope != null">
        repair_scope = #{repairScope,jdbcType=VARCHAR},
      </if>
      <if test="repairChannel != null">
        repair_channel = #{repairChannel,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.MaterialFeeDO">
    update material_fee_config
    set merchant_code = #{merchantCode,jdbcType=VARCHAR},
      mobile_brand = #{mobileBrand,jdbcType=VARCHAR},
      mobile_type = #{mobileType,jdbcType=VARCHAR},
      mobile_model = #{mobileModel,jdbcType=VARCHAR},
      material_fee = #{materialFee,jdbcType=VARCHAR},
      repair_scope = #{repairScope,jdbcType=VARCHAR},
      repair_channel = #{repairChannel,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      extra_info = #{extraInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into material_fee_config
    (id, merchant_code, mobile_brand, mobile_type, mobile_model, material_fee, repair_scope, 
      repair_channel, is_deleted, creator, modifier, gmt_created, gmt_modified, extra_info
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.merchantCode,jdbcType=VARCHAR}, #{item.mobileBrand,jdbcType=VARCHAR}, 
        #{item.mobileType,jdbcType=VARCHAR}, #{item.mobileModel,jdbcType=VARCHAR}, #{item.materialFee,jdbcType=VARCHAR}, 
        #{item.repairScope,jdbcType=VARCHAR}, #{item.repairChannel,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.extraInfo,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into material_fee_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'merchant_code'.toString() == column.value">
          #{item.merchantCode,jdbcType=VARCHAR}
        </if>
        <if test="'mobile_brand'.toString() == column.value">
          #{item.mobileBrand,jdbcType=VARCHAR}
        </if>
        <if test="'mobile_type'.toString() == column.value">
          #{item.mobileType,jdbcType=VARCHAR}
        </if>
        <if test="'mobile_model'.toString() == column.value">
          #{item.mobileModel,jdbcType=VARCHAR}
        </if>
        <if test="'material_fee'.toString() == column.value">
          #{item.materialFee,jdbcType=VARCHAR}
        </if>
        <if test="'repair_scope'.toString() == column.value">
          #{item.repairScope,jdbcType=VARCHAR}
        </if>
        <if test="'repair_channel'.toString() == column.value">
          #{item.repairChannel,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>