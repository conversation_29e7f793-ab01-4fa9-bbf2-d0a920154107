<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.CovidUnionReportMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.CovidUnionReportDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="insurant_certi_no" jdbcType="VARCHAR" property="insurantCertiNo" />
    <result column="package_full_name" jdbcType="VARCHAR" property="packageFullName" />
    <result column="campaign_full_name" jdbcType="VARCHAR" property="campaignFullName" />
    <result column="package_category_name" jdbcType="VARCHAR" property="packageCategoryName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_no, insurant_certi_no, package_full_name, campaign_full_name, package_category_name, 
    is_deleted, creator, gmt_created, modifier, gmt_modified
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.CovidUnionReportExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from covid19_union_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from covid19_union_report
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.CovidUnionReportDO">
    insert into covid19_union_report (id, policy_no, insurant_certi_no, 
      package_full_name, campaign_full_name, package_category_name, 
      is_deleted, creator, gmt_created, 
      modifier, gmt_modified)
    values (#{id,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, #{insurantCertiNo,jdbcType=VARCHAR}, 
      #{packageFullName,jdbcType=VARCHAR}, #{campaignFullName,jdbcType=VARCHAR}, #{packageCategoryName,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, #{creator,jdbcType=VARCHAR}, sysdate(), 
      #{modifier,jdbcType=VARCHAR}, sysdate())
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.CovidUnionReportDO">
    insert into covid19_union_report
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="insurantCertiNo != null">
        insurant_certi_no,
      </if>
      <if test="packageFullName != null">
        package_full_name,
      </if>
      <if test="campaignFullName != null">
        campaign_full_name,
      </if>
      <if test="packageCategoryName != null">
        package_category_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertiNo != null">
        #{insurantCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="packageFullName != null">
        #{packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="campaignFullName != null">
        #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="packageCategoryName != null">
        #{packageCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.CovidUnionReportExample" resultType="java.lang.Long">
    select count(*) from covid19_union_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update covid19_union_report
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCertiNo != null">
        insurant_certi_no = #{record.insurantCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="record.packageFullName != null">
        package_full_name = #{record.packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignFullName != null">
        campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageCategoryName != null">
        package_category_name = #{record.packageCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update covid19_union_report
    set id = #{record.id,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      insurant_certi_no = #{record.insurantCertiNo,jdbcType=VARCHAR},
      package_full_name = #{record.packageFullName,jdbcType=VARCHAR},
      campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      package_category_name = #{record.packageCategoryName,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate()
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.CovidUnionReportDO">
    update covid19_union_report
    <set>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertiNo != null">
        insurant_certi_no = #{insurantCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="packageFullName != null">
        package_full_name = #{packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="campaignFullName != null">
        campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="packageCategoryName != null">
        package_category_name = #{packageCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.CovidUnionReportDO">
    update covid19_union_report
    set policy_no = #{policyNo,jdbcType=VARCHAR},
      insurant_certi_no = #{insurantCertiNo,jdbcType=VARCHAR},
      package_full_name = #{packageFullName,jdbcType=VARCHAR},
      campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      package_category_name = #{packageCategoryName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate()
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into covid19_union_report
    (id, policy_no, insurant_certi_no, package_full_name, campaign_full_name, package_category_name, 
      is_deleted, creator, gmt_created, modifier, gmt_modified)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, #{item.insurantCertiNo,jdbcType=VARCHAR}, 
        #{item.packageFullName,jdbcType=VARCHAR}, #{item.campaignFullName,jdbcType=VARCHAR}, 
        #{item.packageCategoryName,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtModified,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into covid19_union_report (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_certi_no'.toString() == column.value">
          #{item.insurantCertiNo,jdbcType=VARCHAR}
        </if>
        <if test="'package_full_name'.toString() == column.value">
          #{item.packageFullName,jdbcType=VARCHAR}
        </if>
        <if test="'campaign_full_name'.toString() == column.value">
          #{item.campaignFullName,jdbcType=VARCHAR}
        </if>
        <if test="'package_category_name'.toString() == column.value">
          #{item.packageCategoryName,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>