<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.LitigationNegotiationMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.LitigationNegotiationDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="negotiation_no" jdbcType="VARCHAR" property="negotiationNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="negotiate_time" jdbcType="TIMESTAMP" property="negotiateTime" />
    <result column="negotiate_conclusion" jdbcType="VARCHAR" property="negotiateConclusion" />
    <result column="negotiate_scheme" jdbcType="VARCHAR" property="negotiateScheme" />
    <result column="negotiator" jdbcType="VARCHAR" property="negotiator" />
    <result column="negotiator_mobile" jdbcType="VARCHAR" property="negotiatorMobile" />
    <result column="claim_adjuster" jdbcType="VARCHAR" property="claimAdjuster" />
    <result column="relinsured" jdbcType="VARCHAR" property="relinsured" />
    <result column="is_reach_agreement" jdbcType="BIT" property="isReachAgreement" />
    <result column="is_exist_intention" jdbcType="BIT" property="isExistIntention" />
    <result column="is_sent_agreement" jdbcType="BIT" property="isSentAgreement" />
    <result column="is_sign_agreement" jdbcType="BIT" property="isSignAgreement" />
    <result column="associated_out_call_ids" jdbcType="VARCHAR" property="associatedOutCallIds" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, negotiation_no, policy_no, report_no, negotiate_time, negotiate_conclusion, negotiate_scheme, 
    negotiator, negotiator_mobile, claim_adjuster, relinsured, is_reach_agreement, is_exist_intention, 
    is_sent_agreement, is_sign_agreement, associated_out_call_ids, operate_time, creator, 
    modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.LitigationNegotiationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from litigation_negotiation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from litigation_negotiation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.LitigationNegotiationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into litigation_negotiation (negotiation_no, policy_no, report_no, 
      negotiate_time, negotiate_conclusion, negotiate_scheme, 
      negotiator, negotiator_mobile, claim_adjuster, 
      relinsured, is_reach_agreement, is_exist_intention, 
      is_sent_agreement, is_sign_agreement, associated_out_call_ids, 
      operate_time, creator, modifier, 
      gmt_created, gmt_modified, is_deleted
      )
    values (#{negotiationNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{negotiateTime,jdbcType=TIMESTAMP}, #{negotiateConclusion,jdbcType=VARCHAR}, #{negotiateScheme,jdbcType=VARCHAR}, 
      #{negotiator,jdbcType=VARCHAR}, #{negotiatorMobile,jdbcType=VARCHAR}, #{claimAdjuster,jdbcType=VARCHAR}, 
      #{relinsured,jdbcType=VARCHAR}, #{isReachAgreement,jdbcType=BIT}, #{isExistIntention,jdbcType=BIT}, 
      #{isSentAgreement,jdbcType=BIT}, #{isSignAgreement,jdbcType=BIT}, #{associatedOutCallIds,jdbcType=VARCHAR}, 
      #{operateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.LitigationNegotiationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into litigation_negotiation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="negotiationNo != null">
        negotiation_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="negotiateTime != null">
        negotiate_time,
      </if>
      <if test="negotiateConclusion != null">
        negotiate_conclusion,
      </if>
      <if test="negotiateScheme != null">
        negotiate_scheme,
      </if>
      <if test="negotiator != null">
        negotiator,
      </if>
      <if test="negotiatorMobile != null">
        negotiator_mobile,
      </if>
      <if test="claimAdjuster != null">
        claim_adjuster,
      </if>
      <if test="relinsured != null">
        relinsured,
      </if>
      <if test="isReachAgreement != null">
        is_reach_agreement,
      </if>
      <if test="isExistIntention != null">
        is_exist_intention,
      </if>
      <if test="isSentAgreement != null">
        is_sent_agreement,
      </if>
      <if test="isSignAgreement != null">
        is_sign_agreement,
      </if>
      <if test="associatedOutCallIds != null">
        associated_out_call_ids,
      </if>
      <if test="operateTime != null">
        operate_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="negotiationNo != null">
        #{negotiationNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="negotiateTime != null">
        #{negotiateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="negotiateConclusion != null">
        #{negotiateConclusion,jdbcType=VARCHAR},
      </if>
      <if test="negotiateScheme != null">
        #{negotiateScheme,jdbcType=VARCHAR},
      </if>
      <if test="negotiator != null">
        #{negotiator,jdbcType=VARCHAR},
      </if>
      <if test="negotiatorMobile != null">
        #{negotiatorMobile,jdbcType=VARCHAR},
      </if>
      <if test="claimAdjuster != null">
        #{claimAdjuster,jdbcType=VARCHAR},
      </if>
      <if test="relinsured != null">
        #{relinsured,jdbcType=VARCHAR},
      </if>
      <if test="isReachAgreement != null">
        #{isReachAgreement,jdbcType=BIT},
      </if>
      <if test="isExistIntention != null">
        #{isExistIntention,jdbcType=BIT},
      </if>
      <if test="isSentAgreement != null">
        #{isSentAgreement,jdbcType=BIT},
      </if>
      <if test="isSignAgreement != null">
        #{isSignAgreement,jdbcType=BIT},
      </if>
      <if test="associatedOutCallIds != null">
        #{associatedOutCallIds,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.LitigationNegotiationExample" resultType="java.lang.Long">
    select count(*) from litigation_negotiation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update litigation_negotiation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.negotiationNo != null">
        negotiation_no = #{record.negotiationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.negotiateTime != null">
        negotiate_time = #{record.negotiateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.negotiateConclusion != null">
        negotiate_conclusion = #{record.negotiateConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.negotiateScheme != null">
        negotiate_scheme = #{record.negotiateScheme,jdbcType=VARCHAR},
      </if>
      <if test="record.negotiator != null">
        negotiator = #{record.negotiator,jdbcType=VARCHAR},
      </if>
      <if test="record.negotiatorMobile != null">
        negotiator_mobile = #{record.negotiatorMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.claimAdjuster != null">
        claim_adjuster = #{record.claimAdjuster,jdbcType=VARCHAR},
      </if>
      <if test="record.relinsured != null">
        relinsured = #{record.relinsured,jdbcType=VARCHAR},
      </if>
      <if test="record.isReachAgreement != null">
        is_reach_agreement = #{record.isReachAgreement,jdbcType=BIT},
      </if>
      <if test="record.isExistIntention != null">
        is_exist_intention = #{record.isExistIntention,jdbcType=BIT},
      </if>
      <if test="record.isSentAgreement != null">
        is_sent_agreement = #{record.isSentAgreement,jdbcType=BIT},
      </if>
      <if test="record.isSignAgreement != null">
        is_sign_agreement = #{record.isSignAgreement,jdbcType=BIT},
      </if>
      <if test="record.associatedOutCallIds != null">
        associated_out_call_ids = #{record.associatedOutCallIds,jdbcType=VARCHAR},
      </if>
      <if test="record.operateTime != null">
        operate_time = #{record.operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update litigation_negotiation
    set id = #{record.id,jdbcType=BIGINT},
      negotiation_no = #{record.negotiationNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      negotiate_time = #{record.negotiateTime,jdbcType=TIMESTAMP},
      negotiate_conclusion = #{record.negotiateConclusion,jdbcType=VARCHAR},
      negotiate_scheme = #{record.negotiateScheme,jdbcType=VARCHAR},
      negotiator = #{record.negotiator,jdbcType=VARCHAR},
      negotiator_mobile = #{record.negotiatorMobile,jdbcType=VARCHAR},
      claim_adjuster = #{record.claimAdjuster,jdbcType=VARCHAR},
      relinsured = #{record.relinsured,jdbcType=VARCHAR},
      is_reach_agreement = #{record.isReachAgreement,jdbcType=BIT},
      is_exist_intention = #{record.isExistIntention,jdbcType=BIT},
      is_sent_agreement = #{record.isSentAgreement,jdbcType=BIT},
      is_sign_agreement = #{record.isSignAgreement,jdbcType=BIT},
      associated_out_call_ids = #{record.associatedOutCallIds,jdbcType=VARCHAR},
      operate_time = #{record.operateTime,jdbcType=TIMESTAMP},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.LitigationNegotiationDO">
    update litigation_negotiation
    <set>
      <if test="negotiationNo != null">
        negotiation_no = #{negotiationNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="negotiateTime != null">
        negotiate_time = #{negotiateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="negotiateConclusion != null">
        negotiate_conclusion = #{negotiateConclusion,jdbcType=VARCHAR},
      </if>
      <if test="negotiateScheme != null">
        negotiate_scheme = #{negotiateScheme,jdbcType=VARCHAR},
      </if>
      <if test="negotiator != null">
        negotiator = #{negotiator,jdbcType=VARCHAR},
      </if>
      <if test="negotiatorMobile != null">
        negotiator_mobile = #{negotiatorMobile,jdbcType=VARCHAR},
      </if>
      <if test="claimAdjuster != null">
        claim_adjuster = #{claimAdjuster,jdbcType=VARCHAR},
      </if>
      <if test="relinsured != null">
        relinsured = #{relinsured,jdbcType=VARCHAR},
      </if>
      <if test="isReachAgreement != null">
        is_reach_agreement = #{isReachAgreement,jdbcType=BIT},
      </if>
      <if test="isExistIntention != null">
        is_exist_intention = #{isExistIntention,jdbcType=BIT},
      </if>
      <if test="isSentAgreement != null">
        is_sent_agreement = #{isSentAgreement,jdbcType=BIT},
      </if>
      <if test="isSignAgreement != null">
        is_sign_agreement = #{isSignAgreement,jdbcType=BIT},
      </if>
      <if test="associatedOutCallIds != null">
        associated_out_call_ids = #{associatedOutCallIds,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        operate_time = #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.LitigationNegotiationDO">
    update litigation_negotiation
    set negotiation_no = #{negotiationNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      negotiate_time = #{negotiateTime,jdbcType=TIMESTAMP},
      negotiate_conclusion = #{negotiateConclusion,jdbcType=VARCHAR},
      negotiate_scheme = #{negotiateScheme,jdbcType=VARCHAR},
      negotiator = #{negotiator,jdbcType=VARCHAR},
      negotiator_mobile = #{negotiatorMobile,jdbcType=VARCHAR},
      claim_adjuster = #{claimAdjuster,jdbcType=VARCHAR},
      relinsured = #{relinsured,jdbcType=VARCHAR},
      is_reach_agreement = #{isReachAgreement,jdbcType=BIT},
      is_exist_intention = #{isExistIntention,jdbcType=BIT},
      is_sent_agreement = #{isSentAgreement,jdbcType=BIT},
      is_sign_agreement = #{isSignAgreement,jdbcType=BIT},
      associated_out_call_ids = #{associatedOutCallIds,jdbcType=VARCHAR},
      operate_time = #{operateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into litigation_negotiation
    (negotiation_no, policy_no, report_no, negotiate_time, negotiate_conclusion, negotiate_scheme, 
      negotiator, negotiator_mobile, claim_adjuster, relinsured, is_reach_agreement, 
      is_exist_intention, is_sent_agreement, is_sign_agreement, associated_out_call_ids, 
      operate_time, creator, modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.negotiationNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.negotiateTime,jdbcType=TIMESTAMP}, #{item.negotiateConclusion,jdbcType=VARCHAR}, 
        #{item.negotiateScheme,jdbcType=VARCHAR}, #{item.negotiator,jdbcType=VARCHAR}, 
        #{item.negotiatorMobile,jdbcType=VARCHAR}, #{item.claimAdjuster,jdbcType=VARCHAR}, 
        #{item.relinsured,jdbcType=VARCHAR}, #{item.isReachAgreement,jdbcType=BIT}, #{item.isExistIntention,jdbcType=BIT}, 
        #{item.isSentAgreement,jdbcType=BIT}, #{item.isSignAgreement,jdbcType=BIT}, #{item.associatedOutCallIds,jdbcType=VARCHAR}, 
        #{item.operateTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into litigation_negotiation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'negotiation_no'.toString() == column.value">
          #{item.negotiationNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'negotiate_time'.toString() == column.value">
          #{item.negotiateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'negotiate_conclusion'.toString() == column.value">
          #{item.negotiateConclusion,jdbcType=VARCHAR}
        </if>
        <if test="'negotiate_scheme'.toString() == column.value">
          #{item.negotiateScheme,jdbcType=VARCHAR}
        </if>
        <if test="'negotiator'.toString() == column.value">
          #{item.negotiator,jdbcType=VARCHAR}
        </if>
        <if test="'negotiator_mobile'.toString() == column.value">
          #{item.negotiatorMobile,jdbcType=VARCHAR}
        </if>
        <if test="'claim_adjuster'.toString() == column.value">
          #{item.claimAdjuster,jdbcType=VARCHAR}
        </if>
        <if test="'relinsured'.toString() == column.value">
          #{item.relinsured,jdbcType=VARCHAR}
        </if>
        <if test="'is_reach_agreement'.toString() == column.value">
          #{item.isReachAgreement,jdbcType=BIT}
        </if>
        <if test="'is_exist_intention'.toString() == column.value">
          #{item.isExistIntention,jdbcType=BIT}
        </if>
        <if test="'is_sent_agreement'.toString() == column.value">
          #{item.isSentAgreement,jdbcType=BIT}
        </if>
        <if test="'is_sign_agreement'.toString() == column.value">
          #{item.isSignAgreement,jdbcType=BIT}
        </if>
        <if test="'associated_out_call_ids'.toString() == column.value">
          #{item.associatedOutCallIds,jdbcType=VARCHAR}
        </if>
        <if test="'operate_time'.toString() == column.value">
          #{item.operateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>