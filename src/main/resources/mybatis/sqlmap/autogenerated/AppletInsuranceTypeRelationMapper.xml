<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AppletInsuranceTypeRelationMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AppletInsuranceTypeRelationDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="insurance_type_name" jdbcType="VARCHAR" property="insuranceTypeName" />
    <result column="biz_type_id" jdbcType="BIGINT" property="bizTypeId" />
    <result column="temp_id" jdbcType="VARCHAR" property="tempId" />
    <result column="cus_type" jdbcType="CHAR" property="cusType" />
    <result column="sort_val" jdbcType="INTEGER" property="sortVal" />
    <result column="templates" jdbcType="VARCHAR" property="templates" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, insurance_type_name, biz_type_id, temp_id, cus_type, sort_val, templates, remark, 
    extra_info, creator, gmt_created, modifier, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AppletInsuranceTypeRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from applet_insurance_type_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from applet_insurance_type_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AppletInsuranceTypeRelationDO">
    insert into applet_insurance_type_relation (id, insurance_type_name, biz_type_id, 
      temp_id, cus_type, sort_val, 
      templates, remark, extra_info, 
      creator, gmt_created, modifier, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{insuranceTypeName,jdbcType=VARCHAR}, #{bizTypeId,jdbcType=BIGINT}, 
      #{tempId,jdbcType=VARCHAR}, #{cusType,jdbcType=CHAR}, #{sortVal,jdbcType=INTEGER}, 
      #{templates,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, sysdate(), #{modifier,jdbcType=VARCHAR}, 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AppletInsuranceTypeRelationDO">
    insert into applet_insurance_type_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="insuranceTypeName != null">
        insurance_type_name,
      </if>
      <if test="bizTypeId != null">
        biz_type_id,
      </if>
      <if test="tempId != null">
        temp_id,
      </if>
      <if test="cusType != null">
        cus_type,
      </if>
      <if test="sortVal != null">
        sort_val,
      </if>
      <if test="templates != null">
        templates,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="insuranceTypeName != null">
        #{insuranceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="bizTypeId != null">
        #{bizTypeId,jdbcType=BIGINT},
      </if>
      <if test="tempId != null">
        #{tempId,jdbcType=VARCHAR},
      </if>
      <if test="cusType != null">
        #{cusType,jdbcType=CHAR},
      </if>
      <if test="sortVal != null">
        #{sortVal,jdbcType=INTEGER},
      </if>
      <if test="templates != null">
        #{templates,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AppletInsuranceTypeRelationExample" resultType="java.lang.Long">
    select count(*) from applet_insurance_type_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update applet_insurance_type_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.insuranceTypeName != null">
        insurance_type_name = #{record.insuranceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.bizTypeId != null">
        biz_type_id = #{record.bizTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.tempId != null">
        temp_id = #{record.tempId,jdbcType=VARCHAR},
      </if>
      <if test="record.cusType != null">
        cus_type = #{record.cusType,jdbcType=CHAR},
      </if>
      <if test="record.sortVal != null">
        sort_val = #{record.sortVal,jdbcType=INTEGER},
      </if>
      <if test="record.templates != null">
        templates = #{record.templates,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update applet_insurance_type_relation
    set id = #{record.id,jdbcType=BIGINT},
      insurance_type_name = #{record.insuranceTypeName,jdbcType=VARCHAR},
      biz_type_id = #{record.bizTypeId,jdbcType=BIGINT},
      temp_id = #{record.tempId,jdbcType=VARCHAR},
      cus_type = #{record.cusType,jdbcType=CHAR},
      sort_val = #{record.sortVal,jdbcType=INTEGER},
      templates = #{record.templates,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AppletInsuranceTypeRelationDO">
    update applet_insurance_type_relation
    <set>
      <if test="insuranceTypeName != null">
        insurance_type_name = #{insuranceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="bizTypeId != null">
        biz_type_id = #{bizTypeId,jdbcType=BIGINT},
      </if>
      <if test="tempId != null">
        temp_id = #{tempId,jdbcType=VARCHAR},
      </if>
      <if test="cusType != null">
        cus_type = #{cusType,jdbcType=CHAR},
      </if>
      <if test="sortVal != null">
        sort_val = #{sortVal,jdbcType=INTEGER},
      </if>
      <if test="templates != null">
        templates = #{templates,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AppletInsuranceTypeRelationDO">
    update applet_insurance_type_relation
    set insurance_type_name = #{insuranceTypeName,jdbcType=VARCHAR},
      biz_type_id = #{bizTypeId,jdbcType=BIGINT},
      temp_id = #{tempId,jdbcType=VARCHAR},
      cus_type = #{cusType,jdbcType=CHAR},
      sort_val = #{sortVal,jdbcType=INTEGER},
      templates = #{templates,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into applet_insurance_type_relation
    (id, insurance_type_name, biz_type_id, temp_id, cus_type, sort_val, templates, remark, 
      extra_info, creator, gmt_created, modifier, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.insuranceTypeName,jdbcType=VARCHAR}, #{item.bizTypeId,jdbcType=BIGINT}, 
        #{item.tempId,jdbcType=VARCHAR}, #{item.cusType,jdbcType=CHAR}, #{item.sortVal,jdbcType=INTEGER}, 
        #{item.templates,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into applet_insurance_type_relation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'insurance_type_name'.toString() == column.value">
          #{item.insuranceTypeName,jdbcType=VARCHAR}
        </if>
        <if test="'biz_type_id'.toString() == column.value">
          #{item.bizTypeId,jdbcType=BIGINT}
        </if>
        <if test="'temp_id'.toString() == column.value">
          #{item.tempId,jdbcType=VARCHAR}
        </if>
        <if test="'cus_type'.toString() == column.value">
          #{item.cusType,jdbcType=CHAR}
        </if>
        <if test="'sort_val'.toString() == column.value">
          #{item.sortVal,jdbcType=INTEGER}
        </if>
        <if test="'templates'.toString() == column.value">
          #{item.templates,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>