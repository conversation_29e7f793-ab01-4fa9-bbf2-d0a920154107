<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AssessRiskInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AssessRiskInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="dispatcher_no" jdbcType="VARCHAR" property="dispatcherNo" />
    <result column="is_hospital_risk" jdbcType="CHAR" property="isHospitalRisk" />
    <result column="risk_hospital" jdbcType="VARCHAR" property="riskHospital" />
    <result column="risk_department" jdbcType="VARCHAR" property="riskDepartment" />
    <result column="risk_doctor" jdbcType="VARCHAR" property="riskDoctor" />
    <result column="risk_reason" jdbcType="VARCHAR" property="riskReason" />
    <result column="complaint_reason" jdbcType="VARCHAR" property="complaintReason" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, policy_no, dispatcher_no, is_hospital_risk, risk_hospital, risk_department, 
    risk_doctor, risk_reason, complaint_reason, gmt_created, gmt_modified, creator, modifier, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AssessRiskInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from assess_risk_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from assess_risk_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AssessRiskInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into assess_risk_info (report_no, policy_no, dispatcher_no, 
      is_hospital_risk, risk_hospital, risk_department, 
      risk_doctor, risk_reason, complaint_reason, 
      gmt_created, gmt_modified, creator, 
      modifier, is_deleted)
    values (#{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, #{dispatcherNo,jdbcType=VARCHAR}, 
      #{isHospitalRisk,jdbcType=CHAR}, #{riskHospital,jdbcType=VARCHAR}, #{riskDepartment,jdbcType=VARCHAR}, 
      #{riskDoctor,jdbcType=VARCHAR}, #{riskReason,jdbcType=VARCHAR}, #{complaintReason,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AssessRiskInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into assess_risk_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no,
      </if>
      <if test="isHospitalRisk != null">
        is_hospital_risk,
      </if>
      <if test="riskHospital != null">
        risk_hospital,
      </if>
      <if test="riskDepartment != null">
        risk_department,
      </if>
      <if test="riskDoctor != null">
        risk_doctor,
      </if>
      <if test="riskReason != null">
        risk_reason,
      </if>
      <if test="complaintReason != null">
        complaint_reason,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherNo != null">
        #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="isHospitalRisk != null">
        #{isHospitalRisk,jdbcType=CHAR},
      </if>
      <if test="riskHospital != null">
        #{riskHospital,jdbcType=VARCHAR},
      </if>
      <if test="riskDepartment != null">
        #{riskDepartment,jdbcType=VARCHAR},
      </if>
      <if test="riskDoctor != null">
        #{riskDoctor,jdbcType=VARCHAR},
      </if>
      <if test="riskReason != null">
        #{riskReason,jdbcType=VARCHAR},
      </if>
      <if test="complaintReason != null">
        #{complaintReason,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AssessRiskInfoExample" resultType="java.lang.Long">
    select count(*) from assess_risk_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update assess_risk_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.dispatcherNo != null">
        dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="record.isHospitalRisk != null">
        is_hospital_risk = #{record.isHospitalRisk,jdbcType=CHAR},
      </if>
      <if test="record.riskHospital != null">
        risk_hospital = #{record.riskHospital,jdbcType=VARCHAR},
      </if>
      <if test="record.riskDepartment != null">
        risk_department = #{record.riskDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.riskDoctor != null">
        risk_doctor = #{record.riskDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.riskReason != null">
        risk_reason = #{record.riskReason,jdbcType=VARCHAR},
      </if>
      <if test="record.complaintReason != null">
        complaint_reason = #{record.complaintReason,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update assess_risk_info
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      is_hospital_risk = #{record.isHospitalRisk,jdbcType=CHAR},
      risk_hospital = #{record.riskHospital,jdbcType=VARCHAR},
      risk_department = #{record.riskDepartment,jdbcType=VARCHAR},
      risk_doctor = #{record.riskDoctor,jdbcType=VARCHAR},
      risk_reason = #{record.riskReason,jdbcType=VARCHAR},
      complaint_reason = #{record.complaintReason,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AssessRiskInfoDO">
    update assess_risk_info
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="isHospitalRisk != null">
        is_hospital_risk = #{isHospitalRisk,jdbcType=CHAR},
      </if>
      <if test="riskHospital != null">
        risk_hospital = #{riskHospital,jdbcType=VARCHAR},
      </if>
      <if test="riskDepartment != null">
        risk_department = #{riskDepartment,jdbcType=VARCHAR},
      </if>
      <if test="riskDoctor != null">
        risk_doctor = #{riskDoctor,jdbcType=VARCHAR},
      </if>
      <if test="riskReason != null">
        risk_reason = #{riskReason,jdbcType=VARCHAR},
      </if>
      <if test="complaintReason != null">
        complaint_reason = #{complaintReason,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AssessRiskInfoDO">
    update assess_risk_info
    set report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      is_hospital_risk = #{isHospitalRisk,jdbcType=CHAR},
      risk_hospital = #{riskHospital,jdbcType=VARCHAR},
      risk_department = #{riskDepartment,jdbcType=VARCHAR},
      risk_doctor = #{riskDoctor,jdbcType=VARCHAR},
      risk_reason = #{riskReason,jdbcType=VARCHAR},
      complaint_reason = #{complaintReason,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into assess_risk_info
    (report_no, policy_no, dispatcher_no, is_hospital_risk, risk_hospital, risk_department, 
      risk_doctor, risk_reason, complaint_reason, gmt_created, gmt_modified, creator, 
      modifier, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.dispatcherNo,jdbcType=VARCHAR}, 
        #{item.isHospitalRisk,jdbcType=CHAR}, #{item.riskHospital,jdbcType=VARCHAR}, #{item.riskDepartment,jdbcType=VARCHAR}, 
        #{item.riskDoctor,jdbcType=VARCHAR}, #{item.riskReason,jdbcType=VARCHAR}, #{item.complaintReason,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into assess_risk_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'dispatcher_no'.toString() == column.value">
          #{item.dispatcherNo,jdbcType=VARCHAR}
        </if>
        <if test="'is_hospital_risk'.toString() == column.value">
          #{item.isHospitalRisk,jdbcType=CHAR}
        </if>
        <if test="'risk_hospital'.toString() == column.value">
          #{item.riskHospital,jdbcType=VARCHAR}
        </if>
        <if test="'risk_department'.toString() == column.value">
          #{item.riskDepartment,jdbcType=VARCHAR}
        </if>
        <if test="'risk_doctor'.toString() == column.value">
          #{item.riskDoctor,jdbcType=VARCHAR}
        </if>
        <if test="'risk_reason'.toString() == column.value">
          #{item.riskReason,jdbcType=VARCHAR}
        </if>
        <if test="'complaint_reason'.toString() == column.value">
          #{item.complaintReason,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>