<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimUnderwritingMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimUnderwritingDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="inscompanyName" jdbcType="VARCHAR" property="inscompanyname" />
    <result column="inscompanyId" jdbcType="VARCHAR" property="inscompanyid" />
    <result column="postCode" jdbcType="VARCHAR" property="postcode" />
    <result column="contactAddress" jdbcType="VARCHAR" property="contactaddress" />
    <result column="contactPhone" jdbcType="VARCHAR" property="contactphone" />
    <result column="contactName" jdbcType="VARCHAR" property="contactname" />
    <result column="contactEmail" jdbcType="VARCHAR" property="contactemail" />
    <result column="multipleInsStartDate" jdbcType="VARCHAR" property="multipleinsstartdate" />
    <result column="multipleInsEndDate" jdbcType="VARCHAR" property="multipleinsenddate" />
    <result column="multipleTotalInsAmount" jdbcType="VARCHAR" property="multipletotalinsamount" />
    <result column="batch_claim_bill_no" jdbcType="VARCHAR" property="batchClaimBillNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, inscompanyName, inscompanyId, postCode, contactAddress, contactPhone, contactName, 
    contactEmail, multipleInsStartDate, multipleInsEndDate, multipleTotalInsAmount, batch_claim_bill_no, 
    report_no, creator, gmt_created, modifier, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimUnderwritingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_claim_underwriting_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_claim_underwriting_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimUnderwritingDO">
    insert into cargo_claim_underwriting_info (id, inscompanyName, inscompanyId, 
      postCode, contactAddress, contactPhone, 
      contactName, contactEmail, multipleInsStartDate, 
      multipleInsEndDate, multipleTotalInsAmount, 
      batch_claim_bill_no, report_no, creator, 
      gmt_created, modifier, gmt_modified, 
      is_deleted)
    values (#{id,jdbcType=BIGINT}, #{inscompanyname,jdbcType=VARCHAR}, #{inscompanyid,jdbcType=VARCHAR}, 
      #{postcode,jdbcType=VARCHAR}, #{contactaddress,jdbcType=VARCHAR}, #{contactphone,jdbcType=VARCHAR}, 
      #{contactname,jdbcType=VARCHAR}, #{contactemail,jdbcType=VARCHAR}, #{multipleinsstartdate,jdbcType=VARCHAR}, 
      #{multipleinsenddate,jdbcType=VARCHAR}, #{multipletotalinsamount,jdbcType=VARCHAR}, 
      #{batchClaimBillNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      sysdate(), #{modifier,jdbcType=VARCHAR}, sysdate(), 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimUnderwritingDO">
    insert into cargo_claim_underwriting_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="inscompanyname != null">
        inscompanyName,
      </if>
      <if test="inscompanyid != null">
        inscompanyId,
      </if>
      <if test="postcode != null">
        postCode,
      </if>
      <if test="contactaddress != null">
        contactAddress,
      </if>
      <if test="contactphone != null">
        contactPhone,
      </if>
      <if test="contactname != null">
        contactName,
      </if>
      <if test="contactemail != null">
        contactEmail,
      </if>
      <if test="multipleinsstartdate != null">
        multipleInsStartDate,
      </if>
      <if test="multipleinsenddate != null">
        multipleInsEndDate,
      </if>
      <if test="multipletotalinsamount != null">
        multipleTotalInsAmount,
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="inscompanyname != null">
        #{inscompanyname,jdbcType=VARCHAR},
      </if>
      <if test="inscompanyid != null">
        #{inscompanyid,jdbcType=VARCHAR},
      </if>
      <if test="postcode != null">
        #{postcode,jdbcType=VARCHAR},
      </if>
      <if test="contactaddress != null">
        #{contactaddress,jdbcType=VARCHAR},
      </if>
      <if test="contactphone != null">
        #{contactphone,jdbcType=VARCHAR},
      </if>
      <if test="contactname != null">
        #{contactname,jdbcType=VARCHAR},
      </if>
      <if test="contactemail != null">
        #{contactemail,jdbcType=VARCHAR},
      </if>
      <if test="multipleinsstartdate != null">
        #{multipleinsstartdate,jdbcType=VARCHAR},
      </if>
      <if test="multipleinsenddate != null">
        #{multipleinsenddate,jdbcType=VARCHAR},
      </if>
      <if test="multipletotalinsamount != null">
        #{multipletotalinsamount,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillNo != null">
        #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimUnderwritingExample" resultType="java.lang.Long">
    select count(*) from cargo_claim_underwriting_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_claim_underwriting_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.inscompanyname != null">
        inscompanyName = #{record.inscompanyname,jdbcType=VARCHAR},
      </if>
      <if test="record.inscompanyid != null">
        inscompanyId = #{record.inscompanyid,jdbcType=VARCHAR},
      </if>
      <if test="record.postcode != null">
        postCode = #{record.postcode,jdbcType=VARCHAR},
      </if>
      <if test="record.contactaddress != null">
        contactAddress = #{record.contactaddress,jdbcType=VARCHAR},
      </if>
      <if test="record.contactphone != null">
        contactPhone = #{record.contactphone,jdbcType=VARCHAR},
      </if>
      <if test="record.contactname != null">
        contactName = #{record.contactname,jdbcType=VARCHAR},
      </if>
      <if test="record.contactemail != null">
        contactEmail = #{record.contactemail,jdbcType=VARCHAR},
      </if>
      <if test="record.multipleinsstartdate != null">
        multipleInsStartDate = #{record.multipleinsstartdate,jdbcType=VARCHAR},
      </if>
      <if test="record.multipleinsenddate != null">
        multipleInsEndDate = #{record.multipleinsenddate,jdbcType=VARCHAR},
      </if>
      <if test="record.multipletotalinsamount != null">
        multipleTotalInsAmount = #{record.multipletotalinsamount,jdbcType=VARCHAR},
      </if>
      <if test="record.batchClaimBillNo != null">
        batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_claim_underwriting_info
    set id = #{record.id,jdbcType=BIGINT},
      inscompanyName = #{record.inscompanyname,jdbcType=VARCHAR},
      inscompanyId = #{record.inscompanyid,jdbcType=VARCHAR},
      postCode = #{record.postcode,jdbcType=VARCHAR},
      contactAddress = #{record.contactaddress,jdbcType=VARCHAR},
      contactPhone = #{record.contactphone,jdbcType=VARCHAR},
      contactName = #{record.contactname,jdbcType=VARCHAR},
      contactEmail = #{record.contactemail,jdbcType=VARCHAR},
      multipleInsStartDate = #{record.multipleinsstartdate,jdbcType=VARCHAR},
      multipleInsEndDate = #{record.multipleinsenddate,jdbcType=VARCHAR},
      multipleTotalInsAmount = #{record.multipletotalinsamount,jdbcType=VARCHAR},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimUnderwritingDO">
    update cargo_claim_underwriting_info
    <set>
      <if test="inscompanyname != null">
        inscompanyName = #{inscompanyname,jdbcType=VARCHAR},
      </if>
      <if test="inscompanyid != null">
        inscompanyId = #{inscompanyid,jdbcType=VARCHAR},
      </if>
      <if test="postcode != null">
        postCode = #{postcode,jdbcType=VARCHAR},
      </if>
      <if test="contactaddress != null">
        contactAddress = #{contactaddress,jdbcType=VARCHAR},
      </if>
      <if test="contactphone != null">
        contactPhone = #{contactphone,jdbcType=VARCHAR},
      </if>
      <if test="contactname != null">
        contactName = #{contactname,jdbcType=VARCHAR},
      </if>
      <if test="contactemail != null">
        contactEmail = #{contactemail,jdbcType=VARCHAR},
      </if>
      <if test="multipleinsstartdate != null">
        multipleInsStartDate = #{multipleinsstartdate,jdbcType=VARCHAR},
      </if>
      <if test="multipleinsenddate != null">
        multipleInsEndDate = #{multipleinsenddate,jdbcType=VARCHAR},
      </if>
      <if test="multipletotalinsamount != null">
        multipleTotalInsAmount = #{multipletotalinsamount,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimUnderwritingDO">
    update cargo_claim_underwriting_info
    set inscompanyName = #{inscompanyname,jdbcType=VARCHAR},
      inscompanyId = #{inscompanyid,jdbcType=VARCHAR},
      postCode = #{postcode,jdbcType=VARCHAR},
      contactAddress = #{contactaddress,jdbcType=VARCHAR},
      contactPhone = #{contactphone,jdbcType=VARCHAR},
      contactName = #{contactname,jdbcType=VARCHAR},
      contactEmail = #{contactemail,jdbcType=VARCHAR},
      multipleInsStartDate = #{multipleinsstartdate,jdbcType=VARCHAR},
      multipleInsEndDate = #{multipleinsenddate,jdbcType=VARCHAR},
      multipleTotalInsAmount = #{multipletotalinsamount,jdbcType=VARCHAR},
      batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_claim_underwriting_info
    (id, inscompanyName, inscompanyId, postCode, contactAddress, contactPhone, contactName, 
      contactEmail, multipleInsStartDate, multipleInsEndDate, multipleTotalInsAmount, 
      batch_claim_bill_no, report_no, creator, gmt_created, modifier, gmt_modified, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.inscompanyname,jdbcType=VARCHAR}, #{item.inscompanyid,jdbcType=VARCHAR}, 
        #{item.postcode,jdbcType=VARCHAR}, #{item.contactaddress,jdbcType=VARCHAR}, #{item.contactphone,jdbcType=VARCHAR}, 
        #{item.contactname,jdbcType=VARCHAR}, #{item.contactemail,jdbcType=VARCHAR}, #{item.multipleinsstartdate,jdbcType=VARCHAR}, 
        #{item.multipleinsenddate,jdbcType=VARCHAR}, #{item.multipletotalinsamount,jdbcType=VARCHAR}, 
        #{item.batchClaimBillNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_claim_underwriting_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'inscompanyName'.toString() == column.value">
          #{item.inscompanyname,jdbcType=VARCHAR}
        </if>
        <if test="'inscompanyId'.toString() == column.value">
          #{item.inscompanyid,jdbcType=VARCHAR}
        </if>
        <if test="'postCode'.toString() == column.value">
          #{item.postcode,jdbcType=VARCHAR}
        </if>
        <if test="'contactAddress'.toString() == column.value">
          #{item.contactaddress,jdbcType=VARCHAR}
        </if>
        <if test="'contactPhone'.toString() == column.value">
          #{item.contactphone,jdbcType=VARCHAR}
        </if>
        <if test="'contactName'.toString() == column.value">
          #{item.contactname,jdbcType=VARCHAR}
        </if>
        <if test="'contactEmail'.toString() == column.value">
          #{item.contactemail,jdbcType=VARCHAR}
        </if>
        <if test="'multipleInsStartDate'.toString() == column.value">
          #{item.multipleinsstartdate,jdbcType=VARCHAR}
        </if>
        <if test="'multipleInsEndDate'.toString() == column.value">
          #{item.multipleinsenddate,jdbcType=VARCHAR}
        </if>
        <if test="'multipleTotalInsAmount'.toString() == column.value">
          #{item.multipletotalinsamount,jdbcType=VARCHAR}
        </if>
        <if test="'batch_claim_bill_no'.toString() == column.value">
          #{item.batchClaimBillNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>