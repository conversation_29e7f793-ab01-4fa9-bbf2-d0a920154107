<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.FlightdelayCommunityDetailMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.FlightdelayCommunityDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="community_id" jdbcType="VARCHAR" property="communityId" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="effective_date" jdbcType="VARCHAR" property="effectiveDate" />
    <result column="insure_date" jdbcType="VARCHAR" property="insureDate" />
    <result column="premium" jdbcType="VARCHAR" property="premium" />
    <result column="orig_premium" jdbcType="VARCHAR" property="origPremium" />
    <result column="policy_status_id" jdbcType="VARCHAR" property="policyStatusId" />
    <result column="is_claim_type" jdbcType="VARCHAR" property="isClaimType" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="claimant_phone" jdbcType="VARCHAR" property="claimantPhone" />
    <result column="claimant_phone_processed" jdbcType="VARCHAR" property="claimantPhoneProcessed" />
    <result column="holder_name" jdbcType="VARCHAR" property="holderName" />
    <result column="holder_cert" jdbcType="VARCHAR" property="holderCert" />
    <result column="holder_cert_processed" jdbcType="VARCHAR" property="holderCertProcessed" />
    <result column="holder_passport_processed" jdbcType="VARCHAR" property="holderPassportProcessed" />
    <result column="insurant_name" jdbcType="VARCHAR" property="insurantName" />
    <result column="insurant_cert" jdbcType="VARCHAR" property="insurantCert" />
    <result column="insurant_phone" jdbcType="VARCHAR" property="insurantPhone" />
    <result column="holder_phone" jdbcType="VARCHAR" property="holderPhone" />
    <result column="holder_phone_processed" jdbcType="VARCHAR" property="holderPhoneProcessed" />
    <result column="campaign_name" jdbcType="VARCHAR" property="campaignName" />
    <result column="package_full_name" jdbcType="VARCHAR" property="packageFullName" />
    <result column="accident_date" jdbcType="VARCHAR" property="accidentDate" />
    <result column="accident_place" jdbcType="VARCHAR" property="accidentPlace" />
    <result column="report_date" jdbcType="VARCHAR" property="reportDate" />
    <result column="claim_status" jdbcType="VARCHAR" property="claimStatus" />
    <result column="paid_amount" jdbcType="VARCHAR" property="paidAmount" />
    <result column="accident_reason" jdbcType="VARCHAR" property="accidentReason" />
    <result column="insurant_city" jdbcType="VARCHAR" property="insurantCity" />
    <result column="pay_account" jdbcType="VARCHAR" property="payAccount" />
    <result column="pay_account_processed" jdbcType="VARCHAR" property="payAccountProcessed" />
    <result column="receiver_name" jdbcType="VARCHAR" property="receiverName" />
    <result column="black_type" jdbcType="VARCHAR" property="blackType" />
    <result column="deptime" jdbcType="VARCHAR" property="deptime" />
    <result column="deptimeplan" jdbcType="VARCHAR" property="deptimeplan" />
    <result column="arrivetime" jdbcType="VARCHAR" property="arrivetime" />
    <result column="arrivetimeplan" jdbcType="VARCHAR" property="arrivetimeplan" />
    <result column="flight_no" jdbcType="VARCHAR" property="flightNo" />
    <result column="sour_city_name" jdbcType="VARCHAR" property="sourCityName" />
    <result column="arr_city_name" jdbcType="VARCHAR" property="arrCityName" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, community_id, policy_no, effective_date, insure_date, premium, orig_premium, 
    policy_status_id, is_claim_type, report_no, claimant_phone, claimant_phone_processed, 
    holder_name, holder_cert, holder_cert_processed, holder_passport_processed, insurant_name, 
    insurant_cert, insurant_phone, holder_phone, holder_phone_processed, campaign_name, 
    package_full_name, accident_date, accident_place, report_date, claim_status, paid_amount, 
    accident_reason, insurant_city, pay_account, pay_account_processed, receiver_name, 
    black_type, deptime, deptimeplan, arrivetime, arrivetimeplan, flight_no, sour_city_name, 
    arr_city_name, gmt_created, gmt_modified, creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.FlightdelayCommunityDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cdm_claim_flightdelay_community_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cdm_claim_flightdelay_community_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.FlightdelayCommunityDetailDO">
    insert into cdm_claim_flightdelay_community_detail (id, community_id, policy_no, 
      effective_date, insure_date, premium, 
      orig_premium, policy_status_id, is_claim_type, 
      report_no, claimant_phone, claimant_phone_processed, 
      holder_name, holder_cert, holder_cert_processed, 
      holder_passport_processed, insurant_name, 
      insurant_cert, insurant_phone, holder_phone, 
      holder_phone_processed, campaign_name, package_full_name, 
      accident_date, accident_place, report_date, 
      claim_status, paid_amount, accident_reason, 
      insurant_city, pay_account, pay_account_processed, 
      receiver_name, black_type, deptime, 
      deptimeplan, arrivetime, arrivetimeplan, 
      flight_no, sour_city_name, arr_city_name, 
      gmt_created, gmt_modified, creator, 
      modifier, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{communityId,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, 
      #{effectiveDate,jdbcType=VARCHAR}, #{insureDate,jdbcType=VARCHAR}, #{premium,jdbcType=VARCHAR}, 
      #{origPremium,jdbcType=VARCHAR}, #{policyStatusId,jdbcType=VARCHAR}, #{isClaimType,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{claimantPhone,jdbcType=VARCHAR}, #{claimantPhoneProcessed,jdbcType=VARCHAR}, 
      #{holderName,jdbcType=VARCHAR}, #{holderCert,jdbcType=VARCHAR}, #{holderCertProcessed,jdbcType=VARCHAR}, 
      #{holderPassportProcessed,jdbcType=VARCHAR}, #{insurantName,jdbcType=VARCHAR}, 
      #{insurantCert,jdbcType=VARCHAR}, #{insurantPhone,jdbcType=VARCHAR}, #{holderPhone,jdbcType=VARCHAR}, 
      #{holderPhoneProcessed,jdbcType=VARCHAR}, #{campaignName,jdbcType=VARCHAR}, #{packageFullName,jdbcType=VARCHAR}, 
      #{accidentDate,jdbcType=VARCHAR}, #{accidentPlace,jdbcType=VARCHAR}, #{reportDate,jdbcType=VARCHAR}, 
      #{claimStatus,jdbcType=VARCHAR}, #{paidAmount,jdbcType=VARCHAR}, #{accidentReason,jdbcType=VARCHAR}, 
      #{insurantCity,jdbcType=VARCHAR}, #{payAccount,jdbcType=VARCHAR}, #{payAccountProcessed,jdbcType=VARCHAR}, 
      #{receiverName,jdbcType=VARCHAR}, #{blackType,jdbcType=VARCHAR}, #{deptime,jdbcType=VARCHAR}, 
      #{deptimeplan,jdbcType=VARCHAR}, #{arrivetime,jdbcType=VARCHAR}, #{arrivetimeplan,jdbcType=VARCHAR}, 
      #{flightNo,jdbcType=VARCHAR}, #{sourCityName,jdbcType=VARCHAR}, #{arrCityName,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.FlightdelayCommunityDetailDO">
    insert into cdm_claim_flightdelay_community_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="communityId != null">
        community_id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="effectiveDate != null">
        effective_date,
      </if>
      <if test="insureDate != null">
        insure_date,
      </if>
      <if test="premium != null">
        premium,
      </if>
      <if test="origPremium != null">
        orig_premium,
      </if>
      <if test="policyStatusId != null">
        policy_status_id,
      </if>
      <if test="isClaimType != null">
        is_claim_type,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="claimantPhone != null">
        claimant_phone,
      </if>
      <if test="claimantPhoneProcessed != null">
        claimant_phone_processed,
      </if>
      <if test="holderName != null">
        holder_name,
      </if>
      <if test="holderCert != null">
        holder_cert,
      </if>
      <if test="holderCertProcessed != null">
        holder_cert_processed,
      </if>
      <if test="holderPassportProcessed != null">
        holder_passport_processed,
      </if>
      <if test="insurantName != null">
        insurant_name,
      </if>
      <if test="insurantCert != null">
        insurant_cert,
      </if>
      <if test="insurantPhone != null">
        insurant_phone,
      </if>
      <if test="holderPhone != null">
        holder_phone,
      </if>
      <if test="holderPhoneProcessed != null">
        holder_phone_processed,
      </if>
      <if test="campaignName != null">
        campaign_name,
      </if>
      <if test="packageFullName != null">
        package_full_name,
      </if>
      <if test="accidentDate != null">
        accident_date,
      </if>
      <if test="accidentPlace != null">
        accident_place,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="claimStatus != null">
        claim_status,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="accidentReason != null">
        accident_reason,
      </if>
      <if test="insurantCity != null">
        insurant_city,
      </if>
      <if test="payAccount != null">
        pay_account,
      </if>
      <if test="payAccountProcessed != null">
        pay_account_processed,
      </if>
      <if test="receiverName != null">
        receiver_name,
      </if>
      <if test="blackType != null">
        black_type,
      </if>
      <if test="deptime != null">
        deptime,
      </if>
      <if test="deptimeplan != null">
        deptimeplan,
      </if>
      <if test="arrivetime != null">
        arrivetime,
      </if>
      <if test="arrivetimeplan != null">
        arrivetimeplan,
      </if>
      <if test="flightNo != null">
        flight_no,
      </if>
      <if test="sourCityName != null">
        sour_city_name,
      </if>
      <if test="arrCityName != null">
        arr_city_name,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="communityId != null">
        #{communityId,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDate != null">
        #{effectiveDate,jdbcType=VARCHAR},
      </if>
      <if test="insureDate != null">
        #{insureDate,jdbcType=VARCHAR},
      </if>
      <if test="premium != null">
        #{premium,jdbcType=VARCHAR},
      </if>
      <if test="origPremium != null">
        #{origPremium,jdbcType=VARCHAR},
      </if>
      <if test="policyStatusId != null">
        #{policyStatusId,jdbcType=VARCHAR},
      </if>
      <if test="isClaimType != null">
        #{isClaimType,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimantPhone != null">
        #{claimantPhone,jdbcType=VARCHAR},
      </if>
      <if test="claimantPhoneProcessed != null">
        #{claimantPhoneProcessed,jdbcType=VARCHAR},
      </if>
      <if test="holderName != null">
        #{holderName,jdbcType=VARCHAR},
      </if>
      <if test="holderCert != null">
        #{holderCert,jdbcType=VARCHAR},
      </if>
      <if test="holderCertProcessed != null">
        #{holderCertProcessed,jdbcType=VARCHAR},
      </if>
      <if test="holderPassportProcessed != null">
        #{holderPassportProcessed,jdbcType=VARCHAR},
      </if>
      <if test="insurantName != null">
        #{insurantName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCert != null">
        #{insurantCert,jdbcType=VARCHAR},
      </if>
      <if test="insurantPhone != null">
        #{insurantPhone,jdbcType=VARCHAR},
      </if>
      <if test="holderPhone != null">
        #{holderPhone,jdbcType=VARCHAR},
      </if>
      <if test="holderPhoneProcessed != null">
        #{holderPhoneProcessed,jdbcType=VARCHAR},
      </if>
      <if test="campaignName != null">
        #{campaignName,jdbcType=VARCHAR},
      </if>
      <if test="packageFullName != null">
        #{packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        #{accidentDate,jdbcType=VARCHAR},
      </if>
      <if test="accidentPlace != null">
        #{accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        #{claimStatus,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="accidentReason != null">
        #{accidentReason,jdbcType=VARCHAR},
      </if>
      <if test="insurantCity != null">
        #{insurantCity,jdbcType=VARCHAR},
      </if>
      <if test="payAccount != null">
        #{payAccount,jdbcType=VARCHAR},
      </if>
      <if test="payAccountProcessed != null">
        #{payAccountProcessed,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null">
        #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="blackType != null">
        #{blackType,jdbcType=VARCHAR},
      </if>
      <if test="deptime != null">
        #{deptime,jdbcType=VARCHAR},
      </if>
      <if test="deptimeplan != null">
        #{deptimeplan,jdbcType=VARCHAR},
      </if>
      <if test="arrivetime != null">
        #{arrivetime,jdbcType=VARCHAR},
      </if>
      <if test="arrivetimeplan != null">
        #{arrivetimeplan,jdbcType=VARCHAR},
      </if>
      <if test="flightNo != null">
        #{flightNo,jdbcType=VARCHAR},
      </if>
      <if test="sourCityName != null">
        #{sourCityName,jdbcType=VARCHAR},
      </if>
      <if test="arrCityName != null">
        #{arrCityName,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.FlightdelayCommunityDetailExample" resultType="java.lang.Long">
    select count(*) from cdm_claim_flightdelay_community_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cdm_claim_flightdelay_community_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.communityId != null">
        community_id = #{record.communityId,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.effectiveDate != null">
        effective_date = #{record.effectiveDate,jdbcType=VARCHAR},
      </if>
      <if test="record.insureDate != null">
        insure_date = #{record.insureDate,jdbcType=VARCHAR},
      </if>
      <if test="record.premium != null">
        premium = #{record.premium,jdbcType=VARCHAR},
      </if>
      <if test="record.origPremium != null">
        orig_premium = #{record.origPremium,jdbcType=VARCHAR},
      </if>
      <if test="record.policyStatusId != null">
        policy_status_id = #{record.policyStatusId,jdbcType=VARCHAR},
      </if>
      <if test="record.isClaimType != null">
        is_claim_type = #{record.isClaimType,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimantPhone != null">
        claimant_phone = #{record.claimantPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.claimantPhoneProcessed != null">
        claimant_phone_processed = #{record.claimantPhoneProcessed,jdbcType=VARCHAR},
      </if>
      <if test="record.holderName != null">
        holder_name = #{record.holderName,jdbcType=VARCHAR},
      </if>
      <if test="record.holderCert != null">
        holder_cert = #{record.holderCert,jdbcType=VARCHAR},
      </if>
      <if test="record.holderCertProcessed != null">
        holder_cert_processed = #{record.holderCertProcessed,jdbcType=VARCHAR},
      </if>
      <if test="record.holderPassportProcessed != null">
        holder_passport_processed = #{record.holderPassportProcessed,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantName != null">
        insurant_name = #{record.insurantName,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCert != null">
        insurant_cert = #{record.insurantCert,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantPhone != null">
        insurant_phone = #{record.insurantPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.holderPhone != null">
        holder_phone = #{record.holderPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.holderPhoneProcessed != null">
        holder_phone_processed = #{record.holderPhoneProcessed,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignName != null">
        campaign_name = #{record.campaignName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageFullName != null">
        package_full_name = #{record.packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentDate != null">
        accident_date = #{record.accidentDate,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentPlace != null">
        accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDate != null">
        report_date = #{record.reportDate,jdbcType=VARCHAR},
      </if>
      <if test="record.claimStatus != null">
        claim_status = #{record.claimStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentReason != null">
        accident_reason = #{record.accidentReason,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCity != null">
        insurant_city = #{record.insurantCity,jdbcType=VARCHAR},
      </if>
      <if test="record.payAccount != null">
        pay_account = #{record.payAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.payAccountProcessed != null">
        pay_account_processed = #{record.payAccountProcessed,jdbcType=VARCHAR},
      </if>
      <if test="record.receiverName != null">
        receiver_name = #{record.receiverName,jdbcType=VARCHAR},
      </if>
      <if test="record.blackType != null">
        black_type = #{record.blackType,jdbcType=VARCHAR},
      </if>
      <if test="record.deptime != null">
        deptime = #{record.deptime,jdbcType=VARCHAR},
      </if>
      <if test="record.deptimeplan != null">
        deptimeplan = #{record.deptimeplan,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivetime != null">
        arrivetime = #{record.arrivetime,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivetimeplan != null">
        arrivetimeplan = #{record.arrivetimeplan,jdbcType=VARCHAR},
      </if>
      <if test="record.flightNo != null">
        flight_no = #{record.flightNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sourCityName != null">
        sour_city_name = #{record.sourCityName,jdbcType=VARCHAR},
      </if>
      <if test="record.arrCityName != null">
        arr_city_name = #{record.arrCityName,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cdm_claim_flightdelay_community_detail
    set id = #{record.id,jdbcType=BIGINT},
      community_id = #{record.communityId,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      effective_date = #{record.effectiveDate,jdbcType=VARCHAR},
      insure_date = #{record.insureDate,jdbcType=VARCHAR},
      premium = #{record.premium,jdbcType=VARCHAR},
      orig_premium = #{record.origPremium,jdbcType=VARCHAR},
      policy_status_id = #{record.policyStatusId,jdbcType=VARCHAR},
      is_claim_type = #{record.isClaimType,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      claimant_phone = #{record.claimantPhone,jdbcType=VARCHAR},
      claimant_phone_processed = #{record.claimantPhoneProcessed,jdbcType=VARCHAR},
      holder_name = #{record.holderName,jdbcType=VARCHAR},
      holder_cert = #{record.holderCert,jdbcType=VARCHAR},
      holder_cert_processed = #{record.holderCertProcessed,jdbcType=VARCHAR},
      holder_passport_processed = #{record.holderPassportProcessed,jdbcType=VARCHAR},
      insurant_name = #{record.insurantName,jdbcType=VARCHAR},
      insurant_cert = #{record.insurantCert,jdbcType=VARCHAR},
      insurant_phone = #{record.insurantPhone,jdbcType=VARCHAR},
      holder_phone = #{record.holderPhone,jdbcType=VARCHAR},
      holder_phone_processed = #{record.holderPhoneProcessed,jdbcType=VARCHAR},
      campaign_name = #{record.campaignName,jdbcType=VARCHAR},
      package_full_name = #{record.packageFullName,jdbcType=VARCHAR},
      accident_date = #{record.accidentDate,jdbcType=VARCHAR},
      accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      report_date = #{record.reportDate,jdbcType=VARCHAR},
      claim_status = #{record.claimStatus,jdbcType=VARCHAR},
      paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      accident_reason = #{record.accidentReason,jdbcType=VARCHAR},
      insurant_city = #{record.insurantCity,jdbcType=VARCHAR},
      pay_account = #{record.payAccount,jdbcType=VARCHAR},
      pay_account_processed = #{record.payAccountProcessed,jdbcType=VARCHAR},
      receiver_name = #{record.receiverName,jdbcType=VARCHAR},
      black_type = #{record.blackType,jdbcType=VARCHAR},
      deptime = #{record.deptime,jdbcType=VARCHAR},
      deptimeplan = #{record.deptimeplan,jdbcType=VARCHAR},
      arrivetime = #{record.arrivetime,jdbcType=VARCHAR},
      arrivetimeplan = #{record.arrivetimeplan,jdbcType=VARCHAR},
      flight_no = #{record.flightNo,jdbcType=VARCHAR},
      sour_city_name = #{record.sourCityName,jdbcType=VARCHAR},
      arr_city_name = #{record.arrCityName,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.FlightdelayCommunityDetailDO">
    update cdm_claim_flightdelay_community_detail
    <set>
      <if test="communityId != null">
        community_id = #{communityId,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDate != null">
        effective_date = #{effectiveDate,jdbcType=VARCHAR},
      </if>
      <if test="insureDate != null">
        insure_date = #{insureDate,jdbcType=VARCHAR},
      </if>
      <if test="premium != null">
        premium = #{premium,jdbcType=VARCHAR},
      </if>
      <if test="origPremium != null">
        orig_premium = #{origPremium,jdbcType=VARCHAR},
      </if>
      <if test="policyStatusId != null">
        policy_status_id = #{policyStatusId,jdbcType=VARCHAR},
      </if>
      <if test="isClaimType != null">
        is_claim_type = #{isClaimType,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimantPhone != null">
        claimant_phone = #{claimantPhone,jdbcType=VARCHAR},
      </if>
      <if test="claimantPhoneProcessed != null">
        claimant_phone_processed = #{claimantPhoneProcessed,jdbcType=VARCHAR},
      </if>
      <if test="holderName != null">
        holder_name = #{holderName,jdbcType=VARCHAR},
      </if>
      <if test="holderCert != null">
        holder_cert = #{holderCert,jdbcType=VARCHAR},
      </if>
      <if test="holderCertProcessed != null">
        holder_cert_processed = #{holderCertProcessed,jdbcType=VARCHAR},
      </if>
      <if test="holderPassportProcessed != null">
        holder_passport_processed = #{holderPassportProcessed,jdbcType=VARCHAR},
      </if>
      <if test="insurantName != null">
        insurant_name = #{insurantName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCert != null">
        insurant_cert = #{insurantCert,jdbcType=VARCHAR},
      </if>
      <if test="insurantPhone != null">
        insurant_phone = #{insurantPhone,jdbcType=VARCHAR},
      </if>
      <if test="holderPhone != null">
        holder_phone = #{holderPhone,jdbcType=VARCHAR},
      </if>
      <if test="holderPhoneProcessed != null">
        holder_phone_processed = #{holderPhoneProcessed,jdbcType=VARCHAR},
      </if>
      <if test="campaignName != null">
        campaign_name = #{campaignName,jdbcType=VARCHAR},
      </if>
      <if test="packageFullName != null">
        package_full_name = #{packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        accident_date = #{accidentDate,jdbcType=VARCHAR},
      </if>
      <if test="accidentPlace != null">
        accident_place = #{accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        claim_status = #{claimStatus,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="accidentReason != null">
        accident_reason = #{accidentReason,jdbcType=VARCHAR},
      </if>
      <if test="insurantCity != null">
        insurant_city = #{insurantCity,jdbcType=VARCHAR},
      </if>
      <if test="payAccount != null">
        pay_account = #{payAccount,jdbcType=VARCHAR},
      </if>
      <if test="payAccountProcessed != null">
        pay_account_processed = #{payAccountProcessed,jdbcType=VARCHAR},
      </if>
      <if test="receiverName != null">
        receiver_name = #{receiverName,jdbcType=VARCHAR},
      </if>
      <if test="blackType != null">
        black_type = #{blackType,jdbcType=VARCHAR},
      </if>
      <if test="deptime != null">
        deptime = #{deptime,jdbcType=VARCHAR},
      </if>
      <if test="deptimeplan != null">
        deptimeplan = #{deptimeplan,jdbcType=VARCHAR},
      </if>
      <if test="arrivetime != null">
        arrivetime = #{arrivetime,jdbcType=VARCHAR},
      </if>
      <if test="arrivetimeplan != null">
        arrivetimeplan = #{arrivetimeplan,jdbcType=VARCHAR},
      </if>
      <if test="flightNo != null">
        flight_no = #{flightNo,jdbcType=VARCHAR},
      </if>
      <if test="sourCityName != null">
        sour_city_name = #{sourCityName,jdbcType=VARCHAR},
      </if>
      <if test="arrCityName != null">
        arr_city_name = #{arrCityName,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.FlightdelayCommunityDetailDO">
    update cdm_claim_flightdelay_community_detail
    set community_id = #{communityId,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      effective_date = #{effectiveDate,jdbcType=VARCHAR},
      insure_date = #{insureDate,jdbcType=VARCHAR},
      premium = #{premium,jdbcType=VARCHAR},
      orig_premium = #{origPremium,jdbcType=VARCHAR},
      policy_status_id = #{policyStatusId,jdbcType=VARCHAR},
      is_claim_type = #{isClaimType,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      claimant_phone = #{claimantPhone,jdbcType=VARCHAR},
      claimant_phone_processed = #{claimantPhoneProcessed,jdbcType=VARCHAR},
      holder_name = #{holderName,jdbcType=VARCHAR},
      holder_cert = #{holderCert,jdbcType=VARCHAR},
      holder_cert_processed = #{holderCertProcessed,jdbcType=VARCHAR},
      holder_passport_processed = #{holderPassportProcessed,jdbcType=VARCHAR},
      insurant_name = #{insurantName,jdbcType=VARCHAR},
      insurant_cert = #{insurantCert,jdbcType=VARCHAR},
      insurant_phone = #{insurantPhone,jdbcType=VARCHAR},
      holder_phone = #{holderPhone,jdbcType=VARCHAR},
      holder_phone_processed = #{holderPhoneProcessed,jdbcType=VARCHAR},
      campaign_name = #{campaignName,jdbcType=VARCHAR},
      package_full_name = #{packageFullName,jdbcType=VARCHAR},
      accident_date = #{accidentDate,jdbcType=VARCHAR},
      accident_place = #{accidentPlace,jdbcType=VARCHAR},
      report_date = #{reportDate,jdbcType=VARCHAR},
      claim_status = #{claimStatus,jdbcType=VARCHAR},
      paid_amount = #{paidAmount,jdbcType=VARCHAR},
      accident_reason = #{accidentReason,jdbcType=VARCHAR},
      insurant_city = #{insurantCity,jdbcType=VARCHAR},
      pay_account = #{payAccount,jdbcType=VARCHAR},
      pay_account_processed = #{payAccountProcessed,jdbcType=VARCHAR},
      receiver_name = #{receiverName,jdbcType=VARCHAR},
      black_type = #{blackType,jdbcType=VARCHAR},
      deptime = #{deptime,jdbcType=VARCHAR},
      deptimeplan = #{deptimeplan,jdbcType=VARCHAR},
      arrivetime = #{arrivetime,jdbcType=VARCHAR},
      arrivetimeplan = #{arrivetimeplan,jdbcType=VARCHAR},
      flight_no = #{flightNo,jdbcType=VARCHAR},
      sour_city_name = #{sourCityName,jdbcType=VARCHAR},
      arr_city_name = #{arrCityName,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cdm_claim_flightdelay_community_detail
    (id, community_id, policy_no, effective_date, insure_date, premium, orig_premium, 
      policy_status_id, is_claim_type, report_no, claimant_phone, claimant_phone_processed, 
      holder_name, holder_cert, holder_cert_processed, holder_passport_processed, insurant_name, 
      insurant_cert, insurant_phone, holder_phone, holder_phone_processed, campaign_name, 
      package_full_name, accident_date, accident_place, report_date, claim_status, paid_amount, 
      accident_reason, insurant_city, pay_account, pay_account_processed, receiver_name, 
      black_type, deptime, deptimeplan, arrivetime, arrivetimeplan, flight_no, sour_city_name, 
      arr_city_name, gmt_created, gmt_modified, creator, modifier, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.communityId,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.effectiveDate,jdbcType=VARCHAR}, #{item.insureDate,jdbcType=VARCHAR}, #{item.premium,jdbcType=VARCHAR}, 
        #{item.origPremium,jdbcType=VARCHAR}, #{item.policyStatusId,jdbcType=VARCHAR}, 
        #{item.isClaimType,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.claimantPhone,jdbcType=VARCHAR}, 
        #{item.claimantPhoneProcessed,jdbcType=VARCHAR}, #{item.holderName,jdbcType=VARCHAR}, 
        #{item.holderCert,jdbcType=VARCHAR}, #{item.holderCertProcessed,jdbcType=VARCHAR}, 
        #{item.holderPassportProcessed,jdbcType=VARCHAR}, #{item.insurantName,jdbcType=VARCHAR}, 
        #{item.insurantCert,jdbcType=VARCHAR}, #{item.insurantPhone,jdbcType=VARCHAR}, 
        #{item.holderPhone,jdbcType=VARCHAR}, #{item.holderPhoneProcessed,jdbcType=VARCHAR}, 
        #{item.campaignName,jdbcType=VARCHAR}, #{item.packageFullName,jdbcType=VARCHAR}, 
        #{item.accidentDate,jdbcType=VARCHAR}, #{item.accidentPlace,jdbcType=VARCHAR}, 
        #{item.reportDate,jdbcType=VARCHAR}, #{item.claimStatus,jdbcType=VARCHAR}, #{item.paidAmount,jdbcType=VARCHAR}, 
        #{item.accidentReason,jdbcType=VARCHAR}, #{item.insurantCity,jdbcType=VARCHAR}, 
        #{item.payAccount,jdbcType=VARCHAR}, #{item.payAccountProcessed,jdbcType=VARCHAR}, 
        #{item.receiverName,jdbcType=VARCHAR}, #{item.blackType,jdbcType=VARCHAR}, #{item.deptime,jdbcType=VARCHAR}, 
        #{item.deptimeplan,jdbcType=VARCHAR}, #{item.arrivetime,jdbcType=VARCHAR}, #{item.arrivetimeplan,jdbcType=VARCHAR}, 
        #{item.flightNo,jdbcType=VARCHAR}, #{item.sourCityName,jdbcType=VARCHAR}, #{item.arrCityName,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cdm_claim_flightdelay_community_detail (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'community_id'.toString() == column.value">
          #{item.communityId,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'effective_date'.toString() == column.value">
          #{item.effectiveDate,jdbcType=VARCHAR}
        </if>
        <if test="'insure_date'.toString() == column.value">
          #{item.insureDate,jdbcType=VARCHAR}
        </if>
        <if test="'premium'.toString() == column.value">
          #{item.premium,jdbcType=VARCHAR}
        </if>
        <if test="'orig_premium'.toString() == column.value">
          #{item.origPremium,jdbcType=VARCHAR}
        </if>
        <if test="'policy_status_id'.toString() == column.value">
          #{item.policyStatusId,jdbcType=VARCHAR}
        </if>
        <if test="'is_claim_type'.toString() == column.value">
          #{item.isClaimType,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'claimant_phone'.toString() == column.value">
          #{item.claimantPhone,jdbcType=VARCHAR}
        </if>
        <if test="'claimant_phone_processed'.toString() == column.value">
          #{item.claimantPhoneProcessed,jdbcType=VARCHAR}
        </if>
        <if test="'holder_name'.toString() == column.value">
          #{item.holderName,jdbcType=VARCHAR}
        </if>
        <if test="'holder_cert'.toString() == column.value">
          #{item.holderCert,jdbcType=VARCHAR}
        </if>
        <if test="'holder_cert_processed'.toString() == column.value">
          #{item.holderCertProcessed,jdbcType=VARCHAR}
        </if>
        <if test="'holder_passport_processed'.toString() == column.value">
          #{item.holderPassportProcessed,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_name'.toString() == column.value">
          #{item.insurantName,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_cert'.toString() == column.value">
          #{item.insurantCert,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_phone'.toString() == column.value">
          #{item.insurantPhone,jdbcType=VARCHAR}
        </if>
        <if test="'holder_phone'.toString() == column.value">
          #{item.holderPhone,jdbcType=VARCHAR}
        </if>
        <if test="'holder_phone_processed'.toString() == column.value">
          #{item.holderPhoneProcessed,jdbcType=VARCHAR}
        </if>
        <if test="'campaign_name'.toString() == column.value">
          #{item.campaignName,jdbcType=VARCHAR}
        </if>
        <if test="'package_full_name'.toString() == column.value">
          #{item.packageFullName,jdbcType=VARCHAR}
        </if>
        <if test="'accident_date'.toString() == column.value">
          #{item.accidentDate,jdbcType=VARCHAR}
        </if>
        <if test="'accident_place'.toString() == column.value">
          #{item.accidentPlace,jdbcType=VARCHAR}
        </if>
        <if test="'report_date'.toString() == column.value">
          #{item.reportDate,jdbcType=VARCHAR}
        </if>
        <if test="'claim_status'.toString() == column.value">
          #{item.claimStatus,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount'.toString() == column.value">
          #{item.paidAmount,jdbcType=VARCHAR}
        </if>
        <if test="'accident_reason'.toString() == column.value">
          #{item.accidentReason,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_city'.toString() == column.value">
          #{item.insurantCity,jdbcType=VARCHAR}
        </if>
        <if test="'pay_account'.toString() == column.value">
          #{item.payAccount,jdbcType=VARCHAR}
        </if>
        <if test="'pay_account_processed'.toString() == column.value">
          #{item.payAccountProcessed,jdbcType=VARCHAR}
        </if>
        <if test="'receiver_name'.toString() == column.value">
          #{item.receiverName,jdbcType=VARCHAR}
        </if>
        <if test="'black_type'.toString() == column.value">
          #{item.blackType,jdbcType=VARCHAR}
        </if>
        <if test="'deptime'.toString() == column.value">
          #{item.deptime,jdbcType=VARCHAR}
        </if>
        <if test="'deptimeplan'.toString() == column.value">
          #{item.deptimeplan,jdbcType=VARCHAR}
        </if>
        <if test="'arrivetime'.toString() == column.value">
          #{item.arrivetime,jdbcType=VARCHAR}
        </if>
        <if test="'arrivetimeplan'.toString() == column.value">
          #{item.arrivetimeplan,jdbcType=VARCHAR}
        </if>
        <if test="'flight_no'.toString() == column.value">
          #{item.flightNo,jdbcType=VARCHAR}
        </if>
        <if test="'sour_city_name'.toString() == column.value">
          #{item.sourCityName,jdbcType=VARCHAR}
        </if>
        <if test="'arr_city_name'.toString() == column.value">
          #{item.arrCityName,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>