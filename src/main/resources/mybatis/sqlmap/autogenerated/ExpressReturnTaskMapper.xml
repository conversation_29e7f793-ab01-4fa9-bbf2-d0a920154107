<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ExpressReturnTaskMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ExpressReturnTaskDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="origin" jdbcType="VARCHAR" property="origin" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="applicant" jdbcType="VARCHAR" property="applicant" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="recipient" jdbcType="VARCHAR" property="recipient" />
    <result column="recipient_address" jdbcType="VARCHAR" property="recipientAddress" />
    <result column="recipient_phone" jdbcType="VARCHAR" property="recipientPhone" />
    <result column="required_return_content" jdbcType="VARCHAR" property="requiredReturnContent" />
    <result column="return_express_number" jdbcType="VARCHAR" property="returnExpressNumber" />
    <result column="return_express_company" jdbcType="VARCHAR" property="returnExpressCompany" />
    <result column="return_date" jdbcType="TIMESTAMP" property="returnDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="receipt_date" jdbcType="TIMESTAMP" property="receiptDate" />
    <result column="related_attach_ids" jdbcType="VARCHAR" property="relatedAttachIds" />
    <result column="related_express_id" jdbcType="BIGINT" property="relatedExpressId" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, policy_no, origin, status, applicant, auditor, recipient, recipient_address, 
    recipient_phone, required_return_content, return_express_number, return_express_company, 
    return_date, remark, receipt_date, related_attach_ids, related_express_id, extra_info, 
    creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ExpressReturnTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from express_return_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from express_return_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ExpressReturnTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into express_return_task (report_no, policy_no, origin, 
      status, applicant, auditor, 
      recipient, recipient_address, recipient_phone, 
      required_return_content, return_express_number, 
      return_express_company, return_date, remark, 
      receipt_date, related_attach_ids, related_express_id, 
      extra_info, creator, modifier, 
      gmt_created, gmt_modified, is_deleted
      )
    values (#{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, #{origin,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{applicant,jdbcType=VARCHAR}, #{auditor,jdbcType=VARCHAR}, 
      #{recipient,jdbcType=VARCHAR}, #{recipientAddress,jdbcType=VARCHAR}, #{recipientPhone,jdbcType=VARCHAR}, 
      #{requiredReturnContent,jdbcType=VARCHAR}, #{returnExpressNumber,jdbcType=VARCHAR}, 
      #{returnExpressCompany,jdbcType=VARCHAR}, #{returnDate,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{receiptDate,jdbcType=TIMESTAMP}, #{relatedAttachIds,jdbcType=VARCHAR}, #{relatedExpressId,jdbcType=BIGINT}, 
      #{extraInfo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ExpressReturnTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into express_return_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="origin != null">
        origin,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="applicant != null">
        applicant,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="recipient != null">
        recipient,
      </if>
      <if test="recipientAddress != null">
        recipient_address,
      </if>
      <if test="recipientPhone != null">
        recipient_phone,
      </if>
      <if test="requiredReturnContent != null">
        required_return_content,
      </if>
      <if test="returnExpressNumber != null">
        return_express_number,
      </if>
      <if test="returnExpressCompany != null">
        return_express_company,
      </if>
      <if test="returnDate != null">
        return_date,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="receiptDate != null">
        receipt_date,
      </if>
      <if test="relatedAttachIds != null">
        related_attach_ids,
      </if>
      <if test="relatedExpressId != null">
        related_express_id,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="origin != null">
        #{origin,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="applicant != null">
        #{applicant,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="recipient != null">
        #{recipient,jdbcType=VARCHAR},
      </if>
      <if test="recipientAddress != null">
        #{recipientAddress,jdbcType=VARCHAR},
      </if>
      <if test="recipientPhone != null">
        #{recipientPhone,jdbcType=VARCHAR},
      </if>
      <if test="requiredReturnContent != null">
        #{requiredReturnContent,jdbcType=VARCHAR},
      </if>
      <if test="returnExpressNumber != null">
        #{returnExpressNumber,jdbcType=VARCHAR},
      </if>
      <if test="returnExpressCompany != null">
        #{returnExpressCompany,jdbcType=VARCHAR},
      </if>
      <if test="returnDate != null">
        #{returnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="receiptDate != null">
        #{receiptDate,jdbcType=TIMESTAMP},
      </if>
      <if test="relatedAttachIds != null">
        #{relatedAttachIds,jdbcType=VARCHAR},
      </if>
      <if test="relatedExpressId != null">
        #{relatedExpressId,jdbcType=BIGINT},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ExpressReturnTaskExample" resultType="java.lang.Long">
    select count(*) from express_return_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update express_return_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.origin != null">
        origin = #{record.origin,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.applicant != null">
        applicant = #{record.applicant,jdbcType=VARCHAR},
      </if>
      <if test="record.auditor != null">
        auditor = #{record.auditor,jdbcType=VARCHAR},
      </if>
      <if test="record.recipient != null">
        recipient = #{record.recipient,jdbcType=VARCHAR},
      </if>
      <if test="record.recipientAddress != null">
        recipient_address = #{record.recipientAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.recipientPhone != null">
        recipient_phone = #{record.recipientPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.requiredReturnContent != null">
        required_return_content = #{record.requiredReturnContent,jdbcType=VARCHAR},
      </if>
      <if test="record.returnExpressNumber != null">
        return_express_number = #{record.returnExpressNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.returnExpressCompany != null">
        return_express_company = #{record.returnExpressCompany,jdbcType=VARCHAR},
      </if>
      <if test="record.returnDate != null">
        return_date = #{record.returnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptDate != null">
        receipt_date = #{record.receiptDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.relatedAttachIds != null">
        related_attach_ids = #{record.relatedAttachIds,jdbcType=VARCHAR},
      </if>
      <if test="record.relatedExpressId != null">
        related_express_id = #{record.relatedExpressId,jdbcType=BIGINT},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update express_return_task
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      origin = #{record.origin,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      applicant = #{record.applicant,jdbcType=VARCHAR},
      auditor = #{record.auditor,jdbcType=VARCHAR},
      recipient = #{record.recipient,jdbcType=VARCHAR},
      recipient_address = #{record.recipientAddress,jdbcType=VARCHAR},
      recipient_phone = #{record.recipientPhone,jdbcType=VARCHAR},
      required_return_content = #{record.requiredReturnContent,jdbcType=VARCHAR},
      return_express_number = #{record.returnExpressNumber,jdbcType=VARCHAR},
      return_express_company = #{record.returnExpressCompany,jdbcType=VARCHAR},
      return_date = #{record.returnDate,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=VARCHAR},
      receipt_date = #{record.receiptDate,jdbcType=TIMESTAMP},
      related_attach_ids = #{record.relatedAttachIds,jdbcType=VARCHAR},
      related_express_id = #{record.relatedExpressId,jdbcType=BIGINT},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ExpressReturnTaskDO">
    update express_return_task
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="origin != null">
        origin = #{origin,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="applicant != null">
        applicant = #{applicant,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="recipient != null">
        recipient = #{recipient,jdbcType=VARCHAR},
      </if>
      <if test="recipientAddress != null">
        recipient_address = #{recipientAddress,jdbcType=VARCHAR},
      </if>
      <if test="recipientPhone != null">
        recipient_phone = #{recipientPhone,jdbcType=VARCHAR},
      </if>
      <if test="requiredReturnContent != null">
        required_return_content = #{requiredReturnContent,jdbcType=VARCHAR},
      </if>
      <if test="returnExpressNumber != null">
        return_express_number = #{returnExpressNumber,jdbcType=VARCHAR},
      </if>
      <if test="returnExpressCompany != null">
        return_express_company = #{returnExpressCompany,jdbcType=VARCHAR},
      </if>
      <if test="returnDate != null">
        return_date = #{returnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="receiptDate != null">
        receipt_date = #{receiptDate,jdbcType=TIMESTAMP},
      </if>
      <if test="relatedAttachIds != null">
        related_attach_ids = #{relatedAttachIds,jdbcType=VARCHAR},
      </if>
      <if test="relatedExpressId != null">
        related_express_id = #{relatedExpressId,jdbcType=BIGINT},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ExpressReturnTaskDO">
    update express_return_task
    set report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      origin = #{origin,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      applicant = #{applicant,jdbcType=VARCHAR},
      auditor = #{auditor,jdbcType=VARCHAR},
      recipient = #{recipient,jdbcType=VARCHAR},
      recipient_address = #{recipientAddress,jdbcType=VARCHAR},
      recipient_phone = #{recipientPhone,jdbcType=VARCHAR},
      required_return_content = #{requiredReturnContent,jdbcType=VARCHAR},
      return_express_number = #{returnExpressNumber,jdbcType=VARCHAR},
      return_express_company = #{returnExpressCompany,jdbcType=VARCHAR},
      return_date = #{returnDate,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      receipt_date = #{receiptDate,jdbcType=TIMESTAMP},
      related_attach_ids = #{relatedAttachIds,jdbcType=VARCHAR},
      related_express_id = #{relatedExpressId,jdbcType=BIGINT},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into express_return_task
    (report_no, policy_no, origin, status, applicant, auditor, recipient, recipient_address, 
      recipient_phone, required_return_content, return_express_number, return_express_company, 
      return_date, remark, receipt_date, related_attach_ids, related_express_id, extra_info, 
      creator, modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.origin,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=VARCHAR}, #{item.applicant,jdbcType=VARCHAR}, #{item.auditor,jdbcType=VARCHAR}, 
        #{item.recipient,jdbcType=VARCHAR}, #{item.recipientAddress,jdbcType=VARCHAR}, 
        #{item.recipientPhone,jdbcType=VARCHAR}, #{item.requiredReturnContent,jdbcType=VARCHAR}, 
        #{item.returnExpressNumber,jdbcType=VARCHAR}, #{item.returnExpressCompany,jdbcType=VARCHAR}, 
        #{item.returnDate,jdbcType=TIMESTAMP}, #{item.remark,jdbcType=VARCHAR}, #{item.receiptDate,jdbcType=TIMESTAMP}, 
        #{item.relatedAttachIds,jdbcType=VARCHAR}, #{item.relatedExpressId,jdbcType=BIGINT}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into express_return_task (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'origin'.toString() == column.value">
          #{item.origin,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=VARCHAR}
        </if>
        <if test="'applicant'.toString() == column.value">
          #{item.applicant,jdbcType=VARCHAR}
        </if>
        <if test="'auditor'.toString() == column.value">
          #{item.auditor,jdbcType=VARCHAR}
        </if>
        <if test="'recipient'.toString() == column.value">
          #{item.recipient,jdbcType=VARCHAR}
        </if>
        <if test="'recipient_address'.toString() == column.value">
          #{item.recipientAddress,jdbcType=VARCHAR}
        </if>
        <if test="'recipient_phone'.toString() == column.value">
          #{item.recipientPhone,jdbcType=VARCHAR}
        </if>
        <if test="'required_return_content'.toString() == column.value">
          #{item.requiredReturnContent,jdbcType=VARCHAR}
        </if>
        <if test="'return_express_number'.toString() == column.value">
          #{item.returnExpressNumber,jdbcType=VARCHAR}
        </if>
        <if test="'return_express_company'.toString() == column.value">
          #{item.returnExpressCompany,jdbcType=VARCHAR}
        </if>
        <if test="'return_date'.toString() == column.value">
          #{item.returnDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'receipt_date'.toString() == column.value">
          #{item.receiptDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'related_attach_ids'.toString() == column.value">
          #{item.relatedAttachIds,jdbcType=VARCHAR}
        </if>
        <if test="'related_express_id'.toString() == column.value">
          #{item.relatedExpressId,jdbcType=BIGINT}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>