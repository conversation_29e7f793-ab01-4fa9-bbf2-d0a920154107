<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimProductProcessorConfigMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimProductProcessorConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="prod_no" jdbcType="VARCHAR" property="prodNo" />
    <result column="pipeline" jdbcType="VARCHAR" property="pipeline" />
    <result column="biz_node" jdbcType="VARCHAR" property="bizNode" />
    <result column="processor_code" jdbcType="VARCHAR" property="processorCode" />
    <result column="processor_sort" jdbcType="TINYINT" property="processorSort" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, product_id, prod_no, pipeline, biz_node, processor_code, processor_sort, creator, 
    modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductProcessorConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_product_processor_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_product_processor_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductProcessorConfigDO">
    insert into claim_product_processor_config (id, product_id, prod_no, 
      pipeline, biz_node, processor_code, 
      processor_sort, creator, modifier, 
      gmt_created, gmt_modified, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{productId,jdbcType=BIGINT}, #{prodNo,jdbcType=VARCHAR}, 
      #{pipeline,jdbcType=VARCHAR}, #{bizNode,jdbcType=VARCHAR}, #{processorCode,jdbcType=VARCHAR}, 
      #{processorSort,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductProcessorConfigDO">
    insert into claim_product_processor_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="prodNo != null">
        prod_no,
      </if>
      <if test="pipeline != null">
        pipeline,
      </if>
      <if test="bizNode != null">
        biz_node,
      </if>
      <if test="processorCode != null">
        processor_code,
      </if>
      <if test="processorSort != null">
        processor_sort,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="prodNo != null">
        #{prodNo,jdbcType=VARCHAR},
      </if>
      <if test="pipeline != null">
        #{pipeline,jdbcType=VARCHAR},
      </if>
      <if test="bizNode != null">
        #{bizNode,jdbcType=VARCHAR},
      </if>
      <if test="processorCode != null">
        #{processorCode,jdbcType=VARCHAR},
      </if>
      <if test="processorSort != null">
        #{processorSort,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductProcessorConfigExample" resultType="java.lang.Long">
    select count(*) from claim_product_processor_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_product_processor_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.prodNo != null">
        prod_no = #{record.prodNo,jdbcType=VARCHAR},
      </if>
      <if test="record.pipeline != null">
        pipeline = #{record.pipeline,jdbcType=VARCHAR},
      </if>
      <if test="record.bizNode != null">
        biz_node = #{record.bizNode,jdbcType=VARCHAR},
      </if>
      <if test="record.processorCode != null">
        processor_code = #{record.processorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.processorSort != null">
        processor_sort = #{record.processorSort,jdbcType=TINYINT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_product_processor_config
    set id = #{record.id,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=BIGINT},
      prod_no = #{record.prodNo,jdbcType=VARCHAR},
      pipeline = #{record.pipeline,jdbcType=VARCHAR},
      biz_node = #{record.bizNode,jdbcType=VARCHAR},
      processor_code = #{record.processorCode,jdbcType=VARCHAR},
      processor_sort = #{record.processorSort,jdbcType=TINYINT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductProcessorConfigDO">
    update claim_product_processor_config
    <set>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="prodNo != null">
        prod_no = #{prodNo,jdbcType=VARCHAR},
      </if>
      <if test="pipeline != null">
        pipeline = #{pipeline,jdbcType=VARCHAR},
      </if>
      <if test="bizNode != null">
        biz_node = #{bizNode,jdbcType=VARCHAR},
      </if>
      <if test="processorCode != null">
        processor_code = #{processorCode,jdbcType=VARCHAR},
      </if>
      <if test="processorSort != null">
        processor_sort = #{processorSort,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductProcessorConfigDO">
    update claim_product_processor_config
    set product_id = #{productId,jdbcType=BIGINT},
      prod_no = #{prodNo,jdbcType=VARCHAR},
      pipeline = #{pipeline,jdbcType=VARCHAR},
      biz_node = #{bizNode,jdbcType=VARCHAR},
      processor_code = #{processorCode,jdbcType=VARCHAR},
      processor_sort = #{processorSort,jdbcType=TINYINT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_product_processor_config
    (id, product_id, prod_no, pipeline, biz_node, processor_code, processor_sort, creator, 
      modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.productId,jdbcType=BIGINT}, #{item.prodNo,jdbcType=VARCHAR}, 
        #{item.pipeline,jdbcType=VARCHAR}, #{item.bizNode,jdbcType=VARCHAR}, #{item.processorCode,jdbcType=VARCHAR}, 
        #{item.processorSort,jdbcType=TINYINT}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_product_processor_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'product_id'.toString() == column.value">
          #{item.productId,jdbcType=BIGINT}
        </if>
        <if test="'prod_no'.toString() == column.value">
          #{item.prodNo,jdbcType=VARCHAR}
        </if>
        <if test="'pipeline'.toString() == column.value">
          #{item.pipeline,jdbcType=VARCHAR}
        </if>
        <if test="'biz_node'.toString() == column.value">
          #{item.bizNode,jdbcType=VARCHAR}
        </if>
        <if test="'processor_code'.toString() == column.value">
          #{item.processorCode,jdbcType=VARCHAR}
        </if>
        <if test="'processor_sort'.toString() == column.value">
          #{item.processorSort,jdbcType=TINYINT}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>