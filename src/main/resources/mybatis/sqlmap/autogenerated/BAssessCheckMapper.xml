<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.BAssessCheckMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.BAssessCheckDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_claim_bill_id" jdbcType="BIGINT" property="batchClaimBillId" />
    <result column="batch_claim_bill_no" jdbcType="VARCHAR" property="batchClaimBillNo" />
    <result column="check_type" jdbcType="TINYINT" property="checkType" />
    <result column="agency_user_id" jdbcType="BIGINT" property="agencyUserId" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="audit_result" jdbcType="TINYINT" property="auditResult" />
    <result column="audit_conclusion" jdbcType="VARCHAR" property="auditConclusion" />
    <result column="assess_conclusion" jdbcType="VARCHAR" property="assessConclusion" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, batch_claim_bill_id, batch_claim_bill_no, check_type, agency_user_id, submit_time, 
    audit_time, auditor, audit_result, audit_conclusion, assess_conclusion, extra_info, 
    is_deleted, creator, modifier, gmt_created, gmt_modified
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.BAssessCheckExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_batch_assess_check
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_batch_assess_check
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.BAssessCheckDO">
    insert into claim_batch_assess_check (id, batch_claim_bill_id, batch_claim_bill_no, 
      check_type, agency_user_id, submit_time, 
      audit_time, auditor, audit_result, 
      audit_conclusion, assess_conclusion, extra_info, 
      is_deleted, creator, modifier, 
      gmt_created, gmt_modified)
    values (#{id,jdbcType=BIGINT}, #{batchClaimBillId,jdbcType=BIGINT}, #{batchClaimBillNo,jdbcType=VARCHAR}, 
      #{checkType,jdbcType=TINYINT}, #{agencyUserId,jdbcType=BIGINT}, #{submitTime,jdbcType=TIMESTAMP}, 
      #{auditTime,jdbcType=TIMESTAMP}, #{auditor,jdbcType=VARCHAR}, #{auditResult,jdbcType=TINYINT}, 
      #{auditConclusion,jdbcType=VARCHAR}, #{assessConclusion,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate())
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.BAssessCheckDO">
    insert into claim_batch_assess_check
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="batchClaimBillId != null">
        batch_claim_bill_id,
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no,
      </if>
      <if test="checkType != null">
        check_type,
      </if>
      <if test="agencyUserId != null">
        agency_user_id,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="auditResult != null">
        audit_result,
      </if>
      <if test="auditConclusion != null">
        audit_conclusion,
      </if>
      <if test="assessConclusion != null">
        assess_conclusion,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillId != null">
        #{batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillNo != null">
        #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="checkType != null">
        #{checkType,jdbcType=TINYINT},
      </if>
      <if test="agencyUserId != null">
        #{agencyUserId,jdbcType=BIGINT},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        #{auditResult,jdbcType=TINYINT},
      </if>
      <if test="auditConclusion != null">
        #{auditConclusion,jdbcType=VARCHAR},
      </if>
      <if test="assessConclusion != null">
        #{assessConclusion,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.BAssessCheckExample" resultType="java.lang.Long">
    select count(*) from claim_batch_assess_check
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_batch_assess_check
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.batchClaimBillId != null">
        batch_claim_bill_id = #{record.batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="record.batchClaimBillNo != null">
        batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.checkType != null">
        check_type = #{record.checkType,jdbcType=TINYINT},
      </if>
      <if test="record.agencyUserId != null">
        agency_user_id = #{record.agencyUserId,jdbcType=BIGINT},
      </if>
      <if test="record.submitTime != null">
        submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.auditTime != null">
        audit_time = #{record.auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.auditor != null">
        auditor = #{record.auditor,jdbcType=VARCHAR},
      </if>
      <if test="record.auditResult != null">
        audit_result = #{record.auditResult,jdbcType=TINYINT},
      </if>
      <if test="record.auditConclusion != null">
        audit_conclusion = #{record.auditConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.assessConclusion != null">
        assess_conclusion = #{record.assessConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_batch_assess_check
    set id = #{record.id,jdbcType=BIGINT},
      batch_claim_bill_id = #{record.batchClaimBillId,jdbcType=BIGINT},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      check_type = #{record.checkType,jdbcType=TINYINT},
      agency_user_id = #{record.agencyUserId,jdbcType=BIGINT},
      submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      audit_time = #{record.auditTime,jdbcType=TIMESTAMP},
      auditor = #{record.auditor,jdbcType=VARCHAR},
      audit_result = #{record.auditResult,jdbcType=TINYINT},
      audit_conclusion = #{record.auditConclusion,jdbcType=VARCHAR},
      assess_conclusion = #{record.assessConclusion,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate()
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.BAssessCheckDO">
    update claim_batch_assess_check
    <set>
      <if test="batchClaimBillId != null">
        batch_claim_bill_id = #{batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="checkType != null">
        check_type = #{checkType,jdbcType=TINYINT},
      </if>
      <if test="agencyUserId != null">
        agency_user_id = #{agencyUserId,jdbcType=BIGINT},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        audit_result = #{auditResult,jdbcType=TINYINT},
      </if>
      <if test="auditConclusion != null">
        audit_conclusion = #{auditConclusion,jdbcType=VARCHAR},
      </if>
      <if test="assessConclusion != null">
        assess_conclusion = #{assessConclusion,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.BAssessCheckDO">
    update claim_batch_assess_check
    set batch_claim_bill_id = #{batchClaimBillId,jdbcType=BIGINT},
      batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      check_type = #{checkType,jdbcType=TINYINT},
      agency_user_id = #{agencyUserId,jdbcType=BIGINT},
      submit_time = #{submitTime,jdbcType=TIMESTAMP},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      auditor = #{auditor,jdbcType=VARCHAR},
      audit_result = #{auditResult,jdbcType=TINYINT},
      audit_conclusion = #{auditConclusion,jdbcType=VARCHAR},
      assess_conclusion = #{assessConclusion,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate()
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_batch_assess_check
    (id, batch_claim_bill_id, batch_claim_bill_no, check_type, agency_user_id, submit_time, 
      audit_time, auditor, audit_result, audit_conclusion, assess_conclusion, extra_info, 
      is_deleted, creator, modifier, gmt_created, gmt_modified)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.batchClaimBillId,jdbcType=BIGINT}, #{item.batchClaimBillNo,jdbcType=VARCHAR}, 
        #{item.checkType,jdbcType=TINYINT}, #{item.agencyUserId,jdbcType=BIGINT}, #{item.submitTime,jdbcType=TIMESTAMP}, 
        #{item.auditTime,jdbcType=TIMESTAMP}, #{item.auditor,jdbcType=VARCHAR}, #{item.auditResult,jdbcType=TINYINT}, 
        #{item.auditConclusion,jdbcType=VARCHAR}, #{item.assessConclusion,jdbcType=VARCHAR}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_batch_assess_check (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'batch_claim_bill_id'.toString() == column.value">
          #{item.batchClaimBillId,jdbcType=BIGINT}
        </if>
        <if test="'batch_claim_bill_no'.toString() == column.value">
          #{item.batchClaimBillNo,jdbcType=VARCHAR}
        </if>
        <if test="'check_type'.toString() == column.value">
          #{item.checkType,jdbcType=TINYINT}
        </if>
        <if test="'agency_user_id'.toString() == column.value">
          #{item.agencyUserId,jdbcType=BIGINT}
        </if>
        <if test="'submit_time'.toString() == column.value">
          #{item.submitTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'audit_time'.toString() == column.value">
          #{item.auditTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'auditor'.toString() == column.value">
          #{item.auditor,jdbcType=VARCHAR}
        </if>
        <if test="'audit_result'.toString() == column.value">
          #{item.auditResult,jdbcType=TINYINT}
        </if>
        <if test="'audit_conclusion'.toString() == column.value">
          #{item.auditConclusion,jdbcType=VARCHAR}
        </if>
        <if test="'assess_conclusion'.toString() == column.value">
          #{item.assessConclusion,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>