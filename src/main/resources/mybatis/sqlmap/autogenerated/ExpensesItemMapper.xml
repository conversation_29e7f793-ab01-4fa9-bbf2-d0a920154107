<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ExpensesItemMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ExpensesItemDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="payment_no" jdbcType="VARCHAR" property="paymentNo" />
    <result column="expenses_type" jdbcType="TINYINT" property="expensesType" />
    <result column="expenses_line" jdbcType="INTEGER" property="expensesLine" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="cost_no" jdbcType="VARCHAR" property="costNo" />
    <result column="expenses_amount" jdbcType="DECIMAL" property="expensesAmount" />
    <result column="expenses_remark" jdbcType="VARCHAR" property="expensesRemark" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, payment_no, expenses_type, expenses_line, status, report_no, policy_no, cost_no, 
    expenses_amount, expenses_remark, remark, extra_info, creator, modifier, gmt_created, 
    gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ExpensesItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from expenses_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from expenses_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ExpensesItemDO">
    insert into expenses_item (id, payment_no, expenses_type, 
      expenses_line, status, report_no, 
      policy_no, cost_no, expenses_amount, 
      expenses_remark, remark, extra_info, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{paymentNo,jdbcType=VARCHAR}, #{expensesType,jdbcType=TINYINT}, 
      #{expensesLine,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{reportNo,jdbcType=VARCHAR}, 
      #{policyNo,jdbcType=VARCHAR}, #{costNo,jdbcType=VARCHAR}, #{expensesAmount,jdbcType=DECIMAL}, 
      #{expensesRemark,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ExpensesItemDO">
    insert into expenses_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="paymentNo != null">
        payment_no,
      </if>
      <if test="expensesType != null">
        expenses_type,
      </if>
      <if test="expensesLine != null">
        expenses_line,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="costNo != null">
        cost_no,
      </if>
      <if test="expensesAmount != null">
        expenses_amount,
      </if>
      <if test="expensesRemark != null">
        expenses_remark,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="paymentNo != null">
        #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="expensesType != null">
        #{expensesType,jdbcType=TINYINT},
      </if>
      <if test="expensesLine != null">
        #{expensesLine,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="costNo != null">
        #{costNo,jdbcType=VARCHAR},
      </if>
      <if test="expensesAmount != null">
        #{expensesAmount,jdbcType=DECIMAL},
      </if>
      <if test="expensesRemark != null">
        #{expensesRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ExpensesItemExample" resultType="java.lang.Long">
    select count(*) from expenses_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update expenses_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.paymentNo != null">
        payment_no = #{record.paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="record.expensesType != null">
        expenses_type = #{record.expensesType,jdbcType=TINYINT},
      </if>
      <if test="record.expensesLine != null">
        expenses_line = #{record.expensesLine,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.costNo != null">
        cost_no = #{record.costNo,jdbcType=VARCHAR},
      </if>
      <if test="record.expensesAmount != null">
        expenses_amount = #{record.expensesAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.expensesRemark != null">
        expenses_remark = #{record.expensesRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update expenses_item
    set id = #{record.id,jdbcType=BIGINT},
      payment_no = #{record.paymentNo,jdbcType=VARCHAR},
      expenses_type = #{record.expensesType,jdbcType=TINYINT},
      expenses_line = #{record.expensesLine,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      cost_no = #{record.costNo,jdbcType=VARCHAR},
      expenses_amount = #{record.expensesAmount,jdbcType=DECIMAL},
      expenses_remark = #{record.expensesRemark,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ExpensesItemDO">
    update expenses_item
    <set>
      <if test="paymentNo != null">
        payment_no = #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="expensesType != null">
        expenses_type = #{expensesType,jdbcType=TINYINT},
      </if>
      <if test="expensesLine != null">
        expenses_line = #{expensesLine,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="costNo != null">
        cost_no = #{costNo,jdbcType=VARCHAR},
      </if>
      <if test="expensesAmount != null">
        expenses_amount = #{expensesAmount,jdbcType=DECIMAL},
      </if>
      <if test="expensesRemark != null">
        expenses_remark = #{expensesRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ExpensesItemDO">
    update expenses_item
    set payment_no = #{paymentNo,jdbcType=VARCHAR},
      expenses_type = #{expensesType,jdbcType=TINYINT},
      expenses_line = #{expensesLine,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      cost_no = #{costNo,jdbcType=VARCHAR},
      expenses_amount = #{expensesAmount,jdbcType=DECIMAL},
      expenses_remark = #{expensesRemark,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into expenses_item
    (id, payment_no, expenses_type, expenses_line, status, report_no, policy_no, cost_no, 
      expenses_amount, expenses_remark, remark, extra_info, creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.paymentNo,jdbcType=VARCHAR}, #{item.expensesType,jdbcType=TINYINT}, 
        #{item.expensesLine,jdbcType=INTEGER}, #{item.status,jdbcType=TINYINT}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.policyNo,jdbcType=VARCHAR}, #{item.costNo,jdbcType=VARCHAR}, #{item.expensesAmount,jdbcType=DECIMAL}, 
        #{item.expensesRemark,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into expenses_item (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'payment_no'.toString() == column.value">
          #{item.paymentNo,jdbcType=VARCHAR}
        </if>
        <if test="'expenses_type'.toString() == column.value">
          #{item.expensesType,jdbcType=TINYINT}
        </if>
        <if test="'expenses_line'.toString() == column.value">
          #{item.expensesLine,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'cost_no'.toString() == column.value">
          #{item.costNo,jdbcType=VARCHAR}
        </if>
        <if test="'expenses_amount'.toString() == column.value">
          #{item.expensesAmount,jdbcType=DECIMAL}
        </if>
        <if test="'expenses_remark'.toString() == column.value">
          #{item.expensesRemark,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>