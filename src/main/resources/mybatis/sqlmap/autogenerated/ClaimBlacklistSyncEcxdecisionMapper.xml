<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimBlacklistSyncEcxdecisionMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimBlacklistSyncEcxdecisionDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="list_group_id" jdbcType="BIGINT" property="listGroupId" />
    <result column="list_status" jdbcType="TINYINT" property="listStatus" />
    <result column="list_level" jdbcType="VARCHAR" property="listLevel" />
    <result column="list_start_time" jdbcType="TIMESTAMP" property="listStartTime" />
    <result column="list_end_time" jdbcType="TIMESTAMP" property="listEndTime" />
    <result column="id_name" jdbcType="VARCHAR" property="idName" />
    <result column="id_number" jdbcType="VARCHAR" property="idNumber" />
    <result column="list_obj_type" jdbcType="TINYINT" property="listObjType" />
    <result column="list_value" jdbcType="VARCHAR" property="listValue" />
    <result column="mobile_phone" jdbcType="VARCHAR" property="mobilePhone" />
    <result column="has_credit" jdbcType="CHAR" property="hasCredit" />
    <result column="credit" jdbcType="VARCHAR" property="credit" />
    <result column="is_other_type" jdbcType="CHAR" property="isOtherType" />
    <result column="other_content_type" jdbcType="VARCHAR" property="otherContentType" />
    <result column="other_content" jdbcType="VARCHAR" property="otherContent" />
    <result column="reason_code" jdbcType="VARCHAR" property="reasonCode" />
    <result column="is_enable" jdbcType="TINYINT" property="isEnable" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, list_group_id, list_status, list_level, list_start_time, list_end_time, id_name, 
    id_number, list_obj_type, list_value, mobile_phone, has_credit, credit, is_other_type, 
    other_content_type, other_content, reason_code, is_enable, is_deleted, gmt_modified, 
    gmt_created, remark, extra_info, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBlacklistSyncEcxdecisionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_blacklist_sync_ecxdecision
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_blacklist_sync_ecxdecision
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimBlacklistSyncEcxdecisionDO">
    insert into claim_blacklist_sync_ecxdecision (id, list_group_id, list_status, 
      list_level, list_start_time, list_end_time, 
      id_name, id_number, list_obj_type, 
      list_value, mobile_phone, has_credit, 
      credit, is_other_type, other_content_type, 
      other_content, reason_code, is_enable, 
      is_deleted, gmt_modified, gmt_created, 
      remark, extra_info, creator, 
      modifier)
    values (#{id,jdbcType=BIGINT}, #{listGroupId,jdbcType=BIGINT}, #{listStatus,jdbcType=TINYINT}, 
      #{listLevel,jdbcType=VARCHAR}, #{listStartTime,jdbcType=TIMESTAMP}, #{listEndTime,jdbcType=TIMESTAMP}, 
      #{idName,jdbcType=VARCHAR}, #{idNumber,jdbcType=VARCHAR}, #{listObjType,jdbcType=TINYINT}, 
      #{listValue,jdbcType=VARCHAR}, #{mobilePhone,jdbcType=VARCHAR}, #{hasCredit,jdbcType=CHAR}, 
      #{credit,jdbcType=VARCHAR}, #{isOtherType,jdbcType=CHAR}, #{otherContentType,jdbcType=VARCHAR}, 
      #{otherContent,jdbcType=VARCHAR}, #{reasonCode,jdbcType=VARCHAR}, #{isEnable,jdbcType=TINYINT}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{remark,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBlacklistSyncEcxdecisionDO">
    insert into claim_blacklist_sync_ecxdecision
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="listGroupId != null">
        list_group_id,
      </if>
      <if test="listStatus != null">
        list_status,
      </if>
      <if test="listLevel != null">
        list_level,
      </if>
      <if test="listStartTime != null">
        list_start_time,
      </if>
      <if test="listEndTime != null">
        list_end_time,
      </if>
      <if test="idName != null">
        id_name,
      </if>
      <if test="idNumber != null">
        id_number,
      </if>
      <if test="listObjType != null">
        list_obj_type,
      </if>
      <if test="listValue != null">
        list_value,
      </if>
      <if test="mobilePhone != null">
        mobile_phone,
      </if>
      <if test="hasCredit != null">
        has_credit,
      </if>
      <if test="credit != null">
        credit,
      </if>
      <if test="isOtherType != null">
        is_other_type,
      </if>
      <if test="otherContentType != null">
        other_content_type,
      </if>
      <if test="otherContent != null">
        other_content,
      </if>
      <if test="reasonCode != null">
        reason_code,
      </if>
      <if test="isEnable != null">
        is_enable,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="listGroupId != null">
        #{listGroupId,jdbcType=BIGINT},
      </if>
      <if test="listStatus != null">
        #{listStatus,jdbcType=TINYINT},
      </if>
      <if test="listLevel != null">
        #{listLevel,jdbcType=VARCHAR},
      </if>
      <if test="listStartTime != null">
        #{listStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="listEndTime != null">
        #{listEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="idName != null">
        #{idName,jdbcType=VARCHAR},
      </if>
      <if test="idNumber != null">
        #{idNumber,jdbcType=VARCHAR},
      </if>
      <if test="listObjType != null">
        #{listObjType,jdbcType=TINYINT},
      </if>
      <if test="listValue != null">
        #{listValue,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null">
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="hasCredit != null">
        #{hasCredit,jdbcType=CHAR},
      </if>
      <if test="credit != null">
        #{credit,jdbcType=VARCHAR},
      </if>
      <if test="isOtherType != null">
        #{isOtherType,jdbcType=CHAR},
      </if>
      <if test="otherContentType != null">
        #{otherContentType,jdbcType=VARCHAR},
      </if>
      <if test="otherContent != null">
        #{otherContent,jdbcType=VARCHAR},
      </if>
      <if test="reasonCode != null">
        #{reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBlacklistSyncEcxdecisionExample" resultType="java.lang.Long">
    select count(*) from claim_blacklist_sync_ecxdecision
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_blacklist_sync_ecxdecision
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.listGroupId != null">
        list_group_id = #{record.listGroupId,jdbcType=BIGINT},
      </if>
      <if test="record.listStatus != null">
        list_status = #{record.listStatus,jdbcType=TINYINT},
      </if>
      <if test="record.listLevel != null">
        list_level = #{record.listLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.listStartTime != null">
        list_start_time = #{record.listStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.listEndTime != null">
        list_end_time = #{record.listEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.idName != null">
        id_name = #{record.idName,jdbcType=VARCHAR},
      </if>
      <if test="record.idNumber != null">
        id_number = #{record.idNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.listObjType != null">
        list_obj_type = #{record.listObjType,jdbcType=TINYINT},
      </if>
      <if test="record.listValue != null">
        list_value = #{record.listValue,jdbcType=VARCHAR},
      </if>
      <if test="record.mobilePhone != null">
        mobile_phone = #{record.mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.hasCredit != null">
        has_credit = #{record.hasCredit,jdbcType=CHAR},
      </if>
      <if test="record.credit != null">
        credit = #{record.credit,jdbcType=VARCHAR},
      </if>
      <if test="record.isOtherType != null">
        is_other_type = #{record.isOtherType,jdbcType=CHAR},
      </if>
      <if test="record.otherContentType != null">
        other_content_type = #{record.otherContentType,jdbcType=VARCHAR},
      </if>
      <if test="record.otherContent != null">
        other_content = #{record.otherContent,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonCode != null">
        reason_code = #{record.reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="record.isEnable != null">
        is_enable = #{record.isEnable,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_blacklist_sync_ecxdecision
    set id = #{record.id,jdbcType=BIGINT},
      list_group_id = #{record.listGroupId,jdbcType=BIGINT},
      list_status = #{record.listStatus,jdbcType=TINYINT},
      list_level = #{record.listLevel,jdbcType=VARCHAR},
      list_start_time = #{record.listStartTime,jdbcType=TIMESTAMP},
      list_end_time = #{record.listEndTime,jdbcType=TIMESTAMP},
      id_name = #{record.idName,jdbcType=VARCHAR},
      id_number = #{record.idNumber,jdbcType=VARCHAR},
      list_obj_type = #{record.listObjType,jdbcType=TINYINT},
      list_value = #{record.listValue,jdbcType=VARCHAR},
      mobile_phone = #{record.mobilePhone,jdbcType=VARCHAR},
      has_credit = #{record.hasCredit,jdbcType=CHAR},
      credit = #{record.credit,jdbcType=VARCHAR},
      is_other_type = #{record.isOtherType,jdbcType=CHAR},
      other_content_type = #{record.otherContentType,jdbcType=VARCHAR},
      other_content = #{record.otherContent,jdbcType=VARCHAR},
      reason_code = #{record.reasonCode,jdbcType=VARCHAR},
      is_enable = #{record.isEnable,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      gmt_modified = sysdate(),
    
      remark = #{record.remark,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBlacklistSyncEcxdecisionDO">
    update claim_blacklist_sync_ecxdecision
    <set>
      <if test="listGroupId != null">
        list_group_id = #{listGroupId,jdbcType=BIGINT},
      </if>
      <if test="listStatus != null">
        list_status = #{listStatus,jdbcType=TINYINT},
      </if>
      <if test="listLevel != null">
        list_level = #{listLevel,jdbcType=VARCHAR},
      </if>
      <if test="listStartTime != null">
        list_start_time = #{listStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="listEndTime != null">
        list_end_time = #{listEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="idName != null">
        id_name = #{idName,jdbcType=VARCHAR},
      </if>
      <if test="idNumber != null">
        id_number = #{idNumber,jdbcType=VARCHAR},
      </if>
      <if test="listObjType != null">
        list_obj_type = #{listObjType,jdbcType=TINYINT},
      </if>
      <if test="listValue != null">
        list_value = #{listValue,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null">
        mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="hasCredit != null">
        has_credit = #{hasCredit,jdbcType=CHAR},
      </if>
      <if test="credit != null">
        credit = #{credit,jdbcType=VARCHAR},
      </if>
      <if test="isOtherType != null">
        is_other_type = #{isOtherType,jdbcType=CHAR},
      </if>
      <if test="otherContentType != null">
        other_content_type = #{otherContentType,jdbcType=VARCHAR},
      </if>
      <if test="otherContent != null">
        other_content = #{otherContent,jdbcType=VARCHAR},
      </if>
      <if test="reasonCode != null">
        reason_code = #{reasonCode,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        is_enable = #{isEnable,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimBlacklistSyncEcxdecisionDO">
    update claim_blacklist_sync_ecxdecision
    set list_group_id = #{listGroupId,jdbcType=BIGINT},
      list_status = #{listStatus,jdbcType=TINYINT},
      list_level = #{listLevel,jdbcType=VARCHAR},
      list_start_time = #{listStartTime,jdbcType=TIMESTAMP},
      list_end_time = #{listEndTime,jdbcType=TIMESTAMP},
      id_name = #{idName,jdbcType=VARCHAR},
      id_number = #{idNumber,jdbcType=VARCHAR},
      list_obj_type = #{listObjType,jdbcType=TINYINT},
      list_value = #{listValue,jdbcType=VARCHAR},
      mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
      has_credit = #{hasCredit,jdbcType=CHAR},
      credit = #{credit,jdbcType=VARCHAR},
      is_other_type = #{isOtherType,jdbcType=CHAR},
      other_content_type = #{otherContentType,jdbcType=VARCHAR},
      other_content = #{otherContent,jdbcType=VARCHAR},
      reason_code = #{reasonCode,jdbcType=VARCHAR},
      is_enable = #{isEnable,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      gmt_modified = sysdate(),
    
      remark = #{remark,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_blacklist_sync_ecxdecision
    (id, list_group_id, list_status, list_level, list_start_time, list_end_time, id_name, 
      id_number, list_obj_type, list_value, mobile_phone, has_credit, credit, is_other_type, 
      other_content_type, other_content, reason_code, is_enable, is_deleted, gmt_modified, 
      gmt_created, remark, extra_info, creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.listGroupId,jdbcType=BIGINT}, #{item.listStatus,jdbcType=TINYINT}, 
        #{item.listLevel,jdbcType=VARCHAR}, #{item.listStartTime,jdbcType=TIMESTAMP}, #{item.listEndTime,jdbcType=TIMESTAMP}, 
        #{item.idName,jdbcType=VARCHAR}, #{item.idNumber,jdbcType=VARCHAR}, #{item.listObjType,jdbcType=TINYINT}, 
        #{item.listValue,jdbcType=VARCHAR}, #{item.mobilePhone,jdbcType=VARCHAR}, #{item.hasCredit,jdbcType=CHAR}, 
        #{item.credit,jdbcType=VARCHAR}, #{item.isOtherType,jdbcType=CHAR}, #{item.otherContentType,jdbcType=VARCHAR}, 
        #{item.otherContent,jdbcType=VARCHAR}, #{item.reasonCode,jdbcType=VARCHAR}, #{item.isEnable,jdbcType=TINYINT}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_blacklist_sync_ecxdecision (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'list_group_id'.toString() == column.value">
          #{item.listGroupId,jdbcType=BIGINT}
        </if>
        <if test="'list_status'.toString() == column.value">
          #{item.listStatus,jdbcType=TINYINT}
        </if>
        <if test="'list_level'.toString() == column.value">
          #{item.listLevel,jdbcType=VARCHAR}
        </if>
        <if test="'list_start_time'.toString() == column.value">
          #{item.listStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'list_end_time'.toString() == column.value">
          #{item.listEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'id_name'.toString() == column.value">
          #{item.idName,jdbcType=VARCHAR}
        </if>
        <if test="'id_number'.toString() == column.value">
          #{item.idNumber,jdbcType=VARCHAR}
        </if>
        <if test="'list_obj_type'.toString() == column.value">
          #{item.listObjType,jdbcType=TINYINT}
        </if>
        <if test="'list_value'.toString() == column.value">
          #{item.listValue,jdbcType=VARCHAR}
        </if>
        <if test="'mobile_phone'.toString() == column.value">
          #{item.mobilePhone,jdbcType=VARCHAR}
        </if>
        <if test="'has_credit'.toString() == column.value">
          #{item.hasCredit,jdbcType=CHAR}
        </if>
        <if test="'credit'.toString() == column.value">
          #{item.credit,jdbcType=VARCHAR}
        </if>
        <if test="'is_other_type'.toString() == column.value">
          #{item.isOtherType,jdbcType=CHAR}
        </if>
        <if test="'other_content_type'.toString() == column.value">
          #{item.otherContentType,jdbcType=VARCHAR}
        </if>
        <if test="'other_content'.toString() == column.value">
          #{item.otherContent,jdbcType=VARCHAR}
        </if>
        <if test="'reason_code'.toString() == column.value">
          #{item.reasonCode,jdbcType=VARCHAR}
        </if>
        <if test="'is_enable'.toString() == column.value">
          #{item.isEnable,jdbcType=TINYINT}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>