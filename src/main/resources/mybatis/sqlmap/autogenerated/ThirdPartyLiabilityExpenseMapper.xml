<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ThirdPartyLiabilityExpenseMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityExpenseDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="insurance_info_id" jdbcType="BIGINT" property="insuranceInfoId" />
    <result column="claim_amount" jdbcType="VARCHAR" property="claimAmount" />
    <result column="deduction_amount" jdbcType="VARCHAR" property="deductionAmount" />
    <result column="loss_amount" jdbcType="VARCHAR" property="lossAmount" />
    <result column="paid_amount" jdbcType="VARCHAR" property="paidAmount" />
    <result column="paid_ratio" jdbcType="VARCHAR" property="paidRatio" />
    <result column="fee_type" jdbcType="VARCHAR" property="feeType" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, insurance_info_id, claim_amount, deduction_amount, loss_amount, paid_amount, 
    paid_ratio, fee_type, report_no, remark, creator, modifier, gmt_created, gmt_modified, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityExpenseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_claim_insurance_info_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_claim_insurance_info_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityExpenseDO">
    insert into cargo_claim_insurance_info_detail (id, insurance_info_id, claim_amount, 
      deduction_amount, loss_amount, paid_amount, 
      paid_ratio, fee_type, report_no, 
      remark, creator, modifier, 
      gmt_created, gmt_modified, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{insuranceInfoId,jdbcType=BIGINT}, #{claimAmount,jdbcType=VARCHAR}, 
      #{deductionAmount,jdbcType=VARCHAR}, #{lossAmount,jdbcType=VARCHAR}, #{paidAmount,jdbcType=VARCHAR}, 
      #{paidRatio,jdbcType=VARCHAR}, #{feeType,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityExpenseDO">
    insert into cargo_claim_insurance_info_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="insuranceInfoId != null">
        insurance_info_id,
      </if>
      <if test="claimAmount != null">
        claim_amount,
      </if>
      <if test="deductionAmount != null">
        deduction_amount,
      </if>
      <if test="lossAmount != null">
        loss_amount,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="paidRatio != null">
        paid_ratio,
      </if>
      <if test="feeType != null">
        fee_type,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="insuranceInfoId != null">
        #{insuranceInfoId,jdbcType=BIGINT},
      </if>
      <if test="claimAmount != null">
        #{claimAmount,jdbcType=VARCHAR},
      </if>
      <if test="deductionAmount != null">
        #{deductionAmount,jdbcType=VARCHAR},
      </if>
      <if test="lossAmount != null">
        #{lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="paidRatio != null">
        #{paidRatio,jdbcType=VARCHAR},
      </if>
      <if test="feeType != null">
        #{feeType,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityExpenseExample" resultType="java.lang.Long">
    select count(*) from cargo_claim_insurance_info_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_claim_insurance_info_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.insuranceInfoId != null">
        insurance_info_id = #{record.insuranceInfoId,jdbcType=BIGINT},
      </if>
      <if test="record.claimAmount != null">
        claim_amount = #{record.claimAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.deductionAmount != null">
        deduction_amount = #{record.deductionAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.lossAmount != null">
        loss_amount = #{record.lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.paidRatio != null">
        paid_ratio = #{record.paidRatio,jdbcType=VARCHAR},
      </if>
      <if test="record.feeType != null">
        fee_type = #{record.feeType,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_claim_insurance_info_detail
    set id = #{record.id,jdbcType=BIGINT},
      insurance_info_id = #{record.insuranceInfoId,jdbcType=BIGINT},
      claim_amount = #{record.claimAmount,jdbcType=VARCHAR},
      deduction_amount = #{record.deductionAmount,jdbcType=VARCHAR},
      loss_amount = #{record.lossAmount,jdbcType=VARCHAR},
      paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      paid_ratio = #{record.paidRatio,jdbcType=VARCHAR},
      fee_type = #{record.feeType,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityExpenseDO">
    update cargo_claim_insurance_info_detail
    <set>
      <if test="insuranceInfoId != null">
        insurance_info_id = #{insuranceInfoId,jdbcType=BIGINT},
      </if>
      <if test="claimAmount != null">
        claim_amount = #{claimAmount,jdbcType=VARCHAR},
      </if>
      <if test="deductionAmount != null">
        deduction_amount = #{deductionAmount,jdbcType=VARCHAR},
      </if>
      <if test="lossAmount != null">
        loss_amount = #{lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="paidRatio != null">
        paid_ratio = #{paidRatio,jdbcType=VARCHAR},
      </if>
      <if test="feeType != null">
        fee_type = #{feeType,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ThirdPartyLiabilityExpenseDO">
    update cargo_claim_insurance_info_detail
    set insurance_info_id = #{insuranceInfoId,jdbcType=BIGINT},
      claim_amount = #{claimAmount,jdbcType=VARCHAR},
      deduction_amount = #{deductionAmount,jdbcType=VARCHAR},
      loss_amount = #{lossAmount,jdbcType=VARCHAR},
      paid_amount = #{paidAmount,jdbcType=VARCHAR},
      paid_ratio = #{paidRatio,jdbcType=VARCHAR},
      fee_type = #{feeType,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_claim_insurance_info_detail
    (id, insurance_info_id, claim_amount, deduction_amount, loss_amount, paid_amount, 
      paid_ratio, fee_type, report_no, remark, creator, modifier, gmt_created, gmt_modified, 
      is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.insuranceInfoId,jdbcType=BIGINT}, #{item.claimAmount,jdbcType=VARCHAR}, 
        #{item.deductionAmount,jdbcType=VARCHAR}, #{item.lossAmount,jdbcType=VARCHAR}, 
        #{item.paidAmount,jdbcType=VARCHAR}, #{item.paidRatio,jdbcType=VARCHAR}, #{item.feeType,jdbcType=VARCHAR}, 
        #{item.reportNo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_claim_insurance_info_detail (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'insurance_info_id'.toString() == column.value">
          #{item.insuranceInfoId,jdbcType=BIGINT}
        </if>
        <if test="'claim_amount'.toString() == column.value">
          #{item.claimAmount,jdbcType=VARCHAR}
        </if>
        <if test="'deduction_amount'.toString() == column.value">
          #{item.deductionAmount,jdbcType=VARCHAR}
        </if>
        <if test="'loss_amount'.toString() == column.value">
          #{item.lossAmount,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount'.toString() == column.value">
          #{item.paidAmount,jdbcType=VARCHAR}
        </if>
        <if test="'paid_ratio'.toString() == column.value">
          #{item.paidRatio,jdbcType=VARCHAR}
        </if>
        <if test="'fee_type'.toString() == column.value">
          #{item.feeType,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>