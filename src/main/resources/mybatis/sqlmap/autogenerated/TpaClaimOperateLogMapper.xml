<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.TpaClaimOperateLogMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.TpaClaimOperateLogDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="operator_account" jdbcType="VARCHAR" property="operatorAccount" />
    <result column="operator_company_id" jdbcType="BIGINT" property="operatorCompanyId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="operate_path" jdbcType="VARCHAR" property="operatePath" />
    <result column="operate_detail" jdbcType="VARCHAR" property="operateDetail" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, operator_account, operator_company_id, operator_name, operate_path, 
    operate_detail, operate_time, extra_info, remark, is_deleted, gmt_created, gmt_modified, 
    creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimOperateLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpa_claim_operate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tpa_claim_operate_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimOperateLogDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpa_claim_operate_log (report_no, operator_account, operator_company_id, 
      operator_name, operate_path, operate_detail, 
      operate_time, extra_info, remark, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier)
    values (#{reportNo,jdbcType=VARCHAR}, #{operatorAccount,jdbcType=VARCHAR}, #{operatorCompanyId,jdbcType=BIGINT}, 
      #{operatorName,jdbcType=VARCHAR}, #{operatePath,jdbcType=VARCHAR}, #{operateDetail,jdbcType=VARCHAR}, 
      #{operateTime,jdbcType=TIMESTAMP}, #{extraInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimOperateLogDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpa_claim_operate_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="operatorAccount != null">
        operator_account,
      </if>
      <if test="operatorCompanyId != null">
        operator_company_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="operatePath != null">
        operate_path,
      </if>
      <if test="operateDetail != null">
        operate_detail,
      </if>
      <if test="operateTime != null">
        operate_time,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="operatorAccount != null">
        #{operatorAccount,jdbcType=VARCHAR},
      </if>
      <if test="operatorCompanyId != null">
        #{operatorCompanyId,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatePath != null">
        #{operatePath,jdbcType=VARCHAR},
      </if>
      <if test="operateDetail != null">
        #{operateDetail,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimOperateLogExample" resultType="java.lang.Long">
    select count(*) from tpa_claim_operate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tpa_claim_operate_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorAccount != null">
        operator_account = #{record.operatorAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorCompanyId != null">
        operator_company_id = #{record.operatorCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.operatorName != null">
        operator_name = #{record.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.operatePath != null">
        operate_path = #{record.operatePath,jdbcType=VARCHAR},
      </if>
      <if test="record.operateDetail != null">
        operate_detail = #{record.operateDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.operateTime != null">
        operate_time = #{record.operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tpa_claim_operate_log
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      operator_account = #{record.operatorAccount,jdbcType=VARCHAR},
      operator_company_id = #{record.operatorCompanyId,jdbcType=BIGINT},
      operator_name = #{record.operatorName,jdbcType=VARCHAR},
      operate_path = #{record.operatePath,jdbcType=VARCHAR},
      operate_detail = #{record.operateDetail,jdbcType=VARCHAR},
      operate_time = #{record.operateTime,jdbcType=TIMESTAMP},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimOperateLogDO">
    update tpa_claim_operate_log
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="operatorAccount != null">
        operator_account = #{operatorAccount,jdbcType=VARCHAR},
      </if>
      <if test="operatorCompanyId != null">
        operator_company_id = #{operatorCompanyId,jdbcType=BIGINT},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatePath != null">
        operate_path = #{operatePath,jdbcType=VARCHAR},
      </if>
      <if test="operateDetail != null">
        operate_detail = #{operateDetail,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        operate_time = #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimOperateLogDO">
    update tpa_claim_operate_log
    set report_no = #{reportNo,jdbcType=VARCHAR},
      operator_account = #{operatorAccount,jdbcType=VARCHAR},
      operator_company_id = #{operatorCompanyId,jdbcType=BIGINT},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      operate_path = #{operatePath,jdbcType=VARCHAR},
      operate_detail = #{operateDetail,jdbcType=VARCHAR},
      operate_time = #{operateTime,jdbcType=TIMESTAMP},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into tpa_claim_operate_log
    (report_no, operator_account, operator_company_id, operator_name, operate_path, operate_detail, 
      operate_time, extra_info, remark, is_deleted, gmt_created, gmt_modified, creator, 
      modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reportNo,jdbcType=VARCHAR}, #{item.operatorAccount,jdbcType=VARCHAR}, #{item.operatorCompanyId,jdbcType=BIGINT}, 
        #{item.operatorName,jdbcType=VARCHAR}, #{item.operatePath,jdbcType=VARCHAR}, #{item.operateDetail,jdbcType=VARCHAR}, 
        #{item.operateTime,jdbcType=TIMESTAMP}, #{item.extraInfo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into tpa_claim_operate_log (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'operator_account'.toString() == column.value">
          #{item.operatorAccount,jdbcType=VARCHAR}
        </if>
        <if test="'operator_company_id'.toString() == column.value">
          #{item.operatorCompanyId,jdbcType=BIGINT}
        </if>
        <if test="'operator_name'.toString() == column.value">
          #{item.operatorName,jdbcType=VARCHAR}
        </if>
        <if test="'operate_path'.toString() == column.value">
          #{item.operatePath,jdbcType=VARCHAR}
        </if>
        <if test="'operate_detail'.toString() == column.value">
          #{item.operateDetail,jdbcType=VARCHAR}
        </if>
        <if test="'operate_time'.toString() == column.value">
          #{item.operateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>