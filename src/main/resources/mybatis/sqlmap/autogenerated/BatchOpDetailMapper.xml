<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.BatchOpDetailMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.BatchOpDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="success_count" jdbcType="INTEGER" property="successCount" />
    <result column="fail_count" jdbcType="INTEGER" property="failCount" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, file_name, total_count, success_count, fail_count, status, extra_info, file_type, 
    creator, gmt_created, modifier, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.BatchOpDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tc_batch_op_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tc_batch_op_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.BatchOpDetailDO">
    insert into tc_batch_op_detail (id, file_name, total_count, 
      success_count, fail_count, status, 
      extra_info, file_type, creator, 
      gmt_created, modifier, gmt_modified, 
      is_deleted)
    values (#{id,jdbcType=BIGINT}, #{fileName,jdbcType=VARCHAR}, #{totalCount,jdbcType=INTEGER}, 
      #{successCount,jdbcType=INTEGER}, #{failCount,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, 
      #{extraInfo,jdbcType=VARCHAR}, #{fileType,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      sysdate(), #{modifier,jdbcType=VARCHAR}, sysdate(), 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.BatchOpDetailDO">
    insert into tc_batch_op_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="totalCount != null">
        total_count,
      </if>
      <if test="successCount != null">
        success_count,
      </if>
      <if test="failCount != null">
        fail_count,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="totalCount != null">
        #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="successCount != null">
        #{successCount,jdbcType=INTEGER},
      </if>
      <if test="failCount != null">
        #{failCount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.BatchOpDetailExample" resultType="java.lang.Long">
    select count(*) from tc_batch_op_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tc_batch_op_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.totalCount != null">
        total_count = #{record.totalCount,jdbcType=INTEGER},
      </if>
      <if test="record.successCount != null">
        success_count = #{record.successCount,jdbcType=INTEGER},
      </if>
      <if test="record.failCount != null">
        fail_count = #{record.failCount,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.fileType != null">
        file_type = #{record.fileType,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tc_batch_op_detail
    set id = #{record.id,jdbcType=BIGINT},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      total_count = #{record.totalCount,jdbcType=INTEGER},
      success_count = #{record.successCount,jdbcType=INTEGER},
      fail_count = #{record.failCount,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      file_type = #{record.fileType,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.BatchOpDetailDO">
    update tc_batch_op_detail
    <set>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="totalCount != null">
        total_count = #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="successCount != null">
        success_count = #{successCount,jdbcType=INTEGER},
      </if>
      <if test="failCount != null">
        fail_count = #{failCount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.BatchOpDetailDO">
    update tc_batch_op_detail
    set file_name = #{fileName,jdbcType=VARCHAR},
      total_count = #{totalCount,jdbcType=INTEGER},
      success_count = #{successCount,jdbcType=INTEGER},
      fail_count = #{failCount,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      file_type = #{fileType,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into tc_batch_op_detail
    (id, file_name, total_count, success_count, fail_count, status, extra_info, file_type, 
      creator, gmt_created, modifier, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.fileName,jdbcType=VARCHAR}, #{item.totalCount,jdbcType=INTEGER}, 
        #{item.successCount,jdbcType=INTEGER}, #{item.failCount,jdbcType=INTEGER}, #{item.status,jdbcType=VARCHAR}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.fileType,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into tc_batch_op_detail (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'file_name'.toString() == column.value">
          #{item.fileName,jdbcType=VARCHAR}
        </if>
        <if test="'total_count'.toString() == column.value">
          #{item.totalCount,jdbcType=INTEGER}
        </if>
        <if test="'success_count'.toString() == column.value">
          #{item.successCount,jdbcType=INTEGER}
        </if>
        <if test="'fail_count'.toString() == column.value">
          #{item.failCount,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'file_type'.toString() == column.value">
          #{item.fileType,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>