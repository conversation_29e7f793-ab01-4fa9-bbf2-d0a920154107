<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.DirectSettlementItemMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.DirectSettlementItemDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="no" jdbcType="VARCHAR" property="no" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="count" jdbcType="DECIMAL" property="count" />
    <result column="customer_price" jdbcType="DECIMAL" property="customerPrice" />
    <result column="supplier_price" jdbcType="DECIMAL" property="supplierPrice" />
    <result column="customer_amount" jdbcType="DECIMAL" property="customerAmount" />
    <result column="supplier_amount" jdbcType="DECIMAL" property="supplierAmount" />
    <result column="settlement_count" jdbcType="DECIMAL" property="settlementCount" />
    <result column="customer_settlement_amount" jdbcType="DECIMAL" property="customerSettlementAmount" />
    <result column="supplier_settlement_amount" jdbcType="DECIMAL" property="supplierSettlementAmount" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, no, name, count, customer_price, supplier_price, customer_amount, 
    supplier_amount, settlement_count, customer_settlement_amount, supplier_settlement_amount, 
    creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.DirectSettlementItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pet_direct_settlement_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pet_direct_settlement_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.DirectSettlementItemDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pet_direct_settlement_item (report_no, no, name, 
      count, customer_price, supplier_price, 
      customer_amount, supplier_amount, settlement_count, 
      customer_settlement_amount, supplier_settlement_amount, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{reportNo,jdbcType=VARCHAR}, #{no,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{count,jdbcType=DECIMAL}, #{customerPrice,jdbcType=DECIMAL}, #{supplierPrice,jdbcType=DECIMAL}, 
      #{customerAmount,jdbcType=DECIMAL}, #{supplierAmount,jdbcType=DECIMAL}, #{settlementCount,jdbcType=DECIMAL}, 
      #{customerSettlementAmount,jdbcType=DECIMAL}, #{supplierSettlementAmount,jdbcType=DECIMAL}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.DirectSettlementItemDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pet_direct_settlement_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="no != null">
        no,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="count != null">
        count,
      </if>
      <if test="customerPrice != null">
        customer_price,
      </if>
      <if test="supplierPrice != null">
        supplier_price,
      </if>
      <if test="customerAmount != null">
        customer_amount,
      </if>
      <if test="supplierAmount != null">
        supplier_amount,
      </if>
      <if test="settlementCount != null">
        settlement_count,
      </if>
      <if test="customerSettlementAmount != null">
        customer_settlement_amount,
      </if>
      <if test="supplierSettlementAmount != null">
        supplier_settlement_amount,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="no != null">
        #{no,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="count != null">
        #{count,jdbcType=DECIMAL},
      </if>
      <if test="customerPrice != null">
        #{customerPrice,jdbcType=DECIMAL},
      </if>
      <if test="supplierPrice != null">
        #{supplierPrice,jdbcType=DECIMAL},
      </if>
      <if test="customerAmount != null">
        #{customerAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmount != null">
        #{supplierAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementCount != null">
        #{settlementCount,jdbcType=DECIMAL},
      </if>
      <if test="customerSettlementAmount != null">
        #{customerSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierSettlementAmount != null">
        #{supplierSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.DirectSettlementItemExample" resultType="java.lang.Long">
    select count(*) from pet_direct_settlement_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pet_direct_settlement_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.no != null">
        no = #{record.no,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.count != null">
        count = #{record.count,jdbcType=DECIMAL},
      </if>
      <if test="record.customerPrice != null">
        customer_price = #{record.customerPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.supplierPrice != null">
        supplier_price = #{record.supplierPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.customerAmount != null">
        customer_amount = #{record.customerAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.supplierAmount != null">
        supplier_amount = #{record.supplierAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.settlementCount != null">
        settlement_count = #{record.settlementCount,jdbcType=DECIMAL},
      </if>
      <if test="record.customerSettlementAmount != null">
        customer_settlement_amount = #{record.customerSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.supplierSettlementAmount != null">
        supplier_settlement_amount = #{record.supplierSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pet_direct_settlement_item
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      no = #{record.no,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      count = #{record.count,jdbcType=DECIMAL},
      customer_price = #{record.customerPrice,jdbcType=DECIMAL},
      supplier_price = #{record.supplierPrice,jdbcType=DECIMAL},
      customer_amount = #{record.customerAmount,jdbcType=DECIMAL},
      supplier_amount = #{record.supplierAmount,jdbcType=DECIMAL},
      settlement_count = #{record.settlementCount,jdbcType=DECIMAL},
      customer_settlement_amount = #{record.customerSettlementAmount,jdbcType=DECIMAL},
      supplier_settlement_amount = #{record.supplierSettlementAmount,jdbcType=DECIMAL},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.DirectSettlementItemDO">
    update pet_direct_settlement_item
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="no != null">
        no = #{no,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="count != null">
        count = #{count,jdbcType=DECIMAL},
      </if>
      <if test="customerPrice != null">
        customer_price = #{customerPrice,jdbcType=DECIMAL},
      </if>
      <if test="supplierPrice != null">
        supplier_price = #{supplierPrice,jdbcType=DECIMAL},
      </if>
      <if test="customerAmount != null">
        customer_amount = #{customerAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmount != null">
        supplier_amount = #{supplierAmount,jdbcType=DECIMAL},
      </if>
      <if test="settlementCount != null">
        settlement_count = #{settlementCount,jdbcType=DECIMAL},
      </if>
      <if test="customerSettlementAmount != null">
        customer_settlement_amount = #{customerSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierSettlementAmount != null">
        supplier_settlement_amount = #{supplierSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.DirectSettlementItemDO">
    update pet_direct_settlement_item
    set report_no = #{reportNo,jdbcType=VARCHAR},
      no = #{no,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      count = #{count,jdbcType=DECIMAL},
      customer_price = #{customerPrice,jdbcType=DECIMAL},
      supplier_price = #{supplierPrice,jdbcType=DECIMAL},
      customer_amount = #{customerAmount,jdbcType=DECIMAL},
      supplier_amount = #{supplierAmount,jdbcType=DECIMAL},
      settlement_count = #{settlementCount,jdbcType=DECIMAL},
      customer_settlement_amount = #{customerSettlementAmount,jdbcType=DECIMAL},
      supplier_settlement_amount = #{supplierSettlementAmount,jdbcType=DECIMAL},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into pet_direct_settlement_item
    (report_no, no, name, count, customer_price, supplier_price, customer_amount, supplier_amount, 
      settlement_count, customer_settlement_amount, supplier_settlement_amount, creator, 
      modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reportNo,jdbcType=VARCHAR}, #{item.no,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.count,jdbcType=DECIMAL}, #{item.customerPrice,jdbcType=DECIMAL}, #{item.supplierPrice,jdbcType=DECIMAL}, 
        #{item.customerAmount,jdbcType=DECIMAL}, #{item.supplierAmount,jdbcType=DECIMAL}, 
        #{item.settlementCount,jdbcType=DECIMAL}, #{item.customerSettlementAmount,jdbcType=DECIMAL}, 
        #{item.supplierSettlementAmount,jdbcType=DECIMAL}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into pet_direct_settlement_item (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'no'.toString() == column.value">
          #{item.no,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'count'.toString() == column.value">
          #{item.count,jdbcType=DECIMAL}
        </if>
        <if test="'customer_price'.toString() == column.value">
          #{item.customerPrice,jdbcType=DECIMAL}
        </if>
        <if test="'supplier_price'.toString() == column.value">
          #{item.supplierPrice,jdbcType=DECIMAL}
        </if>
        <if test="'customer_amount'.toString() == column.value">
          #{item.customerAmount,jdbcType=DECIMAL}
        </if>
        <if test="'supplier_amount'.toString() == column.value">
          #{item.supplierAmount,jdbcType=DECIMAL}
        </if>
        <if test="'settlement_count'.toString() == column.value">
          #{item.settlementCount,jdbcType=DECIMAL}
        </if>
        <if test="'customer_settlement_amount'.toString() == column.value">
          #{item.customerSettlementAmount,jdbcType=DECIMAL}
        </if>
        <if test="'supplier_settlement_amount'.toString() == column.value">
          #{item.supplierSettlementAmount,jdbcType=DECIMAL}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>