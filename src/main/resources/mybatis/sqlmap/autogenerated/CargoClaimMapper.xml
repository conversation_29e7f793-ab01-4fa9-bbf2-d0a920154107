<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.CargoClaimMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.CargoClaimDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_id" jdbcType="BIGINT" property="policyId" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="loss_cause" jdbcType="VARCHAR" property="lossCause" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="open_user_id" jdbcType="VARCHAR" property="openUserId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="cargo_user_register_id" jdbcType="BIGINT" property="cargoUserRegisterId" />
    <result column="report_amount" jdbcType="VARCHAR" property="reportAmount" />
    <result column="channel_report_no" jdbcType="VARCHAR" property="channelReportNo" />
    <result column="accident_desc" jdbcType="VARCHAR" property="accidentDesc" />
    <result column="account_type" jdbcType="TINYINT" property="accountType" />
    <result column="account_bank" jdbcType="VARCHAR" property="accountBank" />
    <result column="payee_name" jdbcType="VARCHAR" property="payeeName" />
    <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
    <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="report_date" jdbcType="TIMESTAMP" property="reportDate" />
    <result column="accident_date" jdbcType="TIMESTAMP" property="accidentDate" />
    <result column="accident_place" jdbcType="VARCHAR" property="accidentPlace" />
    <result column="claim_currency" jdbcType="VARCHAR" property="claimCurrency" />
    <result column="report_source" jdbcType="TINYINT" property="reportSource" />
    <result column="claimant_name" jdbcType="VARCHAR" property="claimantName" />
    <result column="claimant_phone" jdbcType="VARCHAR" property="claimantPhone" />
    <result column="claimant_email" jdbcType="VARCHAR" property="claimantEmail" />
    <result column="register_date" jdbcType="TIMESTAMP" property="registerDate" />
    <result column="claim_no" jdbcType="VARCHAR" property="claimNo" />
    <result column="is_auto_register" jdbcType="CHAR" property="isAutoRegister" />
    <result column="is_need_assess" jdbcType="CHAR" property="isNeedAssess" />
    <result column="dispatcher_date" jdbcType="TIMESTAMP" property="dispatcherDate" />
    <result column="dispatcher_no" jdbcType="VARCHAR" property="dispatcherNo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="campaign_def_id" jdbcType="BIGINT" property="campaignDefId" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="claim_type" jdbcType="TINYINT" property="claimType" />
    <result column="register_user" jdbcType="VARCHAR" property="registerUser" />
    <result column="agency_user_id" jdbcType="BIGINT" property="agencyUserId" />
    <result column="policy_insurant" jdbcType="VARCHAR" property="policyInsurant" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="insurance_type_code" jdbcType="TINYINT" property="insuranceTypeCode" />
    <result column="campaign_full_name" jdbcType="VARCHAR" property="campaignFullName" />
    <result column="loss_amount" jdbcType="VARCHAR" property="lossAmount" />
    <result column="report_user_name" jdbcType="VARCHAR" property="reportUserName" />
    <result column="reportor_relinsured" jdbcType="VARCHAR" property="reportorRelinsured" />
    <result column="reportor_phone" jdbcType="VARCHAR" property="reportorPhone" />
    <result column="reportor_email" jdbcType="VARCHAR" property="reportorEmail" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
    <result column="audit_date" jdbcType="TIMESTAMP" property="auditDate" />
    <result column="audit_amount" jdbcType="VARCHAR" property="auditAmount" />
    <result column="audit_conclusion" jdbcType="VARCHAR" property="auditConclusion" />
    <result column="trans_account_type" jdbcType="TINYINT" property="transAccountType" />
    <result column="account_bank_area" jdbcType="VARCHAR" property="accountBankArea" />
    <result column="loss_cause_name" jdbcType="VARCHAR" property="lossCauseName" />
    <result column="is_need_notice" jdbcType="VARCHAR" property="isNeedNotice" />
    <result column="is_hang_up" jdbcType="CHAR" property="isHangUp" />
    <result column="insure_place" jdbcType="VARCHAR" property="insurePlace" />
    <result column="is_allow_report_roll_back" jdbcType="VARCHAR" property="isAllowReportRollBack" />
    <result column="is_reopen" jdbcType="CHAR" property="isReopen" />
    <result column="is_closed_msg_informed" jdbcType="CHAR" property="isClosedMsgInformed" />
    <result column="core_claim_status" jdbcType="VARCHAR" property="coreClaimStatus" />
    <result column="core_synctime" jdbcType="TIMESTAMP" property="coreSynctime" />
    <result column="close_date" jdbcType="TIMESTAMP" property="closeDate" />
    <result column="pay_state" jdbcType="TINYINT" property="payState" />
    <result column="payment_date" jdbcType="TIMESTAMP" property="paymentDate" />
    <result column="is_pay_failed" jdbcType="CHAR" property="isPayFailed" />
    <result column="is_pay_wait" jdbcType="CHAR" property="isPayWait" />
    <result column="process_type" jdbcType="TINYINT" property="processType" />
    <result column="claim_report_source" jdbcType="VARCHAR" property="claimReportSource" />
    <result column="is_include_attachment" jdbcType="VARCHAR" property="isIncludeAttachment" />
    <result column="open_policy_no" jdbcType="VARCHAR" property="openPolicyNo" />
    <result column="sync_flag" jdbcType="VARCHAR" property="syncFlag" />
    <result column="sync_date" jdbcType="TIMESTAMP" property="syncDate" />
    <result column="channel_attachment_is_finished" jdbcType="CHAR" property="channelAttachmentIsFinished" />
    <result column="attachment_last_date" jdbcType="TIMESTAMP" property="attachmentLastDate" />
    <result column="is_need_customize" jdbcType="CHAR" property="isNeedCustomize" />
    <result column="is_loss_elec_attachment" jdbcType="CHAR" property="isLossElecAttachment" />
    <result column="loss_attachment_log_id" jdbcType="BIGINT" property="lossAttachmentLogId" />
    <result column="claim_timeliness" jdbcType="INTEGER" property="claimTimeliness" />
    <result column="is_attachment_completed" jdbcType="CHAR" property="isAttachmentCompleted" />
    <result column="is_total_loss_policy" jdbcType="CHAR" property="isTotalLossPolicy" />
    <result column="traffic_insurance_type" jdbcType="VARCHAR" property="trafficInsuranceType" />
    <result column="report_batch_no" jdbcType="VARCHAR" property="reportBatchNo" />
    <result column="is_compensation" jdbcType="CHAR" property="isCompensation" />
    <result column="is_litigate" jdbcType="VARCHAR" property="isLitigate" />
    <result column="complaint_type" jdbcType="VARCHAR" property="complaintType" />
    <result column="is_handle" jdbcType="CHAR" property="isHandle" />
    <result column="is_bank_code" jdbcType="CHAR" property="isBankCode" />
    <result column="claim_case_no" jdbcType="VARCHAR" property="claimCaseNo" />
    <result column="send_material_status" jdbcType="VARCHAR" property="sendMaterialStatus" />
    <result column="is_send_material" jdbcType="VARCHAR" property="isSendMaterial" />
    <result column="is_free_send_material" jdbcType="VARCHAR" property="isFreeSendMaterial" />
    <result column="hy_close_type" jdbcType="VARCHAR" property="hyCloseType" />
    <result column="didi_security_card_status" jdbcType="VARCHAR" property="didiSecurityCardStatus" />
    <result column="cy_loss_type" jdbcType="VARCHAR" property="cyLossType" />
    <result column="cy_push_status" jdbcType="VARCHAR" property="cyPushStatus" />
    <result column="survey_type" jdbcType="VARCHAR" property="surveyType" />
    <result column="dada_accident_type" jdbcType="VARCHAR" property="dadaAccidentType" />
    <result column="accident_reason" jdbcType="VARCHAR" property="accidentReason" />
    <result column="transportation" jdbcType="VARCHAR" property="transportation" />
    <result column="risk_score" jdbcType="VARCHAR" property="riskScore" />
    <result column="yz_risk_score" jdbcType="VARCHAR" property="yzRiskScore" />
    <result column="pre_paid_audit_status" jdbcType="VARCHAR" property="prePaidAuditStatus" />
    <result column="accident_four_level_loss_cause" jdbcType="VARCHAR" property="accidentFourLevelLossCause" />
    <result column="is_offline_compensation" jdbcType="CHAR" property="isOfflineCompensation" />
    <result column="occupation" jdbcType="VARCHAR" property="occupation" />
    <result column="reserve_audit_status" jdbcType="TINYINT" property="reserveAuditStatus" />
    <result column="reserve_auditor" jdbcType="VARCHAR" property="reserveAuditor" />
    <result column="is_health_relation" jdbcType="CHAR" property="isHealthRelation" />
    <result column="roll_back_timeliness" jdbcType="VARCHAR" property="rollBackTimeliness" />
    <result column="roll_back_date" jdbcType="TIMESTAMP" property="rollBackDate" />
    <result column="bound_status" jdbcType="TINYINT" property="boundStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_id, policy_no, loss_cause, extra_info, report_no, open_user_id, channel_id, 
    cargo_user_register_id, report_amount, channel_report_no, accident_desc, account_type, 
    account_bank, payee_name, account_no, attachment_url, is_deleted, gmt_created, gmt_modified, 
    creator, modifier, report_date, accident_date, accident_place, claim_currency, report_source, 
    claimant_name, claimant_phone, claimant_email, register_date, claim_no, is_auto_register, 
    is_need_assess, dispatcher_date, dispatcher_no, remark, campaign_def_id, package_def_id, 
    channel_name, product_name, status, claim_type, register_user, agency_user_id, policy_insurant, 
    auditor, insurance_type_code, campaign_full_name, loss_amount, report_user_name, 
    reportor_relinsured, reportor_phone, reportor_email, hospital_name, audit_date, audit_amount, 
    audit_conclusion, trans_account_type, account_bank_area, loss_cause_name, is_need_notice, 
    is_hang_up, insure_place, is_allow_report_roll_back, is_reopen, is_closed_msg_informed, 
    core_claim_status, core_synctime, close_date, pay_state, payment_date, is_pay_failed, 
    is_pay_wait, process_type, claim_report_source, is_include_attachment, open_policy_no, 
    sync_flag, sync_date, channel_attachment_is_finished, attachment_last_date, is_need_customize, 
    is_loss_elec_attachment, loss_attachment_log_id, claim_timeliness, is_attachment_completed, 
    is_total_loss_policy, traffic_insurance_type, report_batch_no, is_compensation, is_litigate, 
    complaint_type, is_handle, is_bank_code, claim_case_no, send_material_status, is_send_material, 
    is_free_send_material, hy_close_type, didi_security_card_status, cy_loss_type, cy_push_status, 
    survey_type, dada_accident_type, accident_reason, transportation, risk_score, yz_risk_score, 
    pre_paid_audit_status, accident_four_level_loss_cause, is_offline_compensation, occupation, 
    reserve_audit_status, reserve_auditor, is_health_relation, roll_back_timeliness, 
    roll_back_date, bound_status
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.CargoClaimExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_claim
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.CargoClaimDO">
    insert into cargo_claim (id, policy_id, policy_no, 
      loss_cause, extra_info, report_no, 
      open_user_id, channel_id, cargo_user_register_id, 
      report_amount, channel_report_no, accident_desc, 
      account_type, account_bank, payee_name, 
      account_no, attachment_url, is_deleted, 
      gmt_created, gmt_modified, creator, 
      modifier, report_date, accident_date, 
      accident_place, claim_currency, report_source, 
      claimant_name, claimant_phone, claimant_email, 
      register_date, claim_no, is_auto_register, 
      is_need_assess, dispatcher_date, dispatcher_no, 
      remark, campaign_def_id, package_def_id, 
      channel_name, product_name, status, 
      claim_type, register_user, agency_user_id, 
      policy_insurant, auditor, insurance_type_code, 
      campaign_full_name, loss_amount, report_user_name, 
      reportor_relinsured, reportor_phone, reportor_email, 
      hospital_name, audit_date, audit_amount, 
      audit_conclusion, trans_account_type, account_bank_area, 
      loss_cause_name, is_need_notice, is_hang_up, 
      insure_place, is_allow_report_roll_back, is_reopen, 
      is_closed_msg_informed, core_claim_status, core_synctime, 
      close_date, pay_state, payment_date, 
      is_pay_failed, is_pay_wait, process_type, 
      claim_report_source, is_include_attachment, 
      open_policy_no, sync_flag, sync_date, 
      channel_attachment_is_finished, attachment_last_date, 
      is_need_customize, is_loss_elec_attachment, loss_attachment_log_id, 
      claim_timeliness, is_attachment_completed, is_total_loss_policy, 
      traffic_insurance_type, report_batch_no, is_compensation, 
      is_litigate, complaint_type, is_handle, 
      is_bank_code, claim_case_no, send_material_status, 
      is_send_material, is_free_send_material, hy_close_type, 
      didi_security_card_status, cy_loss_type, cy_push_status, 
      survey_type, dada_accident_type, accident_reason, 
      transportation, risk_score, yz_risk_score, 
      pre_paid_audit_status, accident_four_level_loss_cause, 
      is_offline_compensation, occupation, reserve_audit_status, 
      reserve_auditor, is_health_relation, roll_back_timeliness, 
      roll_back_date, bound_status)
    values (#{id,jdbcType=BIGINT}, #{policyId,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, 
      #{lossCause,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{openUserId,jdbcType=VARCHAR}, #{channelId,jdbcType=BIGINT}, #{cargoUserRegisterId,jdbcType=BIGINT}, 
      #{reportAmount,jdbcType=VARCHAR}, #{channelReportNo,jdbcType=VARCHAR}, #{accidentDesc,jdbcType=VARCHAR}, 
      #{accountType,jdbcType=TINYINT}, #{accountBank,jdbcType=VARCHAR}, #{payeeName,jdbcType=VARCHAR}, 
      #{accountNo,jdbcType=VARCHAR}, #{attachmentUrl,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{reportDate,jdbcType=TIMESTAMP}, #{accidentDate,jdbcType=TIMESTAMP}, 
      #{accidentPlace,jdbcType=VARCHAR}, #{claimCurrency,jdbcType=VARCHAR}, #{reportSource,jdbcType=TINYINT}, 
      #{claimantName,jdbcType=VARCHAR}, #{claimantPhone,jdbcType=VARCHAR}, #{claimantEmail,jdbcType=VARCHAR}, 
      #{registerDate,jdbcType=TIMESTAMP}, #{claimNo,jdbcType=VARCHAR}, #{isAutoRegister,jdbcType=CHAR}, 
      #{isNeedAssess,jdbcType=CHAR}, #{dispatcherDate,jdbcType=TIMESTAMP}, #{dispatcherNo,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{campaignDefId,jdbcType=BIGINT}, #{packageDefId,jdbcType=BIGINT}, 
      #{channelName,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{claimType,jdbcType=TINYINT}, #{registerUser,jdbcType=VARCHAR}, #{agencyUserId,jdbcType=BIGINT}, 
      #{policyInsurant,jdbcType=VARCHAR}, #{auditor,jdbcType=VARCHAR}, #{insuranceTypeCode,jdbcType=TINYINT}, 
      #{campaignFullName,jdbcType=VARCHAR}, #{lossAmount,jdbcType=VARCHAR}, #{reportUserName,jdbcType=VARCHAR}, 
      #{reportorRelinsured,jdbcType=VARCHAR}, #{reportorPhone,jdbcType=VARCHAR}, #{reportorEmail,jdbcType=VARCHAR}, 
      #{hospitalName,jdbcType=VARCHAR}, #{auditDate,jdbcType=TIMESTAMP}, #{auditAmount,jdbcType=VARCHAR}, 
      #{auditConclusion,jdbcType=VARCHAR}, #{transAccountType,jdbcType=TINYINT}, #{accountBankArea,jdbcType=VARCHAR}, 
      #{lossCauseName,jdbcType=VARCHAR}, #{isNeedNotice,jdbcType=VARCHAR}, #{isHangUp,jdbcType=CHAR}, 
      #{insurePlace,jdbcType=VARCHAR}, #{isAllowReportRollBack,jdbcType=VARCHAR}, #{isReopen,jdbcType=CHAR}, 
      #{isClosedMsgInformed,jdbcType=CHAR}, #{coreClaimStatus,jdbcType=VARCHAR}, #{coreSynctime,jdbcType=TIMESTAMP}, 
      #{closeDate,jdbcType=TIMESTAMP}, #{payState,jdbcType=TINYINT}, #{paymentDate,jdbcType=TIMESTAMP}, 
      #{isPayFailed,jdbcType=CHAR}, #{isPayWait,jdbcType=CHAR}, #{processType,jdbcType=TINYINT}, 
      #{claimReportSource,jdbcType=VARCHAR}, #{isIncludeAttachment,jdbcType=VARCHAR}, 
      #{openPolicyNo,jdbcType=VARCHAR}, #{syncFlag,jdbcType=VARCHAR}, #{syncDate,jdbcType=TIMESTAMP}, 
      #{channelAttachmentIsFinished,jdbcType=CHAR}, #{attachmentLastDate,jdbcType=TIMESTAMP}, 
      #{isNeedCustomize,jdbcType=CHAR}, #{isLossElecAttachment,jdbcType=CHAR}, #{lossAttachmentLogId,jdbcType=BIGINT}, 
      #{claimTimeliness,jdbcType=INTEGER}, #{isAttachmentCompleted,jdbcType=CHAR}, #{isTotalLossPolicy,jdbcType=CHAR}, 
      #{trafficInsuranceType,jdbcType=VARCHAR}, #{reportBatchNo,jdbcType=VARCHAR}, #{isCompensation,jdbcType=CHAR}, 
      #{isLitigate,jdbcType=VARCHAR}, #{complaintType,jdbcType=VARCHAR}, #{isHandle,jdbcType=CHAR}, 
      #{isBankCode,jdbcType=CHAR}, #{claimCaseNo,jdbcType=VARCHAR}, #{sendMaterialStatus,jdbcType=VARCHAR}, 
      #{isSendMaterial,jdbcType=VARCHAR}, #{isFreeSendMaterial,jdbcType=VARCHAR}, #{hyCloseType,jdbcType=VARCHAR}, 
      #{didiSecurityCardStatus,jdbcType=VARCHAR}, #{cyLossType,jdbcType=VARCHAR}, #{cyPushStatus,jdbcType=VARCHAR}, 
      #{surveyType,jdbcType=VARCHAR}, #{dadaAccidentType,jdbcType=VARCHAR}, #{accidentReason,jdbcType=VARCHAR}, 
      #{transportation,jdbcType=VARCHAR}, #{riskScore,jdbcType=VARCHAR}, #{yzRiskScore,jdbcType=VARCHAR}, 
      #{prePaidAuditStatus,jdbcType=VARCHAR}, #{accidentFourLevelLossCause,jdbcType=VARCHAR}, 
      #{isOfflineCompensation,jdbcType=CHAR}, #{occupation,jdbcType=VARCHAR}, #{reserveAuditStatus,jdbcType=TINYINT}, 
      #{reserveAuditor,jdbcType=VARCHAR}, #{isHealthRelation,jdbcType=CHAR}, #{rollBackTimeliness,jdbcType=VARCHAR}, 
      #{rollBackDate,jdbcType=TIMESTAMP}, #{boundStatus,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.CargoClaimDO">
    insert into cargo_claim
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="policyId != null">
        policy_id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="lossCause != null">
        loss_cause,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="openUserId != null">
        open_user_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="cargoUserRegisterId != null">
        cargo_user_register_id,
      </if>
      <if test="reportAmount != null">
        report_amount,
      </if>
      <if test="channelReportNo != null">
        channel_report_no,
      </if>
      <if test="accidentDesc != null">
        accident_desc,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="accountBank != null">
        account_bank,
      </if>
      <if test="payeeName != null">
        payee_name,
      </if>
      <if test="accountNo != null">
        account_no,
      </if>
      <if test="attachmentUrl != null">
        attachment_url,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="accidentDate != null">
        accident_date,
      </if>
      <if test="accidentPlace != null">
        accident_place,
      </if>
      <if test="claimCurrency != null">
        claim_currency,
      </if>
      <if test="reportSource != null">
        report_source,
      </if>
      <if test="claimantName != null">
        claimant_name,
      </if>
      <if test="claimantPhone != null">
        claimant_phone,
      </if>
      <if test="claimantEmail != null">
        claimant_email,
      </if>
      <if test="registerDate != null">
        register_date,
      </if>
      <if test="claimNo != null">
        claim_no,
      </if>
      <if test="isAutoRegister != null">
        is_auto_register,
      </if>
      <if test="isNeedAssess != null">
        is_need_assess,
      </if>
      <if test="dispatcherDate != null">
        dispatcher_date,
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="channelName != null">
        channel_name,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="claimType != null">
        claim_type,
      </if>
      <if test="registerUser != null">
        register_user,
      </if>
      <if test="agencyUserId != null">
        agency_user_id,
      </if>
      <if test="policyInsurant != null">
        policy_insurant,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="insuranceTypeCode != null">
        insurance_type_code,
      </if>
      <if test="campaignFullName != null">
        campaign_full_name,
      </if>
      <if test="lossAmount != null">
        loss_amount,
      </if>
      <if test="reportUserName != null">
        report_user_name,
      </if>
      <if test="reportorRelinsured != null">
        reportor_relinsured,
      </if>
      <if test="reportorPhone != null">
        reportor_phone,
      </if>
      <if test="reportorEmail != null">
        reportor_email,
      </if>
      <if test="hospitalName != null">
        hospital_name,
      </if>
      <if test="auditDate != null">
        audit_date,
      </if>
      <if test="auditAmount != null">
        audit_amount,
      </if>
      <if test="auditConclusion != null">
        audit_conclusion,
      </if>
      <if test="transAccountType != null">
        trans_account_type,
      </if>
      <if test="accountBankArea != null">
        account_bank_area,
      </if>
      <if test="lossCauseName != null">
        loss_cause_name,
      </if>
      <if test="isNeedNotice != null">
        is_need_notice,
      </if>
      <if test="isHangUp != null">
        is_hang_up,
      </if>
      <if test="insurePlace != null">
        insure_place,
      </if>
      <if test="isAllowReportRollBack != null">
        is_allow_report_roll_back,
      </if>
      <if test="isReopen != null">
        is_reopen,
      </if>
      <if test="isClosedMsgInformed != null">
        is_closed_msg_informed,
      </if>
      <if test="coreClaimStatus != null">
        core_claim_status,
      </if>
      <if test="coreSynctime != null">
        core_synctime,
      </if>
      <if test="closeDate != null">
        close_date,
      </if>
      <if test="payState != null">
        pay_state,
      </if>
      <if test="paymentDate != null">
        payment_date,
      </if>
      <if test="isPayFailed != null">
        is_pay_failed,
      </if>
      <if test="isPayWait != null">
        is_pay_wait,
      </if>
      <if test="processType != null">
        process_type,
      </if>
      <if test="claimReportSource != null">
        claim_report_source,
      </if>
      <if test="isIncludeAttachment != null">
        is_include_attachment,
      </if>
      <if test="openPolicyNo != null">
        open_policy_no,
      </if>
      <if test="syncFlag != null">
        sync_flag,
      </if>
      <if test="syncDate != null">
        sync_date,
      </if>
      <if test="channelAttachmentIsFinished != null">
        channel_attachment_is_finished,
      </if>
      <if test="attachmentLastDate != null">
        attachment_last_date,
      </if>
      <if test="isNeedCustomize != null">
        is_need_customize,
      </if>
      <if test="isLossElecAttachment != null">
        is_loss_elec_attachment,
      </if>
      <if test="lossAttachmentLogId != null">
        loss_attachment_log_id,
      </if>
      <if test="claimTimeliness != null">
        claim_timeliness,
      </if>
      <if test="isAttachmentCompleted != null">
        is_attachment_completed,
      </if>
      <if test="isTotalLossPolicy != null">
        is_total_loss_policy,
      </if>
      <if test="trafficInsuranceType != null">
        traffic_insurance_type,
      </if>
      <if test="reportBatchNo != null">
        report_batch_no,
      </if>
      <if test="isCompensation != null">
        is_compensation,
      </if>
      <if test="isLitigate != null">
        is_litigate,
      </if>
      <if test="complaintType != null">
        complaint_type,
      </if>
      <if test="isHandle != null">
        is_handle,
      </if>
      <if test="isBankCode != null">
        is_bank_code,
      </if>
      <if test="claimCaseNo != null">
        claim_case_no,
      </if>
      <if test="sendMaterialStatus != null">
        send_material_status,
      </if>
      <if test="isSendMaterial != null">
        is_send_material,
      </if>
      <if test="isFreeSendMaterial != null">
        is_free_send_material,
      </if>
      <if test="hyCloseType != null">
        hy_close_type,
      </if>
      <if test="didiSecurityCardStatus != null">
        didi_security_card_status,
      </if>
      <if test="cyLossType != null">
        cy_loss_type,
      </if>
      <if test="cyPushStatus != null">
        cy_push_status,
      </if>
      <if test="surveyType != null">
        survey_type,
      </if>
      <if test="dadaAccidentType != null">
        dada_accident_type,
      </if>
      <if test="accidentReason != null">
        accident_reason,
      </if>
      <if test="transportation != null">
        transportation,
      </if>
      <if test="riskScore != null">
        risk_score,
      </if>
      <if test="yzRiskScore != null">
        yz_risk_score,
      </if>
      <if test="prePaidAuditStatus != null">
        pre_paid_audit_status,
      </if>
      <if test="accidentFourLevelLossCause != null">
        accident_four_level_loss_cause,
      </if>
      <if test="isOfflineCompensation != null">
        is_offline_compensation,
      </if>
      <if test="occupation != null">
        occupation,
      </if>
      <if test="reserveAuditStatus != null">
        reserve_audit_status,
      </if>
      <if test="reserveAuditor != null">
        reserve_auditor,
      </if>
      <if test="isHealthRelation != null">
        is_health_relation,
      </if>
      <if test="rollBackTimeliness != null">
        roll_back_timeliness,
      </if>
      <if test="rollBackDate != null">
        roll_back_date,
      </if>
      <if test="boundStatus != null">
        bound_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="policyId != null">
        #{policyId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="lossCause != null">
        #{lossCause,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="openUserId != null">
        #{openUserId,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="cargoUserRegisterId != null">
        #{cargoUserRegisterId,jdbcType=BIGINT},
      </if>
      <if test="reportAmount != null">
        #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="channelReportNo != null">
        #{channelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="accidentDesc != null">
        #{accidentDesc,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=TINYINT},
      </if>
      <if test="accountBank != null">
        #{accountBank,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="accountNo != null">
        #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null">
        #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="accidentDate != null">
        #{accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="accidentPlace != null">
        #{accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="claimCurrency != null">
        #{claimCurrency,jdbcType=VARCHAR},
      </if>
      <if test="reportSource != null">
        #{reportSource,jdbcType=TINYINT},
      </if>
      <if test="claimantName != null">
        #{claimantName,jdbcType=VARCHAR},
      </if>
      <if test="claimantPhone != null">
        #{claimantPhone,jdbcType=VARCHAR},
      </if>
      <if test="claimantEmail != null">
        #{claimantEmail,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="claimNo != null">
        #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="isAutoRegister != null">
        #{isAutoRegister,jdbcType=CHAR},
      </if>
      <if test="isNeedAssess != null">
        #{isNeedAssess,jdbcType=CHAR},
      </if>
      <if test="dispatcherDate != null">
        #{dispatcherDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dispatcherNo != null">
        #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="channelName != null">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="claimType != null">
        #{claimType,jdbcType=TINYINT},
      </if>
      <if test="registerUser != null">
        #{registerUser,jdbcType=VARCHAR},
      </if>
      <if test="agencyUserId != null">
        #{agencyUserId,jdbcType=BIGINT},
      </if>
      <if test="policyInsurant != null">
        #{policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="insuranceTypeCode != null">
        #{insuranceTypeCode,jdbcType=TINYINT},
      </if>
      <if test="campaignFullName != null">
        #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="lossAmount != null">
        #{lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="reportUserName != null">
        #{reportUserName,jdbcType=VARCHAR},
      </if>
      <if test="reportorRelinsured != null">
        #{reportorRelinsured,jdbcType=VARCHAR},
      </if>
      <if test="reportorPhone != null">
        #{reportorPhone,jdbcType=VARCHAR},
      </if>
      <if test="reportorEmail != null">
        #{reportorEmail,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="auditDate != null">
        #{auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="auditAmount != null">
        #{auditAmount,jdbcType=VARCHAR},
      </if>
      <if test="auditConclusion != null">
        #{auditConclusion,jdbcType=VARCHAR},
      </if>
      <if test="transAccountType != null">
        #{transAccountType,jdbcType=TINYINT},
      </if>
      <if test="accountBankArea != null">
        #{accountBankArea,jdbcType=VARCHAR},
      </if>
      <if test="lossCauseName != null">
        #{lossCauseName,jdbcType=VARCHAR},
      </if>
      <if test="isNeedNotice != null">
        #{isNeedNotice,jdbcType=VARCHAR},
      </if>
      <if test="isHangUp != null">
        #{isHangUp,jdbcType=CHAR},
      </if>
      <if test="insurePlace != null">
        #{insurePlace,jdbcType=VARCHAR},
      </if>
      <if test="isAllowReportRollBack != null">
        #{isAllowReportRollBack,jdbcType=VARCHAR},
      </if>
      <if test="isReopen != null">
        #{isReopen,jdbcType=CHAR},
      </if>
      <if test="isClosedMsgInformed != null">
        #{isClosedMsgInformed,jdbcType=CHAR},
      </if>
      <if test="coreClaimStatus != null">
        #{coreClaimStatus,jdbcType=VARCHAR},
      </if>
      <if test="coreSynctime != null">
        #{coreSynctime,jdbcType=TIMESTAMP},
      </if>
      <if test="closeDate != null">
        #{closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="payState != null">
        #{payState,jdbcType=TINYINT},
      </if>
      <if test="paymentDate != null">
        #{paymentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isPayFailed != null">
        #{isPayFailed,jdbcType=CHAR},
      </if>
      <if test="isPayWait != null">
        #{isPayWait,jdbcType=CHAR},
      </if>
      <if test="processType != null">
        #{processType,jdbcType=TINYINT},
      </if>
      <if test="claimReportSource != null">
        #{claimReportSource,jdbcType=VARCHAR},
      </if>
      <if test="isIncludeAttachment != null">
        #{isIncludeAttachment,jdbcType=VARCHAR},
      </if>
      <if test="openPolicyNo != null">
        #{openPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="syncFlag != null">
        #{syncFlag,jdbcType=VARCHAR},
      </if>
      <if test="syncDate != null">
        #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="channelAttachmentIsFinished != null">
        #{channelAttachmentIsFinished,jdbcType=CHAR},
      </if>
      <if test="attachmentLastDate != null">
        #{attachmentLastDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isNeedCustomize != null">
        #{isNeedCustomize,jdbcType=CHAR},
      </if>
      <if test="isLossElecAttachment != null">
        #{isLossElecAttachment,jdbcType=CHAR},
      </if>
      <if test="lossAttachmentLogId != null">
        #{lossAttachmentLogId,jdbcType=BIGINT},
      </if>
      <if test="claimTimeliness != null">
        #{claimTimeliness,jdbcType=INTEGER},
      </if>
      <if test="isAttachmentCompleted != null">
        #{isAttachmentCompleted,jdbcType=CHAR},
      </if>
      <if test="isTotalLossPolicy != null">
        #{isTotalLossPolicy,jdbcType=CHAR},
      </if>
      <if test="trafficInsuranceType != null">
        #{trafficInsuranceType,jdbcType=VARCHAR},
      </if>
      <if test="reportBatchNo != null">
        #{reportBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="isCompensation != null">
        #{isCompensation,jdbcType=CHAR},
      </if>
      <if test="isLitigate != null">
        #{isLitigate,jdbcType=VARCHAR},
      </if>
      <if test="complaintType != null">
        #{complaintType,jdbcType=VARCHAR},
      </if>
      <if test="isHandle != null">
        #{isHandle,jdbcType=CHAR},
      </if>
      <if test="isBankCode != null">
        #{isBankCode,jdbcType=CHAR},
      </if>
      <if test="claimCaseNo != null">
        #{claimCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="sendMaterialStatus != null">
        #{sendMaterialStatus,jdbcType=VARCHAR},
      </if>
      <if test="isSendMaterial != null">
        #{isSendMaterial,jdbcType=VARCHAR},
      </if>
      <if test="isFreeSendMaterial != null">
        #{isFreeSendMaterial,jdbcType=VARCHAR},
      </if>
      <if test="hyCloseType != null">
        #{hyCloseType,jdbcType=VARCHAR},
      </if>
      <if test="didiSecurityCardStatus != null">
        #{didiSecurityCardStatus,jdbcType=VARCHAR},
      </if>
      <if test="cyLossType != null">
        #{cyLossType,jdbcType=VARCHAR},
      </if>
      <if test="cyPushStatus != null">
        #{cyPushStatus,jdbcType=VARCHAR},
      </if>
      <if test="surveyType != null">
        #{surveyType,jdbcType=VARCHAR},
      </if>
      <if test="dadaAccidentType != null">
        #{dadaAccidentType,jdbcType=VARCHAR},
      </if>
      <if test="accidentReason != null">
        #{accidentReason,jdbcType=VARCHAR},
      </if>
      <if test="transportation != null">
        #{transportation,jdbcType=VARCHAR},
      </if>
      <if test="riskScore != null">
        #{riskScore,jdbcType=VARCHAR},
      </if>
      <if test="yzRiskScore != null">
        #{yzRiskScore,jdbcType=VARCHAR},
      </if>
      <if test="prePaidAuditStatus != null">
        #{prePaidAuditStatus,jdbcType=VARCHAR},
      </if>
      <if test="accidentFourLevelLossCause != null">
        #{accidentFourLevelLossCause,jdbcType=VARCHAR},
      </if>
      <if test="isOfflineCompensation != null">
        #{isOfflineCompensation,jdbcType=CHAR},
      </if>
      <if test="occupation != null">
        #{occupation,jdbcType=VARCHAR},
      </if>
      <if test="reserveAuditStatus != null">
        #{reserveAuditStatus,jdbcType=TINYINT},
      </if>
      <if test="reserveAuditor != null">
        #{reserveAuditor,jdbcType=VARCHAR},
      </if>
      <if test="isHealthRelation != null">
        #{isHealthRelation,jdbcType=CHAR},
      </if>
      <if test="rollBackTimeliness != null">
        #{rollBackTimeliness,jdbcType=VARCHAR},
      </if>
      <if test="rollBackDate != null">
        #{rollBackDate,jdbcType=TIMESTAMP},
      </if>
      <if test="boundStatus != null">
        #{boundStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.CargoClaimExample" resultType="java.lang.Long">
    select count(*) from cargo_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_claim
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyId != null">
        policy_id = #{record.policyId,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.lossCause != null">
        loss_cause = #{record.lossCause,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.openUserId != null">
        open_user_id = #{record.openUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.cargoUserRegisterId != null">
        cargo_user_register_id = #{record.cargoUserRegisterId,jdbcType=BIGINT},
      </if>
      <if test="record.reportAmount != null">
        report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.channelReportNo != null">
        channel_report_no = #{record.channelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentDesc != null">
        accident_desc = #{record.accidentDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=TINYINT},
      </if>
      <if test="record.accountBank != null">
        account_bank = #{record.accountBank,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeName != null">
        payee_name = #{record.payeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNo != null">
        account_no = #{record.accountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentUrl != null">
        attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDate != null">
        report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accidentDate != null">
        accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accidentPlace != null">
        accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="record.claimCurrency != null">
        claim_currency = #{record.claimCurrency,jdbcType=VARCHAR},
      </if>
      <if test="record.reportSource != null">
        report_source = #{record.reportSource,jdbcType=TINYINT},
      </if>
      <if test="record.claimantName != null">
        claimant_name = #{record.claimantName,jdbcType=VARCHAR},
      </if>
      <if test="record.claimantPhone != null">
        claimant_phone = #{record.claimantPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.claimantEmail != null">
        claimant_email = #{record.claimantEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.registerDate != null">
        register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.claimNo != null">
        claim_no = #{record.claimNo,jdbcType=VARCHAR},
      </if>
      <if test="record.isAutoRegister != null">
        is_auto_register = #{record.isAutoRegister,jdbcType=CHAR},
      </if>
      <if test="record.isNeedAssess != null">
        is_need_assess = #{record.isNeedAssess,jdbcType=CHAR},
      </if>
      <if test="record.dispatcherDate != null">
        dispatcher_date = #{record.dispatcherDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dispatcherNo != null">
        dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.channelName != null">
        channel_name = #{record.channelName,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.claimType != null">
        claim_type = #{record.claimType,jdbcType=TINYINT},
      </if>
      <if test="record.registerUser != null">
        register_user = #{record.registerUser,jdbcType=VARCHAR},
      </if>
      <if test="record.agencyUserId != null">
        agency_user_id = #{record.agencyUserId,jdbcType=BIGINT},
      </if>
      <if test="record.policyInsurant != null">
        policy_insurant = #{record.policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="record.auditor != null">
        auditor = #{record.auditor,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceTypeCode != null">
        insurance_type_code = #{record.insuranceTypeCode,jdbcType=TINYINT},
      </if>
      <if test="record.campaignFullName != null">
        campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.lossAmount != null">
        loss_amount = #{record.lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.reportUserName != null">
        report_user_name = #{record.reportUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.reportorRelinsured != null">
        reportor_relinsured = #{record.reportorRelinsured,jdbcType=VARCHAR},
      </if>
      <if test="record.reportorPhone != null">
        reportor_phone = #{record.reportorPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.reportorEmail != null">
        reportor_email = #{record.reportorEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalName != null">
        hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="record.auditDate != null">
        audit_date = #{record.auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.auditAmount != null">
        audit_amount = #{record.auditAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.auditConclusion != null">
        audit_conclusion = #{record.auditConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.transAccountType != null">
        trans_account_type = #{record.transAccountType,jdbcType=TINYINT},
      </if>
      <if test="record.accountBankArea != null">
        account_bank_area = #{record.accountBankArea,jdbcType=VARCHAR},
      </if>
      <if test="record.lossCauseName != null">
        loss_cause_name = #{record.lossCauseName,jdbcType=VARCHAR},
      </if>
      <if test="record.isNeedNotice != null">
        is_need_notice = #{record.isNeedNotice,jdbcType=VARCHAR},
      </if>
      <if test="record.isHangUp != null">
        is_hang_up = #{record.isHangUp,jdbcType=CHAR},
      </if>
      <if test="record.insurePlace != null">
        insure_place = #{record.insurePlace,jdbcType=VARCHAR},
      </if>
      <if test="record.isAllowReportRollBack != null">
        is_allow_report_roll_back = #{record.isAllowReportRollBack,jdbcType=VARCHAR},
      </if>
      <if test="record.isReopen != null">
        is_reopen = #{record.isReopen,jdbcType=CHAR},
      </if>
      <if test="record.isClosedMsgInformed != null">
        is_closed_msg_informed = #{record.isClosedMsgInformed,jdbcType=CHAR},
      </if>
      <if test="record.coreClaimStatus != null">
        core_claim_status = #{record.coreClaimStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.coreSynctime != null">
        core_synctime = #{record.coreSynctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.closeDate != null">
        close_date = #{record.closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payState != null">
        pay_state = #{record.payState,jdbcType=TINYINT},
      </if>
      <if test="record.paymentDate != null">
        payment_date = #{record.paymentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isPayFailed != null">
        is_pay_failed = #{record.isPayFailed,jdbcType=CHAR},
      </if>
      <if test="record.isPayWait != null">
        is_pay_wait = #{record.isPayWait,jdbcType=CHAR},
      </if>
      <if test="record.processType != null">
        process_type = #{record.processType,jdbcType=TINYINT},
      </if>
      <if test="record.claimReportSource != null">
        claim_report_source = #{record.claimReportSource,jdbcType=VARCHAR},
      </if>
      <if test="record.isIncludeAttachment != null">
        is_include_attachment = #{record.isIncludeAttachment,jdbcType=VARCHAR},
      </if>
      <if test="record.openPolicyNo != null">
        open_policy_no = #{record.openPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.syncFlag != null">
        sync_flag = #{record.syncFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.syncDate != null">
        sync_date = #{record.syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.channelAttachmentIsFinished != null">
        channel_attachment_is_finished = #{record.channelAttachmentIsFinished,jdbcType=CHAR},
      </if>
      <if test="record.attachmentLastDate != null">
        attachment_last_date = #{record.attachmentLastDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isNeedCustomize != null">
        is_need_customize = #{record.isNeedCustomize,jdbcType=CHAR},
      </if>
      <if test="record.isLossElecAttachment != null">
        is_loss_elec_attachment = #{record.isLossElecAttachment,jdbcType=CHAR},
      </if>
      <if test="record.lossAttachmentLogId != null">
        loss_attachment_log_id = #{record.lossAttachmentLogId,jdbcType=BIGINT},
      </if>
      <if test="record.claimTimeliness != null">
        claim_timeliness = #{record.claimTimeliness,jdbcType=INTEGER},
      </if>
      <if test="record.isAttachmentCompleted != null">
        is_attachment_completed = #{record.isAttachmentCompleted,jdbcType=CHAR},
      </if>
      <if test="record.isTotalLossPolicy != null">
        is_total_loss_policy = #{record.isTotalLossPolicy,jdbcType=CHAR},
      </if>
      <if test="record.trafficInsuranceType != null">
        traffic_insurance_type = #{record.trafficInsuranceType,jdbcType=VARCHAR},
      </if>
      <if test="record.reportBatchNo != null">
        report_batch_no = #{record.reportBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.isCompensation != null">
        is_compensation = #{record.isCompensation,jdbcType=CHAR},
      </if>
      <if test="record.isLitigate != null">
        is_litigate = #{record.isLitigate,jdbcType=VARCHAR},
      </if>
      <if test="record.complaintType != null">
        complaint_type = #{record.complaintType,jdbcType=VARCHAR},
      </if>
      <if test="record.isHandle != null">
        is_handle = #{record.isHandle,jdbcType=CHAR},
      </if>
      <if test="record.isBankCode != null">
        is_bank_code = #{record.isBankCode,jdbcType=CHAR},
      </if>
      <if test="record.claimCaseNo != null">
        claim_case_no = #{record.claimCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sendMaterialStatus != null">
        send_material_status = #{record.sendMaterialStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.isSendMaterial != null">
        is_send_material = #{record.isSendMaterial,jdbcType=VARCHAR},
      </if>
      <if test="record.isFreeSendMaterial != null">
        is_free_send_material = #{record.isFreeSendMaterial,jdbcType=VARCHAR},
      </if>
      <if test="record.hyCloseType != null">
        hy_close_type = #{record.hyCloseType,jdbcType=VARCHAR},
      </if>
      <if test="record.didiSecurityCardStatus != null">
        didi_security_card_status = #{record.didiSecurityCardStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.cyLossType != null">
        cy_loss_type = #{record.cyLossType,jdbcType=VARCHAR},
      </if>
      <if test="record.cyPushStatus != null">
        cy_push_status = #{record.cyPushStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.surveyType != null">
        survey_type = #{record.surveyType,jdbcType=VARCHAR},
      </if>
      <if test="record.dadaAccidentType != null">
        dada_accident_type = #{record.dadaAccidentType,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentReason != null">
        accident_reason = #{record.accidentReason,jdbcType=VARCHAR},
      </if>
      <if test="record.transportation != null">
        transportation = #{record.transportation,jdbcType=VARCHAR},
      </if>
      <if test="record.riskScore != null">
        risk_score = #{record.riskScore,jdbcType=VARCHAR},
      </if>
      <if test="record.yzRiskScore != null">
        yz_risk_score = #{record.yzRiskScore,jdbcType=VARCHAR},
      </if>
      <if test="record.prePaidAuditStatus != null">
        pre_paid_audit_status = #{record.prePaidAuditStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentFourLevelLossCause != null">
        accident_four_level_loss_cause = #{record.accidentFourLevelLossCause,jdbcType=VARCHAR},
      </if>
      <if test="record.isOfflineCompensation != null">
        is_offline_compensation = #{record.isOfflineCompensation,jdbcType=CHAR},
      </if>
      <if test="record.occupation != null">
        occupation = #{record.occupation,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveAuditStatus != null">
        reserve_audit_status = #{record.reserveAuditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.reserveAuditor != null">
        reserve_auditor = #{record.reserveAuditor,jdbcType=VARCHAR},
      </if>
      <if test="record.isHealthRelation != null">
        is_health_relation = #{record.isHealthRelation,jdbcType=CHAR},
      </if>
      <if test="record.rollBackTimeliness != null">
        roll_back_timeliness = #{record.rollBackTimeliness,jdbcType=VARCHAR},
      </if>
      <if test="record.rollBackDate != null">
        roll_back_date = #{record.rollBackDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.boundStatus != null">
        bound_status = #{record.boundStatus,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_claim
    set id = #{record.id,jdbcType=BIGINT},
      policy_id = #{record.policyId,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      loss_cause = #{record.lossCause,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      open_user_id = #{record.openUserId,jdbcType=VARCHAR},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      cargo_user_register_id = #{record.cargoUserRegisterId,jdbcType=BIGINT},
      report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      channel_report_no = #{record.channelReportNo,jdbcType=VARCHAR},
      accident_desc = #{record.accidentDesc,jdbcType=VARCHAR},
      account_type = #{record.accountType,jdbcType=TINYINT},
      account_bank = #{record.accountBank,jdbcType=VARCHAR},
      payee_name = #{record.payeeName,jdbcType=VARCHAR},
      account_no = #{record.accountNo,jdbcType=VARCHAR},
      attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      claim_currency = #{record.claimCurrency,jdbcType=VARCHAR},
      report_source = #{record.reportSource,jdbcType=TINYINT},
      claimant_name = #{record.claimantName,jdbcType=VARCHAR},
      claimant_phone = #{record.claimantPhone,jdbcType=VARCHAR},
      claimant_email = #{record.claimantEmail,jdbcType=VARCHAR},
      register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      claim_no = #{record.claimNo,jdbcType=VARCHAR},
      is_auto_register = #{record.isAutoRegister,jdbcType=CHAR},
      is_need_assess = #{record.isNeedAssess,jdbcType=CHAR},
      dispatcher_date = #{record.dispatcherDate,jdbcType=TIMESTAMP},
      dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      channel_name = #{record.channelName,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      claim_type = #{record.claimType,jdbcType=TINYINT},
      register_user = #{record.registerUser,jdbcType=VARCHAR},
      agency_user_id = #{record.agencyUserId,jdbcType=BIGINT},
      policy_insurant = #{record.policyInsurant,jdbcType=VARCHAR},
      auditor = #{record.auditor,jdbcType=VARCHAR},
      insurance_type_code = #{record.insuranceTypeCode,jdbcType=TINYINT},
      campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      loss_amount = #{record.lossAmount,jdbcType=VARCHAR},
      report_user_name = #{record.reportUserName,jdbcType=VARCHAR},
      reportor_relinsured = #{record.reportorRelinsured,jdbcType=VARCHAR},
      reportor_phone = #{record.reportorPhone,jdbcType=VARCHAR},
      reportor_email = #{record.reportorEmail,jdbcType=VARCHAR},
      hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      audit_date = #{record.auditDate,jdbcType=TIMESTAMP},
      audit_amount = #{record.auditAmount,jdbcType=VARCHAR},
      audit_conclusion = #{record.auditConclusion,jdbcType=VARCHAR},
      trans_account_type = #{record.transAccountType,jdbcType=TINYINT},
      account_bank_area = #{record.accountBankArea,jdbcType=VARCHAR},
      loss_cause_name = #{record.lossCauseName,jdbcType=VARCHAR},
      is_need_notice = #{record.isNeedNotice,jdbcType=VARCHAR},
      is_hang_up = #{record.isHangUp,jdbcType=CHAR},
      insure_place = #{record.insurePlace,jdbcType=VARCHAR},
      is_allow_report_roll_back = #{record.isAllowReportRollBack,jdbcType=VARCHAR},
      is_reopen = #{record.isReopen,jdbcType=CHAR},
      is_closed_msg_informed = #{record.isClosedMsgInformed,jdbcType=CHAR},
      core_claim_status = #{record.coreClaimStatus,jdbcType=VARCHAR},
      core_synctime = #{record.coreSynctime,jdbcType=TIMESTAMP},
      close_date = #{record.closeDate,jdbcType=TIMESTAMP},
      pay_state = #{record.payState,jdbcType=TINYINT},
      payment_date = #{record.paymentDate,jdbcType=TIMESTAMP},
      is_pay_failed = #{record.isPayFailed,jdbcType=CHAR},
      is_pay_wait = #{record.isPayWait,jdbcType=CHAR},
      process_type = #{record.processType,jdbcType=TINYINT},
      claim_report_source = #{record.claimReportSource,jdbcType=VARCHAR},
      is_include_attachment = #{record.isIncludeAttachment,jdbcType=VARCHAR},
      open_policy_no = #{record.openPolicyNo,jdbcType=VARCHAR},
      sync_flag = #{record.syncFlag,jdbcType=VARCHAR},
      sync_date = #{record.syncDate,jdbcType=TIMESTAMP},
      channel_attachment_is_finished = #{record.channelAttachmentIsFinished,jdbcType=CHAR},
      attachment_last_date = #{record.attachmentLastDate,jdbcType=TIMESTAMP},
      is_need_customize = #{record.isNeedCustomize,jdbcType=CHAR},
      is_loss_elec_attachment = #{record.isLossElecAttachment,jdbcType=CHAR},
      loss_attachment_log_id = #{record.lossAttachmentLogId,jdbcType=BIGINT},
      claim_timeliness = #{record.claimTimeliness,jdbcType=INTEGER},
      is_attachment_completed = #{record.isAttachmentCompleted,jdbcType=CHAR},
      is_total_loss_policy = #{record.isTotalLossPolicy,jdbcType=CHAR},
      traffic_insurance_type = #{record.trafficInsuranceType,jdbcType=VARCHAR},
      report_batch_no = #{record.reportBatchNo,jdbcType=VARCHAR},
      is_compensation = #{record.isCompensation,jdbcType=CHAR},
      is_litigate = #{record.isLitigate,jdbcType=VARCHAR},
      complaint_type = #{record.complaintType,jdbcType=VARCHAR},
      is_handle = #{record.isHandle,jdbcType=CHAR},
      is_bank_code = #{record.isBankCode,jdbcType=CHAR},
      claim_case_no = #{record.claimCaseNo,jdbcType=VARCHAR},
      send_material_status = #{record.sendMaterialStatus,jdbcType=VARCHAR},
      is_send_material = #{record.isSendMaterial,jdbcType=VARCHAR},
      is_free_send_material = #{record.isFreeSendMaterial,jdbcType=VARCHAR},
      hy_close_type = #{record.hyCloseType,jdbcType=VARCHAR},
      didi_security_card_status = #{record.didiSecurityCardStatus,jdbcType=VARCHAR},
      cy_loss_type = #{record.cyLossType,jdbcType=VARCHAR},
      cy_push_status = #{record.cyPushStatus,jdbcType=VARCHAR},
      survey_type = #{record.surveyType,jdbcType=VARCHAR},
      dada_accident_type = #{record.dadaAccidentType,jdbcType=VARCHAR},
      accident_reason = #{record.accidentReason,jdbcType=VARCHAR},
      transportation = #{record.transportation,jdbcType=VARCHAR},
      risk_score = #{record.riskScore,jdbcType=VARCHAR},
      yz_risk_score = #{record.yzRiskScore,jdbcType=VARCHAR},
      pre_paid_audit_status = #{record.prePaidAuditStatus,jdbcType=VARCHAR},
      accident_four_level_loss_cause = #{record.accidentFourLevelLossCause,jdbcType=VARCHAR},
      is_offline_compensation = #{record.isOfflineCompensation,jdbcType=CHAR},
      occupation = #{record.occupation,jdbcType=VARCHAR},
      reserve_audit_status = #{record.reserveAuditStatus,jdbcType=TINYINT},
      reserve_auditor = #{record.reserveAuditor,jdbcType=VARCHAR},
      is_health_relation = #{record.isHealthRelation,jdbcType=CHAR},
      roll_back_timeliness = #{record.rollBackTimeliness,jdbcType=VARCHAR},
      roll_back_date = #{record.rollBackDate,jdbcType=TIMESTAMP},
      bound_status = #{record.boundStatus,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.CargoClaimDO">
    update cargo_claim
    <set>
      <if test="policyId != null">
        policy_id = #{policyId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="lossCause != null">
        loss_cause = #{lossCause,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="openUserId != null">
        open_user_id = #{openUserId,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="cargoUserRegisterId != null">
        cargo_user_register_id = #{cargoUserRegisterId,jdbcType=BIGINT},
      </if>
      <if test="reportAmount != null">
        report_amount = #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="channelReportNo != null">
        channel_report_no = #{channelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="accidentDesc != null">
        accident_desc = #{accidentDesc,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=TINYINT},
      </if>
      <if test="accountBank != null">
        account_bank = #{accountBank,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        payee_name = #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="accountNo != null">
        account_no = #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null">
        attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="accidentDate != null">
        accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="accidentPlace != null">
        accident_place = #{accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="claimCurrency != null">
        claim_currency = #{claimCurrency,jdbcType=VARCHAR},
      </if>
      <if test="reportSource != null">
        report_source = #{reportSource,jdbcType=TINYINT},
      </if>
      <if test="claimantName != null">
        claimant_name = #{claimantName,jdbcType=VARCHAR},
      </if>
      <if test="claimantPhone != null">
        claimant_phone = #{claimantPhone,jdbcType=VARCHAR},
      </if>
      <if test="claimantEmail != null">
        claimant_email = #{claimantEmail,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        register_date = #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="claimNo != null">
        claim_no = #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="isAutoRegister != null">
        is_auto_register = #{isAutoRegister,jdbcType=CHAR},
      </if>
      <if test="isNeedAssess != null">
        is_need_assess = #{isNeedAssess,jdbcType=CHAR},
      </if>
      <if test="dispatcherDate != null">
        dispatcher_date = #{dispatcherDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="channelName != null">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="claimType != null">
        claim_type = #{claimType,jdbcType=TINYINT},
      </if>
      <if test="registerUser != null">
        register_user = #{registerUser,jdbcType=VARCHAR},
      </if>
      <if test="agencyUserId != null">
        agency_user_id = #{agencyUserId,jdbcType=BIGINT},
      </if>
      <if test="policyInsurant != null">
        policy_insurant = #{policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="insuranceTypeCode != null">
        insurance_type_code = #{insuranceTypeCode,jdbcType=TINYINT},
      </if>
      <if test="campaignFullName != null">
        campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="lossAmount != null">
        loss_amount = #{lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="reportUserName != null">
        report_user_name = #{reportUserName,jdbcType=VARCHAR},
      </if>
      <if test="reportorRelinsured != null">
        reportor_relinsured = #{reportorRelinsured,jdbcType=VARCHAR},
      </if>
      <if test="reportorPhone != null">
        reportor_phone = #{reportorPhone,jdbcType=VARCHAR},
      </if>
      <if test="reportorEmail != null">
        reportor_email = #{reportorEmail,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        hospital_name = #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="auditDate != null">
        audit_date = #{auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="auditAmount != null">
        audit_amount = #{auditAmount,jdbcType=VARCHAR},
      </if>
      <if test="auditConclusion != null">
        audit_conclusion = #{auditConclusion,jdbcType=VARCHAR},
      </if>
      <if test="transAccountType != null">
        trans_account_type = #{transAccountType,jdbcType=TINYINT},
      </if>
      <if test="accountBankArea != null">
        account_bank_area = #{accountBankArea,jdbcType=VARCHAR},
      </if>
      <if test="lossCauseName != null">
        loss_cause_name = #{lossCauseName,jdbcType=VARCHAR},
      </if>
      <if test="isNeedNotice != null">
        is_need_notice = #{isNeedNotice,jdbcType=VARCHAR},
      </if>
      <if test="isHangUp != null">
        is_hang_up = #{isHangUp,jdbcType=CHAR},
      </if>
      <if test="insurePlace != null">
        insure_place = #{insurePlace,jdbcType=VARCHAR},
      </if>
      <if test="isAllowReportRollBack != null">
        is_allow_report_roll_back = #{isAllowReportRollBack,jdbcType=VARCHAR},
      </if>
      <if test="isReopen != null">
        is_reopen = #{isReopen,jdbcType=CHAR},
      </if>
      <if test="isClosedMsgInformed != null">
        is_closed_msg_informed = #{isClosedMsgInformed,jdbcType=CHAR},
      </if>
      <if test="coreClaimStatus != null">
        core_claim_status = #{coreClaimStatus,jdbcType=VARCHAR},
      </if>
      <if test="coreSynctime != null">
        core_synctime = #{coreSynctime,jdbcType=TIMESTAMP},
      </if>
      <if test="closeDate != null">
        close_date = #{closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="payState != null">
        pay_state = #{payState,jdbcType=TINYINT},
      </if>
      <if test="paymentDate != null">
        payment_date = #{paymentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isPayFailed != null">
        is_pay_failed = #{isPayFailed,jdbcType=CHAR},
      </if>
      <if test="isPayWait != null">
        is_pay_wait = #{isPayWait,jdbcType=CHAR},
      </if>
      <if test="processType != null">
        process_type = #{processType,jdbcType=TINYINT},
      </if>
      <if test="claimReportSource != null">
        claim_report_source = #{claimReportSource,jdbcType=VARCHAR},
      </if>
      <if test="isIncludeAttachment != null">
        is_include_attachment = #{isIncludeAttachment,jdbcType=VARCHAR},
      </if>
      <if test="openPolicyNo != null">
        open_policy_no = #{openPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="syncFlag != null">
        sync_flag = #{syncFlag,jdbcType=VARCHAR},
      </if>
      <if test="syncDate != null">
        sync_date = #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="channelAttachmentIsFinished != null">
        channel_attachment_is_finished = #{channelAttachmentIsFinished,jdbcType=CHAR},
      </if>
      <if test="attachmentLastDate != null">
        attachment_last_date = #{attachmentLastDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isNeedCustomize != null">
        is_need_customize = #{isNeedCustomize,jdbcType=CHAR},
      </if>
      <if test="isLossElecAttachment != null">
        is_loss_elec_attachment = #{isLossElecAttachment,jdbcType=CHAR},
      </if>
      <if test="lossAttachmentLogId != null">
        loss_attachment_log_id = #{lossAttachmentLogId,jdbcType=BIGINT},
      </if>
      <if test="claimTimeliness != null">
        claim_timeliness = #{claimTimeliness,jdbcType=INTEGER},
      </if>
      <if test="isAttachmentCompleted != null">
        is_attachment_completed = #{isAttachmentCompleted,jdbcType=CHAR},
      </if>
      <if test="isTotalLossPolicy != null">
        is_total_loss_policy = #{isTotalLossPolicy,jdbcType=CHAR},
      </if>
      <if test="trafficInsuranceType != null">
        traffic_insurance_type = #{trafficInsuranceType,jdbcType=VARCHAR},
      </if>
      <if test="reportBatchNo != null">
        report_batch_no = #{reportBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="isCompensation != null">
        is_compensation = #{isCompensation,jdbcType=CHAR},
      </if>
      <if test="isLitigate != null">
        is_litigate = #{isLitigate,jdbcType=VARCHAR},
      </if>
      <if test="complaintType != null">
        complaint_type = #{complaintType,jdbcType=VARCHAR},
      </if>
      <if test="isHandle != null">
        is_handle = #{isHandle,jdbcType=CHAR},
      </if>
      <if test="isBankCode != null">
        is_bank_code = #{isBankCode,jdbcType=CHAR},
      </if>
      <if test="claimCaseNo != null">
        claim_case_no = #{claimCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="sendMaterialStatus != null">
        send_material_status = #{sendMaterialStatus,jdbcType=VARCHAR},
      </if>
      <if test="isSendMaterial != null">
        is_send_material = #{isSendMaterial,jdbcType=VARCHAR},
      </if>
      <if test="isFreeSendMaterial != null">
        is_free_send_material = #{isFreeSendMaterial,jdbcType=VARCHAR},
      </if>
      <if test="hyCloseType != null">
        hy_close_type = #{hyCloseType,jdbcType=VARCHAR},
      </if>
      <if test="didiSecurityCardStatus != null">
        didi_security_card_status = #{didiSecurityCardStatus,jdbcType=VARCHAR},
      </if>
      <if test="cyLossType != null">
        cy_loss_type = #{cyLossType,jdbcType=VARCHAR},
      </if>
      <if test="cyPushStatus != null">
        cy_push_status = #{cyPushStatus,jdbcType=VARCHAR},
      </if>
      <if test="surveyType != null">
        survey_type = #{surveyType,jdbcType=VARCHAR},
      </if>
      <if test="dadaAccidentType != null">
        dada_accident_type = #{dadaAccidentType,jdbcType=VARCHAR},
      </if>
      <if test="accidentReason != null">
        accident_reason = #{accidentReason,jdbcType=VARCHAR},
      </if>
      <if test="transportation != null">
        transportation = #{transportation,jdbcType=VARCHAR},
      </if>
      <if test="riskScore != null">
        risk_score = #{riskScore,jdbcType=VARCHAR},
      </if>
      <if test="yzRiskScore != null">
        yz_risk_score = #{yzRiskScore,jdbcType=VARCHAR},
      </if>
      <if test="prePaidAuditStatus != null">
        pre_paid_audit_status = #{prePaidAuditStatus,jdbcType=VARCHAR},
      </if>
      <if test="accidentFourLevelLossCause != null">
        accident_four_level_loss_cause = #{accidentFourLevelLossCause,jdbcType=VARCHAR},
      </if>
      <if test="isOfflineCompensation != null">
        is_offline_compensation = #{isOfflineCompensation,jdbcType=CHAR},
      </if>
      <if test="occupation != null">
        occupation = #{occupation,jdbcType=VARCHAR},
      </if>
      <if test="reserveAuditStatus != null">
        reserve_audit_status = #{reserveAuditStatus,jdbcType=TINYINT},
      </if>
      <if test="reserveAuditor != null">
        reserve_auditor = #{reserveAuditor,jdbcType=VARCHAR},
      </if>
      <if test="isHealthRelation != null">
        is_health_relation = #{isHealthRelation,jdbcType=CHAR},
      </if>
      <if test="rollBackTimeliness != null">
        roll_back_timeliness = #{rollBackTimeliness,jdbcType=VARCHAR},
      </if>
      <if test="rollBackDate != null">
        roll_back_date = #{rollBackDate,jdbcType=TIMESTAMP},
      </if>
      <if test="boundStatus != null">
        bound_status = #{boundStatus,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.CargoClaimDO">
    update cargo_claim
    set policy_id = #{policyId,jdbcType=BIGINT},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      loss_cause = #{lossCause,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      open_user_id = #{openUserId,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=BIGINT},
      cargo_user_register_id = #{cargoUserRegisterId,jdbcType=BIGINT},
      report_amount = #{reportAmount,jdbcType=VARCHAR},
      channel_report_no = #{channelReportNo,jdbcType=VARCHAR},
      accident_desc = #{accidentDesc,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=TINYINT},
      account_bank = #{accountBank,jdbcType=VARCHAR},
      payee_name = #{payeeName,jdbcType=VARCHAR},
      account_no = #{accountNo,jdbcType=VARCHAR},
      attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      report_date = #{reportDate,jdbcType=TIMESTAMP},
      accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      accident_place = #{accidentPlace,jdbcType=VARCHAR},
      claim_currency = #{claimCurrency,jdbcType=VARCHAR},
      report_source = #{reportSource,jdbcType=TINYINT},
      claimant_name = #{claimantName,jdbcType=VARCHAR},
      claimant_phone = #{claimantPhone,jdbcType=VARCHAR},
      claimant_email = #{claimantEmail,jdbcType=VARCHAR},
      register_date = #{registerDate,jdbcType=TIMESTAMP},
      claim_no = #{claimNo,jdbcType=VARCHAR},
      is_auto_register = #{isAutoRegister,jdbcType=CHAR},
      is_need_assess = #{isNeedAssess,jdbcType=CHAR},
      dispatcher_date = #{dispatcherDate,jdbcType=TIMESTAMP},
      dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      package_def_id = #{packageDefId,jdbcType=BIGINT},
      channel_name = #{channelName,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      claim_type = #{claimType,jdbcType=TINYINT},
      register_user = #{registerUser,jdbcType=VARCHAR},
      agency_user_id = #{agencyUserId,jdbcType=BIGINT},
      policy_insurant = #{policyInsurant,jdbcType=VARCHAR},
      auditor = #{auditor,jdbcType=VARCHAR},
      insurance_type_code = #{insuranceTypeCode,jdbcType=TINYINT},
      campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      loss_amount = #{lossAmount,jdbcType=VARCHAR},
      report_user_name = #{reportUserName,jdbcType=VARCHAR},
      reportor_relinsured = #{reportorRelinsured,jdbcType=VARCHAR},
      reportor_phone = #{reportorPhone,jdbcType=VARCHAR},
      reportor_email = #{reportorEmail,jdbcType=VARCHAR},
      hospital_name = #{hospitalName,jdbcType=VARCHAR},
      audit_date = #{auditDate,jdbcType=TIMESTAMP},
      audit_amount = #{auditAmount,jdbcType=VARCHAR},
      audit_conclusion = #{auditConclusion,jdbcType=VARCHAR},
      trans_account_type = #{transAccountType,jdbcType=TINYINT},
      account_bank_area = #{accountBankArea,jdbcType=VARCHAR},
      loss_cause_name = #{lossCauseName,jdbcType=VARCHAR},
      is_need_notice = #{isNeedNotice,jdbcType=VARCHAR},
      is_hang_up = #{isHangUp,jdbcType=CHAR},
      insure_place = #{insurePlace,jdbcType=VARCHAR},
      is_allow_report_roll_back = #{isAllowReportRollBack,jdbcType=VARCHAR},
      is_reopen = #{isReopen,jdbcType=CHAR},
      is_closed_msg_informed = #{isClosedMsgInformed,jdbcType=CHAR},
      core_claim_status = #{coreClaimStatus,jdbcType=VARCHAR},
      core_synctime = #{coreSynctime,jdbcType=TIMESTAMP},
      close_date = #{closeDate,jdbcType=TIMESTAMP},
      pay_state = #{payState,jdbcType=TINYINT},
      payment_date = #{paymentDate,jdbcType=TIMESTAMP},
      is_pay_failed = #{isPayFailed,jdbcType=CHAR},
      is_pay_wait = #{isPayWait,jdbcType=CHAR},
      process_type = #{processType,jdbcType=TINYINT},
      claim_report_source = #{claimReportSource,jdbcType=VARCHAR},
      is_include_attachment = #{isIncludeAttachment,jdbcType=VARCHAR},
      open_policy_no = #{openPolicyNo,jdbcType=VARCHAR},
      sync_flag = #{syncFlag,jdbcType=VARCHAR},
      sync_date = #{syncDate,jdbcType=TIMESTAMP},
      channel_attachment_is_finished = #{channelAttachmentIsFinished,jdbcType=CHAR},
      attachment_last_date = #{attachmentLastDate,jdbcType=TIMESTAMP},
      is_need_customize = #{isNeedCustomize,jdbcType=CHAR},
      is_loss_elec_attachment = #{isLossElecAttachment,jdbcType=CHAR},
      loss_attachment_log_id = #{lossAttachmentLogId,jdbcType=BIGINT},
      claim_timeliness = #{claimTimeliness,jdbcType=INTEGER},
      is_attachment_completed = #{isAttachmentCompleted,jdbcType=CHAR},
      is_total_loss_policy = #{isTotalLossPolicy,jdbcType=CHAR},
      traffic_insurance_type = #{trafficInsuranceType,jdbcType=VARCHAR},
      report_batch_no = #{reportBatchNo,jdbcType=VARCHAR},
      is_compensation = #{isCompensation,jdbcType=CHAR},
      is_litigate = #{isLitigate,jdbcType=VARCHAR},
      complaint_type = #{complaintType,jdbcType=VARCHAR},
      is_handle = #{isHandle,jdbcType=CHAR},
      is_bank_code = #{isBankCode,jdbcType=CHAR},
      claim_case_no = #{claimCaseNo,jdbcType=VARCHAR},
      send_material_status = #{sendMaterialStatus,jdbcType=VARCHAR},
      is_send_material = #{isSendMaterial,jdbcType=VARCHAR},
      is_free_send_material = #{isFreeSendMaterial,jdbcType=VARCHAR},
      hy_close_type = #{hyCloseType,jdbcType=VARCHAR},
      didi_security_card_status = #{didiSecurityCardStatus,jdbcType=VARCHAR},
      cy_loss_type = #{cyLossType,jdbcType=VARCHAR},
      cy_push_status = #{cyPushStatus,jdbcType=VARCHAR},
      survey_type = #{surveyType,jdbcType=VARCHAR},
      dada_accident_type = #{dadaAccidentType,jdbcType=VARCHAR},
      accident_reason = #{accidentReason,jdbcType=VARCHAR},
      transportation = #{transportation,jdbcType=VARCHAR},
      risk_score = #{riskScore,jdbcType=VARCHAR},
      yz_risk_score = #{yzRiskScore,jdbcType=VARCHAR},
      pre_paid_audit_status = #{prePaidAuditStatus,jdbcType=VARCHAR},
      accident_four_level_loss_cause = #{accidentFourLevelLossCause,jdbcType=VARCHAR},
      is_offline_compensation = #{isOfflineCompensation,jdbcType=CHAR},
      occupation = #{occupation,jdbcType=VARCHAR},
      reserve_audit_status = #{reserveAuditStatus,jdbcType=TINYINT},
      reserve_auditor = #{reserveAuditor,jdbcType=VARCHAR},
      is_health_relation = #{isHealthRelation,jdbcType=CHAR},
      roll_back_timeliness = #{rollBackTimeliness,jdbcType=VARCHAR},
      roll_back_date = #{rollBackDate,jdbcType=TIMESTAMP},
      bound_status = #{boundStatus,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_claim
    (id, policy_id, policy_no, loss_cause, extra_info, report_no, open_user_id, channel_id, 
      cargo_user_register_id, report_amount, channel_report_no, accident_desc, account_type, 
      account_bank, payee_name, account_no, attachment_url, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, report_date, accident_date, accident_place, claim_currency, 
      report_source, claimant_name, claimant_phone, claimant_email, register_date, claim_no, 
      is_auto_register, is_need_assess, dispatcher_date, dispatcher_no, remark, campaign_def_id, 
      package_def_id, channel_name, product_name, status, claim_type, register_user, 
      agency_user_id, policy_insurant, auditor, insurance_type_code, campaign_full_name, 
      loss_amount, report_user_name, reportor_relinsured, reportor_phone, reportor_email, 
      hospital_name, audit_date, audit_amount, audit_conclusion, trans_account_type, 
      account_bank_area, loss_cause_name, is_need_notice, is_hang_up, insure_place, is_allow_report_roll_back, 
      is_reopen, is_closed_msg_informed, core_claim_status, core_synctime, close_date, 
      pay_state, payment_date, is_pay_failed, is_pay_wait, process_type, claim_report_source, 
      is_include_attachment, open_policy_no, sync_flag, sync_date, channel_attachment_is_finished, 
      attachment_last_date, is_need_customize, is_loss_elec_attachment, loss_attachment_log_id, 
      claim_timeliness, is_attachment_completed, is_total_loss_policy, traffic_insurance_type, 
      report_batch_no, is_compensation, is_litigate, complaint_type, is_handle, is_bank_code, 
      claim_case_no, send_material_status, is_send_material, is_free_send_material, hy_close_type, 
      didi_security_card_status, cy_loss_type, cy_push_status, survey_type, dada_accident_type, 
      accident_reason, transportation, risk_score, yz_risk_score, pre_paid_audit_status, 
      accident_four_level_loss_cause, is_offline_compensation, occupation, reserve_audit_status, 
      reserve_auditor, is_health_relation, roll_back_timeliness, roll_back_date, bound_status
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.policyId,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.lossCause,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.openUserId,jdbcType=VARCHAR}, #{item.channelId,jdbcType=BIGINT}, #{item.cargoUserRegisterId,jdbcType=BIGINT}, 
        #{item.reportAmount,jdbcType=VARCHAR}, #{item.channelReportNo,jdbcType=VARCHAR}, 
        #{item.accidentDesc,jdbcType=VARCHAR}, #{item.accountType,jdbcType=TINYINT}, #{item.accountBank,jdbcType=VARCHAR}, 
        #{item.payeeName,jdbcType=VARCHAR}, #{item.accountNo,jdbcType=VARCHAR}, #{item.attachmentUrl,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.reportDate,jdbcType=TIMESTAMP}, 
        #{item.accidentDate,jdbcType=TIMESTAMP}, #{item.accidentPlace,jdbcType=VARCHAR}, 
        #{item.claimCurrency,jdbcType=VARCHAR}, #{item.reportSource,jdbcType=TINYINT}, 
        #{item.claimantName,jdbcType=VARCHAR}, #{item.claimantPhone,jdbcType=VARCHAR}, 
        #{item.claimantEmail,jdbcType=VARCHAR}, #{item.registerDate,jdbcType=TIMESTAMP}, 
        #{item.claimNo,jdbcType=VARCHAR}, #{item.isAutoRegister,jdbcType=CHAR}, #{item.isNeedAssess,jdbcType=CHAR}, 
        #{item.dispatcherDate,jdbcType=TIMESTAMP}, #{item.dispatcherNo,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.campaignDefId,jdbcType=BIGINT}, #{item.packageDefId,jdbcType=BIGINT}, 
        #{item.channelName,jdbcType=VARCHAR}, #{item.productName,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, 
        #{item.claimType,jdbcType=TINYINT}, #{item.registerUser,jdbcType=VARCHAR}, #{item.agencyUserId,jdbcType=BIGINT}, 
        #{item.policyInsurant,jdbcType=VARCHAR}, #{item.auditor,jdbcType=VARCHAR}, #{item.insuranceTypeCode,jdbcType=TINYINT}, 
        #{item.campaignFullName,jdbcType=VARCHAR}, #{item.lossAmount,jdbcType=VARCHAR}, 
        #{item.reportUserName,jdbcType=VARCHAR}, #{item.reportorRelinsured,jdbcType=VARCHAR}, 
        #{item.reportorPhone,jdbcType=VARCHAR}, #{item.reportorEmail,jdbcType=VARCHAR}, 
        #{item.hospitalName,jdbcType=VARCHAR}, #{item.auditDate,jdbcType=TIMESTAMP}, #{item.auditAmount,jdbcType=VARCHAR}, 
        #{item.auditConclusion,jdbcType=VARCHAR}, #{item.transAccountType,jdbcType=TINYINT}, 
        #{item.accountBankArea,jdbcType=VARCHAR}, #{item.lossCauseName,jdbcType=VARCHAR}, 
        #{item.isNeedNotice,jdbcType=VARCHAR}, #{item.isHangUp,jdbcType=CHAR}, #{item.insurePlace,jdbcType=VARCHAR}, 
        #{item.isAllowReportRollBack,jdbcType=VARCHAR}, #{item.isReopen,jdbcType=CHAR}, 
        #{item.isClosedMsgInformed,jdbcType=CHAR}, #{item.coreClaimStatus,jdbcType=VARCHAR}, 
        #{item.coreSynctime,jdbcType=TIMESTAMP}, #{item.closeDate,jdbcType=TIMESTAMP}, 
        #{item.payState,jdbcType=TINYINT}, #{item.paymentDate,jdbcType=TIMESTAMP}, #{item.isPayFailed,jdbcType=CHAR}, 
        #{item.isPayWait,jdbcType=CHAR}, #{item.processType,jdbcType=TINYINT}, #{item.claimReportSource,jdbcType=VARCHAR}, 
        #{item.isIncludeAttachment,jdbcType=VARCHAR}, #{item.openPolicyNo,jdbcType=VARCHAR}, 
        #{item.syncFlag,jdbcType=VARCHAR}, #{item.syncDate,jdbcType=TIMESTAMP}, #{item.channelAttachmentIsFinished,jdbcType=CHAR}, 
        #{item.attachmentLastDate,jdbcType=TIMESTAMP}, #{item.isNeedCustomize,jdbcType=CHAR}, 
        #{item.isLossElecAttachment,jdbcType=CHAR}, #{item.lossAttachmentLogId,jdbcType=BIGINT}, 
        #{item.claimTimeliness,jdbcType=INTEGER}, #{item.isAttachmentCompleted,jdbcType=CHAR}, 
        #{item.isTotalLossPolicy,jdbcType=CHAR}, #{item.trafficInsuranceType,jdbcType=VARCHAR}, 
        #{item.reportBatchNo,jdbcType=VARCHAR}, #{item.isCompensation,jdbcType=CHAR}, #{item.isLitigate,jdbcType=VARCHAR}, 
        #{item.complaintType,jdbcType=VARCHAR}, #{item.isHandle,jdbcType=CHAR}, #{item.isBankCode,jdbcType=CHAR}, 
        #{item.claimCaseNo,jdbcType=VARCHAR}, #{item.sendMaterialStatus,jdbcType=VARCHAR}, 
        #{item.isSendMaterial,jdbcType=VARCHAR}, #{item.isFreeSendMaterial,jdbcType=VARCHAR}, 
        #{item.hyCloseType,jdbcType=VARCHAR}, #{item.didiSecurityCardStatus,jdbcType=VARCHAR}, 
        #{item.cyLossType,jdbcType=VARCHAR}, #{item.cyPushStatus,jdbcType=VARCHAR}, #{item.surveyType,jdbcType=VARCHAR}, 
        #{item.dadaAccidentType,jdbcType=VARCHAR}, #{item.accidentReason,jdbcType=VARCHAR}, 
        #{item.transportation,jdbcType=VARCHAR}, #{item.riskScore,jdbcType=VARCHAR}, #{item.yzRiskScore,jdbcType=VARCHAR}, 
        #{item.prePaidAuditStatus,jdbcType=VARCHAR}, #{item.accidentFourLevelLossCause,jdbcType=VARCHAR}, 
        #{item.isOfflineCompensation,jdbcType=CHAR}, #{item.occupation,jdbcType=VARCHAR}, 
        #{item.reserveAuditStatus,jdbcType=TINYINT}, #{item.reserveAuditor,jdbcType=VARCHAR}, 
        #{item.isHealthRelation,jdbcType=CHAR}, #{item.rollBackTimeliness,jdbcType=VARCHAR}, 
        #{item.rollBackDate,jdbcType=TIMESTAMP}, #{item.boundStatus,jdbcType=TINYINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_claim (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'policy_id'.toString() == column.value">
          #{item.policyId,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'loss_cause'.toString() == column.value">
          #{item.lossCause,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'open_user_id'.toString() == column.value">
          #{item.openUserId,jdbcType=VARCHAR}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'cargo_user_register_id'.toString() == column.value">
          #{item.cargoUserRegisterId,jdbcType=BIGINT}
        </if>
        <if test="'report_amount'.toString() == column.value">
          #{item.reportAmount,jdbcType=VARCHAR}
        </if>
        <if test="'channel_report_no'.toString() == column.value">
          #{item.channelReportNo,jdbcType=VARCHAR}
        </if>
        <if test="'accident_desc'.toString() == column.value">
          #{item.accidentDesc,jdbcType=VARCHAR}
        </if>
        <if test="'account_type'.toString() == column.value">
          #{item.accountType,jdbcType=TINYINT}
        </if>
        <if test="'account_bank'.toString() == column.value">
          #{item.accountBank,jdbcType=VARCHAR}
        </if>
        <if test="'payee_name'.toString() == column.value">
          #{item.payeeName,jdbcType=VARCHAR}
        </if>
        <if test="'account_no'.toString() == column.value">
          #{item.accountNo,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_url'.toString() == column.value">
          #{item.attachmentUrl,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'report_date'.toString() == column.value">
          #{item.reportDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'accident_date'.toString() == column.value">
          #{item.accidentDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'accident_place'.toString() == column.value">
          #{item.accidentPlace,jdbcType=VARCHAR}
        </if>
        <if test="'claim_currency'.toString() == column.value">
          #{item.claimCurrency,jdbcType=VARCHAR}
        </if>
        <if test="'report_source'.toString() == column.value">
          #{item.reportSource,jdbcType=TINYINT}
        </if>
        <if test="'claimant_name'.toString() == column.value">
          #{item.claimantName,jdbcType=VARCHAR}
        </if>
        <if test="'claimant_phone'.toString() == column.value">
          #{item.claimantPhone,jdbcType=VARCHAR}
        </if>
        <if test="'claimant_email'.toString() == column.value">
          #{item.claimantEmail,jdbcType=VARCHAR}
        </if>
        <if test="'register_date'.toString() == column.value">
          #{item.registerDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'claim_no'.toString() == column.value">
          #{item.claimNo,jdbcType=VARCHAR}
        </if>
        <if test="'is_auto_register'.toString() == column.value">
          #{item.isAutoRegister,jdbcType=CHAR}
        </if>
        <if test="'is_need_assess'.toString() == column.value">
          #{item.isNeedAssess,jdbcType=CHAR}
        </if>
        <if test="'dispatcher_date'.toString() == column.value">
          #{item.dispatcherDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'dispatcher_no'.toString() == column.value">
          #{item.dispatcherNo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=BIGINT}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'channel_name'.toString() == column.value">
          #{item.channelName,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'claim_type'.toString() == column.value">
          #{item.claimType,jdbcType=TINYINT}
        </if>
        <if test="'register_user'.toString() == column.value">
          #{item.registerUser,jdbcType=VARCHAR}
        </if>
        <if test="'agency_user_id'.toString() == column.value">
          #{item.agencyUserId,jdbcType=BIGINT}
        </if>
        <if test="'policy_insurant'.toString() == column.value">
          #{item.policyInsurant,jdbcType=VARCHAR}
        </if>
        <if test="'auditor'.toString() == column.value">
          #{item.auditor,jdbcType=VARCHAR}
        </if>
        <if test="'insurance_type_code'.toString() == column.value">
          #{item.insuranceTypeCode,jdbcType=TINYINT}
        </if>
        <if test="'campaign_full_name'.toString() == column.value">
          #{item.campaignFullName,jdbcType=VARCHAR}
        </if>
        <if test="'loss_amount'.toString() == column.value">
          #{item.lossAmount,jdbcType=VARCHAR}
        </if>
        <if test="'report_user_name'.toString() == column.value">
          #{item.reportUserName,jdbcType=VARCHAR}
        </if>
        <if test="'reportor_relinsured'.toString() == column.value">
          #{item.reportorRelinsured,jdbcType=VARCHAR}
        </if>
        <if test="'reportor_phone'.toString() == column.value">
          #{item.reportorPhone,jdbcType=VARCHAR}
        </if>
        <if test="'reportor_email'.toString() == column.value">
          #{item.reportorEmail,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_name'.toString() == column.value">
          #{item.hospitalName,jdbcType=VARCHAR}
        </if>
        <if test="'audit_date'.toString() == column.value">
          #{item.auditDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'audit_amount'.toString() == column.value">
          #{item.auditAmount,jdbcType=VARCHAR}
        </if>
        <if test="'audit_conclusion'.toString() == column.value">
          #{item.auditConclusion,jdbcType=VARCHAR}
        </if>
        <if test="'trans_account_type'.toString() == column.value">
          #{item.transAccountType,jdbcType=TINYINT}
        </if>
        <if test="'account_bank_area'.toString() == column.value">
          #{item.accountBankArea,jdbcType=VARCHAR}
        </if>
        <if test="'loss_cause_name'.toString() == column.value">
          #{item.lossCauseName,jdbcType=VARCHAR}
        </if>
        <if test="'is_need_notice'.toString() == column.value">
          #{item.isNeedNotice,jdbcType=VARCHAR}
        </if>
        <if test="'is_hang_up'.toString() == column.value">
          #{item.isHangUp,jdbcType=CHAR}
        </if>
        <if test="'insure_place'.toString() == column.value">
          #{item.insurePlace,jdbcType=VARCHAR}
        </if>
        <if test="'is_allow_report_roll_back'.toString() == column.value">
          #{item.isAllowReportRollBack,jdbcType=VARCHAR}
        </if>
        <if test="'is_reopen'.toString() == column.value">
          #{item.isReopen,jdbcType=CHAR}
        </if>
        <if test="'is_closed_msg_informed'.toString() == column.value">
          #{item.isClosedMsgInformed,jdbcType=CHAR}
        </if>
        <if test="'core_claim_status'.toString() == column.value">
          #{item.coreClaimStatus,jdbcType=VARCHAR}
        </if>
        <if test="'core_synctime'.toString() == column.value">
          #{item.coreSynctime,jdbcType=TIMESTAMP}
        </if>
        <if test="'close_date'.toString() == column.value">
          #{item.closeDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'pay_state'.toString() == column.value">
          #{item.payState,jdbcType=TINYINT}
        </if>
        <if test="'payment_date'.toString() == column.value">
          #{item.paymentDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_pay_failed'.toString() == column.value">
          #{item.isPayFailed,jdbcType=CHAR}
        </if>
        <if test="'is_pay_wait'.toString() == column.value">
          #{item.isPayWait,jdbcType=CHAR}
        </if>
        <if test="'process_type'.toString() == column.value">
          #{item.processType,jdbcType=TINYINT}
        </if>
        <if test="'claim_report_source'.toString() == column.value">
          #{item.claimReportSource,jdbcType=VARCHAR}
        </if>
        <if test="'is_include_attachment'.toString() == column.value">
          #{item.isIncludeAttachment,jdbcType=VARCHAR}
        </if>
        <if test="'open_policy_no'.toString() == column.value">
          #{item.openPolicyNo,jdbcType=VARCHAR}
        </if>
        <if test="'sync_flag'.toString() == column.value">
          #{item.syncFlag,jdbcType=VARCHAR}
        </if>
        <if test="'sync_date'.toString() == column.value">
          #{item.syncDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'channel_attachment_is_finished'.toString() == column.value">
          #{item.channelAttachmentIsFinished,jdbcType=CHAR}
        </if>
        <if test="'attachment_last_date'.toString() == column.value">
          #{item.attachmentLastDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_need_customize'.toString() == column.value">
          #{item.isNeedCustomize,jdbcType=CHAR}
        </if>
        <if test="'is_loss_elec_attachment'.toString() == column.value">
          #{item.isLossElecAttachment,jdbcType=CHAR}
        </if>
        <if test="'loss_attachment_log_id'.toString() == column.value">
          #{item.lossAttachmentLogId,jdbcType=BIGINT}
        </if>
        <if test="'claim_timeliness'.toString() == column.value">
          #{item.claimTimeliness,jdbcType=INTEGER}
        </if>
        <if test="'is_attachment_completed'.toString() == column.value">
          #{item.isAttachmentCompleted,jdbcType=CHAR}
        </if>
        <if test="'is_total_loss_policy'.toString() == column.value">
          #{item.isTotalLossPolicy,jdbcType=CHAR}
        </if>
        <if test="'traffic_insurance_type'.toString() == column.value">
          #{item.trafficInsuranceType,jdbcType=VARCHAR}
        </if>
        <if test="'report_batch_no'.toString() == column.value">
          #{item.reportBatchNo,jdbcType=VARCHAR}
        </if>
        <if test="'is_compensation'.toString() == column.value">
          #{item.isCompensation,jdbcType=CHAR}
        </if>
        <if test="'is_litigate'.toString() == column.value">
          #{item.isLitigate,jdbcType=VARCHAR}
        </if>
        <if test="'complaint_type'.toString() == column.value">
          #{item.complaintType,jdbcType=VARCHAR}
        </if>
        <if test="'is_handle'.toString() == column.value">
          #{item.isHandle,jdbcType=CHAR}
        </if>
        <if test="'is_bank_code'.toString() == column.value">
          #{item.isBankCode,jdbcType=CHAR}
        </if>
        <if test="'claim_case_no'.toString() == column.value">
          #{item.claimCaseNo,jdbcType=VARCHAR}
        </if>
        <if test="'send_material_status'.toString() == column.value">
          #{item.sendMaterialStatus,jdbcType=VARCHAR}
        </if>
        <if test="'is_send_material'.toString() == column.value">
          #{item.isSendMaterial,jdbcType=VARCHAR}
        </if>
        <if test="'is_free_send_material'.toString() == column.value">
          #{item.isFreeSendMaterial,jdbcType=VARCHAR}
        </if>
        <if test="'hy_close_type'.toString() == column.value">
          #{item.hyCloseType,jdbcType=VARCHAR}
        </if>
        <if test="'didi_security_card_status'.toString() == column.value">
          #{item.didiSecurityCardStatus,jdbcType=VARCHAR}
        </if>
        <if test="'cy_loss_type'.toString() == column.value">
          #{item.cyLossType,jdbcType=VARCHAR}
        </if>
        <if test="'cy_push_status'.toString() == column.value">
          #{item.cyPushStatus,jdbcType=VARCHAR}
        </if>
        <if test="'survey_type'.toString() == column.value">
          #{item.surveyType,jdbcType=VARCHAR}
        </if>
        <if test="'dada_accident_type'.toString() == column.value">
          #{item.dadaAccidentType,jdbcType=VARCHAR}
        </if>
        <if test="'accident_reason'.toString() == column.value">
          #{item.accidentReason,jdbcType=VARCHAR}
        </if>
        <if test="'transportation'.toString() == column.value">
          #{item.transportation,jdbcType=VARCHAR}
        </if>
        <if test="'risk_score'.toString() == column.value">
          #{item.riskScore,jdbcType=VARCHAR}
        </if>
        <if test="'yz_risk_score'.toString() == column.value">
          #{item.yzRiskScore,jdbcType=VARCHAR}
        </if>
        <if test="'pre_paid_audit_status'.toString() == column.value">
          #{item.prePaidAuditStatus,jdbcType=VARCHAR}
        </if>
        <if test="'accident_four_level_loss_cause'.toString() == column.value">
          #{item.accidentFourLevelLossCause,jdbcType=VARCHAR}
        </if>
        <if test="'is_offline_compensation'.toString() == column.value">
          #{item.isOfflineCompensation,jdbcType=CHAR}
        </if>
        <if test="'occupation'.toString() == column.value">
          #{item.occupation,jdbcType=VARCHAR}
        </if>
        <if test="'reserve_audit_status'.toString() == column.value">
          #{item.reserveAuditStatus,jdbcType=TINYINT}
        </if>
        <if test="'reserve_auditor'.toString() == column.value">
          #{item.reserveAuditor,jdbcType=VARCHAR}
        </if>
        <if test="'is_health_relation'.toString() == column.value">
          #{item.isHealthRelation,jdbcType=CHAR}
        </if>
        <if test="'roll_back_timeliness'.toString() == column.value">
          #{item.rollBackTimeliness,jdbcType=VARCHAR}
        </if>
        <if test="'roll_back_date'.toString() == column.value">
          #{item.rollBackDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'bound_status'.toString() == column.value">
          #{item.boundStatus,jdbcType=TINYINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>