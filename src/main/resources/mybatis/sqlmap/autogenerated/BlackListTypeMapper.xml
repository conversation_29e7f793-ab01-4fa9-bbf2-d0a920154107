<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.BlackListTypeMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.BlackListTypeDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="first_level_reason" jdbcType="VARCHAR" property="firstLevelReason" />
    <result column="insurance_type_code" jdbcType="INTEGER" property="insuranceTypeCode" />
    <result column="secend_level_reason" jdbcType="VARCHAR" property="secendLevelReason" />
    <result column="black_list_code" jdbcType="VARCHAR" property="blackListCode" />
    <result column="black_list_time" jdbcType="INTEGER" property="blackListTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, first_level_reason, insurance_type_code, secend_level_reason, black_list_code, 
    black_list_time, remark, is_deleted, gmt_created, gmt_modified, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.BlackListTypeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from black_list_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from black_list_type
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.BlackListTypeDO">
    insert into black_list_type (id, first_level_reason, insurance_type_code, 
      secend_level_reason, black_list_code, black_list_time, 
      remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier
      )
    values (#{id,jdbcType=BIGINT}, #{firstLevelReason,jdbcType=VARCHAR}, #{insuranceTypeCode,jdbcType=INTEGER}, 
      #{secendLevelReason,jdbcType=VARCHAR}, #{blackListCode,jdbcType=VARCHAR}, #{blackListTime,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.BlackListTypeDO">
    insert into black_list_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="firstLevelReason != null">
        first_level_reason,
      </if>
      <if test="insuranceTypeCode != null">
        insurance_type_code,
      </if>
      <if test="secendLevelReason != null">
        secend_level_reason,
      </if>
      <if test="blackListCode != null">
        black_list_code,
      </if>
      <if test="blackListTime != null">
        black_list_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="firstLevelReason != null">
        #{firstLevelReason,jdbcType=VARCHAR},
      </if>
      <if test="insuranceTypeCode != null">
        #{insuranceTypeCode,jdbcType=INTEGER},
      </if>
      <if test="secendLevelReason != null">
        #{secendLevelReason,jdbcType=VARCHAR},
      </if>
      <if test="blackListCode != null">
        #{blackListCode,jdbcType=VARCHAR},
      </if>
      <if test="blackListTime != null">
        #{blackListTime,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.BlackListTypeExample" resultType="java.lang.Long">
    select count(*) from black_list_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update black_list_type
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.firstLevelReason != null">
        first_level_reason = #{record.firstLevelReason,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceTypeCode != null">
        insurance_type_code = #{record.insuranceTypeCode,jdbcType=INTEGER},
      </if>
      <if test="record.secendLevelReason != null">
        secend_level_reason = #{record.secendLevelReason,jdbcType=VARCHAR},
      </if>
      <if test="record.blackListCode != null">
        black_list_code = #{record.blackListCode,jdbcType=VARCHAR},
      </if>
      <if test="record.blackListTime != null">
        black_list_time = #{record.blackListTime,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update black_list_type
    set id = #{record.id,jdbcType=BIGINT},
      first_level_reason = #{record.firstLevelReason,jdbcType=VARCHAR},
      insurance_type_code = #{record.insuranceTypeCode,jdbcType=INTEGER},
      secend_level_reason = #{record.secendLevelReason,jdbcType=VARCHAR},
      black_list_code = #{record.blackListCode,jdbcType=VARCHAR},
      black_list_time = #{record.blackListTime,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.BlackListTypeDO">
    update black_list_type
    <set>
      <if test="firstLevelReason != null">
        first_level_reason = #{firstLevelReason,jdbcType=VARCHAR},
      </if>
      <if test="insuranceTypeCode != null">
        insurance_type_code = #{insuranceTypeCode,jdbcType=INTEGER},
      </if>
      <if test="secendLevelReason != null">
        secend_level_reason = #{secendLevelReason,jdbcType=VARCHAR},
      </if>
      <if test="blackListCode != null">
        black_list_code = #{blackListCode,jdbcType=VARCHAR},
      </if>
      <if test="blackListTime != null">
        black_list_time = #{blackListTime,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.BlackListTypeDO">
    update black_list_type
    set first_level_reason = #{firstLevelReason,jdbcType=VARCHAR},
      insurance_type_code = #{insuranceTypeCode,jdbcType=INTEGER},
      secend_level_reason = #{secendLevelReason,jdbcType=VARCHAR},
      black_list_code = #{blackListCode,jdbcType=VARCHAR},
      black_list_time = #{blackListTime,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into black_list_type
    (id, first_level_reason, insurance_type_code, secend_level_reason, black_list_code, 
      black_list_time, remark, is_deleted, gmt_created, gmt_modified, creator, modifier
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.firstLevelReason,jdbcType=VARCHAR}, #{item.insuranceTypeCode,jdbcType=INTEGER}, 
        #{item.secendLevelReason,jdbcType=VARCHAR}, #{item.blackListCode,jdbcType=VARCHAR}, 
        #{item.blackListTime,jdbcType=INTEGER}, #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into black_list_type (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'first_level_reason'.toString() == column.value">
          #{item.firstLevelReason,jdbcType=VARCHAR}
        </if>
        <if test="'insurance_type_code'.toString() == column.value">
          #{item.insuranceTypeCode,jdbcType=INTEGER}
        </if>
        <if test="'secend_level_reason'.toString() == column.value">
          #{item.secendLevelReason,jdbcType=VARCHAR}
        </if>
        <if test="'black_list_code'.toString() == column.value">
          #{item.blackListCode,jdbcType=VARCHAR}
        </if>
        <if test="'black_list_time'.toString() == column.value">
          #{item.blackListTime,jdbcType=INTEGER}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>