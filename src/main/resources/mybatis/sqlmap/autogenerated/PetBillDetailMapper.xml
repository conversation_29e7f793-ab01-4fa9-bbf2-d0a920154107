<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.PetBillDetailMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.PetBillDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="attachment_id" jdbcType="BIGINT" property="attachmentId" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="item_amount" jdbcType="DECIMAL" property="itemAmount" />
    <result column="item_price" jdbcType="DECIMAL" property="itemPrice" />
    <result column="item_count" jdbcType="DECIMAL" property="itemCount" />
    <result column="excluded" jdbcType="BIT" property="excluded" />
    <result column="excluded_amount" jdbcType="DECIMAL" property="excludedAmount" />
    <result column="reasonable_amount" jdbcType="DECIMAL" property="reasonableAmount" />
    <result column="latest" jdbcType="BIT" property="latest" />
    <result column="modify_flag" jdbcType="BIGINT" property="modifyFlag" />
    <result column="modify_operation" jdbcType="VARCHAR" property="modifyOperation" />
    <result column="modification_reason" jdbcType="VARCHAR" property="modificationReason" />
    <result column="automatic" jdbcType="BIT" property="automatic" />
    <result column="matched_item_id" jdbcType="VARCHAR" property="matchedItemId" />
    <result column="matched_title" jdbcType="VARCHAR" property="matchedTitle" />
    <result column="matched_type" jdbcType="BIGINT" property="matchedType" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, attachment_id, bill_no, serial_no, item_name, item_amount, item_price, 
    item_count, excluded, excluded_amount, reasonable_amount, latest, modify_flag, modify_operation, 
    modification_reason, automatic, matched_item_id, matched_title, matched_type, extra_info, 
    creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.PetBillDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pet_bill_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pet_bill_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.PetBillDetailDO">
    insert into pet_bill_detail (id, report_no, attachment_id, 
      bill_no, serial_no, item_name, 
      item_amount, item_price, item_count, 
      excluded, excluded_amount, reasonable_amount, 
      latest, modify_flag, modify_operation, 
      modification_reason, automatic, matched_item_id, 
      matched_title, matched_type, extra_info, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{attachmentId,jdbcType=BIGINT}, 
      #{billNo,jdbcType=VARCHAR}, #{serialNo,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR}, 
      #{itemAmount,jdbcType=DECIMAL}, #{itemPrice,jdbcType=DECIMAL}, #{itemCount,jdbcType=DECIMAL}, 
      #{excluded,jdbcType=BIT}, #{excludedAmount,jdbcType=DECIMAL}, #{reasonableAmount,jdbcType=DECIMAL}, 
      #{latest,jdbcType=BIT}, #{modifyFlag,jdbcType=BIGINT}, #{modifyOperation,jdbcType=VARCHAR}, 
      #{modificationReason,jdbcType=VARCHAR}, #{automatic,jdbcType=BIT}, #{matchedItemId,jdbcType=VARCHAR}, 
      #{matchedTitle,jdbcType=VARCHAR}, #{matchedType,jdbcType=BIGINT}, #{extraInfo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.PetBillDetailDO">
    insert into pet_bill_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="attachmentId != null">
        attachment_id,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="itemName != null">
        item_name,
      </if>
      <if test="itemAmount != null">
        item_amount,
      </if>
      <if test="itemPrice != null">
        item_price,
      </if>
      <if test="itemCount != null">
        item_count,
      </if>
      <if test="excluded != null">
        excluded,
      </if>
      <if test="excludedAmount != null">
        excluded_amount,
      </if>
      <if test="reasonableAmount != null">
        reasonable_amount,
      </if>
      <if test="latest != null">
        latest,
      </if>
      <if test="modifyFlag != null">
        modify_flag,
      </if>
      <if test="modifyOperation != null">
        modify_operation,
      </if>
      <if test="modificationReason != null">
        modification_reason,
      </if>
      <if test="automatic != null">
        automatic,
      </if>
      <if test="matchedItemId != null">
        matched_item_id,
      </if>
      <if test="matchedTitle != null">
        matched_title,
      </if>
      <if test="matchedType != null">
        matched_type,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="attachmentId != null">
        #{attachmentId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemAmount != null">
        #{itemAmount,jdbcType=DECIMAL},
      </if>
      <if test="itemPrice != null">
        #{itemPrice,jdbcType=DECIMAL},
      </if>
      <if test="itemCount != null">
        #{itemCount,jdbcType=DECIMAL},
      </if>
      <if test="excluded != null">
        #{excluded,jdbcType=BIT},
      </if>
      <if test="excludedAmount != null">
        #{excludedAmount,jdbcType=DECIMAL},
      </if>
      <if test="reasonableAmount != null">
        #{reasonableAmount,jdbcType=DECIMAL},
      </if>
      <if test="latest != null">
        #{latest,jdbcType=BIT},
      </if>
      <if test="modifyFlag != null">
        #{modifyFlag,jdbcType=BIGINT},
      </if>
      <if test="modifyOperation != null">
        #{modifyOperation,jdbcType=VARCHAR},
      </if>
      <if test="modificationReason != null">
        #{modificationReason,jdbcType=VARCHAR},
      </if>
      <if test="automatic != null">
        #{automatic,jdbcType=BIT},
      </if>
      <if test="matchedItemId != null">
        #{matchedItemId,jdbcType=VARCHAR},
      </if>
      <if test="matchedTitle != null">
        #{matchedTitle,jdbcType=VARCHAR},
      </if>
      <if test="matchedType != null">
        #{matchedType,jdbcType=BIGINT},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.PetBillDetailExample" resultType="java.lang.Long">
    select count(*) from pet_bill_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pet_bill_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentId != null">
        attachment_id = #{record.attachmentId,jdbcType=BIGINT},
      </if>
      <if test="record.billNo != null">
        bill_no = #{record.billNo,jdbcType=VARCHAR},
      </if>
      <if test="record.serialNo != null">
        serial_no = #{record.serialNo,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null">
        item_name = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemAmount != null">
        item_amount = #{record.itemAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.itemPrice != null">
        item_price = #{record.itemPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.itemCount != null">
        item_count = #{record.itemCount,jdbcType=DECIMAL},
      </if>
      <if test="record.excluded != null">
        excluded = #{record.excluded,jdbcType=BIT},
      </if>
      <if test="record.excludedAmount != null">
        excluded_amount = #{record.excludedAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.reasonableAmount != null">
        reasonable_amount = #{record.reasonableAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.latest != null">
        latest = #{record.latest,jdbcType=BIT},
      </if>
      <if test="record.modifyFlag != null">
        modify_flag = #{record.modifyFlag,jdbcType=BIGINT},
      </if>
      <if test="record.modifyOperation != null">
        modify_operation = #{record.modifyOperation,jdbcType=VARCHAR},
      </if>
      <if test="record.modificationReason != null">
        modification_reason = #{record.modificationReason,jdbcType=VARCHAR},
      </if>
      <if test="record.automatic != null">
        automatic = #{record.automatic,jdbcType=BIT},
      </if>
      <if test="record.matchedItemId != null">
        matched_item_id = #{record.matchedItemId,jdbcType=VARCHAR},
      </if>
      <if test="record.matchedTitle != null">
        matched_title = #{record.matchedTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.matchedType != null">
        matched_type = #{record.matchedType,jdbcType=BIGINT},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pet_bill_detail
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      attachment_id = #{record.attachmentId,jdbcType=BIGINT},
      bill_no = #{record.billNo,jdbcType=VARCHAR},
      serial_no = #{record.serialNo,jdbcType=VARCHAR},
      item_name = #{record.itemName,jdbcType=VARCHAR},
      item_amount = #{record.itemAmount,jdbcType=DECIMAL},
      item_price = #{record.itemPrice,jdbcType=DECIMAL},
      item_count = #{record.itemCount,jdbcType=DECIMAL},
      excluded = #{record.excluded,jdbcType=BIT},
      excluded_amount = #{record.excludedAmount,jdbcType=DECIMAL},
      reasonable_amount = #{record.reasonableAmount,jdbcType=DECIMAL},
      latest = #{record.latest,jdbcType=BIT},
      modify_flag = #{record.modifyFlag,jdbcType=BIGINT},
      modify_operation = #{record.modifyOperation,jdbcType=VARCHAR},
      modification_reason = #{record.modificationReason,jdbcType=VARCHAR},
      automatic = #{record.automatic,jdbcType=BIT},
      matched_item_id = #{record.matchedItemId,jdbcType=VARCHAR},
      matched_title = #{record.matchedTitle,jdbcType=VARCHAR},
      matched_type = #{record.matchedType,jdbcType=BIGINT},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.PetBillDetailDO">
    update pet_bill_detail
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="attachmentId != null">
        attachment_id = #{attachmentId,jdbcType=BIGINT},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        item_name = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemAmount != null">
        item_amount = #{itemAmount,jdbcType=DECIMAL},
      </if>
      <if test="itemPrice != null">
        item_price = #{itemPrice,jdbcType=DECIMAL},
      </if>
      <if test="itemCount != null">
        item_count = #{itemCount,jdbcType=DECIMAL},
      </if>
      <if test="excluded != null">
        excluded = #{excluded,jdbcType=BIT},
      </if>
      <if test="excludedAmount != null">
        excluded_amount = #{excludedAmount,jdbcType=DECIMAL},
      </if>
      <if test="reasonableAmount != null">
        reasonable_amount = #{reasonableAmount,jdbcType=DECIMAL},
      </if>
      <if test="latest != null">
        latest = #{latest,jdbcType=BIT},
      </if>
      <if test="modifyFlag != null">
        modify_flag = #{modifyFlag,jdbcType=BIGINT},
      </if>
      <if test="modifyOperation != null">
        modify_operation = #{modifyOperation,jdbcType=VARCHAR},
      </if>
      <if test="modificationReason != null">
        modification_reason = #{modificationReason,jdbcType=VARCHAR},
      </if>
      <if test="automatic != null">
        automatic = #{automatic,jdbcType=BIT},
      </if>
      <if test="matchedItemId != null">
        matched_item_id = #{matchedItemId,jdbcType=VARCHAR},
      </if>
      <if test="matchedTitle != null">
        matched_title = #{matchedTitle,jdbcType=VARCHAR},
      </if>
      <if test="matchedType != null">
        matched_type = #{matchedType,jdbcType=BIGINT},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.PetBillDetailDO">
    update pet_bill_detail
    set report_no = #{reportNo,jdbcType=VARCHAR},
      attachment_id = #{attachmentId,jdbcType=BIGINT},
      bill_no = #{billNo,jdbcType=VARCHAR},
      serial_no = #{serialNo,jdbcType=VARCHAR},
      item_name = #{itemName,jdbcType=VARCHAR},
      item_amount = #{itemAmount,jdbcType=DECIMAL},
      item_price = #{itemPrice,jdbcType=DECIMAL},
      item_count = #{itemCount,jdbcType=DECIMAL},
      excluded = #{excluded,jdbcType=BIT},
      excluded_amount = #{excludedAmount,jdbcType=DECIMAL},
      reasonable_amount = #{reasonableAmount,jdbcType=DECIMAL},
      latest = #{latest,jdbcType=BIT},
      modify_flag = #{modifyFlag,jdbcType=BIGINT},
      modify_operation = #{modifyOperation,jdbcType=VARCHAR},
      modification_reason = #{modificationReason,jdbcType=VARCHAR},
      automatic = #{automatic,jdbcType=BIT},
      matched_item_id = #{matchedItemId,jdbcType=VARCHAR},
      matched_title = #{matchedTitle,jdbcType=VARCHAR},
      matched_type = #{matchedType,jdbcType=BIGINT},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into pet_bill_detail
    (id, report_no, attachment_id, bill_no, serial_no, item_name, item_amount, item_price, 
      item_count, excluded, excluded_amount, reasonable_amount, latest, modify_flag, 
      modify_operation, modification_reason, automatic, matched_item_id, matched_title, 
      matched_type, extra_info, creator, modifier, gmt_created, gmt_modified, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.attachmentId,jdbcType=BIGINT}, 
        #{item.billNo,jdbcType=VARCHAR}, #{item.serialNo,jdbcType=VARCHAR}, #{item.itemName,jdbcType=VARCHAR}, 
        #{item.itemAmount,jdbcType=DECIMAL}, #{item.itemPrice,jdbcType=DECIMAL}, #{item.itemCount,jdbcType=DECIMAL}, 
        #{item.excluded,jdbcType=BIT}, #{item.excludedAmount,jdbcType=DECIMAL}, #{item.reasonableAmount,jdbcType=DECIMAL}, 
        #{item.latest,jdbcType=BIT}, #{item.modifyFlag,jdbcType=BIGINT}, #{item.modifyOperation,jdbcType=VARCHAR}, 
        #{item.modificationReason,jdbcType=VARCHAR}, #{item.automatic,jdbcType=BIT}, #{item.matchedItemId,jdbcType=VARCHAR}, 
        #{item.matchedTitle,jdbcType=VARCHAR}, #{item.matchedType,jdbcType=BIGINT}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into pet_bill_detail (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_id'.toString() == column.value">
          #{item.attachmentId,jdbcType=BIGINT}
        </if>
        <if test="'bill_no'.toString() == column.value">
          #{item.billNo,jdbcType=VARCHAR}
        </if>
        <if test="'serial_no'.toString() == column.value">
          #{item.serialNo,jdbcType=VARCHAR}
        </if>
        <if test="'item_name'.toString() == column.value">
          #{item.itemName,jdbcType=VARCHAR}
        </if>
        <if test="'item_amount'.toString() == column.value">
          #{item.itemAmount,jdbcType=DECIMAL}
        </if>
        <if test="'item_price'.toString() == column.value">
          #{item.itemPrice,jdbcType=DECIMAL}
        </if>
        <if test="'item_count'.toString() == column.value">
          #{item.itemCount,jdbcType=DECIMAL}
        </if>
        <if test="'excluded'.toString() == column.value">
          #{item.excluded,jdbcType=BIT}
        </if>
        <if test="'excluded_amount'.toString() == column.value">
          #{item.excludedAmount,jdbcType=DECIMAL}
        </if>
        <if test="'reasonable_amount'.toString() == column.value">
          #{item.reasonableAmount,jdbcType=DECIMAL}
        </if>
        <if test="'latest'.toString() == column.value">
          #{item.latest,jdbcType=BIT}
        </if>
        <if test="'modify_flag'.toString() == column.value">
          #{item.modifyFlag,jdbcType=BIGINT}
        </if>
        <if test="'modify_operation'.toString() == column.value">
          #{item.modifyOperation,jdbcType=VARCHAR}
        </if>
        <if test="'modification_reason'.toString() == column.value">
          #{item.modificationReason,jdbcType=VARCHAR}
        </if>
        <if test="'automatic'.toString() == column.value">
          #{item.automatic,jdbcType=BIT}
        </if>
        <if test="'matched_item_id'.toString() == column.value">
          #{item.matchedItemId,jdbcType=VARCHAR}
        </if>
        <if test="'matched_title'.toString() == column.value">
          #{item.matchedTitle,jdbcType=VARCHAR}
        </if>
        <if test="'matched_type'.toString() == column.value">
          #{item.matchedType,jdbcType=BIGINT}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>