<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.EtccAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.EtccAttachmentDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName" />
    <result column="attachment_type" jdbcType="TINYINT" property="attachmentType" />
    <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl" />
    <result column="original_attachment_url" jdbcType="VARCHAR" property="originalAttachmentUrl" />
    <result column="identify_attachment_type" jdbcType="TINYINT" property="identifyAttachmentType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, attachment_name, attachment_type, attachment_url, original_attachment_url, identify_attachment_type, 
    remark, is_deleted, gmt_created, gmt_modified, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.EtccAttachmentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from etcc_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from etcc_attachment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.EtccAttachmentDO">
    insert into etcc_attachment (id, attachment_name, attachment_type, 
      attachment_url, original_attachment_url, identify_attachment_type, 
      remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier
      )
    values (#{id,jdbcType=BIGINT}, #{attachmentName,jdbcType=VARCHAR}, #{attachmentType,jdbcType=TINYINT}, 
      #{attachmentUrl,jdbcType=VARCHAR}, #{originalAttachmentUrl,jdbcType=VARCHAR}, #{identifyAttachmentType,jdbcType=TINYINT}, 
      #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.EtccAttachmentDO">
    insert into etcc_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="attachmentName != null">
        attachment_name,
      </if>
      <if test="attachmentType != null">
        attachment_type,
      </if>
      <if test="attachmentUrl != null">
        attachment_url,
      </if>
      <if test="originalAttachmentUrl != null">
        original_attachment_url,
      </if>
      <if test="identifyAttachmentType != null">
        identify_attachment_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="attachmentName != null">
        #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="attachmentType != null">
        #{attachmentType,jdbcType=TINYINT},
      </if>
      <if test="attachmentUrl != null">
        #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="originalAttachmentUrl != null">
        #{originalAttachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="identifyAttachmentType != null">
        #{identifyAttachmentType,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.EtccAttachmentExample" resultType="java.lang.Long">
    select count(*) from etcc_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update etcc_attachment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.attachmentName != null">
        attachment_name = #{record.attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentType != null">
        attachment_type = #{record.attachmentType,jdbcType=TINYINT},
      </if>
      <if test="record.attachmentUrl != null">
        attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.originalAttachmentUrl != null">
        original_attachment_url = #{record.originalAttachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.identifyAttachmentType != null">
        identify_attachment_type = #{record.identifyAttachmentType,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update etcc_attachment
    set id = #{record.id,jdbcType=BIGINT},
      attachment_name = #{record.attachmentName,jdbcType=VARCHAR},
      attachment_type = #{record.attachmentType,jdbcType=TINYINT},
      attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      original_attachment_url = #{record.originalAttachmentUrl,jdbcType=VARCHAR},
      identify_attachment_type = #{record.identifyAttachmentType,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.EtccAttachmentDO">
    update etcc_attachment
    <set>
      <if test="attachmentName != null">
        attachment_name = #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="attachmentType != null">
        attachment_type = #{attachmentType,jdbcType=TINYINT},
      </if>
      <if test="attachmentUrl != null">
        attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="originalAttachmentUrl != null">
        original_attachment_url = #{originalAttachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="identifyAttachmentType != null">
        identify_attachment_type = #{identifyAttachmentType,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.EtccAttachmentDO">
    update etcc_attachment
    set attachment_name = #{attachmentName,jdbcType=VARCHAR},
      attachment_type = #{attachmentType,jdbcType=TINYINT},
      attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      original_attachment_url = #{originalAttachmentUrl,jdbcType=VARCHAR},
      identify_attachment_type = #{identifyAttachmentType,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into etcc_attachment
    (id, attachment_name, attachment_type, attachment_url, original_attachment_url, identify_attachment_type, 
      remark, is_deleted, gmt_created, gmt_modified, creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.attachmentName,jdbcType=VARCHAR}, #{item.attachmentType,jdbcType=TINYINT}, 
        #{item.attachmentUrl,jdbcType=VARCHAR}, #{item.originalAttachmentUrl,jdbcType=VARCHAR}, 
        #{item.identifyAttachmentType,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into etcc_attachment (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'attachment_name'.toString() == column.value">
          #{item.attachmentName,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_type'.toString() == column.value">
          #{item.attachmentType,jdbcType=TINYINT}
        </if>
        <if test="'attachment_url'.toString() == column.value">
          #{item.attachmentUrl,jdbcType=VARCHAR}
        </if>
        <if test="'original_attachment_url'.toString() == column.value">
          #{item.originalAttachmentUrl,jdbcType=VARCHAR}
        </if>
        <if test="'identify_attachment_type'.toString() == column.value">
          #{item.identifyAttachmentType,jdbcType=TINYINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>