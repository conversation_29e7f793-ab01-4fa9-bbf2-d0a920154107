<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.TpaEmployeeInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.TpaEmployeeInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="employee_account" jdbcType="VARCHAR" property="employeeAccount" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="employee_phone" jdbcType="VARCHAR" property="employeePhone" />
    <result column="employee_email" jdbcType="VARCHAR" property="employeeEmail" />
    <result column="employee_roles" jdbcType="VARCHAR" property="employeeRoles" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="sub_company_id" jdbcType="BIGINT" property="subCompanyId" />
    <result column="id_no" jdbcType="VARCHAR" property="idNo" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="cooperate_method" jdbcType="TINYINT" property="cooperateMethod" />
    <result column="is_admin" jdbcType="TINYINT" property="isAdmin" />
    <result column="op_permissions" jdbcType="VARCHAR" property="opPermissions" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="query_support" jdbcType="VARCHAR" property="querySupport" />
    <result column="ua_password" jdbcType="VARCHAR" property="uaPassword" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, employee_account, employee_name, employee_phone, employee_email, employee_roles, 
    company_id, sub_company_id, id_no, password, cooperate_method, is_admin, op_permissions, 
    extra_info, remark, is_deleted, gmt_created, gmt_modified, creator, modifier, query_support, 
    ua_password
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.TpaEmployeeInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpa_employee_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tpa_employee_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.TpaEmployeeInfoDO">
    insert into tpa_employee_info (id, employee_account, employee_name, 
      employee_phone, employee_email, employee_roles, 
      company_id, sub_company_id, id_no, 
      password, cooperate_method, is_admin, 
      op_permissions, extra_info, remark, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier, query_support, 
      ua_password)
    values (#{id,jdbcType=BIGINT}, #{employeeAccount,jdbcType=VARCHAR}, #{employeeName,jdbcType=VARCHAR}, 
      #{employeePhone,jdbcType=VARCHAR}, #{employeeEmail,jdbcType=VARCHAR}, #{employeeRoles,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=BIGINT}, #{subCompanyId,jdbcType=BIGINT}, #{idNo,jdbcType=VARCHAR}, 
      #{password,jdbcType=VARCHAR}, #{cooperateMethod,jdbcType=TINYINT}, #{isAdmin,jdbcType=TINYINT}, 
      #{opPermissions,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{querySupport,jdbcType=VARCHAR}, 
      #{uaPassword,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.TpaEmployeeInfoDO">
    insert into tpa_employee_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="employeeAccount != null">
        employee_account,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
      <if test="employeePhone != null">
        employee_phone,
      </if>
      <if test="employeeEmail != null">
        employee_email,
      </if>
      <if test="employeeRoles != null">
        employee_roles,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="subCompanyId != null">
        sub_company_id,
      </if>
      <if test="idNo != null">
        id_no,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="cooperateMethod != null">
        cooperate_method,
      </if>
      <if test="isAdmin != null">
        is_admin,
      </if>
      <if test="opPermissions != null">
        op_permissions,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="querySupport != null">
        query_support,
      </if>
      <if test="uaPassword != null">
        ua_password,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="employeeAccount != null">
        #{employeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeePhone != null">
        #{employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="employeeEmail != null">
        #{employeeEmail,jdbcType=VARCHAR},
      </if>
      <if test="employeeRoles != null">
        #{employeeRoles,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="subCompanyId != null">
        #{subCompanyId,jdbcType=BIGINT},
      </if>
      <if test="idNo != null">
        #{idNo,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="cooperateMethod != null">
        #{cooperateMethod,jdbcType=TINYINT},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=TINYINT},
      </if>
      <if test="opPermissions != null">
        #{opPermissions,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="querySupport != null">
        #{querySupport,jdbcType=VARCHAR},
      </if>
      <if test="uaPassword != null">
        #{uaPassword,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.TpaEmployeeInfoExample" resultType="java.lang.Long">
    select count(*) from tpa_employee_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tpa_employee_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.employeeAccount != null">
        employee_account = #{record.employeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeName != null">
        employee_name = #{record.employeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.employeePhone != null">
        employee_phone = #{record.employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeEmail != null">
        employee_email = #{record.employeeEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeRoles != null">
        employee_roles = #{record.employeeRoles,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.subCompanyId != null">
        sub_company_id = #{record.subCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.idNo != null">
        id_no = #{record.idNo,jdbcType=VARCHAR},
      </if>
      <if test="record.password != null">
        password = #{record.password,jdbcType=VARCHAR},
      </if>
      <if test="record.cooperateMethod != null">
        cooperate_method = #{record.cooperateMethod,jdbcType=TINYINT},
      </if>
      <if test="record.isAdmin != null">
        is_admin = #{record.isAdmin,jdbcType=TINYINT},
      </if>
      <if test="record.opPermissions != null">
        op_permissions = #{record.opPermissions,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.querySupport != null">
        query_support = #{record.querySupport,jdbcType=VARCHAR},
      </if>
      <if test="record.uaPassword != null">
        ua_password = #{record.uaPassword,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tpa_employee_info
    set id = #{record.id,jdbcType=BIGINT},
      employee_account = #{record.employeeAccount,jdbcType=VARCHAR},
      employee_name = #{record.employeeName,jdbcType=VARCHAR},
      employee_phone = #{record.employeePhone,jdbcType=VARCHAR},
      employee_email = #{record.employeeEmail,jdbcType=VARCHAR},
      employee_roles = #{record.employeeRoles,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=BIGINT},
      sub_company_id = #{record.subCompanyId,jdbcType=BIGINT},
      id_no = #{record.idNo,jdbcType=VARCHAR},
      password = #{record.password,jdbcType=VARCHAR},
      cooperate_method = #{record.cooperateMethod,jdbcType=TINYINT},
      is_admin = #{record.isAdmin,jdbcType=TINYINT},
      op_permissions = #{record.opPermissions,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      query_support = #{record.querySupport,jdbcType=VARCHAR},
      ua_password = #{record.uaPassword,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.TpaEmployeeInfoDO">
    update tpa_employee_info
    <set>
      <if test="employeeAccount != null">
        employee_account = #{employeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        employee_name = #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeePhone != null">
        employee_phone = #{employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="employeeEmail != null">
        employee_email = #{employeeEmail,jdbcType=VARCHAR},
      </if>
      <if test="employeeRoles != null">
        employee_roles = #{employeeRoles,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="subCompanyId != null">
        sub_company_id = #{subCompanyId,jdbcType=BIGINT},
      </if>
      <if test="idNo != null">
        id_no = #{idNo,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="cooperateMethod != null">
        cooperate_method = #{cooperateMethod,jdbcType=TINYINT},
      </if>
      <if test="isAdmin != null">
        is_admin = #{isAdmin,jdbcType=TINYINT},
      </if>
      <if test="opPermissions != null">
        op_permissions = #{opPermissions,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="querySupport != null">
        query_support = #{querySupport,jdbcType=VARCHAR},
      </if>
      <if test="uaPassword != null">
        ua_password = #{uaPassword,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.TpaEmployeeInfoDO">
    update tpa_employee_info
    set employee_account = #{employeeAccount,jdbcType=VARCHAR},
      employee_name = #{employeeName,jdbcType=VARCHAR},
      employee_phone = #{employeePhone,jdbcType=VARCHAR},
      employee_email = #{employeeEmail,jdbcType=VARCHAR},
      employee_roles = #{employeeRoles,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT},
      sub_company_id = #{subCompanyId,jdbcType=BIGINT},
      id_no = #{idNo,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      cooperate_method = #{cooperateMethod,jdbcType=TINYINT},
      is_admin = #{isAdmin,jdbcType=TINYINT},
      op_permissions = #{opPermissions,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      query_support = #{querySupport,jdbcType=VARCHAR},
      ua_password = #{uaPassword,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into tpa_employee_info
    (id, employee_account, employee_name, employee_phone, employee_email, employee_roles, 
      company_id, sub_company_id, id_no, password, cooperate_method, is_admin, op_permissions, 
      extra_info, remark, is_deleted, gmt_created, gmt_modified, creator, modifier, query_support, 
      ua_password)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.employeeAccount,jdbcType=VARCHAR}, #{item.employeeName,jdbcType=VARCHAR}, 
        #{item.employeePhone,jdbcType=VARCHAR}, #{item.employeeEmail,jdbcType=VARCHAR}, 
        #{item.employeeRoles,jdbcType=VARCHAR}, #{item.companyId,jdbcType=BIGINT}, #{item.subCompanyId,jdbcType=BIGINT}, 
        #{item.idNo,jdbcType=VARCHAR}, #{item.password,jdbcType=VARCHAR}, #{item.cooperateMethod,jdbcType=TINYINT}, 
        #{item.isAdmin,jdbcType=TINYINT}, #{item.opPermissions,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.querySupport,jdbcType=VARCHAR}, #{item.uaPassword,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into tpa_employee_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'employee_account'.toString() == column.value">
          #{item.employeeAccount,jdbcType=VARCHAR}
        </if>
        <if test="'employee_name'.toString() == column.value">
          #{item.employeeName,jdbcType=VARCHAR}
        </if>
        <if test="'employee_phone'.toString() == column.value">
          #{item.employeePhone,jdbcType=VARCHAR}
        </if>
        <if test="'employee_email'.toString() == column.value">
          #{item.employeeEmail,jdbcType=VARCHAR}
        </if>
        <if test="'employee_roles'.toString() == column.value">
          #{item.employeeRoles,jdbcType=VARCHAR}
        </if>
        <if test="'company_id'.toString() == column.value">
          #{item.companyId,jdbcType=BIGINT}
        </if>
        <if test="'sub_company_id'.toString() == column.value">
          #{item.subCompanyId,jdbcType=BIGINT}
        </if>
        <if test="'id_no'.toString() == column.value">
          #{item.idNo,jdbcType=VARCHAR}
        </if>
        <if test="'password'.toString() == column.value">
          #{item.password,jdbcType=VARCHAR}
        </if>
        <if test="'cooperate_method'.toString() == column.value">
          #{item.cooperateMethod,jdbcType=TINYINT}
        </if>
        <if test="'is_admin'.toString() == column.value">
          #{item.isAdmin,jdbcType=TINYINT}
        </if>
        <if test="'op_permissions'.toString() == column.value">
          #{item.opPermissions,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'query_support'.toString() == column.value">
          #{item.querySupport,jdbcType=VARCHAR}
        </if>
        <if test="'ua_password'.toString() == column.value">
          #{item.uaPassword,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>