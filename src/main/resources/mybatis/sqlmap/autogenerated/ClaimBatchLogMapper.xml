<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimBatchLogMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimBatchLogDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="dispatcher_no" jdbcType="VARCHAR" property="dispatcherNo" />
    <result column="batch_deal_date" jdbcType="TIMESTAMP" property="batchDealDate" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="total_cnt" jdbcType="VARCHAR" property="totalCnt" />
    <result column="success_cnt" jdbcType="VARCHAR" property="successCnt" />
    <result column="fail_cnt" jdbcType="VARCHAR" property="failCnt" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, batch_no, report_no, dispatcher_no, batch_deal_date, status, result, total_cnt, 
    success_cnt, fail_cnt, remark, creator, gmt_created, modifier, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_claim_batch_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_claim_batch_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchLogDO">
    insert into cargo_claim_batch_log (id, batch_no, report_no, 
      dispatcher_no, batch_deal_date, status, 
      result, total_cnt, success_cnt, 
      fail_cnt, remark, creator, 
      gmt_created, modifier, gmt_modified, 
      is_deleted)
    values (#{id,jdbcType=BIGINT}, #{batchNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{dispatcherNo,jdbcType=VARCHAR}, #{batchDealDate,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR}, 
      #{result,jdbcType=VARCHAR}, #{totalCnt,jdbcType=VARCHAR}, #{successCnt,jdbcType=VARCHAR}, 
      #{failCnt,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      sysdate(), #{modifier,jdbcType=VARCHAR}, sysdate(), 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchLogDO">
    insert into cargo_claim_batch_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no,
      </if>
      <if test="batchDealDate != null">
        batch_deal_date,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="totalCnt != null">
        total_cnt,
      </if>
      <if test="successCnt != null">
        success_cnt,
      </if>
      <if test="failCnt != null">
        fail_cnt,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherNo != null">
        #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="batchDealDate != null">
        #{batchDealDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="totalCnt != null">
        #{totalCnt,jdbcType=VARCHAR},
      </if>
      <if test="successCnt != null">
        #{successCnt,jdbcType=VARCHAR},
      </if>
      <if test="failCnt != null">
        #{failCnt,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchLogExample" resultType="java.lang.Long">
    select count(*) from cargo_claim_batch_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_claim_batch_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.dispatcherNo != null">
        dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchDealDate != null">
        batch_deal_date = #{record.batchDealDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.result != null">
        result = #{record.result,jdbcType=VARCHAR},
      </if>
      <if test="record.totalCnt != null">
        total_cnt = #{record.totalCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.successCnt != null">
        success_cnt = #{record.successCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.failCnt != null">
        fail_cnt = #{record.failCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_claim_batch_log
    set id = #{record.id,jdbcType=BIGINT},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      batch_deal_date = #{record.batchDealDate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=VARCHAR},
      result = #{record.result,jdbcType=VARCHAR},
      total_cnt = #{record.totalCnt,jdbcType=VARCHAR},
      success_cnt = #{record.successCnt,jdbcType=VARCHAR},
      fail_cnt = #{record.failCnt,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchLogDO">
    update cargo_claim_batch_log
    <set>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="batchDealDate != null">
        batch_deal_date = #{batchDealDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=VARCHAR},
      </if>
      <if test="totalCnt != null">
        total_cnt = #{totalCnt,jdbcType=VARCHAR},
      </if>
      <if test="successCnt != null">
        success_cnt = #{successCnt,jdbcType=VARCHAR},
      </if>
      <if test="failCnt != null">
        fail_cnt = #{failCnt,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchLogDO">
    update cargo_claim_batch_log
    set batch_no = #{batchNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      batch_deal_date = #{batchDealDate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR},
      result = #{result,jdbcType=VARCHAR},
      total_cnt = #{totalCnt,jdbcType=VARCHAR},
      success_cnt = #{successCnt,jdbcType=VARCHAR},
      fail_cnt = #{failCnt,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_claim_batch_log
    (id, batch_no, report_no, dispatcher_no, batch_deal_date, status, result, total_cnt, 
      success_cnt, fail_cnt, remark, creator, gmt_created, modifier, gmt_modified, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.batchNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.dispatcherNo,jdbcType=VARCHAR}, #{item.batchDealDate,jdbcType=TIMESTAMP}, 
        #{item.status,jdbcType=VARCHAR}, #{item.result,jdbcType=VARCHAR}, #{item.totalCnt,jdbcType=VARCHAR}, 
        #{item.successCnt,jdbcType=VARCHAR}, #{item.failCnt,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_claim_batch_log (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'batch_no'.toString() == column.value">
          #{item.batchNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'dispatcher_no'.toString() == column.value">
          #{item.dispatcherNo,jdbcType=VARCHAR}
        </if>
        <if test="'batch_deal_date'.toString() == column.value">
          #{item.batchDealDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=VARCHAR}
        </if>
        <if test="'result'.toString() == column.value">
          #{item.result,jdbcType=VARCHAR}
        </if>
        <if test="'total_cnt'.toString() == column.value">
          #{item.totalCnt,jdbcType=VARCHAR}
        </if>
        <if test="'success_cnt'.toString() == column.value">
          #{item.successCnt,jdbcType=VARCHAR}
        </if>
        <if test="'fail_cnt'.toString() == column.value">
          #{item.failCnt,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>