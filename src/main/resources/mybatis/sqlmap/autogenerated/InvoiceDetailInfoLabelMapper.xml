<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.InvoiceDetailInfoLabelMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoLabelDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="invoice_detail_id" jdbcType="BIGINT" property="invoiceDetailId" />
    <result column="risk_label" jdbcType="VARCHAR" property="riskLabel" />
    <result column="risk_level" jdbcType="INTEGER" property="riskLevel" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="common_rule_id" jdbcType="BIGINT" property="commonRuleId" />
    <result column="rule_hit_status" jdbcType="VARCHAR" property="ruleHitStatus" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="bill_number" jdbcType="VARCHAR" property="billNumber" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, invoice_detail_id, risk_label, risk_level, extra_info, remark, is_deleted, gmt_created, 
    gmt_modified, creator, modifier, common_rule_id, rule_hit_status, report_no, bill_number
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoLabelExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from invoice_detail_info_label
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from invoice_detail_info_label
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoLabelDO">
    insert into invoice_detail_info_label (id, invoice_detail_id, risk_label, 
      risk_level, extra_info, remark, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier, common_rule_id, 
      rule_hit_status, report_no, bill_number
      )
    values (#{id,jdbcType=BIGINT}, #{invoiceDetailId,jdbcType=BIGINT}, #{riskLabel,jdbcType=VARCHAR}, 
      #{riskLevel,jdbcType=INTEGER}, #{extraInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{commonRuleId,jdbcType=BIGINT}, 
      #{ruleHitStatus,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{billNumber,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoLabelDO">
    insert into invoice_detail_info_label
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="invoiceDetailId != null">
        invoice_detail_id,
      </if>
      <if test="riskLabel != null">
        risk_label,
      </if>
      <if test="riskLevel != null">
        risk_level,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="commonRuleId != null">
        common_rule_id,
      </if>
      <if test="ruleHitStatus != null">
        rule_hit_status,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="billNumber != null">
        bill_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="invoiceDetailId != null">
        #{invoiceDetailId,jdbcType=BIGINT},
      </if>
      <if test="riskLabel != null">
        #{riskLabel,jdbcType=VARCHAR},
      </if>
      <if test="riskLevel != null">
        #{riskLevel,jdbcType=INTEGER},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="commonRuleId != null">
        #{commonRuleId,jdbcType=BIGINT},
      </if>
      <if test="ruleHitStatus != null">
        #{ruleHitStatus,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="billNumber != null">
        #{billNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoLabelExample" resultType="java.lang.Long">
    select count(*) from invoice_detail_info_label
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update invoice_detail_info_label
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.invoiceDetailId != null">
        invoice_detail_id = #{record.invoiceDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.riskLabel != null">
        risk_label = #{record.riskLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.riskLevel != null">
        risk_level = #{record.riskLevel,jdbcType=INTEGER},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.commonRuleId != null">
        common_rule_id = #{record.commonRuleId,jdbcType=BIGINT},
      </if>
      <if test="record.ruleHitStatus != null">
        rule_hit_status = #{record.ruleHitStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.billNumber != null">
        bill_number = #{record.billNumber,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update invoice_detail_info_label
    set id = #{record.id,jdbcType=BIGINT},
      invoice_detail_id = #{record.invoiceDetailId,jdbcType=BIGINT},
      risk_label = #{record.riskLabel,jdbcType=VARCHAR},
      risk_level = #{record.riskLevel,jdbcType=INTEGER},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      common_rule_id = #{record.commonRuleId,jdbcType=BIGINT},
      rule_hit_status = #{record.ruleHitStatus,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      bill_number = #{record.billNumber,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoLabelDO">
    update invoice_detail_info_label
    <set>
      <if test="invoiceDetailId != null">
        invoice_detail_id = #{invoiceDetailId,jdbcType=BIGINT},
      </if>
      <if test="riskLabel != null">
        risk_label = #{riskLabel,jdbcType=VARCHAR},
      </if>
      <if test="riskLevel != null">
        risk_level = #{riskLevel,jdbcType=INTEGER},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="commonRuleId != null">
        common_rule_id = #{commonRuleId,jdbcType=BIGINT},
      </if>
      <if test="ruleHitStatus != null">
        rule_hit_status = #{ruleHitStatus,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="billNumber != null">
        bill_number = #{billNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoLabelDO">
    update invoice_detail_info_label
    set invoice_detail_id = #{invoiceDetailId,jdbcType=BIGINT},
      risk_label = #{riskLabel,jdbcType=VARCHAR},
      risk_level = #{riskLevel,jdbcType=INTEGER},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      common_rule_id = #{commonRuleId,jdbcType=BIGINT},
      rule_hit_status = #{ruleHitStatus,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      bill_number = #{billNumber,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into invoice_detail_info_label
    (id, invoice_detail_id, risk_label, risk_level, extra_info, remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, common_rule_id, rule_hit_status, report_no, bill_number
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.invoiceDetailId,jdbcType=BIGINT}, #{item.riskLabel,jdbcType=VARCHAR}, 
        #{item.riskLevel,jdbcType=INTEGER}, #{item.extraInfo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.commonRuleId,jdbcType=BIGINT}, 
        #{item.ruleHitStatus,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.billNumber,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into invoice_detail_info_label (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'invoice_detail_id'.toString() == column.value">
          #{item.invoiceDetailId,jdbcType=BIGINT}
        </if>
        <if test="'risk_label'.toString() == column.value">
          #{item.riskLabel,jdbcType=VARCHAR}
        </if>
        <if test="'risk_level'.toString() == column.value">
          #{item.riskLevel,jdbcType=INTEGER}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'common_rule_id'.toString() == column.value">
          #{item.commonRuleId,jdbcType=BIGINT}
        </if>
        <if test="'rule_hit_status'.toString() == column.value">
          #{item.ruleHitStatus,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'bill_number'.toString() == column.value">
          #{item.billNumber,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>