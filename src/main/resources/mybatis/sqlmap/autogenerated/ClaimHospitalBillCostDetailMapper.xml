<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimHospitalBillCostDetailMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimHospitalBillCostDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cost_category" jdbcType="VARCHAR" property="costCategory" />
    <result column="bill_amount" jdbcType="VARCHAR" property="billAmount" />
    <result column="self_expense" jdbcType="VARCHAR" property="selfExpense" />
    <result column="self_expense_category" jdbcType="VARCHAR" property="selfExpenseCategory" />
    <result column="non_responsible_cost" jdbcType="VARCHAR" property="nonResponsibleCost" />
    <result column="medical_insurance_pay" jdbcType="VARCHAR" property="medicalInsurancePay" />
    <result column="other_pay" jdbcType="VARCHAR" property="otherPay" />
    <result column="reasonable_pay" jdbcType="VARCHAR" property="reasonablePay" />
    <result column="deductions_detail" jdbcType="VARCHAR" property="deductionsDetail" />
    <result column="claim_hospital_id" jdbcType="BIGINT" property="claimHospitalId" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, cost_category, bill_amount, self_expense, self_expense_category, non_responsible_cost, 
    medical_insurance_pay, other_pay, reasonable_pay, deductions_detail, claim_hospital_id, 
    report_no, gmt_created, gmt_modified, creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalBillCostDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_hospital_bill_cost_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_hospital_bill_cost_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalBillCostDetailDO">
    insert into claim_hospital_bill_cost_detail (id, cost_category, bill_amount, 
      self_expense, self_expense_category, non_responsible_cost, 
      medical_insurance_pay, other_pay, reasonable_pay, 
      deductions_detail, claim_hospital_id, report_no, 
      gmt_created, gmt_modified, creator, 
      modifier, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{costCategory,jdbcType=VARCHAR}, #{billAmount,jdbcType=VARCHAR}, 
      #{selfExpense,jdbcType=VARCHAR}, #{selfExpenseCategory,jdbcType=VARCHAR}, #{nonResponsibleCost,jdbcType=VARCHAR}, 
      #{medicalInsurancePay,jdbcType=VARCHAR}, #{otherPay,jdbcType=VARCHAR}, #{reasonablePay,jdbcType=VARCHAR}, 
      #{deductionsDetail,jdbcType=VARCHAR}, #{claimHospitalId,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalBillCostDetailDO">
    insert into claim_hospital_bill_cost_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="costCategory != null">
        cost_category,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="selfExpense != null">
        self_expense,
      </if>
      <if test="selfExpenseCategory != null">
        self_expense_category,
      </if>
      <if test="nonResponsibleCost != null">
        non_responsible_cost,
      </if>
      <if test="medicalInsurancePay != null">
        medical_insurance_pay,
      </if>
      <if test="otherPay != null">
        other_pay,
      </if>
      <if test="reasonablePay != null">
        reasonable_pay,
      </if>
      <if test="deductionsDetail != null">
        deductions_detail,
      </if>
      <if test="claimHospitalId != null">
        claim_hospital_id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="costCategory != null">
        #{costCategory,jdbcType=VARCHAR},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=VARCHAR},
      </if>
      <if test="selfExpense != null">
        #{selfExpense,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseCategory != null">
        #{selfExpenseCategory,jdbcType=VARCHAR},
      </if>
      <if test="nonResponsibleCost != null">
        #{nonResponsibleCost,jdbcType=VARCHAR},
      </if>
      <if test="medicalInsurancePay != null">
        #{medicalInsurancePay,jdbcType=VARCHAR},
      </if>
      <if test="otherPay != null">
        #{otherPay,jdbcType=VARCHAR},
      </if>
      <if test="reasonablePay != null">
        #{reasonablePay,jdbcType=VARCHAR},
      </if>
      <if test="deductionsDetail != null">
        #{deductionsDetail,jdbcType=VARCHAR},
      </if>
      <if test="claimHospitalId != null">
        #{claimHospitalId,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalBillCostDetailExample" resultType="java.lang.Long">
    select count(*) from claim_hospital_bill_cost_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_hospital_bill_cost_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.costCategory != null">
        cost_category = #{record.costCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.billAmount != null">
        bill_amount = #{record.billAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.selfExpense != null">
        self_expense = #{record.selfExpense,jdbcType=VARCHAR},
      </if>
      <if test="record.selfExpenseCategory != null">
        self_expense_category = #{record.selfExpenseCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.nonResponsibleCost != null">
        non_responsible_cost = #{record.nonResponsibleCost,jdbcType=VARCHAR},
      </if>
      <if test="record.medicalInsurancePay != null">
        medical_insurance_pay = #{record.medicalInsurancePay,jdbcType=VARCHAR},
      </if>
      <if test="record.otherPay != null">
        other_pay = #{record.otherPay,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonablePay != null">
        reasonable_pay = #{record.reasonablePay,jdbcType=VARCHAR},
      </if>
      <if test="record.deductionsDetail != null">
        deductions_detail = #{record.deductionsDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.claimHospitalId != null">
        claim_hospital_id = #{record.claimHospitalId,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_hospital_bill_cost_detail
    set id = #{record.id,jdbcType=BIGINT},
      cost_category = #{record.costCategory,jdbcType=VARCHAR},
      bill_amount = #{record.billAmount,jdbcType=VARCHAR},
      self_expense = #{record.selfExpense,jdbcType=VARCHAR},
      self_expense_category = #{record.selfExpenseCategory,jdbcType=VARCHAR},
      non_responsible_cost = #{record.nonResponsibleCost,jdbcType=VARCHAR},
      medical_insurance_pay = #{record.medicalInsurancePay,jdbcType=VARCHAR},
      other_pay = #{record.otherPay,jdbcType=VARCHAR},
      reasonable_pay = #{record.reasonablePay,jdbcType=VARCHAR},
      deductions_detail = #{record.deductionsDetail,jdbcType=VARCHAR},
      claim_hospital_id = #{record.claimHospitalId,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalBillCostDetailDO">
    update claim_hospital_bill_cost_detail
    <set>
      <if test="costCategory != null">
        cost_category = #{costCategory,jdbcType=VARCHAR},
      </if>
      <if test="billAmount != null">
        bill_amount = #{billAmount,jdbcType=VARCHAR},
      </if>
      <if test="selfExpense != null">
        self_expense = #{selfExpense,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseCategory != null">
        self_expense_category = #{selfExpenseCategory,jdbcType=VARCHAR},
      </if>
      <if test="nonResponsibleCost != null">
        non_responsible_cost = #{nonResponsibleCost,jdbcType=VARCHAR},
      </if>
      <if test="medicalInsurancePay != null">
        medical_insurance_pay = #{medicalInsurancePay,jdbcType=VARCHAR},
      </if>
      <if test="otherPay != null">
        other_pay = #{otherPay,jdbcType=VARCHAR},
      </if>
      <if test="reasonablePay != null">
        reasonable_pay = #{reasonablePay,jdbcType=VARCHAR},
      </if>
      <if test="deductionsDetail != null">
        deductions_detail = #{deductionsDetail,jdbcType=VARCHAR},
      </if>
      <if test="claimHospitalId != null">
        claim_hospital_id = #{claimHospitalId,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalBillCostDetailDO">
    update claim_hospital_bill_cost_detail
    set cost_category = #{costCategory,jdbcType=VARCHAR},
      bill_amount = #{billAmount,jdbcType=VARCHAR},
      self_expense = #{selfExpense,jdbcType=VARCHAR},
      self_expense_category = #{selfExpenseCategory,jdbcType=VARCHAR},
      non_responsible_cost = #{nonResponsibleCost,jdbcType=VARCHAR},
      medical_insurance_pay = #{medicalInsurancePay,jdbcType=VARCHAR},
      other_pay = #{otherPay,jdbcType=VARCHAR},
      reasonable_pay = #{reasonablePay,jdbcType=VARCHAR},
      deductions_detail = #{deductionsDetail,jdbcType=VARCHAR},
      claim_hospital_id = #{claimHospitalId,jdbcType=BIGINT},
      report_no = #{reportNo,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_hospital_bill_cost_detail
    (id, cost_category, bill_amount, self_expense, self_expense_category, non_responsible_cost, 
      medical_insurance_pay, other_pay, reasonable_pay, deductions_detail, claim_hospital_id, 
      report_no, gmt_created, gmt_modified, creator, modifier, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.costCategory,jdbcType=VARCHAR}, #{item.billAmount,jdbcType=VARCHAR}, 
        #{item.selfExpense,jdbcType=VARCHAR}, #{item.selfExpenseCategory,jdbcType=VARCHAR}, 
        #{item.nonResponsibleCost,jdbcType=VARCHAR}, #{item.medicalInsurancePay,jdbcType=VARCHAR}, 
        #{item.otherPay,jdbcType=VARCHAR}, #{item.reasonablePay,jdbcType=VARCHAR}, #{item.deductionsDetail,jdbcType=VARCHAR}, 
        #{item.claimHospitalId,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_hospital_bill_cost_detail (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'cost_category'.toString() == column.value">
          #{item.costCategory,jdbcType=VARCHAR}
        </if>
        <if test="'bill_amount'.toString() == column.value">
          #{item.billAmount,jdbcType=VARCHAR}
        </if>
        <if test="'self_expense'.toString() == column.value">
          #{item.selfExpense,jdbcType=VARCHAR}
        </if>
        <if test="'self_expense_category'.toString() == column.value">
          #{item.selfExpenseCategory,jdbcType=VARCHAR}
        </if>
        <if test="'non_responsible_cost'.toString() == column.value">
          #{item.nonResponsibleCost,jdbcType=VARCHAR}
        </if>
        <if test="'medical_insurance_pay'.toString() == column.value">
          #{item.medicalInsurancePay,jdbcType=VARCHAR}
        </if>
        <if test="'other_pay'.toString() == column.value">
          #{item.otherPay,jdbcType=VARCHAR}
        </if>
        <if test="'reasonable_pay'.toString() == column.value">
          #{item.reasonablePay,jdbcType=VARCHAR}
        </if>
        <if test="'deductions_detail'.toString() == column.value">
          #{item.deductionsDetail,jdbcType=VARCHAR}
        </if>
        <if test="'claim_hospital_id'.toString() == column.value">
          #{item.claimHospitalId,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>