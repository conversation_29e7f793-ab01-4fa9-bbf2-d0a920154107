<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AntiMoneyLaunderingLogMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AntiMoneyLaunderingLogDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="cert_name" jdbcType="VARCHAR" property="certName" />
    <result column="cert_no" jdbcType="VARCHAR" property="certNo" />
    <result column="cert_type" jdbcType="VARCHAR" property="certType" />
    <result column="cust_type" jdbcType="TINYINT" property="custType" />
    <result column="list_code" jdbcType="VARCHAR" property="listCode" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, product_code, cert_name, cert_no, cert_type, cust_type, list_code, 
    creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AntiMoneyLaunderingLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_anti_money_laundering_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_anti_money_laundering_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AntiMoneyLaunderingLogDO">
    insert into cargo_anti_money_laundering_log (id, report_no, product_code, 
      cert_name, cert_no, cert_type, 
      cust_type, list_code, creator, 
      modifier, gmt_created, gmt_modified, 
      is_deleted)
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, 
      #{certName,jdbcType=VARCHAR}, #{certNo,jdbcType=VARCHAR}, #{certType,jdbcType=VARCHAR}, 
      #{custType,jdbcType=TINYINT}, #{listCode,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate(), 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AntiMoneyLaunderingLogDO">
    insert into cargo_anti_money_laundering_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="productCode != null">
        product_code,
      </if>
      <if test="certName != null">
        cert_name,
      </if>
      <if test="certNo != null">
        cert_no,
      </if>
      <if test="certType != null">
        cert_type,
      </if>
      <if test="custType != null">
        cust_type,
      </if>
      <if test="listCode != null">
        list_code,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="certName != null">
        #{certName,jdbcType=VARCHAR},
      </if>
      <if test="certNo != null">
        #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="certType != null">
        #{certType,jdbcType=VARCHAR},
      </if>
      <if test="custType != null">
        #{custType,jdbcType=TINYINT},
      </if>
      <if test="listCode != null">
        #{listCode,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AntiMoneyLaunderingLogExample" resultType="java.lang.Long">
    select count(*) from cargo_anti_money_laundering_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_anti_money_laundering_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.productCode != null">
        product_code = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.certName != null">
        cert_name = #{record.certName,jdbcType=VARCHAR},
      </if>
      <if test="record.certNo != null">
        cert_no = #{record.certNo,jdbcType=VARCHAR},
      </if>
      <if test="record.certType != null">
        cert_type = #{record.certType,jdbcType=VARCHAR},
      </if>
      <if test="record.custType != null">
        cust_type = #{record.custType,jdbcType=TINYINT},
      </if>
      <if test="record.listCode != null">
        list_code = #{record.listCode,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_anti_money_laundering_log
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      product_code = #{record.productCode,jdbcType=VARCHAR},
      cert_name = #{record.certName,jdbcType=VARCHAR},
      cert_no = #{record.certNo,jdbcType=VARCHAR},
      cert_type = #{record.certType,jdbcType=VARCHAR},
      cust_type = #{record.custType,jdbcType=TINYINT},
      list_code = #{record.listCode,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AntiMoneyLaunderingLogDO">
    update cargo_anti_money_laundering_log
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="certName != null">
        cert_name = #{certName,jdbcType=VARCHAR},
      </if>
      <if test="certNo != null">
        cert_no = #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="certType != null">
        cert_type = #{certType,jdbcType=VARCHAR},
      </if>
      <if test="custType != null">
        cust_type = #{custType,jdbcType=TINYINT},
      </if>
      <if test="listCode != null">
        list_code = #{listCode,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AntiMoneyLaunderingLogDO">
    update cargo_anti_money_laundering_log
    set report_no = #{reportNo,jdbcType=VARCHAR},
      product_code = #{productCode,jdbcType=VARCHAR},
      cert_name = #{certName,jdbcType=VARCHAR},
      cert_no = #{certNo,jdbcType=VARCHAR},
      cert_type = #{certType,jdbcType=VARCHAR},
      cust_type = #{custType,jdbcType=TINYINT},
      list_code = #{listCode,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_anti_money_laundering_log
    (id, report_no, product_code, cert_name, cert_no, cert_type, cust_type, list_code, 
      creator, modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.productCode,jdbcType=VARCHAR}, 
        #{item.certName,jdbcType=VARCHAR}, #{item.certNo,jdbcType=VARCHAR}, #{item.certType,jdbcType=VARCHAR}, 
        #{item.custType,jdbcType=TINYINT}, #{item.listCode,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_anti_money_laundering_log (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'product_code'.toString() == column.value">
          #{item.productCode,jdbcType=VARCHAR}
        </if>
        <if test="'cert_name'.toString() == column.value">
          #{item.certName,jdbcType=VARCHAR}
        </if>
        <if test="'cert_no'.toString() == column.value">
          #{item.certNo,jdbcType=VARCHAR}
        </if>
        <if test="'cert_type'.toString() == column.value">
          #{item.certType,jdbcType=VARCHAR}
        </if>
        <if test="'cust_type'.toString() == column.value">
          #{item.custType,jdbcType=TINYINT}
        </if>
        <if test="'list_code'.toString() == column.value">
          #{item.listCode,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>