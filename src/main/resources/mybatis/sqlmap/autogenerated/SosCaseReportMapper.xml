<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.SosCaseReportMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.SosCaseReportDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_id" jdbcType="BIGINT" property="caseId" />
    <result column="report_date" jdbcType="TIMESTAMP" property="reportDate" />
    <result column="report_agency" jdbcType="VARCHAR" property="reportAgency" />
    <result column="report_content" jdbcType="VARCHAR" property="reportContent" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, case_id, report_date, report_agency, report_content, status, creator, modifier, 
    gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.SosCaseReportExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sos_case_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sos_case_report
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.SosCaseReportDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sos_case_report (case_id, report_date, report_agency, 
      report_content, status, creator, 
      modifier, gmt_created, gmt_modified, 
      is_deleted)
    values (#{caseId,jdbcType=BIGINT}, #{reportDate,jdbcType=TIMESTAMP}, #{reportAgency,jdbcType=VARCHAR}, 
      #{reportContent,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate(), 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.SosCaseReportDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sos_case_report
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        case_id,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="reportAgency != null">
        report_agency,
      </if>
      <if test="reportContent != null">
        report_content,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        #{caseId,jdbcType=BIGINT},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportAgency != null">
        #{reportAgency,jdbcType=VARCHAR},
      </if>
      <if test="reportContent != null">
        #{reportContent,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.SosCaseReportExample" resultType="java.lang.Long">
    select count(*) from sos_case_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sos_case_report
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.caseId != null">
        case_id = #{record.caseId,jdbcType=BIGINT},
      </if>
      <if test="record.reportDate != null">
        report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportAgency != null">
        report_agency = #{record.reportAgency,jdbcType=VARCHAR},
      </if>
      <if test="record.reportContent != null">
        report_content = #{record.reportContent,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sos_case_report
    set id = #{record.id,jdbcType=BIGINT},
      case_id = #{record.caseId,jdbcType=BIGINT},
      report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      report_agency = #{record.reportAgency,jdbcType=VARCHAR},
      report_content = #{record.reportContent,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.SosCaseReportDO">
    update sos_case_report
    <set>
      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportAgency != null">
        report_agency = #{reportAgency,jdbcType=VARCHAR},
      </if>
      <if test="reportContent != null">
        report_content = #{reportContent,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.SosCaseReportDO">
    update sos_case_report
    set case_id = #{caseId,jdbcType=BIGINT},
      report_date = #{reportDate,jdbcType=TIMESTAMP},
      report_agency = #{reportAgency,jdbcType=VARCHAR},
      report_content = #{reportContent,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into sos_case_report
    (case_id, report_date, report_agency, report_content, status, creator, modifier, 
      gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.caseId,jdbcType=BIGINT}, #{item.reportDate,jdbcType=TIMESTAMP}, #{item.reportAgency,jdbcType=VARCHAR}, 
        #{item.reportContent,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into sos_case_report (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'case_id'.toString() == column.value">
          #{item.caseId,jdbcType=BIGINT}
        </if>
        <if test="'report_date'.toString() == column.value">
          #{item.reportDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'report_agency'.toString() == column.value">
          #{item.reportAgency,jdbcType=VARCHAR}
        </if>
        <if test="'report_content'.toString() == column.value">
          #{item.reportContent,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>