<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.FrontReserveHistoryMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.FrontReserveHistoryDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="source_policy_id" jdbcType="BIGINT" property="sourcePolicyId" />
    <result column="source_policy_product_id" jdbcType="BIGINT" property="sourcePolicyProductId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="liability_id" jdbcType="BIGINT" property="liabilityId" />
    <result column="liability_code" jdbcType="VARCHAR" property="liabilityCode" />
    <result column="liability_name" jdbcType="VARCHAR" property="liabilityName" />
    <result column="amount" jdbcType="VARCHAR" property="amount" />
    <result column="before_outstanding_amount" jdbcType="VARCHAR" property="beforeOutstandingAmount" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="op_date" jdbcType="TIMESTAMP" property="opDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="expense_amount" jdbcType="VARCHAR" property="expenseAmount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, source_policy_id, source_policy_product_id, product_id, product_code, 
    product_name, liability_id, liability_code, liability_name, amount, before_outstanding_amount, 
    auditor, audit_status, operator, op_date, remark, is_deleted, creator, modifier, 
    gmt_created, gmt_modified, expense_amount
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.FrontReserveHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_hospital_front_reserve_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_hospital_front_reserve_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.FrontReserveHistoryDO">
    insert into claim_hospital_front_reserve_history (id, report_no, source_policy_id, 
      source_policy_product_id, product_id, product_code, 
      product_name, liability_id, liability_code, 
      liability_name, amount, before_outstanding_amount, 
      auditor, audit_status, operator, 
      op_date, remark, is_deleted, 
      creator, modifier, gmt_created, 
      gmt_modified, expense_amount)
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{sourcePolicyId,jdbcType=BIGINT}, 
      #{sourcePolicyProductId,jdbcType=BIGINT}, #{productId,jdbcType=BIGINT}, #{productCode,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{liabilityId,jdbcType=BIGINT}, #{liabilityCode,jdbcType=VARCHAR}, 
      #{liabilityName,jdbcType=VARCHAR}, #{amount,jdbcType=VARCHAR}, #{beforeOutstandingAmount,jdbcType=VARCHAR}, 
      #{auditor,jdbcType=VARCHAR}, #{auditStatus,jdbcType=TINYINT}, #{operator,jdbcType=VARCHAR}, 
      #{opDate,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{expenseAmount,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.FrontReserveHistoryDO">
    insert into claim_hospital_front_reserve_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="sourcePolicyId != null">
        source_policy_id,
      </if>
      <if test="sourcePolicyProductId != null">
        source_policy_product_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productCode != null">
        product_code,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="liabilityId != null">
        liability_id,
      </if>
      <if test="liabilityCode != null">
        liability_code,
      </if>
      <if test="liabilityName != null">
        liability_name,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="beforeOutstandingAmount != null">
        before_outstanding_amount,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="opDate != null">
        op_date,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="expenseAmount != null">
        expense_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="sourcePolicyId != null">
        #{sourcePolicyId,jdbcType=BIGINT},
      </if>
      <if test="sourcePolicyProductId != null">
        #{sourcePolicyProductId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityId != null">
        #{liabilityId,jdbcType=BIGINT},
      </if>
      <if test="liabilityCode != null">
        #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="liabilityName != null">
        #{liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=VARCHAR},
      </if>
      <if test="beforeOutstandingAmount != null">
        #{beforeOutstandingAmount,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="opDate != null">
        #{opDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="expenseAmount != null">
        #{expenseAmount,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.FrontReserveHistoryExample" resultType="java.lang.Long">
    select count(*) from claim_hospital_front_reserve_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_hospital_front_reserve_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePolicyId != null">
        source_policy_id = #{record.sourcePolicyId,jdbcType=BIGINT},
      </if>
      <if test="record.sourcePolicyProductId != null">
        source_policy_product_id = #{record.sourcePolicyProductId,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.productCode != null">
        product_code = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityId != null">
        liability_id = #{record.liabilityId,jdbcType=BIGINT},
      </if>
      <if test="record.liabilityCode != null">
        liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityName != null">
        liability_name = #{record.liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=VARCHAR},
      </if>
      <if test="record.beforeOutstandingAmount != null">
        before_outstanding_amount = #{record.beforeOutstandingAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.auditor != null">
        auditor = #{record.auditor,jdbcType=VARCHAR},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.opDate != null">
        op_date = #{record.opDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.expenseAmount != null">
        expense_amount = #{record.expenseAmount,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_hospital_front_reserve_history
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      source_policy_id = #{record.sourcePolicyId,jdbcType=BIGINT},
      source_policy_product_id = #{record.sourcePolicyProductId,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=BIGINT},
      product_code = #{record.productCode,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      liability_id = #{record.liabilityId,jdbcType=BIGINT},
      liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      liability_name = #{record.liabilityName,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=VARCHAR},
      before_outstanding_amount = #{record.beforeOutstandingAmount,jdbcType=VARCHAR},
      auditor = #{record.auditor,jdbcType=VARCHAR},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      operator = #{record.operator,jdbcType=VARCHAR},
      op_date = #{record.opDate,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      expense_amount = #{record.expenseAmount,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.FrontReserveHistoryDO">
    update claim_hospital_front_reserve_history
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="sourcePolicyId != null">
        source_policy_id = #{sourcePolicyId,jdbcType=BIGINT},
      </if>
      <if test="sourcePolicyProductId != null">
        source_policy_product_id = #{sourcePolicyProductId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="productCode != null">
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityId != null">
        liability_id = #{liabilityId,jdbcType=BIGINT},
      </if>
      <if test="liabilityCode != null">
        liability_code = #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="liabilityName != null">
        liability_name = #{liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=VARCHAR},
      </if>
      <if test="beforeOutstandingAmount != null">
        before_outstanding_amount = #{beforeOutstandingAmount,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="opDate != null">
        op_date = #{opDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="expenseAmount != null">
        expense_amount = #{expenseAmount,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.FrontReserveHistoryDO">
    update claim_hospital_front_reserve_history
    set report_no = #{reportNo,jdbcType=VARCHAR},
      source_policy_id = #{sourcePolicyId,jdbcType=BIGINT},
      source_policy_product_id = #{sourcePolicyProductId,jdbcType=BIGINT},
      product_id = #{productId,jdbcType=BIGINT},
      product_code = #{productCode,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      liability_id = #{liabilityId,jdbcType=BIGINT},
      liability_code = #{liabilityCode,jdbcType=VARCHAR},
      liability_name = #{liabilityName,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=VARCHAR},
      before_outstanding_amount = #{beforeOutstandingAmount,jdbcType=VARCHAR},
      auditor = #{auditor,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      operator = #{operator,jdbcType=VARCHAR},
      op_date = #{opDate,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      expense_amount = #{expenseAmount,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_hospital_front_reserve_history
    (id, report_no, source_policy_id, source_policy_product_id, product_id, product_code, 
      product_name, liability_id, liability_code, liability_name, amount, before_outstanding_amount, 
      auditor, audit_status, operator, op_date, remark, is_deleted, creator, modifier, 
      gmt_created, gmt_modified, expense_amount)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.sourcePolicyId,jdbcType=BIGINT}, 
        #{item.sourcePolicyProductId,jdbcType=BIGINT}, #{item.productId,jdbcType=BIGINT}, 
        #{item.productCode,jdbcType=VARCHAR}, #{item.productName,jdbcType=VARCHAR}, #{item.liabilityId,jdbcType=BIGINT}, 
        #{item.liabilityCode,jdbcType=VARCHAR}, #{item.liabilityName,jdbcType=VARCHAR}, 
        #{item.amount,jdbcType=VARCHAR}, #{item.beforeOutstandingAmount,jdbcType=VARCHAR}, 
        #{item.auditor,jdbcType=VARCHAR}, #{item.auditStatus,jdbcType=TINYINT}, #{item.operator,jdbcType=VARCHAR}, 
        #{item.opDate,jdbcType=TIMESTAMP}, #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.expenseAmount,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_hospital_front_reserve_history (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'source_policy_id'.toString() == column.value">
          #{item.sourcePolicyId,jdbcType=BIGINT}
        </if>
        <if test="'source_policy_product_id'.toString() == column.value">
          #{item.sourcePolicyProductId,jdbcType=BIGINT}
        </if>
        <if test="'product_id'.toString() == column.value">
          #{item.productId,jdbcType=BIGINT}
        </if>
        <if test="'product_code'.toString() == column.value">
          #{item.productCode,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'liability_id'.toString() == column.value">
          #{item.liabilityId,jdbcType=BIGINT}
        </if>
        <if test="'liability_code'.toString() == column.value">
          #{item.liabilityCode,jdbcType=VARCHAR}
        </if>
        <if test="'liability_name'.toString() == column.value">
          #{item.liabilityName,jdbcType=VARCHAR}
        </if>
        <if test="'amount'.toString() == column.value">
          #{item.amount,jdbcType=VARCHAR}
        </if>
        <if test="'before_outstanding_amount'.toString() == column.value">
          #{item.beforeOutstandingAmount,jdbcType=VARCHAR}
        </if>
        <if test="'auditor'.toString() == column.value">
          #{item.auditor,jdbcType=VARCHAR}
        </if>
        <if test="'audit_status'.toString() == column.value">
          #{item.auditStatus,jdbcType=TINYINT}
        </if>
        <if test="'operator'.toString() == column.value">
          #{item.operator,jdbcType=VARCHAR}
        </if>
        <if test="'op_date'.toString() == column.value">
          #{item.opDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'expense_amount'.toString() == column.value">
          #{item.expenseAmount,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>