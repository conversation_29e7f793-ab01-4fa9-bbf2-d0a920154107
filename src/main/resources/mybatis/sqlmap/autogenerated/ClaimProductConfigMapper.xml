<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimProductConfigMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimProductConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="campaign_def_id" jdbcType="BIGINT" property="campaignDefId" />
    <result column="campaign_def_name" jdbcType="VARCHAR" property="campaignDefName" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="package_def_name" jdbcType="VARCHAR" property="packageDefName" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="prod_no" jdbcType="VARCHAR" property="prodNo" />
    <result column="insurance_type" jdbcType="VARCHAR" property="insuranceType" />
    <result column="pipeline" jdbcType="VARCHAR" property="pipeline" />
    <result column="tags" jdbcType="BIGINT" property="tags" />
    <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="product_desc" jdbcType="VARCHAR" property="productDesc" />
    <result column="product_note" jdbcType="VARCHAR" property="productNote" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, campaign_def_id, campaign_def_name, package_def_id, package_def_name, channel_name, 
    prod_no, insurance_type, pipeline, tags, effective_time, enable, product_desc, product_note, 
    creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_product_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_product_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductConfigDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_product_config (campaign_def_id, campaign_def_name, package_def_id, 
      package_def_name, channel_name, prod_no, 
      insurance_type, pipeline, tags, 
      effective_time, enable, product_desc, 
      product_note, creator, modifier, 
      gmt_created, gmt_modified, is_deleted
      )
    values (#{campaignDefId,jdbcType=BIGINT}, #{campaignDefName,jdbcType=VARCHAR}, #{packageDefId,jdbcType=BIGINT}, 
      #{packageDefName,jdbcType=VARCHAR}, #{channelName,jdbcType=VARCHAR}, #{prodNo,jdbcType=VARCHAR}, 
      #{insuranceType,jdbcType=VARCHAR}, #{pipeline,jdbcType=VARCHAR}, #{tags,jdbcType=BIGINT}, 
      #{effectiveTime,jdbcType=TIMESTAMP}, #{enable,jdbcType=BIT}, #{productDesc,jdbcType=VARCHAR}, 
      #{productNote,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductConfigDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_product_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="campaignDefName != null">
        campaign_def_name,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="packageDefName != null">
        package_def_name,
      </if>
      <if test="channelName != null">
        channel_name,
      </if>
      <if test="prodNo != null">
        prod_no,
      </if>
      <if test="insuranceType != null">
        insurance_type,
      </if>
      <if test="pipeline != null">
        pipeline,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="effectiveTime != null">
        effective_time,
      </if>
      <if test="enable != null">
        enable,
      </if>
      <if test="productDesc != null">
        product_desc,
      </if>
      <if test="productNote != null">
        product_note,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="campaignDefName != null">
        #{campaignDefName,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="packageDefName != null">
        #{packageDefName,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="prodNo != null">
        #{prodNo,jdbcType=VARCHAR},
      </if>
      <if test="insuranceType != null">
        #{insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="pipeline != null">
        #{pipeline,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=BIGINT},
      </if>
      <if test="effectiveTime != null">
        #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="productDesc != null">
        #{productDesc,jdbcType=VARCHAR},
      </if>
      <if test="productNote != null">
        #{productNote,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductConfigExample" resultType="java.lang.Long">
    select count(*) from claim_product_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_product_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="record.campaignDefName != null">
        campaign_def_name = #{record.campaignDefName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.packageDefName != null">
        package_def_name = #{record.packageDefName,jdbcType=VARCHAR},
      </if>
      <if test="record.channelName != null">
        channel_name = #{record.channelName,jdbcType=VARCHAR},
      </if>
      <if test="record.prodNo != null">
        prod_no = #{record.prodNo,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceType != null">
        insurance_type = #{record.insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="record.pipeline != null">
        pipeline = #{record.pipeline,jdbcType=VARCHAR},
      </if>
      <if test="record.tags != null">
        tags = #{record.tags,jdbcType=BIGINT},
      </if>
      <if test="record.effectiveTime != null">
        effective_time = #{record.effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.enable != null">
        enable = #{record.enable,jdbcType=BIT},
      </if>
      <if test="record.productDesc != null">
        product_desc = #{record.productDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.productNote != null">
        product_note = #{record.productNote,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_product_config
    set id = #{record.id,jdbcType=BIGINT},
      campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      campaign_def_name = #{record.campaignDefName,jdbcType=VARCHAR},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      package_def_name = #{record.packageDefName,jdbcType=VARCHAR},
      channel_name = #{record.channelName,jdbcType=VARCHAR},
      prod_no = #{record.prodNo,jdbcType=VARCHAR},
      insurance_type = #{record.insuranceType,jdbcType=VARCHAR},
      pipeline = #{record.pipeline,jdbcType=VARCHAR},
      tags = #{record.tags,jdbcType=BIGINT},
      effective_time = #{record.effectiveTime,jdbcType=TIMESTAMP},
      enable = #{record.enable,jdbcType=BIT},
      product_desc = #{record.productDesc,jdbcType=VARCHAR},
      product_note = #{record.productNote,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductConfigDO">
    update claim_product_config
    <set>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="campaignDefName != null">
        campaign_def_name = #{campaignDefName,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="packageDefName != null">
        package_def_name = #{packageDefName,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="prodNo != null">
        prod_no = #{prodNo,jdbcType=VARCHAR},
      </if>
      <if test="insuranceType != null">
        insurance_type = #{insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="pipeline != null">
        pipeline = #{pipeline,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        tags = #{tags,jdbcType=BIGINT},
      </if>
      <if test="effectiveTime != null">
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=BIT},
      </if>
      <if test="productDesc != null">
        product_desc = #{productDesc,jdbcType=VARCHAR},
      </if>
      <if test="productNote != null">
        product_note = #{productNote,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimProductConfigDO">
    update claim_product_config
    set campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      campaign_def_name = #{campaignDefName,jdbcType=VARCHAR},
      package_def_id = #{packageDefId,jdbcType=BIGINT},
      package_def_name = #{packageDefName,jdbcType=VARCHAR},
      channel_name = #{channelName,jdbcType=VARCHAR},
      prod_no = #{prodNo,jdbcType=VARCHAR},
      insurance_type = #{insuranceType,jdbcType=VARCHAR},
      pipeline = #{pipeline,jdbcType=VARCHAR},
      tags = #{tags,jdbcType=BIGINT},
      effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
      enable = #{enable,jdbcType=BIT},
      product_desc = #{productDesc,jdbcType=VARCHAR},
      product_note = #{productNote,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into claim_product_config
    (campaign_def_id, campaign_def_name, package_def_id, package_def_name, channel_name, 
      prod_no, insurance_type, pipeline, tags, effective_time, enable, product_desc, 
      product_note, creator, modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.campaignDefId,jdbcType=BIGINT}, #{item.campaignDefName,jdbcType=VARCHAR}, 
        #{item.packageDefId,jdbcType=BIGINT}, #{item.packageDefName,jdbcType=VARCHAR}, 
        #{item.channelName,jdbcType=VARCHAR}, #{item.prodNo,jdbcType=VARCHAR}, #{item.insuranceType,jdbcType=VARCHAR}, 
        #{item.pipeline,jdbcType=VARCHAR}, #{item.tags,jdbcType=BIGINT}, #{item.effectiveTime,jdbcType=TIMESTAMP}, 
        #{item.enable,jdbcType=BIT}, #{item.productDesc,jdbcType=VARCHAR}, #{item.productNote,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into claim_product_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=BIGINT}
        </if>
        <if test="'campaign_def_name'.toString() == column.value">
          #{item.campaignDefName,jdbcType=VARCHAR}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'package_def_name'.toString() == column.value">
          #{item.packageDefName,jdbcType=VARCHAR}
        </if>
        <if test="'channel_name'.toString() == column.value">
          #{item.channelName,jdbcType=VARCHAR}
        </if>
        <if test="'prod_no'.toString() == column.value">
          #{item.prodNo,jdbcType=VARCHAR}
        </if>
        <if test="'insurance_type'.toString() == column.value">
          #{item.insuranceType,jdbcType=VARCHAR}
        </if>
        <if test="'pipeline'.toString() == column.value">
          #{item.pipeline,jdbcType=VARCHAR}
        </if>
        <if test="'tags'.toString() == column.value">
          #{item.tags,jdbcType=BIGINT}
        </if>
        <if test="'effective_time'.toString() == column.value">
          #{item.effectiveTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'enable'.toString() == column.value">
          #{item.enable,jdbcType=BIT}
        </if>
        <if test="'product_desc'.toString() == column.value">
          #{item.productDesc,jdbcType=VARCHAR}
        </if>
        <if test="'product_note'.toString() == column.value">
          #{item.productNote,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>