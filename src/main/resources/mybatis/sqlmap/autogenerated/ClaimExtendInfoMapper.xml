<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimExtendInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimExtendInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="flight_no" jdbcType="VARCHAR" property="flightNo" />
    <result column="ticket_no" jdbcType="VARCHAR" property="ticketNo" />
    <result column="third_biz_no" jdbcType="VARCHAR" property="thirdBizNo" />
    <result column="claim_extend_type" jdbcType="VARCHAR" property="claimExtendType" />
    <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl" />
    <result column="revise_extra_info" jdbcType="VARCHAR" property="reviseExtraInfo" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="show_extend_info" jdbcType="VARCHAR" property="showExtendInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, flight_no, ticket_no, third_biz_no, claim_extend_type, attachment_url, 
    revise_extra_info, extra_info, show_extend_info, creator, gmt_created, modifier, 
    gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimExtendInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_claim_extend_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_claim_extend_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimExtendInfoDO">
    insert into cargo_claim_extend_info (id, report_no, flight_no, 
      ticket_no, third_biz_no, claim_extend_type, 
      attachment_url, revise_extra_info, extra_info, 
      show_extend_info, creator, gmt_created, 
      modifier, gmt_modified, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{flightNo,jdbcType=VARCHAR}, 
      #{ticketNo,jdbcType=VARCHAR}, #{thirdBizNo,jdbcType=VARCHAR}, #{claimExtendType,jdbcType=VARCHAR}, 
      #{attachmentUrl,jdbcType=VARCHAR}, #{reviseExtraInfo,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      #{showExtendInfo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, sysdate(), 
      #{modifier,jdbcType=VARCHAR}, sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimExtendInfoDO">
    insert into cargo_claim_extend_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="flightNo != null">
        flight_no,
      </if>
      <if test="ticketNo != null">
        ticket_no,
      </if>
      <if test="thirdBizNo != null">
        third_biz_no,
      </if>
      <if test="claimExtendType != null">
        claim_extend_type,
      </if>
      <if test="attachmentUrl != null">
        attachment_url,
      </if>
      <if test="reviseExtraInfo != null">
        revise_extra_info,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="showExtendInfo != null">
        show_extend_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="flightNo != null">
        #{flightNo,jdbcType=VARCHAR},
      </if>
      <if test="ticketNo != null">
        #{ticketNo,jdbcType=VARCHAR},
      </if>
      <if test="thirdBizNo != null">
        #{thirdBizNo,jdbcType=VARCHAR},
      </if>
      <if test="claimExtendType != null">
        #{claimExtendType,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null">
        #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="reviseExtraInfo != null">
        #{reviseExtraInfo,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="showExtendInfo != null">
        #{showExtendInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimExtendInfoExample" resultType="java.lang.Long">
    select count(*) from cargo_claim_extend_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_claim_extend_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.flightNo != null">
        flight_no = #{record.flightNo,jdbcType=VARCHAR},
      </if>
      <if test="record.ticketNo != null">
        ticket_no = #{record.ticketNo,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdBizNo != null">
        third_biz_no = #{record.thirdBizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimExtendType != null">
        claim_extend_type = #{record.claimExtendType,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentUrl != null">
        attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.reviseExtraInfo != null">
        revise_extra_info = #{record.reviseExtraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.showExtendInfo != null">
        show_extend_info = #{record.showExtendInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_claim_extend_info
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      flight_no = #{record.flightNo,jdbcType=VARCHAR},
      ticket_no = #{record.ticketNo,jdbcType=VARCHAR},
      third_biz_no = #{record.thirdBizNo,jdbcType=VARCHAR},
      claim_extend_type = #{record.claimExtendType,jdbcType=VARCHAR},
      attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      revise_extra_info = #{record.reviseExtraInfo,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      show_extend_info = #{record.showExtendInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimExtendInfoDO">
    update cargo_claim_extend_info
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="flightNo != null">
        flight_no = #{flightNo,jdbcType=VARCHAR},
      </if>
      <if test="ticketNo != null">
        ticket_no = #{ticketNo,jdbcType=VARCHAR},
      </if>
      <if test="thirdBizNo != null">
        third_biz_no = #{thirdBizNo,jdbcType=VARCHAR},
      </if>
      <if test="claimExtendType != null">
        claim_extend_type = #{claimExtendType,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null">
        attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="reviseExtraInfo != null">
        revise_extra_info = #{reviseExtraInfo,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="showExtendInfo != null">
        show_extend_info = #{showExtendInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimExtendInfoDO">
    update cargo_claim_extend_info
    set report_no = #{reportNo,jdbcType=VARCHAR},
      flight_no = #{flightNo,jdbcType=VARCHAR},
      ticket_no = #{ticketNo,jdbcType=VARCHAR},
      third_biz_no = #{thirdBizNo,jdbcType=VARCHAR},
      claim_extend_type = #{claimExtendType,jdbcType=VARCHAR},
      attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      revise_extra_info = #{reviseExtraInfo,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      show_extend_info = #{showExtendInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_claim_extend_info
    (id, report_no, flight_no, ticket_no, third_biz_no, claim_extend_type, attachment_url, 
      revise_extra_info, extra_info, show_extend_info, creator, gmt_created, modifier, 
      gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.flightNo,jdbcType=VARCHAR}, 
        #{item.ticketNo,jdbcType=VARCHAR}, #{item.thirdBizNo,jdbcType=VARCHAR}, #{item.claimExtendType,jdbcType=VARCHAR}, 
        #{item.attachmentUrl,jdbcType=VARCHAR}, #{item.reviseExtraInfo,jdbcType=VARCHAR}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.showExtendInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_claim_extend_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'flight_no'.toString() == column.value">
          #{item.flightNo,jdbcType=VARCHAR}
        </if>
        <if test="'ticket_no'.toString() == column.value">
          #{item.ticketNo,jdbcType=VARCHAR}
        </if>
        <if test="'third_biz_no'.toString() == column.value">
          #{item.thirdBizNo,jdbcType=VARCHAR}
        </if>
        <if test="'claim_extend_type'.toString() == column.value">
          #{item.claimExtendType,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_url'.toString() == column.value">
          #{item.attachmentUrl,jdbcType=VARCHAR}
        </if>
        <if test="'revise_extra_info'.toString() == column.value">
          #{item.reviseExtraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'show_extend_info'.toString() == column.value">
          #{item.showExtendInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>