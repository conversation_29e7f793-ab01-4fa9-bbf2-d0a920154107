<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimHospitalDiseaseInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimHospitalDiseaseInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="disease_remark" jdbcType="VARCHAR" property="diseaseRemark" />
    <result column="disease_name" jdbcType="VARCHAR" property="diseaseName" />
    <result column="disease_describe" jdbcType="VARCHAR" property="diseaseDescribe" />
    <result column="similarity_degree" jdbcType="VARCHAR" property="similarityDegree" />
    <result column="claim_hospital_id" jdbcType="BIGINT" property="claimHospitalId" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, disease_remark, disease_name, disease_describe, similarity_degree, claim_hospital_id, 
    gmt_created, gmt_modified, creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalDiseaseInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_hospital_disease_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_hospital_disease_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalDiseaseInfoDO">
    insert into claim_hospital_disease_info (id, disease_remark, disease_name, 
      disease_describe, similarity_degree, claim_hospital_id, 
      gmt_created, gmt_modified, creator, 
      modifier, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{diseaseRemark,jdbcType=VARCHAR}, #{diseaseName,jdbcType=VARCHAR}, 
      #{diseaseDescribe,jdbcType=VARCHAR}, #{similarityDegree,jdbcType=VARCHAR}, #{claimHospitalId,jdbcType=BIGINT}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalDiseaseInfoDO">
    insert into claim_hospital_disease_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="diseaseRemark != null">
        disease_remark,
      </if>
      <if test="diseaseName != null">
        disease_name,
      </if>
      <if test="diseaseDescribe != null">
        disease_describe,
      </if>
      <if test="similarityDegree != null">
        similarity_degree,
      </if>
      <if test="claimHospitalId != null">
        claim_hospital_id,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="diseaseRemark != null">
        #{diseaseRemark,jdbcType=VARCHAR},
      </if>
      <if test="diseaseName != null">
        #{diseaseName,jdbcType=VARCHAR},
      </if>
      <if test="diseaseDescribe != null">
        #{diseaseDescribe,jdbcType=VARCHAR},
      </if>
      <if test="similarityDegree != null">
        #{similarityDegree,jdbcType=VARCHAR},
      </if>
      <if test="claimHospitalId != null">
        #{claimHospitalId,jdbcType=BIGINT},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalDiseaseInfoExample" resultType="java.lang.Long">
    select count(*) from claim_hospital_disease_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_hospital_disease_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.diseaseRemark != null">
        disease_remark = #{record.diseaseRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.diseaseName != null">
        disease_name = #{record.diseaseName,jdbcType=VARCHAR},
      </if>
      <if test="record.diseaseDescribe != null">
        disease_describe = #{record.diseaseDescribe,jdbcType=VARCHAR},
      </if>
      <if test="record.similarityDegree != null">
        similarity_degree = #{record.similarityDegree,jdbcType=VARCHAR},
      </if>
      <if test="record.claimHospitalId != null">
        claim_hospital_id = #{record.claimHospitalId,jdbcType=BIGINT},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_hospital_disease_info
    set id = #{record.id,jdbcType=BIGINT},
      disease_remark = #{record.diseaseRemark,jdbcType=VARCHAR},
      disease_name = #{record.diseaseName,jdbcType=VARCHAR},
      disease_describe = #{record.diseaseDescribe,jdbcType=VARCHAR},
      similarity_degree = #{record.similarityDegree,jdbcType=VARCHAR},
      claim_hospital_id = #{record.claimHospitalId,jdbcType=BIGINT},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalDiseaseInfoDO">
    update claim_hospital_disease_info
    <set>
      <if test="diseaseRemark != null">
        disease_remark = #{diseaseRemark,jdbcType=VARCHAR},
      </if>
      <if test="diseaseName != null">
        disease_name = #{diseaseName,jdbcType=VARCHAR},
      </if>
      <if test="diseaseDescribe != null">
        disease_describe = #{diseaseDescribe,jdbcType=VARCHAR},
      </if>
      <if test="similarityDegree != null">
        similarity_degree = #{similarityDegree,jdbcType=VARCHAR},
      </if>
      <if test="claimHospitalId != null">
        claim_hospital_id = #{claimHospitalId,jdbcType=BIGINT},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimHospitalDiseaseInfoDO">
    update claim_hospital_disease_info
    set disease_remark = #{diseaseRemark,jdbcType=VARCHAR},
      disease_name = #{diseaseName,jdbcType=VARCHAR},
      disease_describe = #{diseaseDescribe,jdbcType=VARCHAR},
      similarity_degree = #{similarityDegree,jdbcType=VARCHAR},
      claim_hospital_id = #{claimHospitalId,jdbcType=BIGINT},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_hospital_disease_info
    (id, disease_remark, disease_name, disease_describe, similarity_degree, claim_hospital_id, 
      gmt_created, gmt_modified, creator, modifier, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.diseaseRemark,jdbcType=VARCHAR}, #{item.diseaseName,jdbcType=VARCHAR}, 
        #{item.diseaseDescribe,jdbcType=VARCHAR}, #{item.similarityDegree,jdbcType=VARCHAR}, 
        #{item.claimHospitalId,jdbcType=BIGINT}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_hospital_disease_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'disease_remark'.toString() == column.value">
          #{item.diseaseRemark,jdbcType=VARCHAR}
        </if>
        <if test="'disease_name'.toString() == column.value">
          #{item.diseaseName,jdbcType=VARCHAR}
        </if>
        <if test="'disease_describe'.toString() == column.value">
          #{item.diseaseDescribe,jdbcType=VARCHAR}
        </if>
        <if test="'similarity_degree'.toString() == column.value">
          #{item.similarityDegree,jdbcType=VARCHAR}
        </if>
        <if test="'claim_hospital_id'.toString() == column.value">
          #{item.claimHospitalId,jdbcType=BIGINT}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>