<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimBatchRelationInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimBatchRelationInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_claim_bill_id" jdbcType="BIGINT" property="batchClaimBillId" />
    <result column="batch_claim_bill_no" jdbcType="VARCHAR" property="batchClaimBillNo" />
    <result column="policy_id" jdbcType="BIGINT" property="policyId" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="insurant_name" jdbcType="VARCHAR" property="insurantName" />
    <result column="insurant_cert_type" jdbcType="VARCHAR" property="insurantCertType" />
    <result column="insurant_cert_no" jdbcType="VARCHAR" property="insurantCertNo" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="core_product_d" jdbcType="BIGINT" property="coreProductD" />
    <result column="core_package_def_id" jdbcType="BIGINT" property="corePackageDefId" />
    <result column="insure_date" jdbcType="TIMESTAMP" property="insureDate" />
    <result column="effective_date" jdbcType="TIMESTAMP" property="effectiveDate" />
    <result column="expiry_date" jdbcType="TIMESTAMP" property="expiryDate" />
    <result column="channel_policy_end_time" jdbcType="TIMESTAMP" property="channelPolicyEndTime" />
    <result column="sum_insured" jdbcType="VARCHAR" property="sumInsured" />
    <result column="unused_sum_insured" jdbcType="VARCHAR" property="unusedSumInsured" />
    <result column="premium" jdbcType="VARCHAR" property="premium" />
    <result column="policy_status" jdbcType="TINYINT" property="policyStatus" />
    <result column="is_valid_policy" jdbcType="CHAR" property="isValidPolicy" />
    <result column="claim_id" jdbcType="BIGINT" property="claimId" />
    <result column="loss_amount" jdbcType="VARCHAR" property="lossAmount" />
    <result column="report_amount" jdbcType="VARCHAR" property="reportAmount" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="claim_no" jdbcType="VARCHAR" property="claimNo" />
    <result column="accident_date" jdbcType="TIMESTAMP" property="accidentDate" />
    <result column="report_date" jdbcType="TIMESTAMP" property="reportDate" />
    <result column="register_date" jdbcType="TIMESTAMP" property="registerDate" />
    <result column="settlement_date" jdbcType="TIMESTAMP" property="settlementDate" />
    <result column="close_date" jdbcType="TIMESTAMP" property="closeDate" />
    <result column="allow_wo" jdbcType="CHAR" property="allowWo" />
    <result column="claim_status" jdbcType="INTEGER" property="claimStatus" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, batch_claim_bill_id, batch_claim_bill_no, policy_id, policy_no, insurant_name, 
    insurant_cert_type, insurant_cert_no, product_code, product_name, core_product_d, 
    core_package_def_id, insure_date, effective_date, expiry_date, channel_policy_end_time, 
    sum_insured, unused_sum_insured, premium, policy_status, is_valid_policy, claim_id, 
    loss_amount, report_amount, report_no, claim_no, accident_date, report_date, register_date, 
    settlement_date, close_date, allow_wo, claim_status, is_deleted, creator, modifier, 
    gmt_created, gmt_modified
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchRelationInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_batch_relation_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_batch_relation_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchRelationInfoDO">
    insert into claim_batch_relation_info (id, batch_claim_bill_id, batch_claim_bill_no, 
      policy_id, policy_no, insurant_name, 
      insurant_cert_type, insurant_cert_no, product_code, 
      product_name, core_product_d, core_package_def_id, 
      insure_date, effective_date, expiry_date, 
      channel_policy_end_time, sum_insured, unused_sum_insured, 
      premium, policy_status, is_valid_policy, 
      claim_id, loss_amount, report_amount, 
      report_no, claim_no, accident_date, 
      report_date, register_date, settlement_date, 
      close_date, allow_wo, claim_status, 
      is_deleted, creator, modifier, 
      gmt_created, gmt_modified)
    values (#{id,jdbcType=BIGINT}, #{batchClaimBillId,jdbcType=BIGINT}, #{batchClaimBillNo,jdbcType=VARCHAR}, 
      #{policyId,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, #{insurantName,jdbcType=VARCHAR}, 
      #{insurantCertType,jdbcType=VARCHAR}, #{insurantCertNo,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{coreProductD,jdbcType=BIGINT}, #{corePackageDefId,jdbcType=BIGINT}, 
      #{insureDate,jdbcType=TIMESTAMP}, #{effectiveDate,jdbcType=TIMESTAMP}, #{expiryDate,jdbcType=TIMESTAMP}, 
      #{channelPolicyEndTime,jdbcType=TIMESTAMP}, #{sumInsured,jdbcType=VARCHAR}, #{unusedSumInsured,jdbcType=VARCHAR}, 
      #{premium,jdbcType=VARCHAR}, #{policyStatus,jdbcType=TINYINT}, #{isValidPolicy,jdbcType=CHAR}, 
      #{claimId,jdbcType=BIGINT}, #{lossAmount,jdbcType=VARCHAR}, #{reportAmount,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{claimNo,jdbcType=VARCHAR}, #{accidentDate,jdbcType=TIMESTAMP}, 
      #{reportDate,jdbcType=TIMESTAMP}, #{registerDate,jdbcType=TIMESTAMP}, #{settlementDate,jdbcType=TIMESTAMP}, 
      #{closeDate,jdbcType=TIMESTAMP}, #{allowWo,jdbcType=CHAR}, #{claimStatus,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=CHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate())
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchRelationInfoDO">
    insert into claim_batch_relation_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="batchClaimBillId != null">
        batch_claim_bill_id,
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no,
      </if>
      <if test="policyId != null">
        policy_id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="insurantName != null">
        insurant_name,
      </if>
      <if test="insurantCertType != null">
        insurant_cert_type,
      </if>
      <if test="insurantCertNo != null">
        insurant_cert_no,
      </if>
      <if test="productCode != null">
        product_code,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="coreProductD != null">
        core_product_d,
      </if>
      <if test="corePackageDefId != null">
        core_package_def_id,
      </if>
      <if test="insureDate != null">
        insure_date,
      </if>
      <if test="effectiveDate != null">
        effective_date,
      </if>
      <if test="expiryDate != null">
        expiry_date,
      </if>
      <if test="channelPolicyEndTime != null">
        channel_policy_end_time,
      </if>
      <if test="sumInsured != null">
        sum_insured,
      </if>
      <if test="unusedSumInsured != null">
        unused_sum_insured,
      </if>
      <if test="premium != null">
        premium,
      </if>
      <if test="policyStatus != null">
        policy_status,
      </if>
      <if test="isValidPolicy != null">
        is_valid_policy,
      </if>
      <if test="claimId != null">
        claim_id,
      </if>
      <if test="lossAmount != null">
        loss_amount,
      </if>
      <if test="reportAmount != null">
        report_amount,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="claimNo != null">
        claim_no,
      </if>
      <if test="accidentDate != null">
        accident_date,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="registerDate != null">
        register_date,
      </if>
      <if test="settlementDate != null">
        settlement_date,
      </if>
      <if test="closeDate != null">
        close_date,
      </if>
      <if test="allowWo != null">
        allow_wo,
      </if>
      <if test="claimStatus != null">
        claim_status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillId != null">
        #{batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillNo != null">
        #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        #{policyId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantName != null">
        #{insurantName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertType != null">
        #{insurantCertType,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertNo != null">
        #{insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="coreProductD != null">
        #{coreProductD,jdbcType=BIGINT},
      </if>
      <if test="corePackageDefId != null">
        #{corePackageDefId,jdbcType=BIGINT},
      </if>
      <if test="insureDate != null">
        #{insureDate,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveDate != null">
        #{effectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expiryDate != null">
        #{expiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="channelPolicyEndTime != null">
        #{channelPolicyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sumInsured != null">
        #{sumInsured,jdbcType=VARCHAR},
      </if>
      <if test="unusedSumInsured != null">
        #{unusedSumInsured,jdbcType=VARCHAR},
      </if>
      <if test="premium != null">
        #{premium,jdbcType=VARCHAR},
      </if>
      <if test="policyStatus != null">
        #{policyStatus,jdbcType=TINYINT},
      </if>
      <if test="isValidPolicy != null">
        #{isValidPolicy,jdbcType=CHAR},
      </if>
      <if test="claimId != null">
        #{claimId,jdbcType=BIGINT},
      </if>
      <if test="lossAmount != null">
        #{lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="reportAmount != null">
        #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        #{accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="registerDate != null">
        #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementDate != null">
        #{settlementDate,jdbcType=TIMESTAMP},
      </if>
      <if test="closeDate != null">
        #{closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allowWo != null">
        #{allowWo,jdbcType=CHAR},
      </if>
      <if test="claimStatus != null">
        #{claimStatus,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchRelationInfoExample" resultType="java.lang.Long">
    select count(*) from claim_batch_relation_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_batch_relation_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.batchClaimBillId != null">
        batch_claim_bill_id = #{record.batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="record.batchClaimBillNo != null">
        batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyId != null">
        policy_id = #{record.policyId,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantName != null">
        insurant_name = #{record.insurantName,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCertType != null">
        insurant_cert_type = #{record.insurantCertType,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCertNo != null">
        insurant_cert_no = #{record.insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="record.productCode != null">
        product_code = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.coreProductD != null">
        core_product_d = #{record.coreProductD,jdbcType=BIGINT},
      </if>
      <if test="record.corePackageDefId != null">
        core_package_def_id = #{record.corePackageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.insureDate != null">
        insure_date = #{record.insureDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.effectiveDate != null">
        effective_date = #{record.effectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expiryDate != null">
        expiry_date = #{record.expiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.channelPolicyEndTime != null">
        channel_policy_end_time = #{record.channelPolicyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sumInsured != null">
        sum_insured = #{record.sumInsured,jdbcType=VARCHAR},
      </if>
      <if test="record.unusedSumInsured != null">
        unused_sum_insured = #{record.unusedSumInsured,jdbcType=VARCHAR},
      </if>
      <if test="record.premium != null">
        premium = #{record.premium,jdbcType=VARCHAR},
      </if>
      <if test="record.policyStatus != null">
        policy_status = #{record.policyStatus,jdbcType=TINYINT},
      </if>
      <if test="record.isValidPolicy != null">
        is_valid_policy = #{record.isValidPolicy,jdbcType=CHAR},
      </if>
      <if test="record.claimId != null">
        claim_id = #{record.claimId,jdbcType=BIGINT},
      </if>
      <if test="record.lossAmount != null">
        loss_amount = #{record.lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.reportAmount != null">
        report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimNo != null">
        claim_no = #{record.claimNo,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentDate != null">
        accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportDate != null">
        report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.registerDate != null">
        register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.settlementDate != null">
        settlement_date = #{record.settlementDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.closeDate != null">
        close_date = #{record.closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.allowWo != null">
        allow_wo = #{record.allowWo,jdbcType=CHAR},
      </if>
      <if test="record.claimStatus != null">
        claim_status = #{record.claimStatus,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_batch_relation_info
    set id = #{record.id,jdbcType=BIGINT},
      batch_claim_bill_id = #{record.batchClaimBillId,jdbcType=BIGINT},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      policy_id = #{record.policyId,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      insurant_name = #{record.insurantName,jdbcType=VARCHAR},
      insurant_cert_type = #{record.insurantCertType,jdbcType=VARCHAR},
      insurant_cert_no = #{record.insurantCertNo,jdbcType=VARCHAR},
      product_code = #{record.productCode,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      core_product_d = #{record.coreProductD,jdbcType=BIGINT},
      core_package_def_id = #{record.corePackageDefId,jdbcType=BIGINT},
      insure_date = #{record.insureDate,jdbcType=TIMESTAMP},
      effective_date = #{record.effectiveDate,jdbcType=TIMESTAMP},
      expiry_date = #{record.expiryDate,jdbcType=TIMESTAMP},
      channel_policy_end_time = #{record.channelPolicyEndTime,jdbcType=TIMESTAMP},
      sum_insured = #{record.sumInsured,jdbcType=VARCHAR},
      unused_sum_insured = #{record.unusedSumInsured,jdbcType=VARCHAR},
      premium = #{record.premium,jdbcType=VARCHAR},
      policy_status = #{record.policyStatus,jdbcType=TINYINT},
      is_valid_policy = #{record.isValidPolicy,jdbcType=CHAR},
      claim_id = #{record.claimId,jdbcType=BIGINT},
      loss_amount = #{record.lossAmount,jdbcType=VARCHAR},
      report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      claim_no = #{record.claimNo,jdbcType=VARCHAR},
      accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      settlement_date = #{record.settlementDate,jdbcType=TIMESTAMP},
      close_date = #{record.closeDate,jdbcType=TIMESTAMP},
      allow_wo = #{record.allowWo,jdbcType=CHAR},
      claim_status = #{record.claimStatus,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate()
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchRelationInfoDO">
    update claim_batch_relation_info
    <set>
      <if test="batchClaimBillId != null">
        batch_claim_bill_id = #{batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        policy_id = #{policyId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantName != null">
        insurant_name = #{insurantName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertType != null">
        insurant_cert_type = #{insurantCertType,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertNo != null">
        insurant_cert_no = #{insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="coreProductD != null">
        core_product_d = #{coreProductD,jdbcType=BIGINT},
      </if>
      <if test="corePackageDefId != null">
        core_package_def_id = #{corePackageDefId,jdbcType=BIGINT},
      </if>
      <if test="insureDate != null">
        insure_date = #{insureDate,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveDate != null">
        effective_date = #{effectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expiryDate != null">
        expiry_date = #{expiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="channelPolicyEndTime != null">
        channel_policy_end_time = #{channelPolicyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sumInsured != null">
        sum_insured = #{sumInsured,jdbcType=VARCHAR},
      </if>
      <if test="unusedSumInsured != null">
        unused_sum_insured = #{unusedSumInsured,jdbcType=VARCHAR},
      </if>
      <if test="premium != null">
        premium = #{premium,jdbcType=VARCHAR},
      </if>
      <if test="policyStatus != null">
        policy_status = #{policyStatus,jdbcType=TINYINT},
      </if>
      <if test="isValidPolicy != null">
        is_valid_policy = #{isValidPolicy,jdbcType=CHAR},
      </if>
      <if test="claimId != null">
        claim_id = #{claimId,jdbcType=BIGINT},
      </if>
      <if test="lossAmount != null">
        loss_amount = #{lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="reportAmount != null">
        report_amount = #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        claim_no = #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="registerDate != null">
        register_date = #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementDate != null">
        settlement_date = #{settlementDate,jdbcType=TIMESTAMP},
      </if>
      <if test="closeDate != null">
        close_date = #{closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="allowWo != null">
        allow_wo = #{allowWo,jdbcType=CHAR},
      </if>
      <if test="claimStatus != null">
        claim_status = #{claimStatus,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchRelationInfoDO">
    update claim_batch_relation_info
    set batch_claim_bill_id = #{batchClaimBillId,jdbcType=BIGINT},
      batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      policy_id = #{policyId,jdbcType=BIGINT},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      insurant_name = #{insurantName,jdbcType=VARCHAR},
      insurant_cert_type = #{insurantCertType,jdbcType=VARCHAR},
      insurant_cert_no = #{insurantCertNo,jdbcType=VARCHAR},
      product_code = #{productCode,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      core_product_d = #{coreProductD,jdbcType=BIGINT},
      core_package_def_id = #{corePackageDefId,jdbcType=BIGINT},
      insure_date = #{insureDate,jdbcType=TIMESTAMP},
      effective_date = #{effectiveDate,jdbcType=TIMESTAMP},
      expiry_date = #{expiryDate,jdbcType=TIMESTAMP},
      channel_policy_end_time = #{channelPolicyEndTime,jdbcType=TIMESTAMP},
      sum_insured = #{sumInsured,jdbcType=VARCHAR},
      unused_sum_insured = #{unusedSumInsured,jdbcType=VARCHAR},
      premium = #{premium,jdbcType=VARCHAR},
      policy_status = #{policyStatus,jdbcType=TINYINT},
      is_valid_policy = #{isValidPolicy,jdbcType=CHAR},
      claim_id = #{claimId,jdbcType=BIGINT},
      loss_amount = #{lossAmount,jdbcType=VARCHAR},
      report_amount = #{reportAmount,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      claim_no = #{claimNo,jdbcType=VARCHAR},
      accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      report_date = #{reportDate,jdbcType=TIMESTAMP},
      register_date = #{registerDate,jdbcType=TIMESTAMP},
      settlement_date = #{settlementDate,jdbcType=TIMESTAMP},
      close_date = #{closeDate,jdbcType=TIMESTAMP},
      allow_wo = #{allowWo,jdbcType=CHAR},
      claim_status = #{claimStatus,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate()
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_batch_relation_info
    (id, batch_claim_bill_id, batch_claim_bill_no, policy_id, policy_no, insurant_name, 
      insurant_cert_type, insurant_cert_no, product_code, product_name, core_product_d, 
      core_package_def_id, insure_date, effective_date, expiry_date, channel_policy_end_time, 
      sum_insured, unused_sum_insured, premium, policy_status, is_valid_policy, claim_id, 
      loss_amount, report_amount, report_no, claim_no, accident_date, report_date, register_date, 
      settlement_date, close_date, allow_wo, claim_status, is_deleted, creator, modifier, 
      gmt_created, gmt_modified)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.batchClaimBillId,jdbcType=BIGINT}, #{item.batchClaimBillNo,jdbcType=VARCHAR}, 
        #{item.policyId,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, #{item.insurantName,jdbcType=VARCHAR}, 
        #{item.insurantCertType,jdbcType=VARCHAR}, #{item.insurantCertNo,jdbcType=VARCHAR}, 
        #{item.productCode,jdbcType=VARCHAR}, #{item.productName,jdbcType=VARCHAR}, #{item.coreProductD,jdbcType=BIGINT}, 
        #{item.corePackageDefId,jdbcType=BIGINT}, #{item.insureDate,jdbcType=TIMESTAMP}, 
        #{item.effectiveDate,jdbcType=TIMESTAMP}, #{item.expiryDate,jdbcType=TIMESTAMP}, 
        #{item.channelPolicyEndTime,jdbcType=TIMESTAMP}, #{item.sumInsured,jdbcType=VARCHAR}, 
        #{item.unusedSumInsured,jdbcType=VARCHAR}, #{item.premium,jdbcType=VARCHAR}, #{item.policyStatus,jdbcType=TINYINT}, 
        #{item.isValidPolicy,jdbcType=CHAR}, #{item.claimId,jdbcType=BIGINT}, #{item.lossAmount,jdbcType=VARCHAR}, 
        #{item.reportAmount,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.claimNo,jdbcType=VARCHAR}, 
        #{item.accidentDate,jdbcType=TIMESTAMP}, #{item.reportDate,jdbcType=TIMESTAMP}, 
        #{item.registerDate,jdbcType=TIMESTAMP}, #{item.settlementDate,jdbcType=TIMESTAMP}, 
        #{item.closeDate,jdbcType=TIMESTAMP}, #{item.allowWo,jdbcType=CHAR}, #{item.claimStatus,jdbcType=INTEGER}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_batch_relation_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'batch_claim_bill_id'.toString() == column.value">
          #{item.batchClaimBillId,jdbcType=BIGINT}
        </if>
        <if test="'batch_claim_bill_no'.toString() == column.value">
          #{item.batchClaimBillNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_id'.toString() == column.value">
          #{item.policyId,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_name'.toString() == column.value">
          #{item.insurantName,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_cert_type'.toString() == column.value">
          #{item.insurantCertType,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_cert_no'.toString() == column.value">
          #{item.insurantCertNo,jdbcType=VARCHAR}
        </if>
        <if test="'product_code'.toString() == column.value">
          #{item.productCode,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'core_product_d'.toString() == column.value">
          #{item.coreProductD,jdbcType=BIGINT}
        </if>
        <if test="'core_package_def_id'.toString() == column.value">
          #{item.corePackageDefId,jdbcType=BIGINT}
        </if>
        <if test="'insure_date'.toString() == column.value">
          #{item.insureDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'effective_date'.toString() == column.value">
          #{item.effectiveDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'expiry_date'.toString() == column.value">
          #{item.expiryDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'channel_policy_end_time'.toString() == column.value">
          #{item.channelPolicyEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'sum_insured'.toString() == column.value">
          #{item.sumInsured,jdbcType=VARCHAR}
        </if>
        <if test="'unused_sum_insured'.toString() == column.value">
          #{item.unusedSumInsured,jdbcType=VARCHAR}
        </if>
        <if test="'premium'.toString() == column.value">
          #{item.premium,jdbcType=VARCHAR}
        </if>
        <if test="'policy_status'.toString() == column.value">
          #{item.policyStatus,jdbcType=TINYINT}
        </if>
        <if test="'is_valid_policy'.toString() == column.value">
          #{item.isValidPolicy,jdbcType=CHAR}
        </if>
        <if test="'claim_id'.toString() == column.value">
          #{item.claimId,jdbcType=BIGINT}
        </if>
        <if test="'loss_amount'.toString() == column.value">
          #{item.lossAmount,jdbcType=VARCHAR}
        </if>
        <if test="'report_amount'.toString() == column.value">
          #{item.reportAmount,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'claim_no'.toString() == column.value">
          #{item.claimNo,jdbcType=VARCHAR}
        </if>
        <if test="'accident_date'.toString() == column.value">
          #{item.accidentDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'report_date'.toString() == column.value">
          #{item.reportDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'register_date'.toString() == column.value">
          #{item.registerDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'settlement_date'.toString() == column.value">
          #{item.settlementDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'close_date'.toString() == column.value">
          #{item.closeDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'allow_wo'.toString() == column.value">
          #{item.allowWo,jdbcType=CHAR}
        </if>
        <if test="'claim_status'.toString() == column.value">
          #{item.claimStatus,jdbcType=INTEGER}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>