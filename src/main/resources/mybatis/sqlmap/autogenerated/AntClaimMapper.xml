<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AntClaimMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AntClaimDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_id" jdbcType="BIGINT" property="policyId" />
    <result column="claim_id" jdbcType="BIGINT" property="claimId" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="claim_no" jdbcType="VARCHAR" property="claimNo" />
    <result column="claim_case_no" jdbcType="VARCHAR" property="claimCaseNo" />
    <result column="channel_report_no" jdbcType="VARCHAR" property="channelReportNo" />
    <result column="channel_policy_no" jdbcType="VARCHAR" property="channelPolicyNo" />
    <result column="campaign_def_id" jdbcType="BIGINT" property="campaignDefId" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="claimer_cert_no" jdbcType="VARCHAR" property="claimerCertNo" />
    <result column="accident_time" jdbcType="TIMESTAMP" property="accidentTime" />
    <result column="report_time" jdbcType="TIMESTAMP" property="reportTime" />
    <result column="resolve_time" jdbcType="TIMESTAMP" property="resolveTime" />
    <result column="claim_record_time" jdbcType="TIMESTAMP" property="claimRecordTime" />
    <result column="paid_amount" jdbcType="VARCHAR" property="paidAmount" />
    <result column="report_amount" jdbcType="VARCHAR" property="reportAmount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="not_matched_node" jdbcType="VARCHAR" property="notMatchedNode" />
    <result column="retry_time" jdbcType="TIMESTAMP" property="retryTime" />
    <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
    <result column="hangup" jdbcType="CHAR" property="hangup" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_id, claim_id, policy_no, report_no, claim_no, claim_case_no, channel_report_no, 
    channel_policy_no, campaign_def_id, package_def_id, claimer_cert_no, accident_time, 
    report_time, resolve_time, claim_record_time, paid_amount, report_amount, status, 
    not_matched_node, retry_time, retry_count, hangup, creator, modifier, gmt_created, 
    gmt_modified, extra_info, remark, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AntClaimExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ant_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ant_claim
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AntClaimDO">
    insert into ant_claim (id, policy_id, claim_id, 
      policy_no, report_no, claim_no, 
      claim_case_no, channel_report_no, channel_policy_no, 
      campaign_def_id, package_def_id, claimer_cert_no, 
      accident_time, report_time, resolve_time, 
      claim_record_time, paid_amount, report_amount, 
      status, not_matched_node, retry_time, 
      retry_count, hangup, creator, 
      modifier, gmt_created, gmt_modified, 
      extra_info, remark, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{policyId,jdbcType=BIGINT}, #{claimId,jdbcType=BIGINT}, 
      #{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{claimNo,jdbcType=VARCHAR}, 
      #{claimCaseNo,jdbcType=VARCHAR}, #{channelReportNo,jdbcType=VARCHAR}, #{channelPolicyNo,jdbcType=VARCHAR}, 
      #{campaignDefId,jdbcType=BIGINT}, #{packageDefId,jdbcType=BIGINT}, #{claimerCertNo,jdbcType=VARCHAR}, 
      #{accidentTime,jdbcType=TIMESTAMP}, #{reportTime,jdbcType=TIMESTAMP}, #{resolveTime,jdbcType=TIMESTAMP}, 
      #{claimRecordTime,jdbcType=TIMESTAMP}, #{paidAmount,jdbcType=VARCHAR}, #{reportAmount,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{notMatchedNode,jdbcType=VARCHAR}, #{retryTime,jdbcType=TIMESTAMP}, 
      #{retryCount,jdbcType=INTEGER}, #{hangup,jdbcType=CHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate(), 
      #{extraInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AntClaimDO">
    insert into ant_claim
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="policyId != null">
        policy_id,
      </if>
      <if test="claimId != null">
        claim_id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="claimNo != null">
        claim_no,
      </if>
      <if test="claimCaseNo != null">
        claim_case_no,
      </if>
      <if test="channelReportNo != null">
        channel_report_no,
      </if>
      <if test="channelPolicyNo != null">
        channel_policy_no,
      </if>
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="claimerCertNo != null">
        claimer_cert_no,
      </if>
      <if test="accidentTime != null">
        accident_time,
      </if>
      <if test="reportTime != null">
        report_time,
      </if>
      <if test="resolveTime != null">
        resolve_time,
      </if>
      <if test="claimRecordTime != null">
        claim_record_time,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="reportAmount != null">
        report_amount,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="notMatchedNode != null">
        not_matched_node,
      </if>
      <if test="retryTime != null">
        retry_time,
      </if>
      <if test="retryCount != null">
        retry_count,
      </if>
      <if test="hangup != null">
        hangup,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="policyId != null">
        #{policyId,jdbcType=BIGINT},
      </if>
      <if test="claimId != null">
        #{claimId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="claimCaseNo != null">
        #{claimCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="channelReportNo != null">
        #{channelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="channelPolicyNo != null">
        #{channelPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="claimerCertNo != null">
        #{claimerCertNo,jdbcType=VARCHAR},
      </if>
      <if test="accidentTime != null">
        #{accidentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportTime != null">
        #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resolveTime != null">
        #{resolveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="claimRecordTime != null">
        #{claimRecordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="reportAmount != null">
        #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="notMatchedNode != null">
        #{notMatchedNode,jdbcType=VARCHAR},
      </if>
      <if test="retryTime != null">
        #{retryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="retryCount != null">
        #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="hangup != null">
        #{hangup,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AntClaimExample" resultType="java.lang.Long">
    select count(*) from ant_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ant_claim
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyId != null">
        policy_id = #{record.policyId,jdbcType=BIGINT},
      </if>
      <if test="record.claimId != null">
        claim_id = #{record.claimId,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimNo != null">
        claim_no = #{record.claimNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimCaseNo != null">
        claim_case_no = #{record.claimCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.channelReportNo != null">
        channel_report_no = #{record.channelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.channelPolicyNo != null">
        channel_policy_no = #{record.channelPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.claimerCertNo != null">
        claimer_cert_no = #{record.claimerCertNo,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentTime != null">
        accident_time = #{record.accidentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportTime != null">
        report_time = #{record.reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.resolveTime != null">
        resolve_time = #{record.resolveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.claimRecordTime != null">
        claim_record_time = #{record.claimRecordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.reportAmount != null">
        report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.notMatchedNode != null">
        not_matched_node = #{record.notMatchedNode,jdbcType=VARCHAR},
      </if>
      <if test="record.retryTime != null">
        retry_time = #{record.retryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.retryCount != null">
        retry_count = #{record.retryCount,jdbcType=INTEGER},
      </if>
      <if test="record.hangup != null">
        hangup = #{record.hangup,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ant_claim
    set id = #{record.id,jdbcType=BIGINT},
      policy_id = #{record.policyId,jdbcType=BIGINT},
      claim_id = #{record.claimId,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      claim_no = #{record.claimNo,jdbcType=VARCHAR},
      claim_case_no = #{record.claimCaseNo,jdbcType=VARCHAR},
      channel_report_no = #{record.channelReportNo,jdbcType=VARCHAR},
      channel_policy_no = #{record.channelPolicyNo,jdbcType=VARCHAR},
      campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      claimer_cert_no = #{record.claimerCertNo,jdbcType=VARCHAR},
      accident_time = #{record.accidentTime,jdbcType=TIMESTAMP},
      report_time = #{record.reportTime,jdbcType=TIMESTAMP},
      resolve_time = #{record.resolveTime,jdbcType=TIMESTAMP},
      claim_record_time = #{record.claimRecordTime,jdbcType=TIMESTAMP},
      paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      not_matched_node = #{record.notMatchedNode,jdbcType=VARCHAR},
      retry_time = #{record.retryTime,jdbcType=TIMESTAMP},
      retry_count = #{record.retryCount,jdbcType=INTEGER},
      hangup = #{record.hangup,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AntClaimDO">
    update ant_claim
    <set>
      <if test="policyId != null">
        policy_id = #{policyId,jdbcType=BIGINT},
      </if>
      <if test="claimId != null">
        claim_id = #{claimId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        claim_no = #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="claimCaseNo != null">
        claim_case_no = #{claimCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="channelReportNo != null">
        channel_report_no = #{channelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="channelPolicyNo != null">
        channel_policy_no = #{channelPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="claimerCertNo != null">
        claimer_cert_no = #{claimerCertNo,jdbcType=VARCHAR},
      </if>
      <if test="accidentTime != null">
        accident_time = #{accidentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportTime != null">
        report_time = #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resolveTime != null">
        resolve_time = #{resolveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="claimRecordTime != null">
        claim_record_time = #{claimRecordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="reportAmount != null">
        report_amount = #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="notMatchedNode != null">
        not_matched_node = #{notMatchedNode,jdbcType=VARCHAR},
      </if>
      <if test="retryTime != null">
        retry_time = #{retryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="retryCount != null">
        retry_count = #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="hangup != null">
        hangup = #{hangup,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AntClaimDO">
    update ant_claim
    set policy_id = #{policyId,jdbcType=BIGINT},
      claim_id = #{claimId,jdbcType=BIGINT},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      claim_no = #{claimNo,jdbcType=VARCHAR},
      claim_case_no = #{claimCaseNo,jdbcType=VARCHAR},
      channel_report_no = #{channelReportNo,jdbcType=VARCHAR},
      channel_policy_no = #{channelPolicyNo,jdbcType=VARCHAR},
      campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      package_def_id = #{packageDefId,jdbcType=BIGINT},
      claimer_cert_no = #{claimerCertNo,jdbcType=VARCHAR},
      accident_time = #{accidentTime,jdbcType=TIMESTAMP},
      report_time = #{reportTime,jdbcType=TIMESTAMP},
      resolve_time = #{resolveTime,jdbcType=TIMESTAMP},
      claim_record_time = #{claimRecordTime,jdbcType=TIMESTAMP},
      paid_amount = #{paidAmount,jdbcType=VARCHAR},
      report_amount = #{reportAmount,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      not_matched_node = #{notMatchedNode,jdbcType=VARCHAR},
      retry_time = #{retryTime,jdbcType=TIMESTAMP},
      retry_count = #{retryCount,jdbcType=INTEGER},
      hangup = #{hangup,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into ant_claim
    (id, policy_id, claim_id, policy_no, report_no, claim_no, claim_case_no, channel_report_no, 
      channel_policy_no, campaign_def_id, package_def_id, claimer_cert_no, accident_time, 
      report_time, resolve_time, claim_record_time, paid_amount, report_amount, status, 
      not_matched_node, retry_time, retry_count, hangup, creator, modifier, gmt_created, 
      gmt_modified, extra_info, remark, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.policyId,jdbcType=BIGINT}, #{item.claimId,jdbcType=BIGINT}, 
        #{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.claimNo,jdbcType=VARCHAR}, 
        #{item.claimCaseNo,jdbcType=VARCHAR}, #{item.channelReportNo,jdbcType=VARCHAR}, 
        #{item.channelPolicyNo,jdbcType=VARCHAR}, #{item.campaignDefId,jdbcType=BIGINT}, 
        #{item.packageDefId,jdbcType=BIGINT}, #{item.claimerCertNo,jdbcType=VARCHAR}, #{item.accidentTime,jdbcType=TIMESTAMP}, 
        #{item.reportTime,jdbcType=TIMESTAMP}, #{item.resolveTime,jdbcType=TIMESTAMP}, 
        #{item.claimRecordTime,jdbcType=TIMESTAMP}, #{item.paidAmount,jdbcType=VARCHAR}, 
        #{item.reportAmount,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.notMatchedNode,jdbcType=VARCHAR}, 
        #{item.retryTime,jdbcType=TIMESTAMP}, #{item.retryCount,jdbcType=INTEGER}, #{item.hangup,jdbcType=CHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.extraInfo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into ant_claim (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'policy_id'.toString() == column.value">
          #{item.policyId,jdbcType=BIGINT}
        </if>
        <if test="'claim_id'.toString() == column.value">
          #{item.claimId,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'claim_no'.toString() == column.value">
          #{item.claimNo,jdbcType=VARCHAR}
        </if>
        <if test="'claim_case_no'.toString() == column.value">
          #{item.claimCaseNo,jdbcType=VARCHAR}
        </if>
        <if test="'channel_report_no'.toString() == column.value">
          #{item.channelReportNo,jdbcType=VARCHAR}
        </if>
        <if test="'channel_policy_no'.toString() == column.value">
          #{item.channelPolicyNo,jdbcType=VARCHAR}
        </if>
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=BIGINT}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'claimer_cert_no'.toString() == column.value">
          #{item.claimerCertNo,jdbcType=VARCHAR}
        </if>
        <if test="'accident_time'.toString() == column.value">
          #{item.accidentTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'report_time'.toString() == column.value">
          #{item.reportTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'resolve_time'.toString() == column.value">
          #{item.resolveTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'claim_record_time'.toString() == column.value">
          #{item.claimRecordTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'paid_amount'.toString() == column.value">
          #{item.paidAmount,jdbcType=VARCHAR}
        </if>
        <if test="'report_amount'.toString() == column.value">
          #{item.reportAmount,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'not_matched_node'.toString() == column.value">
          #{item.notMatchedNode,jdbcType=VARCHAR}
        </if>
        <if test="'retry_time'.toString() == column.value">
          #{item.retryTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'retry_count'.toString() == column.value">
          #{item.retryCount,jdbcType=INTEGER}
        </if>
        <if test="'hangup'.toString() == column.value">
          #{item.hangup,jdbcType=CHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>