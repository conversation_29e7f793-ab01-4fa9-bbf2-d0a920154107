<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.LitigationMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.LitigationDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="litigation_no" jdbcType="VARCHAR" property="litigationNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="stage" jdbcType="TINYINT" property="stage" />
    <result column="litigate_time" jdbcType="TIMESTAMP" property="litigateTime" />
    <result column="litigate_amount" jdbcType="DECIMAL" property="litigateAmount" />
    <result column="estimated_loss_amount" jdbcType="DECIMAL" property="estimatedLossAmount" />
    <result column="plaintiff_names" jdbcType="VARCHAR" property="plaintiffNames" />
    <result column="plaintiff_law_firm" jdbcType="VARCHAR" property="plaintiffLawFirm" />
    <result column="plaintiff_agent_name" jdbcType="VARCHAR" property="plaintiffAgentName" />
    <result column="plaintiff_agent_mobile" jdbcType="VARCHAR" property="plaintiffAgentMobile" />
    <result column="court" jdbcType="VARCHAR" property="court" />
    <result column="judge_name" jdbcType="VARCHAR" property="judgeName" />
    <result column="court_address" jdbcType="VARCHAR" property="courtAddress" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, litigation_no, policy_no, report_no, stage, litigate_time, litigate_amount, estimated_loss_amount, 
    plaintiff_names, plaintiff_law_firm, plaintiff_agent_name, plaintiff_agent_mobile, 
    court, judge_name, court_address, remark, creator, modifier, gmt_created, gmt_modified, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.LitigationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from litigation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from litigation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.LitigationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into litigation (litigation_no, policy_no, report_no, 
      stage, litigate_time, litigate_amount, 
      estimated_loss_amount, plaintiff_names, plaintiff_law_firm, 
      plaintiff_agent_name, plaintiff_agent_mobile, 
      court, judge_name, court_address, 
      remark, creator, modifier, 
      gmt_created, gmt_modified, is_deleted
      )
    values (#{litigationNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{stage,jdbcType=TINYINT}, #{litigateTime,jdbcType=TIMESTAMP}, #{litigateAmount,jdbcType=DECIMAL}, 
      #{estimatedLossAmount,jdbcType=DECIMAL}, #{plaintiffNames,jdbcType=VARCHAR}, #{plaintiffLawFirm,jdbcType=VARCHAR}, 
      #{plaintiffAgentName,jdbcType=VARCHAR}, #{plaintiffAgentMobile,jdbcType=VARCHAR}, 
      #{court,jdbcType=VARCHAR}, #{judgeName,jdbcType=VARCHAR}, #{courtAddress,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.LitigationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into litigation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="litigationNo != null">
        litigation_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="stage != null">
        stage,
      </if>
      <if test="litigateTime != null">
        litigate_time,
      </if>
      <if test="litigateAmount != null">
        litigate_amount,
      </if>
      <if test="estimatedLossAmount != null">
        estimated_loss_amount,
      </if>
      <if test="plaintiffNames != null">
        plaintiff_names,
      </if>
      <if test="plaintiffLawFirm != null">
        plaintiff_law_firm,
      </if>
      <if test="plaintiffAgentName != null">
        plaintiff_agent_name,
      </if>
      <if test="plaintiffAgentMobile != null">
        plaintiff_agent_mobile,
      </if>
      <if test="court != null">
        court,
      </if>
      <if test="judgeName != null">
        judge_name,
      </if>
      <if test="courtAddress != null">
        court_address,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="litigationNo != null">
        #{litigationNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="stage != null">
        #{stage,jdbcType=TINYINT},
      </if>
      <if test="litigateTime != null">
        #{litigateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="litigateAmount != null">
        #{litigateAmount,jdbcType=DECIMAL},
      </if>
      <if test="estimatedLossAmount != null">
        #{estimatedLossAmount,jdbcType=DECIMAL},
      </if>
      <if test="plaintiffNames != null">
        #{plaintiffNames,jdbcType=VARCHAR},
      </if>
      <if test="plaintiffLawFirm != null">
        #{plaintiffLawFirm,jdbcType=VARCHAR},
      </if>
      <if test="plaintiffAgentName != null">
        #{plaintiffAgentName,jdbcType=VARCHAR},
      </if>
      <if test="plaintiffAgentMobile != null">
        #{plaintiffAgentMobile,jdbcType=VARCHAR},
      </if>
      <if test="court != null">
        #{court,jdbcType=VARCHAR},
      </if>
      <if test="judgeName != null">
        #{judgeName,jdbcType=VARCHAR},
      </if>
      <if test="courtAddress != null">
        #{courtAddress,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.LitigationExample" resultType="java.lang.Long">
    select count(*) from litigation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update litigation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.litigationNo != null">
        litigation_no = #{record.litigationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.stage != null">
        stage = #{record.stage,jdbcType=TINYINT},
      </if>
      <if test="record.litigateTime != null">
        litigate_time = #{record.litigateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.litigateAmount != null">
        litigate_amount = #{record.litigateAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.estimatedLossAmount != null">
        estimated_loss_amount = #{record.estimatedLossAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.plaintiffNames != null">
        plaintiff_names = #{record.plaintiffNames,jdbcType=VARCHAR},
      </if>
      <if test="record.plaintiffLawFirm != null">
        plaintiff_law_firm = #{record.plaintiffLawFirm,jdbcType=VARCHAR},
      </if>
      <if test="record.plaintiffAgentName != null">
        plaintiff_agent_name = #{record.plaintiffAgentName,jdbcType=VARCHAR},
      </if>
      <if test="record.plaintiffAgentMobile != null">
        plaintiff_agent_mobile = #{record.plaintiffAgentMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.court != null">
        court = #{record.court,jdbcType=VARCHAR},
      </if>
      <if test="record.judgeName != null">
        judge_name = #{record.judgeName,jdbcType=VARCHAR},
      </if>
      <if test="record.courtAddress != null">
        court_address = #{record.courtAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update litigation
    set id = #{record.id,jdbcType=BIGINT},
      litigation_no = #{record.litigationNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      stage = #{record.stage,jdbcType=TINYINT},
      litigate_time = #{record.litigateTime,jdbcType=TIMESTAMP},
      litigate_amount = #{record.litigateAmount,jdbcType=DECIMAL},
      estimated_loss_amount = #{record.estimatedLossAmount,jdbcType=DECIMAL},
      plaintiff_names = #{record.plaintiffNames,jdbcType=VARCHAR},
      plaintiff_law_firm = #{record.plaintiffLawFirm,jdbcType=VARCHAR},
      plaintiff_agent_name = #{record.plaintiffAgentName,jdbcType=VARCHAR},
      plaintiff_agent_mobile = #{record.plaintiffAgentMobile,jdbcType=VARCHAR},
      court = #{record.court,jdbcType=VARCHAR},
      judge_name = #{record.judgeName,jdbcType=VARCHAR},
      court_address = #{record.courtAddress,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.LitigationDO">
    update litigation
    <set>
      <if test="litigationNo != null">
        litigation_no = #{litigationNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="stage != null">
        stage = #{stage,jdbcType=TINYINT},
      </if>
      <if test="litigateTime != null">
        litigate_time = #{litigateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="litigateAmount != null">
        litigate_amount = #{litigateAmount,jdbcType=DECIMAL},
      </if>
      <if test="estimatedLossAmount != null">
        estimated_loss_amount = #{estimatedLossAmount,jdbcType=DECIMAL},
      </if>
      <if test="plaintiffNames != null">
        plaintiff_names = #{plaintiffNames,jdbcType=VARCHAR},
      </if>
      <if test="plaintiffLawFirm != null">
        plaintiff_law_firm = #{plaintiffLawFirm,jdbcType=VARCHAR},
      </if>
      <if test="plaintiffAgentName != null">
        plaintiff_agent_name = #{plaintiffAgentName,jdbcType=VARCHAR},
      </if>
      <if test="plaintiffAgentMobile != null">
        plaintiff_agent_mobile = #{plaintiffAgentMobile,jdbcType=VARCHAR},
      </if>
      <if test="court != null">
        court = #{court,jdbcType=VARCHAR},
      </if>
      <if test="judgeName != null">
        judge_name = #{judgeName,jdbcType=VARCHAR},
      </if>
      <if test="courtAddress != null">
        court_address = #{courtAddress,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.LitigationDO">
    update litigation
    set litigation_no = #{litigationNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      stage = #{stage,jdbcType=TINYINT},
      litigate_time = #{litigateTime,jdbcType=TIMESTAMP},
      litigate_amount = #{litigateAmount,jdbcType=DECIMAL},
      estimated_loss_amount = #{estimatedLossAmount,jdbcType=DECIMAL},
      plaintiff_names = #{plaintiffNames,jdbcType=VARCHAR},
      plaintiff_law_firm = #{plaintiffLawFirm,jdbcType=VARCHAR},
      plaintiff_agent_name = #{plaintiffAgentName,jdbcType=VARCHAR},
      plaintiff_agent_mobile = #{plaintiffAgentMobile,jdbcType=VARCHAR},
      court = #{court,jdbcType=VARCHAR},
      judge_name = #{judgeName,jdbcType=VARCHAR},
      court_address = #{courtAddress,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into litigation
    (litigation_no, policy_no, report_no, stage, litigate_time, litigate_amount, estimated_loss_amount, 
      plaintiff_names, plaintiff_law_firm, plaintiff_agent_name, plaintiff_agent_mobile, 
      court, judge_name, court_address, remark, creator, modifier, gmt_created, gmt_modified, 
      is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.litigationNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.stage,jdbcType=TINYINT}, #{item.litigateTime,jdbcType=TIMESTAMP}, #{item.litigateAmount,jdbcType=DECIMAL}, 
        #{item.estimatedLossAmount,jdbcType=DECIMAL}, #{item.plaintiffNames,jdbcType=VARCHAR}, 
        #{item.plaintiffLawFirm,jdbcType=VARCHAR}, #{item.plaintiffAgentName,jdbcType=VARCHAR}, 
        #{item.plaintiffAgentMobile,jdbcType=VARCHAR}, #{item.court,jdbcType=VARCHAR}, 
        #{item.judgeName,jdbcType=VARCHAR}, #{item.courtAddress,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into litigation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'litigation_no'.toString() == column.value">
          #{item.litigationNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'stage'.toString() == column.value">
          #{item.stage,jdbcType=TINYINT}
        </if>
        <if test="'litigate_time'.toString() == column.value">
          #{item.litigateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'litigate_amount'.toString() == column.value">
          #{item.litigateAmount,jdbcType=DECIMAL}
        </if>
        <if test="'estimated_loss_amount'.toString() == column.value">
          #{item.estimatedLossAmount,jdbcType=DECIMAL}
        </if>
        <if test="'plaintiff_names'.toString() == column.value">
          #{item.plaintiffNames,jdbcType=VARCHAR}
        </if>
        <if test="'plaintiff_law_firm'.toString() == column.value">
          #{item.plaintiffLawFirm,jdbcType=VARCHAR}
        </if>
        <if test="'plaintiff_agent_name'.toString() == column.value">
          #{item.plaintiffAgentName,jdbcType=VARCHAR}
        </if>
        <if test="'plaintiff_agent_mobile'.toString() == column.value">
          #{item.plaintiffAgentMobile,jdbcType=VARCHAR}
        </if>
        <if test="'court'.toString() == column.value">
          #{item.court,jdbcType=VARCHAR}
        </if>
        <if test="'judge_name'.toString() == column.value">
          #{item.judgeName,jdbcType=VARCHAR}
        </if>
        <if test="'court_address'.toString() == column.value">
          #{item.courtAddress,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>