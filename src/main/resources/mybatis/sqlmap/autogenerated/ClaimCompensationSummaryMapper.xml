<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimCompensationSummaryMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimCompensationSummaryDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="statis_year" jdbcType="VARCHAR" property="statisYear" />
    <result column="campaign_def_id" jdbcType="VARCHAR" property="campaignDefId" />
    <result column="campaign_name" jdbcType="VARCHAR" property="campaignName" />
    <result column="package_def_id" jdbcType="VARCHAR" property="packageDefId" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="manager_name" jdbcType="VARCHAR" property="managerName" />
    <result column="is_effective" jdbcType="CHAR" property="isEffective" />
    <result column="qb_policy_amt_no_tax_amt" jdbcType="DECIMAL" property="qbPolicyAmtNoTaxAmt" />
    <result column="claim_amt_fee" jdbcType="DECIMAL" property="claimAmtFee" />
    <result column="mq_policy_no_tax_amt" jdbcType="DECIMAL" property="mqPolicyNoTaxAmt" />
    <result column="mq_claim_amt_fee" jdbcType="DECIMAL" property="mqClaimAmtFee" />
    <result column="channel_fee" jdbcType="DECIMAL" property="channelFee" />
    <result column="other_fee" jdbcType="DECIMAL" property="otherFee" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, statis_year, campaign_def_id, campaign_name, package_def_id, package_name, product_name, 
    manager_name, is_effective, qb_policy_amt_no_tax_amt, claim_amt_fee, mq_policy_no_tax_amt, 
    mq_claim_amt_fee, channel_fee, other_fee, extra_info, creator, modifier, gmt_created, 
    gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimCompensationSummaryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_compensation_summary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_compensation_summary
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimCompensationSummaryDO">
    insert into claim_compensation_summary (id, statis_year, campaign_def_id, 
      campaign_name, package_def_id, package_name, 
      product_name, manager_name, is_effective, 
      qb_policy_amt_no_tax_amt, claim_amt_fee, mq_policy_no_tax_amt, 
      mq_claim_amt_fee, channel_fee, other_fee, 
      extra_info, creator, modifier, 
      gmt_created, gmt_modified, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{statisYear,jdbcType=VARCHAR}, #{campaignDefId,jdbcType=VARCHAR}, 
      #{campaignName,jdbcType=VARCHAR}, #{packageDefId,jdbcType=VARCHAR}, #{packageName,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{managerName,jdbcType=VARCHAR}, #{isEffective,jdbcType=CHAR}, 
      #{qbPolicyAmtNoTaxAmt,jdbcType=DECIMAL}, #{claimAmtFee,jdbcType=DECIMAL}, #{mqPolicyNoTaxAmt,jdbcType=DECIMAL}, 
      #{mqClaimAmtFee,jdbcType=DECIMAL}, #{channelFee,jdbcType=DECIMAL}, #{otherFee,jdbcType=DECIMAL}, 
      #{extraInfo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimCompensationSummaryDO">
    insert into claim_compensation_summary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="statisYear != null">
        statis_year,
      </if>
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="campaignName != null">
        campaign_name,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="packageName != null">
        package_name,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="managerName != null">
        manager_name,
      </if>
      <if test="isEffective != null">
        is_effective,
      </if>
      <if test="qbPolicyAmtNoTaxAmt != null">
        qb_policy_amt_no_tax_amt,
      </if>
      <if test="claimAmtFee != null">
        claim_amt_fee,
      </if>
      <if test="mqPolicyNoTaxAmt != null">
        mq_policy_no_tax_amt,
      </if>
      <if test="mqClaimAmtFee != null">
        mq_claim_amt_fee,
      </if>
      <if test="channelFee != null">
        channel_fee,
      </if>
      <if test="otherFee != null">
        other_fee,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="statisYear != null">
        #{statisYear,jdbcType=VARCHAR},
      </if>
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=VARCHAR},
      </if>
      <if test="campaignName != null">
        #{campaignName,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="managerName != null">
        #{managerName,jdbcType=VARCHAR},
      </if>
      <if test="isEffective != null">
        #{isEffective,jdbcType=CHAR},
      </if>
      <if test="qbPolicyAmtNoTaxAmt != null">
        #{qbPolicyAmtNoTaxAmt,jdbcType=DECIMAL},
      </if>
      <if test="claimAmtFee != null">
        #{claimAmtFee,jdbcType=DECIMAL},
      </if>
      <if test="mqPolicyNoTaxAmt != null">
        #{mqPolicyNoTaxAmt,jdbcType=DECIMAL},
      </if>
      <if test="mqClaimAmtFee != null">
        #{mqClaimAmtFee,jdbcType=DECIMAL},
      </if>
      <if test="channelFee != null">
        #{channelFee,jdbcType=DECIMAL},
      </if>
      <if test="otherFee != null">
        #{otherFee,jdbcType=DECIMAL},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimCompensationSummaryExample" resultType="java.lang.Long">
    select count(*) from claim_compensation_summary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_compensation_summary
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.statisYear != null">
        statis_year = #{record.statisYear,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignName != null">
        campaign_name = #{record.campaignName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=VARCHAR},
      </if>
      <if test="record.packageName != null">
        package_name = #{record.packageName,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.managerName != null">
        manager_name = #{record.managerName,jdbcType=VARCHAR},
      </if>
      <if test="record.isEffective != null">
        is_effective = #{record.isEffective,jdbcType=CHAR},
      </if>
      <if test="record.qbPolicyAmtNoTaxAmt != null">
        qb_policy_amt_no_tax_amt = #{record.qbPolicyAmtNoTaxAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.claimAmtFee != null">
        claim_amt_fee = #{record.claimAmtFee,jdbcType=DECIMAL},
      </if>
      <if test="record.mqPolicyNoTaxAmt != null">
        mq_policy_no_tax_amt = #{record.mqPolicyNoTaxAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.mqClaimAmtFee != null">
        mq_claim_amt_fee = #{record.mqClaimAmtFee,jdbcType=DECIMAL},
      </if>
      <if test="record.channelFee != null">
        channel_fee = #{record.channelFee,jdbcType=DECIMAL},
      </if>
      <if test="record.otherFee != null">
        other_fee = #{record.otherFee,jdbcType=DECIMAL},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_compensation_summary
    set id = #{record.id,jdbcType=BIGINT},
      statis_year = #{record.statisYear,jdbcType=VARCHAR},
      campaign_def_id = #{record.campaignDefId,jdbcType=VARCHAR},
      campaign_name = #{record.campaignName,jdbcType=VARCHAR},
      package_def_id = #{record.packageDefId,jdbcType=VARCHAR},
      package_name = #{record.packageName,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      manager_name = #{record.managerName,jdbcType=VARCHAR},
      is_effective = #{record.isEffective,jdbcType=CHAR},
      qb_policy_amt_no_tax_amt = #{record.qbPolicyAmtNoTaxAmt,jdbcType=DECIMAL},
      claim_amt_fee = #{record.claimAmtFee,jdbcType=DECIMAL},
      mq_policy_no_tax_amt = #{record.mqPolicyNoTaxAmt,jdbcType=DECIMAL},
      mq_claim_amt_fee = #{record.mqClaimAmtFee,jdbcType=DECIMAL},
      channel_fee = #{record.channelFee,jdbcType=DECIMAL},
      other_fee = #{record.otherFee,jdbcType=DECIMAL},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimCompensationSummaryDO">
    update claim_compensation_summary
    <set>
      <if test="statisYear != null">
        statis_year = #{statisYear,jdbcType=VARCHAR},
      </if>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=VARCHAR},
      </if>
      <if test="campaignName != null">
        campaign_name = #{campaignName,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        package_name = #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="managerName != null">
        manager_name = #{managerName,jdbcType=VARCHAR},
      </if>
      <if test="isEffective != null">
        is_effective = #{isEffective,jdbcType=CHAR},
      </if>
      <if test="qbPolicyAmtNoTaxAmt != null">
        qb_policy_amt_no_tax_amt = #{qbPolicyAmtNoTaxAmt,jdbcType=DECIMAL},
      </if>
      <if test="claimAmtFee != null">
        claim_amt_fee = #{claimAmtFee,jdbcType=DECIMAL},
      </if>
      <if test="mqPolicyNoTaxAmt != null">
        mq_policy_no_tax_amt = #{mqPolicyNoTaxAmt,jdbcType=DECIMAL},
      </if>
      <if test="mqClaimAmtFee != null">
        mq_claim_amt_fee = #{mqClaimAmtFee,jdbcType=DECIMAL},
      </if>
      <if test="channelFee != null">
        channel_fee = #{channelFee,jdbcType=DECIMAL},
      </if>
      <if test="otherFee != null">
        other_fee = #{otherFee,jdbcType=DECIMAL},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimCompensationSummaryDO">
    update claim_compensation_summary
    set statis_year = #{statisYear,jdbcType=VARCHAR},
      campaign_def_id = #{campaignDefId,jdbcType=VARCHAR},
      campaign_name = #{campaignName,jdbcType=VARCHAR},
      package_def_id = #{packageDefId,jdbcType=VARCHAR},
      package_name = #{packageName,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      manager_name = #{managerName,jdbcType=VARCHAR},
      is_effective = #{isEffective,jdbcType=CHAR},
      qb_policy_amt_no_tax_amt = #{qbPolicyAmtNoTaxAmt,jdbcType=DECIMAL},
      claim_amt_fee = #{claimAmtFee,jdbcType=DECIMAL},
      mq_policy_no_tax_amt = #{mqPolicyNoTaxAmt,jdbcType=DECIMAL},
      mq_claim_amt_fee = #{mqClaimAmtFee,jdbcType=DECIMAL},
      channel_fee = #{channelFee,jdbcType=DECIMAL},
      other_fee = #{otherFee,jdbcType=DECIMAL},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_compensation_summary
    (id, statis_year, campaign_def_id, campaign_name, package_def_id, package_name, product_name, 
      manager_name, is_effective, qb_policy_amt_no_tax_amt, claim_amt_fee, mq_policy_no_tax_amt, 
      mq_claim_amt_fee, channel_fee, other_fee, extra_info, creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.statisYear,jdbcType=VARCHAR}, #{item.campaignDefId,jdbcType=VARCHAR}, 
        #{item.campaignName,jdbcType=VARCHAR}, #{item.packageDefId,jdbcType=VARCHAR}, #{item.packageName,jdbcType=VARCHAR}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.managerName,jdbcType=VARCHAR}, #{item.isEffective,jdbcType=CHAR}, 
        #{item.qbPolicyAmtNoTaxAmt,jdbcType=DECIMAL}, #{item.claimAmtFee,jdbcType=DECIMAL}, 
        #{item.mqPolicyNoTaxAmt,jdbcType=DECIMAL}, #{item.mqClaimAmtFee,jdbcType=DECIMAL}, 
        #{item.channelFee,jdbcType=DECIMAL}, #{item.otherFee,jdbcType=DECIMAL}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_compensation_summary (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'statis_year'.toString() == column.value">
          #{item.statisYear,jdbcType=VARCHAR}
        </if>
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=VARCHAR}
        </if>
        <if test="'campaign_name'.toString() == column.value">
          #{item.campaignName,jdbcType=VARCHAR}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=VARCHAR}
        </if>
        <if test="'package_name'.toString() == column.value">
          #{item.packageName,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'manager_name'.toString() == column.value">
          #{item.managerName,jdbcType=VARCHAR}
        </if>
        <if test="'is_effective'.toString() == column.value">
          #{item.isEffective,jdbcType=CHAR}
        </if>
        <if test="'qb_policy_amt_no_tax_amt'.toString() == column.value">
          #{item.qbPolicyAmtNoTaxAmt,jdbcType=DECIMAL}
        </if>
        <if test="'claim_amt_fee'.toString() == column.value">
          #{item.claimAmtFee,jdbcType=DECIMAL}
        </if>
        <if test="'mq_policy_no_tax_amt'.toString() == column.value">
          #{item.mqPolicyNoTaxAmt,jdbcType=DECIMAL}
        </if>
        <if test="'mq_claim_amt_fee'.toString() == column.value">
          #{item.mqClaimAmtFee,jdbcType=DECIMAL}
        </if>
        <if test="'channel_fee'.toString() == column.value">
          #{item.channelFee,jdbcType=DECIMAL}
        </if>
        <if test="'other_fee'.toString() == column.value">
          #{item.otherFee,jdbcType=DECIMAL}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>