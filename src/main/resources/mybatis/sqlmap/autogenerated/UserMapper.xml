<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.UserMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.UserDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_no" jdbcType="VARCHAR" property="userNo" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="purview" jdbcType="VARCHAR" property="purview" />
    <result column="chinese_name" jdbcType="VARCHAR" property="chineseName" />
    <result column="audit_amount" jdbcType="VARCHAR" property="auditAmount" />
    <result column="reserve_amount" jdbcType="VARCHAR" property="reserveAmount" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="belong_dept" jdbcType="VARCHAR" property="belongDept" />
    <result column="is_claim" jdbcType="VARCHAR" property="isClaim" />
    <result column="is_valid" jdbcType="VARCHAR" property="isValid" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_no, user_name, purview, chinese_name, audit_amount, reserve_amount, phone, 
    email, belong_dept, is_claim, is_valid, is_deleted, creator, modifier, gmt_created, 
    gmt_modified
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.UserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_mgr_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_mgr_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.UserDO">
    insert into cargo_mgr_user (id, user_no, user_name, 
      purview, chinese_name, audit_amount, 
      reserve_amount, phone, email, 
      belong_dept, is_claim, is_valid, 
      is_deleted, creator, modifier, 
      gmt_created, gmt_modified)
    values (#{id,jdbcType=BIGINT}, #{userNo,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, 
      #{purview,jdbcType=VARCHAR}, #{chineseName,jdbcType=VARCHAR}, #{auditAmount,jdbcType=VARCHAR}, 
      #{reserveAmount,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{belongDept,jdbcType=VARCHAR}, #{isClaim,jdbcType=VARCHAR}, #{isValid,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate())
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.UserDO">
    insert into cargo_mgr_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userNo != null">
        user_no,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="purview != null">
        purview,
      </if>
      <if test="chineseName != null">
        chinese_name,
      </if>
      <if test="auditAmount != null">
        audit_amount,
      </if>
      <if test="reserveAmount != null">
        reserve_amount,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="belongDept != null">
        belong_dept,
      </if>
      <if test="isClaim != null">
        is_claim,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userNo != null">
        #{userNo,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="purview != null">
        #{purview,jdbcType=VARCHAR},
      </if>
      <if test="chineseName != null">
        #{chineseName,jdbcType=VARCHAR},
      </if>
      <if test="auditAmount != null">
        #{auditAmount,jdbcType=VARCHAR},
      </if>
      <if test="reserveAmount != null">
        #{reserveAmount,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="belongDept != null">
        #{belongDept,jdbcType=VARCHAR},
      </if>
      <if test="isClaim != null">
        #{isClaim,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.UserExample" resultType="java.lang.Long">
    select count(*) from cargo_mgr_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_mgr_user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userNo != null">
        user_no = #{record.userNo,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.purview != null">
        purview = #{record.purview,jdbcType=VARCHAR},
      </if>
      <if test="record.chineseName != null">
        chinese_name = #{record.chineseName,jdbcType=VARCHAR},
      </if>
      <if test="record.auditAmount != null">
        audit_amount = #{record.auditAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveAmount != null">
        reserve_amount = #{record.reserveAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.belongDept != null">
        belong_dept = #{record.belongDept,jdbcType=VARCHAR},
      </if>
      <if test="record.isClaim != null">
        is_claim = #{record.isClaim,jdbcType=VARCHAR},
      </if>
      <if test="record.isValid != null">
        is_valid = #{record.isValid,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_mgr_user
    set id = #{record.id,jdbcType=BIGINT},
      user_no = #{record.userNo,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      purview = #{record.purview,jdbcType=VARCHAR},
      chinese_name = #{record.chineseName,jdbcType=VARCHAR},
      audit_amount = #{record.auditAmount,jdbcType=VARCHAR},
      reserve_amount = #{record.reserveAmount,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      belong_dept = #{record.belongDept,jdbcType=VARCHAR},
      is_claim = #{record.isClaim,jdbcType=VARCHAR},
      is_valid = #{record.isValid,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate()
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.UserDO">
    update cargo_mgr_user
    <set>
      <if test="userNo != null">
        user_no = #{userNo,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="purview != null">
        purview = #{purview,jdbcType=VARCHAR},
      </if>
      <if test="chineseName != null">
        chinese_name = #{chineseName,jdbcType=VARCHAR},
      </if>
      <if test="auditAmount != null">
        audit_amount = #{auditAmount,jdbcType=VARCHAR},
      </if>
      <if test="reserveAmount != null">
        reserve_amount = #{reserveAmount,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="belongDept != null">
        belong_dept = #{belongDept,jdbcType=VARCHAR},
      </if>
      <if test="isClaim != null">
        is_claim = #{isClaim,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.UserDO">
    update cargo_mgr_user
    set user_no = #{userNo,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      purview = #{purview,jdbcType=VARCHAR},
      chinese_name = #{chineseName,jdbcType=VARCHAR},
      audit_amount = #{auditAmount,jdbcType=VARCHAR},
      reserve_amount = #{reserveAmount,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      belong_dept = #{belongDept,jdbcType=VARCHAR},
      is_claim = #{isClaim,jdbcType=VARCHAR},
      is_valid = #{isValid,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate()
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_mgr_user
    (id, user_no, user_name, purview, chinese_name, audit_amount, reserve_amount, phone, 
      email, belong_dept, is_claim, is_valid, is_deleted, creator, modifier, gmt_created, 
      gmt_modified)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.userNo,jdbcType=VARCHAR}, #{item.userName,jdbcType=VARCHAR}, 
        #{item.purview,jdbcType=VARCHAR}, #{item.chineseName,jdbcType=VARCHAR}, #{item.auditAmount,jdbcType=VARCHAR}, 
        #{item.reserveAmount,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, #{item.email,jdbcType=VARCHAR}, 
        #{item.belongDept,jdbcType=VARCHAR}, #{item.isClaim,jdbcType=VARCHAR}, #{item.isValid,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_mgr_user (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'user_no'.toString() == column.value">
          #{item.userNo,jdbcType=VARCHAR}
        </if>
        <if test="'user_name'.toString() == column.value">
          #{item.userName,jdbcType=VARCHAR}
        </if>
        <if test="'purview'.toString() == column.value">
          #{item.purview,jdbcType=VARCHAR}
        </if>
        <if test="'chinese_name'.toString() == column.value">
          #{item.chineseName,jdbcType=VARCHAR}
        </if>
        <if test="'audit_amount'.toString() == column.value">
          #{item.auditAmount,jdbcType=VARCHAR}
        </if>
        <if test="'reserve_amount'.toString() == column.value">
          #{item.reserveAmount,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'email'.toString() == column.value">
          #{item.email,jdbcType=VARCHAR}
        </if>
        <if test="'belong_dept'.toString() == column.value">
          #{item.belongDept,jdbcType=VARCHAR}
        </if>
        <if test="'is_claim'.toString() == column.value">
          #{item.isClaim,jdbcType=VARCHAR}
        </if>
        <if test="'is_valid'.toString() == column.value">
          #{item.isValid,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>