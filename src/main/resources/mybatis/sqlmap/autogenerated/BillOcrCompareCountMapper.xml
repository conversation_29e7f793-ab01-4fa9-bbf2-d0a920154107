<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.BillOcrCompareCountMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.BillOcrCompareCountDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="count_date" jdbcType="TIMESTAMP" property="countDate" />
    <result column="total_num" jdbcType="INTEGER" property="totalNum" />
    <result column="hos_correct_num" jdbcType="INTEGER" property="hosCorrectNum" />
    <result column="amount_correct_num" jdbcType="INTEGER" property="amountCorrectNum" />
    <result column="bill_num_correct_num" jdbcType="INTEGER" property="billNumCorrectNum" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="type_correct_num" jdbcType="INTEGER" property="typeCorrectNum" />
    <result column="visit_start_correct_num" jdbcType="INTEGER" property="visitStartCorrectNum" />
    <result column="visit_end_correct_num" jdbcType="INTEGER" property="visitEndCorrectNum" />
    <result column="self_expense_correct_num" jdbcType="INTEGER" property="selfExpenseCorrectNum" />
    <result column="self_expense_category_correct_num" jdbcType="INTEGER" property="selfExpenseCategoryCorrectNum" />
    <result column="medical_pay_correct_num" jdbcType="INTEGER" property="medicalPayCorrectNum" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="patient_name_correct_num" jdbcType="INTEGER" property="patientNameCorrectNum" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, count_date, total_num, hos_correct_num, amount_correct_num, bill_num_correct_num, 
    source, is_deleted, gmt_created, gmt_modified, creator, modifier, type_correct_num, 
    visit_start_correct_num, visit_end_correct_num, self_expense_correct_num, self_expense_category_correct_num, 
    medical_pay_correct_num, province, patient_name_correct_num
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareCountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bill_ocr_compare_count
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_ocr_compare_count
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareCountDO">
    insert into bill_ocr_compare_count (id, count_date, total_num, 
      hos_correct_num, amount_correct_num, bill_num_correct_num, 
      source, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, 
      type_correct_num, visit_start_correct_num, visit_end_correct_num, 
      self_expense_correct_num, self_expense_category_correct_num, 
      medical_pay_correct_num, province, patient_name_correct_num
      )
    values (#{id,jdbcType=BIGINT}, #{countDate,jdbcType=TIMESTAMP}, #{totalNum,jdbcType=INTEGER}, 
      #{hosCorrectNum,jdbcType=INTEGER}, #{amountCorrectNum,jdbcType=INTEGER}, #{billNumCorrectNum,jdbcType=INTEGER}, 
      #{source,jdbcType=TINYINT}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{typeCorrectNum,jdbcType=INTEGER}, #{visitStartCorrectNum,jdbcType=INTEGER}, #{visitEndCorrectNum,jdbcType=INTEGER}, 
      #{selfExpenseCorrectNum,jdbcType=INTEGER}, #{selfExpenseCategoryCorrectNum,jdbcType=INTEGER}, 
      #{medicalPayCorrectNum,jdbcType=INTEGER}, #{province,jdbcType=VARCHAR}, #{patientNameCorrectNum,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareCountDO">
    insert into bill_ocr_compare_count
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="countDate != null">
        count_date,
      </if>
      <if test="totalNum != null">
        total_num,
      </if>
      <if test="hosCorrectNum != null">
        hos_correct_num,
      </if>
      <if test="amountCorrectNum != null">
        amount_correct_num,
      </if>
      <if test="billNumCorrectNum != null">
        bill_num_correct_num,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="typeCorrectNum != null">
        type_correct_num,
      </if>
      <if test="visitStartCorrectNum != null">
        visit_start_correct_num,
      </if>
      <if test="visitEndCorrectNum != null">
        visit_end_correct_num,
      </if>
      <if test="selfExpenseCorrectNum != null">
        self_expense_correct_num,
      </if>
      <if test="selfExpenseCategoryCorrectNum != null">
        self_expense_category_correct_num,
      </if>
      <if test="medicalPayCorrectNum != null">
        medical_pay_correct_num,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="patientNameCorrectNum != null">
        patient_name_correct_num,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="countDate != null">
        #{countDate,jdbcType=TIMESTAMP},
      </if>
      <if test="totalNum != null">
        #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="hosCorrectNum != null">
        #{hosCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="amountCorrectNum != null">
        #{amountCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="billNumCorrectNum != null">
        #{billNumCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="typeCorrectNum != null">
        #{typeCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="visitStartCorrectNum != null">
        #{visitStartCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="visitEndCorrectNum != null">
        #{visitEndCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="selfExpenseCorrectNum != null">
        #{selfExpenseCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="selfExpenseCategoryCorrectNum != null">
        #{selfExpenseCategoryCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="medicalPayCorrectNum != null">
        #{medicalPayCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="patientNameCorrectNum != null">
        #{patientNameCorrectNum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareCountExample" resultType="java.lang.Long">
    select count(*) from bill_ocr_compare_count
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bill_ocr_compare_count
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.countDate != null">
        count_date = #{record.countDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.totalNum != null">
        total_num = #{record.totalNum,jdbcType=INTEGER},
      </if>
      <if test="record.hosCorrectNum != null">
        hos_correct_num = #{record.hosCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="record.amountCorrectNum != null">
        amount_correct_num = #{record.amountCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="record.billNumCorrectNum != null">
        bill_num_correct_num = #{record.billNumCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.typeCorrectNum != null">
        type_correct_num = #{record.typeCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="record.visitStartCorrectNum != null">
        visit_start_correct_num = #{record.visitStartCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="record.visitEndCorrectNum != null">
        visit_end_correct_num = #{record.visitEndCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="record.selfExpenseCorrectNum != null">
        self_expense_correct_num = #{record.selfExpenseCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="record.selfExpenseCategoryCorrectNum != null">
        self_expense_category_correct_num = #{record.selfExpenseCategoryCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="record.medicalPayCorrectNum != null">
        medical_pay_correct_num = #{record.medicalPayCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.patientNameCorrectNum != null">
        patient_name_correct_num = #{record.patientNameCorrectNum,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bill_ocr_compare_count
    set id = #{record.id,jdbcType=BIGINT},
      count_date = #{record.countDate,jdbcType=TIMESTAMP},
      total_num = #{record.totalNum,jdbcType=INTEGER},
      hos_correct_num = #{record.hosCorrectNum,jdbcType=INTEGER},
      amount_correct_num = #{record.amountCorrectNum,jdbcType=INTEGER},
      bill_num_correct_num = #{record.billNumCorrectNum,jdbcType=INTEGER},
      source = #{record.source,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      type_correct_num = #{record.typeCorrectNum,jdbcType=INTEGER},
      visit_start_correct_num = #{record.visitStartCorrectNum,jdbcType=INTEGER},
      visit_end_correct_num = #{record.visitEndCorrectNum,jdbcType=INTEGER},
      self_expense_correct_num = #{record.selfExpenseCorrectNum,jdbcType=INTEGER},
      self_expense_category_correct_num = #{record.selfExpenseCategoryCorrectNum,jdbcType=INTEGER},
      medical_pay_correct_num = #{record.medicalPayCorrectNum,jdbcType=INTEGER},
      province = #{record.province,jdbcType=VARCHAR},
      patient_name_correct_num = #{record.patientNameCorrectNum,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareCountDO">
    update bill_ocr_compare_count
    <set>
      <if test="countDate != null">
        count_date = #{countDate,jdbcType=TIMESTAMP},
      </if>
      <if test="totalNum != null">
        total_num = #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="hosCorrectNum != null">
        hos_correct_num = #{hosCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="amountCorrectNum != null">
        amount_correct_num = #{amountCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="billNumCorrectNum != null">
        bill_num_correct_num = #{billNumCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="typeCorrectNum != null">
        type_correct_num = #{typeCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="visitStartCorrectNum != null">
        visit_start_correct_num = #{visitStartCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="visitEndCorrectNum != null">
        visit_end_correct_num = #{visitEndCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="selfExpenseCorrectNum != null">
        self_expense_correct_num = #{selfExpenseCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="selfExpenseCategoryCorrectNum != null">
        self_expense_category_correct_num = #{selfExpenseCategoryCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="medicalPayCorrectNum != null">
        medical_pay_correct_num = #{medicalPayCorrectNum,jdbcType=INTEGER},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="patientNameCorrectNum != null">
        patient_name_correct_num = #{patientNameCorrectNum,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.BillOcrCompareCountDO">
    update bill_ocr_compare_count
    set count_date = #{countDate,jdbcType=TIMESTAMP},
      total_num = #{totalNum,jdbcType=INTEGER},
      hos_correct_num = #{hosCorrectNum,jdbcType=INTEGER},
      amount_correct_num = #{amountCorrectNum,jdbcType=INTEGER},
      bill_num_correct_num = #{billNumCorrectNum,jdbcType=INTEGER},
      source = #{source,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      type_correct_num = #{typeCorrectNum,jdbcType=INTEGER},
      visit_start_correct_num = #{visitStartCorrectNum,jdbcType=INTEGER},
      visit_end_correct_num = #{visitEndCorrectNum,jdbcType=INTEGER},
      self_expense_correct_num = #{selfExpenseCorrectNum,jdbcType=INTEGER},
      self_expense_category_correct_num = #{selfExpenseCategoryCorrectNum,jdbcType=INTEGER},
      medical_pay_correct_num = #{medicalPayCorrectNum,jdbcType=INTEGER},
      province = #{province,jdbcType=VARCHAR},
      patient_name_correct_num = #{patientNameCorrectNum,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into bill_ocr_compare_count
    (id, count_date, total_num, hos_correct_num, amount_correct_num, bill_num_correct_num, 
      source, is_deleted, gmt_created, gmt_modified, creator, modifier, type_correct_num, 
      visit_start_correct_num, visit_end_correct_num, self_expense_correct_num, self_expense_category_correct_num, 
      medical_pay_correct_num, province, patient_name_correct_num)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.countDate,jdbcType=TIMESTAMP}, #{item.totalNum,jdbcType=INTEGER}, 
        #{item.hosCorrectNum,jdbcType=INTEGER}, #{item.amountCorrectNum,jdbcType=INTEGER}, 
        #{item.billNumCorrectNum,jdbcType=INTEGER}, #{item.source,jdbcType=TINYINT}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.typeCorrectNum,jdbcType=INTEGER}, 
        #{item.visitStartCorrectNum,jdbcType=INTEGER}, #{item.visitEndCorrectNum,jdbcType=INTEGER}, 
        #{item.selfExpenseCorrectNum,jdbcType=INTEGER}, #{item.selfExpenseCategoryCorrectNum,jdbcType=INTEGER}, 
        #{item.medicalPayCorrectNum,jdbcType=INTEGER}, #{item.province,jdbcType=VARCHAR}, 
        #{item.patientNameCorrectNum,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into bill_ocr_compare_count (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'count_date'.toString() == column.value">
          #{item.countDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'total_num'.toString() == column.value">
          #{item.totalNum,jdbcType=INTEGER}
        </if>
        <if test="'hos_correct_num'.toString() == column.value">
          #{item.hosCorrectNum,jdbcType=INTEGER}
        </if>
        <if test="'amount_correct_num'.toString() == column.value">
          #{item.amountCorrectNum,jdbcType=INTEGER}
        </if>
        <if test="'bill_num_correct_num'.toString() == column.value">
          #{item.billNumCorrectNum,jdbcType=INTEGER}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=TINYINT}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'type_correct_num'.toString() == column.value">
          #{item.typeCorrectNum,jdbcType=INTEGER}
        </if>
        <if test="'visit_start_correct_num'.toString() == column.value">
          #{item.visitStartCorrectNum,jdbcType=INTEGER}
        </if>
        <if test="'visit_end_correct_num'.toString() == column.value">
          #{item.visitEndCorrectNum,jdbcType=INTEGER}
        </if>
        <if test="'self_expense_correct_num'.toString() == column.value">
          #{item.selfExpenseCorrectNum,jdbcType=INTEGER}
        </if>
        <if test="'self_expense_category_correct_num'.toString() == column.value">
          #{item.selfExpenseCategoryCorrectNum,jdbcType=INTEGER}
        </if>
        <if test="'medical_pay_correct_num'.toString() == column.value">
          #{item.medicalPayCorrectNum,jdbcType=INTEGER}
        </if>
        <if test="'province'.toString() == column.value">
          #{item.province,jdbcType=VARCHAR}
        </if>
        <if test="'patient_name_correct_num'.toString() == column.value">
          #{item.patientNameCorrectNum,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>