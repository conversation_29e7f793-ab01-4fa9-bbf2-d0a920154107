<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ByteDanceClaimPetInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ByteDanceClaimPetInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel_report_no" jdbcType="VARCHAR" property="channelReportNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
    <result column="hospital_address" jdbcType="VARCHAR" property="hospitalAddress" />
    <result column="hospital_info" jdbcType="VARCHAR" property="hospitalInfo" />
    <result column="disease_info" jdbcType="VARCHAR" property="diseaseInfo" />
    <result column="treatment_date" jdbcType="TIMESTAMP" property="treatmentDate" />
    <result column="include_surgery" jdbcType="VARCHAR" property="includeSurgery" />
    <result column="is_affiliate_members" jdbcType="VARCHAR" property="isAffiliateMembers" />
    <result column="risk_msg" jdbcType="VARCHAR" property="riskMsg" />
    <result column="pic_list" jdbcType="VARCHAR" property="picList" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="wait_period_end_time" jdbcType="TIMESTAMP" property="waitPeriodEndTime" />
    <result column="sync_pic_success" jdbcType="TINYINT" property="syncPicSuccess" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, channel_report_no, report_no, policy_no, hospital_name, hospital_address, hospital_info, 
    disease_info, treatment_date, include_surgery, is_affiliate_members, risk_msg, pic_list, 
    is_deleted, gmt_created, gmt_modified, creator, modifier, wait_period_end_time, sync_pic_success
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ByteDanceClaimPetInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bd_claim_pet_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bd_claim_pet_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ByteDanceClaimPetInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bd_claim_pet_info (channel_report_no, report_no, policy_no, 
      hospital_name, hospital_address, hospital_info, 
      disease_info, treatment_date, include_surgery, 
      is_affiliate_members, risk_msg, pic_list, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier, wait_period_end_time, 
      sync_pic_success)
    values (#{channelReportNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, 
      #{hospitalName,jdbcType=VARCHAR}, #{hospitalAddress,jdbcType=VARCHAR}, #{hospitalInfo,jdbcType=VARCHAR}, 
      #{diseaseInfo,jdbcType=VARCHAR}, #{treatmentDate,jdbcType=TIMESTAMP}, #{includeSurgery,jdbcType=VARCHAR}, 
      #{isAffiliateMembers,jdbcType=VARCHAR}, #{riskMsg,jdbcType=VARCHAR}, #{picList,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{waitPeriodEndTime,jdbcType=TIMESTAMP}, 
      #{syncPicSuccess,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ByteDanceClaimPetInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bd_claim_pet_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="channelReportNo != null">
        channel_report_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="hospitalName != null">
        hospital_name,
      </if>
      <if test="hospitalAddress != null">
        hospital_address,
      </if>
      <if test="hospitalInfo != null">
        hospital_info,
      </if>
      <if test="diseaseInfo != null">
        disease_info,
      </if>
      <if test="treatmentDate != null">
        treatment_date,
      </if>
      <if test="includeSurgery != null">
        include_surgery,
      </if>
      <if test="isAffiliateMembers != null">
        is_affiliate_members,
      </if>
      <if test="riskMsg != null">
        risk_msg,
      </if>
      <if test="picList != null">
        pic_list,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="waitPeriodEndTime != null">
        wait_period_end_time,
      </if>
      <if test="syncPicSuccess != null">
        sync_pic_success,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="channelReportNo != null">
        #{channelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalAddress != null">
        #{hospitalAddress,jdbcType=VARCHAR},
      </if>
      <if test="hospitalInfo != null">
        #{hospitalInfo,jdbcType=VARCHAR},
      </if>
      <if test="diseaseInfo != null">
        #{diseaseInfo,jdbcType=VARCHAR},
      </if>
      <if test="treatmentDate != null">
        #{treatmentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="includeSurgery != null">
        #{includeSurgery,jdbcType=VARCHAR},
      </if>
      <if test="isAffiliateMembers != null">
        #{isAffiliateMembers,jdbcType=VARCHAR},
      </if>
      <if test="riskMsg != null">
        #{riskMsg,jdbcType=VARCHAR},
      </if>
      <if test="picList != null">
        #{picList,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="waitPeriodEndTime != null">
        #{waitPeriodEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="syncPicSuccess != null">
        #{syncPicSuccess,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ByteDanceClaimPetInfoExample" resultType="java.lang.Long">
    select count(*) from bd_claim_pet_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bd_claim_pet_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.channelReportNo != null">
        channel_report_no = #{record.channelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalName != null">
        hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalAddress != null">
        hospital_address = #{record.hospitalAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalInfo != null">
        hospital_info = #{record.hospitalInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.diseaseInfo != null">
        disease_info = #{record.diseaseInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.treatmentDate != null">
        treatment_date = #{record.treatmentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.includeSurgery != null">
        include_surgery = #{record.includeSurgery,jdbcType=VARCHAR},
      </if>
      <if test="record.isAffiliateMembers != null">
        is_affiliate_members = #{record.isAffiliateMembers,jdbcType=VARCHAR},
      </if>
      <if test="record.riskMsg != null">
        risk_msg = #{record.riskMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.picList != null">
        pic_list = #{record.picList,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.waitPeriodEndTime != null">
        wait_period_end_time = #{record.waitPeriodEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.syncPicSuccess != null">
        sync_pic_success = #{record.syncPicSuccess,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bd_claim_pet_info
    set id = #{record.id,jdbcType=BIGINT},
      channel_report_no = #{record.channelReportNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      hospital_address = #{record.hospitalAddress,jdbcType=VARCHAR},
      hospital_info = #{record.hospitalInfo,jdbcType=VARCHAR},
      disease_info = #{record.diseaseInfo,jdbcType=VARCHAR},
      treatment_date = #{record.treatmentDate,jdbcType=TIMESTAMP},
      include_surgery = #{record.includeSurgery,jdbcType=VARCHAR},
      is_affiliate_members = #{record.isAffiliateMembers,jdbcType=VARCHAR},
      risk_msg = #{record.riskMsg,jdbcType=VARCHAR},
      pic_list = #{record.picList,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      wait_period_end_time = #{record.waitPeriodEndTime,jdbcType=TIMESTAMP},
      sync_pic_success = #{record.syncPicSuccess,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ByteDanceClaimPetInfoDO">
    update bd_claim_pet_info
    <set>
      <if test="channelReportNo != null">
        channel_report_no = #{channelReportNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        hospital_name = #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalAddress != null">
        hospital_address = #{hospitalAddress,jdbcType=VARCHAR},
      </if>
      <if test="hospitalInfo != null">
        hospital_info = #{hospitalInfo,jdbcType=VARCHAR},
      </if>
      <if test="diseaseInfo != null">
        disease_info = #{diseaseInfo,jdbcType=VARCHAR},
      </if>
      <if test="treatmentDate != null">
        treatment_date = #{treatmentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="includeSurgery != null">
        include_surgery = #{includeSurgery,jdbcType=VARCHAR},
      </if>
      <if test="isAffiliateMembers != null">
        is_affiliate_members = #{isAffiliateMembers,jdbcType=VARCHAR},
      </if>
      <if test="riskMsg != null">
        risk_msg = #{riskMsg,jdbcType=VARCHAR},
      </if>
      <if test="picList != null">
        pic_list = #{picList,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="waitPeriodEndTime != null">
        wait_period_end_time = #{waitPeriodEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="syncPicSuccess != null">
        sync_pic_success = #{syncPicSuccess,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ByteDanceClaimPetInfoDO">
    update bd_claim_pet_info
    set channel_report_no = #{channelReportNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      hospital_name = #{hospitalName,jdbcType=VARCHAR},
      hospital_address = #{hospitalAddress,jdbcType=VARCHAR},
      hospital_info = #{hospitalInfo,jdbcType=VARCHAR},
      disease_info = #{diseaseInfo,jdbcType=VARCHAR},
      treatment_date = #{treatmentDate,jdbcType=TIMESTAMP},
      include_surgery = #{includeSurgery,jdbcType=VARCHAR},
      is_affiliate_members = #{isAffiliateMembers,jdbcType=VARCHAR},
      risk_msg = #{riskMsg,jdbcType=VARCHAR},
      pic_list = #{picList,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      wait_period_end_time = #{waitPeriodEndTime,jdbcType=TIMESTAMP},
      sync_pic_success = #{syncPicSuccess,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into bd_claim_pet_info
    (channel_report_no, report_no, policy_no, hospital_name, hospital_address, hospital_info, 
      disease_info, treatment_date, include_surgery, is_affiliate_members, risk_msg, 
      pic_list, is_deleted, gmt_created, gmt_modified, creator, modifier, wait_period_end_time, 
      sync_pic_success)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.channelReportNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.hospitalName,jdbcType=VARCHAR}, #{item.hospitalAddress,jdbcType=VARCHAR}, 
        #{item.hospitalInfo,jdbcType=VARCHAR}, #{item.diseaseInfo,jdbcType=VARCHAR}, #{item.treatmentDate,jdbcType=TIMESTAMP}, 
        #{item.includeSurgery,jdbcType=VARCHAR}, #{item.isAffiliateMembers,jdbcType=VARCHAR}, 
        #{item.riskMsg,jdbcType=VARCHAR}, #{item.picList,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.waitPeriodEndTime,jdbcType=TIMESTAMP}, 
        #{item.syncPicSuccess,jdbcType=TINYINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into bd_claim_pet_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'channel_report_no'.toString() == column.value">
          #{item.channelReportNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_name'.toString() == column.value">
          #{item.hospitalName,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_address'.toString() == column.value">
          #{item.hospitalAddress,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_info'.toString() == column.value">
          #{item.hospitalInfo,jdbcType=VARCHAR}
        </if>
        <if test="'disease_info'.toString() == column.value">
          #{item.diseaseInfo,jdbcType=VARCHAR}
        </if>
        <if test="'treatment_date'.toString() == column.value">
          #{item.treatmentDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'include_surgery'.toString() == column.value">
          #{item.includeSurgery,jdbcType=VARCHAR}
        </if>
        <if test="'is_affiliate_members'.toString() == column.value">
          #{item.isAffiliateMembers,jdbcType=VARCHAR}
        </if>
        <if test="'risk_msg'.toString() == column.value">
          #{item.riskMsg,jdbcType=VARCHAR}
        </if>
        <if test="'pic_list'.toString() == column.value">
          #{item.picList,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'wait_period_end_time'.toString() == column.value">
          #{item.waitPeriodEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'sync_pic_success'.toString() == column.value">
          #{item.syncPicSuccess,jdbcType=TINYINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>