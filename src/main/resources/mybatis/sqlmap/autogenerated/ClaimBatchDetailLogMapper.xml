<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimBatchDetailLogMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimBatchDetailLogDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_id" jdbcType="BIGINT" property="batchId" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="dispatcher_no" jdbcType="VARCHAR" property="dispatcherNo" />
    <result column="dealor" jdbcType="VARCHAR" property="dealor" />
    <result column="deal_date" jdbcType="TIMESTAMP" property="dealDate" />
    <result column="deal_status" jdbcType="CHAR" property="dealStatus" />
    <result column="deal_result" jdbcType="CHAR" property="dealResult" />
    <result column="deal_remark" jdbcType="VARCHAR" property="dealRemark" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, batch_id, batch_no, report_no, dispatcher_no, dealor, deal_date, deal_status, 
    deal_result, deal_remark, creator, gmt_created, modifier, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchDetailLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_claim_batch_detail_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_claim_batch_detail_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchDetailLogDO">
    insert into cargo_claim_batch_detail_log (id, batch_id, batch_no, 
      report_no, dispatcher_no, dealor, 
      deal_date, deal_status, deal_result, 
      deal_remark, creator, gmt_created, 
      modifier, gmt_modified, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{batchId,jdbcType=BIGINT}, #{batchNo,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{dispatcherNo,jdbcType=VARCHAR}, #{dealor,jdbcType=VARCHAR}, 
      #{dealDate,jdbcType=TIMESTAMP}, #{dealStatus,jdbcType=CHAR}, #{dealResult,jdbcType=CHAR}, 
      #{dealRemark,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, sysdate(), 
      #{modifier,jdbcType=VARCHAR}, sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchDetailLogDO">
    insert into cargo_claim_batch_detail_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no,
      </if>
      <if test="dealor != null">
        dealor,
      </if>
      <if test="dealDate != null">
        deal_date,
      </if>
      <if test="dealStatus != null">
        deal_status,
      </if>
      <if test="dealResult != null">
        deal_result,
      </if>
      <if test="dealRemark != null">
        deal_remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batchId != null">
        #{batchId,jdbcType=BIGINT},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherNo != null">
        #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="dealor != null">
        #{dealor,jdbcType=VARCHAR},
      </if>
      <if test="dealDate != null">
        #{dealDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dealStatus != null">
        #{dealStatus,jdbcType=CHAR},
      </if>
      <if test="dealResult != null">
        #{dealResult,jdbcType=CHAR},
      </if>
      <if test="dealRemark != null">
        #{dealRemark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchDetailLogExample" resultType="java.lang.Long">
    select count(*) from cargo_claim_batch_detail_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_claim_batch_detail_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.batchId != null">
        batch_id = #{record.batchId,jdbcType=BIGINT},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.dispatcherNo != null">
        dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="record.dealor != null">
        dealor = #{record.dealor,jdbcType=VARCHAR},
      </if>
      <if test="record.dealDate != null">
        deal_date = #{record.dealDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dealStatus != null">
        deal_status = #{record.dealStatus,jdbcType=CHAR},
      </if>
      <if test="record.dealResult != null">
        deal_result = #{record.dealResult,jdbcType=CHAR},
      </if>
      <if test="record.dealRemark != null">
        deal_remark = #{record.dealRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_claim_batch_detail_log
    set id = #{record.id,jdbcType=BIGINT},
      batch_id = #{record.batchId,jdbcType=BIGINT},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      dealor = #{record.dealor,jdbcType=VARCHAR},
      deal_date = #{record.dealDate,jdbcType=TIMESTAMP},
      deal_status = #{record.dealStatus,jdbcType=CHAR},
      deal_result = #{record.dealResult,jdbcType=CHAR},
      deal_remark = #{record.dealRemark,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchDetailLogDO">
    update cargo_claim_batch_detail_log
    <set>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=BIGINT},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="dealor != null">
        dealor = #{dealor,jdbcType=VARCHAR},
      </if>
      <if test="dealDate != null">
        deal_date = #{dealDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dealStatus != null">
        deal_status = #{dealStatus,jdbcType=CHAR},
      </if>
      <if test="dealResult != null">
        deal_result = #{dealResult,jdbcType=CHAR},
      </if>
      <if test="dealRemark != null">
        deal_remark = #{dealRemark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchDetailLogDO">
    update cargo_claim_batch_detail_log
    set batch_id = #{batchId,jdbcType=BIGINT},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      dealor = #{dealor,jdbcType=VARCHAR},
      deal_date = #{dealDate,jdbcType=TIMESTAMP},
      deal_status = #{dealStatus,jdbcType=CHAR},
      deal_result = #{dealResult,jdbcType=CHAR},
      deal_remark = #{dealRemark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_claim_batch_detail_log
    (id, batch_id, batch_no, report_no, dispatcher_no, dealor, deal_date, deal_status, 
      deal_result, deal_remark, creator, gmt_created, modifier, gmt_modified, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.batchId,jdbcType=BIGINT}, #{item.batchNo,jdbcType=VARCHAR}, 
        #{item.reportNo,jdbcType=VARCHAR}, #{item.dispatcherNo,jdbcType=VARCHAR}, #{item.dealor,jdbcType=VARCHAR}, 
        #{item.dealDate,jdbcType=TIMESTAMP}, #{item.dealStatus,jdbcType=CHAR}, #{item.dealResult,jdbcType=CHAR}, 
        #{item.dealRemark,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_claim_batch_detail_log (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'batch_id'.toString() == column.value">
          #{item.batchId,jdbcType=BIGINT}
        </if>
        <if test="'batch_no'.toString() == column.value">
          #{item.batchNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'dispatcher_no'.toString() == column.value">
          #{item.dispatcherNo,jdbcType=VARCHAR}
        </if>
        <if test="'dealor'.toString() == column.value">
          #{item.dealor,jdbcType=VARCHAR}
        </if>
        <if test="'deal_date'.toString() == column.value">
          #{item.dealDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'deal_status'.toString() == column.value">
          #{item.dealStatus,jdbcType=CHAR}
        </if>
        <if test="'deal_result'.toString() == column.value">
          #{item.dealResult,jdbcType=CHAR}
        </if>
        <if test="'deal_remark'.toString() == column.value">
          #{item.dealRemark,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>