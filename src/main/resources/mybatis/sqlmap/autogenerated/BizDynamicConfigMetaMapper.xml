<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.BizDynamicConfigMetaMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.BizDynamicConfigMetaDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="scope" jdbcType="VARCHAR" property="scope" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="multiple" jdbcType="BIT" property="multiple" />
    <result column="key_optional" jdbcType="VARCHAR" property="keyOptional" />
    <result column="exist_sub_key" jdbcType="BIT" property="existSubKey" />
    <result column="sub_key_optional" jdbcType="VARCHAR" property="subKeyOptional" />
    <result column="value_type" jdbcType="VARCHAR" property="valueType" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `scope`, code, `name`, `desc`, multiple, key_optional, exist_sub_key, sub_key_optional,
    value_type, creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.BizDynamicConfigMetaExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from biz_dynamic_config_meta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from biz_dynamic_config_meta
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.BizDynamicConfigMetaDO">
    insert into biz_dynamic_config_meta (id, scope, code, 
      name, desc, multiple, key_optional, 
      exist_sub_key, sub_key_optional, value_type, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{scope,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{desc,jdbcType=VARCHAR}, #{multiple,jdbcType=BIT}, #{keyOptional,jdbcType=VARCHAR}, 
      #{existSubKey,jdbcType=BIT}, #{subKeyOptional,jdbcType=VARCHAR}, #{valueType,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.BizDynamicConfigMetaDO">
    insert into biz_dynamic_config_meta
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="scope != null">
        scope,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="desc != null">
        desc,
      </if>
      <if test="multiple != null">
        multiple,
      </if>
      <if test="keyOptional != null">
        key_optional,
      </if>
      <if test="existSubKey != null">
        exist_sub_key,
      </if>
      <if test="subKeyOptional != null">
        sub_key_optional,
      </if>
      <if test="valueType != null">
        value_type,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="scope != null">
        #{scope,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="multiple != null">
        #{multiple,jdbcType=BIT},
      </if>
      <if test="keyOptional != null">
        #{keyOptional,jdbcType=VARCHAR},
      </if>
      <if test="existSubKey != null">
        #{existSubKey,jdbcType=BIT},
      </if>
      <if test="subKeyOptional != null">
        #{subKeyOptional,jdbcType=VARCHAR},
      </if>
      <if test="valueType != null">
        #{valueType,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.BizDynamicConfigMetaExample" resultType="java.lang.Long">
    select count(*) from biz_dynamic_config_meta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update biz_dynamic_config_meta
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.scope != null">
        scope = #{record.scope,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.desc != null">
        desc = #{record.desc,jdbcType=VARCHAR},
      </if>
      <if test="record.multiple != null">
        multiple = #{record.multiple,jdbcType=BIT},
      </if>
      <if test="record.keyOptional != null">
        key_optional = #{record.keyOptional,jdbcType=VARCHAR},
      </if>
      <if test="record.existSubKey != null">
        exist_sub_key = #{record.existSubKey,jdbcType=BIT},
      </if>
      <if test="record.subKeyOptional != null">
        sub_key_optional = #{record.subKeyOptional,jdbcType=VARCHAR},
      </if>
      <if test="record.valueType != null">
        value_type = #{record.valueType,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update biz_dynamic_config_meta
    set id = #{record.id,jdbcType=BIGINT},
      scope = #{record.scope,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      desc = #{record.desc,jdbcType=VARCHAR},
      multiple = #{record.multiple,jdbcType=BIT},
      key_optional = #{record.keyOptional,jdbcType=VARCHAR},
      exist_sub_key = #{record.existSubKey,jdbcType=BIT},
      sub_key_optional = #{record.subKeyOptional,jdbcType=VARCHAR},
      value_type = #{record.valueType,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.BizDynamicConfigMetaDO">
    update biz_dynamic_config_meta
    <set>
      <if test="scope != null">
        scope = #{scope,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        desc = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="multiple != null">
        multiple = #{multiple,jdbcType=BIT},
      </if>
      <if test="keyOptional != null">
        key_optional = #{keyOptional,jdbcType=VARCHAR},
      </if>
      <if test="existSubKey != null">
        exist_sub_key = #{existSubKey,jdbcType=BIT},
      </if>
      <if test="subKeyOptional != null">
        sub_key_optional = #{subKeyOptional,jdbcType=VARCHAR},
      </if>
      <if test="valueType != null">
        value_type = #{valueType,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.BizDynamicConfigMetaDO">
    update biz_dynamic_config_meta
    set scope = #{scope,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      desc = #{desc,jdbcType=VARCHAR},
      multiple = #{multiple,jdbcType=BIT},
      key_optional = #{keyOptional,jdbcType=VARCHAR},
      exist_sub_key = #{existSubKey,jdbcType=BIT},
      sub_key_optional = #{subKeyOptional,jdbcType=VARCHAR},
      value_type = #{valueType,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into biz_dynamic_config_meta
    (id, scope, code, name, desc, multiple, key_optional, exist_sub_key, sub_key_optional, 
      value_type, creator, modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.scope,jdbcType=VARCHAR}, #{item.code,jdbcType=VARCHAR}, 
        #{item.name,jdbcType=VARCHAR}, #{item.desc,jdbcType=VARCHAR}, #{item.multiple,jdbcType=BIT}, 
        #{item.keyOptional,jdbcType=VARCHAR}, #{item.existSubKey,jdbcType=BIT}, #{item.subKeyOptional,jdbcType=VARCHAR}, 
        #{item.valueType,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into biz_dynamic_config_meta (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'scope'.toString() == column.value">
          #{item.scope,jdbcType=VARCHAR}
        </if>
        <if test="'code'.toString() == column.value">
          #{item.code,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'desc'.toString() == column.value">
          #{item.desc,jdbcType=VARCHAR}
        </if>
        <if test="'multiple'.toString() == column.value">
          #{item.multiple,jdbcType=BIT}
        </if>
        <if test="'key_optional'.toString() == column.value">
          #{item.keyOptional,jdbcType=VARCHAR}
        </if>
        <if test="'exist_sub_key'.toString() == column.value">
          #{item.existSubKey,jdbcType=BIT}
        </if>
        <if test="'sub_key_optional'.toString() == column.value">
          #{item.subKeyOptional,jdbcType=VARCHAR}
        </if>
        <if test="'value_type'.toString() == column.value">
          #{item.valueType,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>