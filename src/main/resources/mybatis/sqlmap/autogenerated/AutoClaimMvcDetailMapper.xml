<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AutoClaimMvcDetailMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AutoClaimMvcDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="associate_batch_no" jdbcType="VARCHAR" property="associateBatchNo" />
    <result column="paid_amount_be" jdbcType="VARCHAR" property="paidAmountBe" />
    <result column="paid_amount_af" jdbcType="VARCHAR" property="paidAmountAf" />
    <result column="paid_amount_diff" jdbcType="DECIMAL" property="paidAmountDiff" />
    <result column="paid_amount_diff_per" jdbcType="DECIMAL" property="paidAmountDiffPer" />
    <result column="non_responsible_cost_be" jdbcType="VARCHAR" property="nonResponsibleCostBe" />
    <result column="non_responsible_cost_af" jdbcType="VARCHAR" property="nonResponsibleCostAf" />
    <result column="non_responsible_cost_diff" jdbcType="DECIMAL" property="nonResponsibleCostDiff" />
    <result column="non_responsible_cost_diff_per" jdbcType="DECIMAL" property="nonResponsibleCostDiffPer" />
    <result column="self_expense_category_be" jdbcType="VARCHAR" property="selfExpenseCategoryBe" />
    <result column="self_expense_category_af" jdbcType="VARCHAR" property="selfExpenseCategoryAf" />
    <result column="self_expense_category_diff" jdbcType="DECIMAL" property="selfExpenseCategoryDiff" />
    <result column="self_expense_category_diff_per" jdbcType="DECIMAL" property="selfExpenseCategoryDiffPer" />
    <result column="self_expense_be" jdbcType="VARCHAR" property="selfExpenseBe" />
    <result column="self_expense_af" jdbcType="VARCHAR" property="selfExpenseAf" />
    <result column="self_expense_diff" jdbcType="DECIMAL" property="selfExpenseDiff" />
    <result column="self_expense_diff_per" jdbcType="DECIMAL" property="selfExpenseDiffPer" />
    <result column="medical_insurance_pay_be" jdbcType="VARCHAR" property="medicalInsurancePayBe" />
    <result column="medical_insurance_pay_af" jdbcType="VARCHAR" property="medicalInsurancePayAf" />
    <result column="medical_insurance_pay_diff" jdbcType="DECIMAL" property="medicalInsurancePayDiff" />
    <result column="medical_insurance_pay_diff_per" jdbcType="DECIMAL" property="medicalInsurancePayDiffPer" />
    <result column="close_result_be" jdbcType="VARCHAR" property="closeResultBe" />
    <result column="close_result_af" jdbcType="VARCHAR" property="closeResultAf" />
    <result column="prd_result" jdbcType="VARCHAR" property="prdResult" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, batch_no, associate_batch_no, paid_amount_be, paid_amount_af, paid_amount_diff, 
    paid_amount_diff_per, non_responsible_cost_be, non_responsible_cost_af, non_responsible_cost_diff, 
    non_responsible_cost_diff_per, self_expense_category_be, self_expense_category_af, 
    self_expense_category_diff, self_expense_category_diff_per, self_expense_be, self_expense_af, 
    self_expense_diff, self_expense_diff_per, medical_insurance_pay_be, medical_insurance_pay_af, 
    medical_insurance_pay_diff, medical_insurance_pay_diff_per, close_result_be, close_result_af, 
    prd_result, extra_info, gmt_created, gmt_modified, creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AutoClaimMvcDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auto_claim_mvc_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auto_claim_mvc_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AutoClaimMvcDetailDO">
    insert into auto_claim_mvc_detail (id, report_no, batch_no, 
      associate_batch_no, paid_amount_be, paid_amount_af, 
      paid_amount_diff, paid_amount_diff_per, non_responsible_cost_be, 
      non_responsible_cost_af, non_responsible_cost_diff, 
      non_responsible_cost_diff_per, self_expense_category_be, 
      self_expense_category_af, self_expense_category_diff, 
      self_expense_category_diff_per, self_expense_be, 
      self_expense_af, self_expense_diff, self_expense_diff_per, 
      medical_insurance_pay_be, medical_insurance_pay_af, 
      medical_insurance_pay_diff, medical_insurance_pay_diff_per, 
      close_result_be, close_result_af, prd_result, 
      extra_info, gmt_created, gmt_modified, 
      creator, modifier, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, 
      #{associateBatchNo,jdbcType=VARCHAR}, #{paidAmountBe,jdbcType=VARCHAR}, #{paidAmountAf,jdbcType=VARCHAR}, 
      #{paidAmountDiff,jdbcType=DECIMAL}, #{paidAmountDiffPer,jdbcType=DECIMAL}, #{nonResponsibleCostBe,jdbcType=VARCHAR}, 
      #{nonResponsibleCostAf,jdbcType=VARCHAR}, #{nonResponsibleCostDiff,jdbcType=DECIMAL}, 
      #{nonResponsibleCostDiffPer,jdbcType=DECIMAL}, #{selfExpenseCategoryBe,jdbcType=VARCHAR}, 
      #{selfExpenseCategoryAf,jdbcType=VARCHAR}, #{selfExpenseCategoryDiff,jdbcType=DECIMAL}, 
      #{selfExpenseCategoryDiffPer,jdbcType=DECIMAL}, #{selfExpenseBe,jdbcType=VARCHAR}, 
      #{selfExpenseAf,jdbcType=VARCHAR}, #{selfExpenseDiff,jdbcType=DECIMAL}, #{selfExpenseDiffPer,jdbcType=DECIMAL}, 
      #{medicalInsurancePayBe,jdbcType=VARCHAR}, #{medicalInsurancePayAf,jdbcType=VARCHAR}, 
      #{medicalInsurancePayDiff,jdbcType=DECIMAL}, #{medicalInsurancePayDiffPer,jdbcType=DECIMAL}, 
      #{closeResultBe,jdbcType=VARCHAR}, #{closeResultAf,jdbcType=VARCHAR}, #{prdResult,jdbcType=VARCHAR}, 
      #{extraInfo,jdbcType=VARCHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AutoClaimMvcDetailDO">
    insert into auto_claim_mvc_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="associateBatchNo != null">
        associate_batch_no,
      </if>
      <if test="paidAmountBe != null">
        paid_amount_be,
      </if>
      <if test="paidAmountAf != null">
        paid_amount_af,
      </if>
      <if test="paidAmountDiff != null">
        paid_amount_diff,
      </if>
      <if test="paidAmountDiffPer != null">
        paid_amount_diff_per,
      </if>
      <if test="nonResponsibleCostBe != null">
        non_responsible_cost_be,
      </if>
      <if test="nonResponsibleCostAf != null">
        non_responsible_cost_af,
      </if>
      <if test="nonResponsibleCostDiff != null">
        non_responsible_cost_diff,
      </if>
      <if test="nonResponsibleCostDiffPer != null">
        non_responsible_cost_diff_per,
      </if>
      <if test="selfExpenseCategoryBe != null">
        self_expense_category_be,
      </if>
      <if test="selfExpenseCategoryAf != null">
        self_expense_category_af,
      </if>
      <if test="selfExpenseCategoryDiff != null">
        self_expense_category_diff,
      </if>
      <if test="selfExpenseCategoryDiffPer != null">
        self_expense_category_diff_per,
      </if>
      <if test="selfExpenseBe != null">
        self_expense_be,
      </if>
      <if test="selfExpenseAf != null">
        self_expense_af,
      </if>
      <if test="selfExpenseDiff != null">
        self_expense_diff,
      </if>
      <if test="selfExpenseDiffPer != null">
        self_expense_diff_per,
      </if>
      <if test="medicalInsurancePayBe != null">
        medical_insurance_pay_be,
      </if>
      <if test="medicalInsurancePayAf != null">
        medical_insurance_pay_af,
      </if>
      <if test="medicalInsurancePayDiff != null">
        medical_insurance_pay_diff,
      </if>
      <if test="medicalInsurancePayDiffPer != null">
        medical_insurance_pay_diff_per,
      </if>
      <if test="closeResultBe != null">
        close_result_be,
      </if>
      <if test="closeResultAf != null">
        close_result_af,
      </if>
      <if test="prdResult != null">
        prd_result,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="associateBatchNo != null">
        #{associateBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="paidAmountBe != null">
        #{paidAmountBe,jdbcType=VARCHAR},
      </if>
      <if test="paidAmountAf != null">
        #{paidAmountAf,jdbcType=VARCHAR},
      </if>
      <if test="paidAmountDiff != null">
        #{paidAmountDiff,jdbcType=DECIMAL},
      </if>
      <if test="paidAmountDiffPer != null">
        #{paidAmountDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="nonResponsibleCostBe != null">
        #{nonResponsibleCostBe,jdbcType=VARCHAR},
      </if>
      <if test="nonResponsibleCostAf != null">
        #{nonResponsibleCostAf,jdbcType=VARCHAR},
      </if>
      <if test="nonResponsibleCostDiff != null">
        #{nonResponsibleCostDiff,jdbcType=DECIMAL},
      </if>
      <if test="nonResponsibleCostDiffPer != null">
        #{nonResponsibleCostDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="selfExpenseCategoryBe != null">
        #{selfExpenseCategoryBe,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseCategoryAf != null">
        #{selfExpenseCategoryAf,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseCategoryDiff != null">
        #{selfExpenseCategoryDiff,jdbcType=DECIMAL},
      </if>
      <if test="selfExpenseCategoryDiffPer != null">
        #{selfExpenseCategoryDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="selfExpenseBe != null">
        #{selfExpenseBe,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseAf != null">
        #{selfExpenseAf,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseDiff != null">
        #{selfExpenseDiff,jdbcType=DECIMAL},
      </if>
      <if test="selfExpenseDiffPer != null">
        #{selfExpenseDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="medicalInsurancePayBe != null">
        #{medicalInsurancePayBe,jdbcType=VARCHAR},
      </if>
      <if test="medicalInsurancePayAf != null">
        #{medicalInsurancePayAf,jdbcType=VARCHAR},
      </if>
      <if test="medicalInsurancePayDiff != null">
        #{medicalInsurancePayDiff,jdbcType=DECIMAL},
      </if>
      <if test="medicalInsurancePayDiffPer != null">
        #{medicalInsurancePayDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="closeResultBe != null">
        #{closeResultBe,jdbcType=VARCHAR},
      </if>
      <if test="closeResultAf != null">
        #{closeResultAf,jdbcType=VARCHAR},
      </if>
      <if test="prdResult != null">
        #{prdResult,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AutoClaimMvcDetailExample" resultType="java.lang.Long">
    select count(*) from auto_claim_mvc_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update auto_claim_mvc_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.associateBatchNo != null">
        associate_batch_no = #{record.associateBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmountBe != null">
        paid_amount_be = #{record.paidAmountBe,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmountAf != null">
        paid_amount_af = #{record.paidAmountAf,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmountDiff != null">
        paid_amount_diff = #{record.paidAmountDiff,jdbcType=DECIMAL},
      </if>
      <if test="record.paidAmountDiffPer != null">
        paid_amount_diff_per = #{record.paidAmountDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="record.nonResponsibleCostBe != null">
        non_responsible_cost_be = #{record.nonResponsibleCostBe,jdbcType=VARCHAR},
      </if>
      <if test="record.nonResponsibleCostAf != null">
        non_responsible_cost_af = #{record.nonResponsibleCostAf,jdbcType=VARCHAR},
      </if>
      <if test="record.nonResponsibleCostDiff != null">
        non_responsible_cost_diff = #{record.nonResponsibleCostDiff,jdbcType=DECIMAL},
      </if>
      <if test="record.nonResponsibleCostDiffPer != null">
        non_responsible_cost_diff_per = #{record.nonResponsibleCostDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="record.selfExpenseCategoryBe != null">
        self_expense_category_be = #{record.selfExpenseCategoryBe,jdbcType=VARCHAR},
      </if>
      <if test="record.selfExpenseCategoryAf != null">
        self_expense_category_af = #{record.selfExpenseCategoryAf,jdbcType=VARCHAR},
      </if>
      <if test="record.selfExpenseCategoryDiff != null">
        self_expense_category_diff = #{record.selfExpenseCategoryDiff,jdbcType=DECIMAL},
      </if>
      <if test="record.selfExpenseCategoryDiffPer != null">
        self_expense_category_diff_per = #{record.selfExpenseCategoryDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="record.selfExpenseBe != null">
        self_expense_be = #{record.selfExpenseBe,jdbcType=VARCHAR},
      </if>
      <if test="record.selfExpenseAf != null">
        self_expense_af = #{record.selfExpenseAf,jdbcType=VARCHAR},
      </if>
      <if test="record.selfExpenseDiff != null">
        self_expense_diff = #{record.selfExpenseDiff,jdbcType=DECIMAL},
      </if>
      <if test="record.selfExpenseDiffPer != null">
        self_expense_diff_per = #{record.selfExpenseDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="record.medicalInsurancePayBe != null">
        medical_insurance_pay_be = #{record.medicalInsurancePayBe,jdbcType=VARCHAR},
      </if>
      <if test="record.medicalInsurancePayAf != null">
        medical_insurance_pay_af = #{record.medicalInsurancePayAf,jdbcType=VARCHAR},
      </if>
      <if test="record.medicalInsurancePayDiff != null">
        medical_insurance_pay_diff = #{record.medicalInsurancePayDiff,jdbcType=DECIMAL},
      </if>
      <if test="record.medicalInsurancePayDiffPer != null">
        medical_insurance_pay_diff_per = #{record.medicalInsurancePayDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="record.closeResultBe != null">
        close_result_be = #{record.closeResultBe,jdbcType=VARCHAR},
      </if>
      <if test="record.closeResultAf != null">
        close_result_af = #{record.closeResultAf,jdbcType=VARCHAR},
      </if>
      <if test="record.prdResult != null">
        prd_result = #{record.prdResult,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update auto_claim_mvc_detail
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      associate_batch_no = #{record.associateBatchNo,jdbcType=VARCHAR},
      paid_amount_be = #{record.paidAmountBe,jdbcType=VARCHAR},
      paid_amount_af = #{record.paidAmountAf,jdbcType=VARCHAR},
      paid_amount_diff = #{record.paidAmountDiff,jdbcType=DECIMAL},
      paid_amount_diff_per = #{record.paidAmountDiffPer,jdbcType=DECIMAL},
      non_responsible_cost_be = #{record.nonResponsibleCostBe,jdbcType=VARCHAR},
      non_responsible_cost_af = #{record.nonResponsibleCostAf,jdbcType=VARCHAR},
      non_responsible_cost_diff = #{record.nonResponsibleCostDiff,jdbcType=DECIMAL},
      non_responsible_cost_diff_per = #{record.nonResponsibleCostDiffPer,jdbcType=DECIMAL},
      self_expense_category_be = #{record.selfExpenseCategoryBe,jdbcType=VARCHAR},
      self_expense_category_af = #{record.selfExpenseCategoryAf,jdbcType=VARCHAR},
      self_expense_category_diff = #{record.selfExpenseCategoryDiff,jdbcType=DECIMAL},
      self_expense_category_diff_per = #{record.selfExpenseCategoryDiffPer,jdbcType=DECIMAL},
      self_expense_be = #{record.selfExpenseBe,jdbcType=VARCHAR},
      self_expense_af = #{record.selfExpenseAf,jdbcType=VARCHAR},
      self_expense_diff = #{record.selfExpenseDiff,jdbcType=DECIMAL},
      self_expense_diff_per = #{record.selfExpenseDiffPer,jdbcType=DECIMAL},
      medical_insurance_pay_be = #{record.medicalInsurancePayBe,jdbcType=VARCHAR},
      medical_insurance_pay_af = #{record.medicalInsurancePayAf,jdbcType=VARCHAR},
      medical_insurance_pay_diff = #{record.medicalInsurancePayDiff,jdbcType=DECIMAL},
      medical_insurance_pay_diff_per = #{record.medicalInsurancePayDiffPer,jdbcType=DECIMAL},
      close_result_be = #{record.closeResultBe,jdbcType=VARCHAR},
      close_result_af = #{record.closeResultAf,jdbcType=VARCHAR},
      prd_result = #{record.prdResult,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AutoClaimMvcDetailDO">
    update auto_claim_mvc_detail
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="associateBatchNo != null">
        associate_batch_no = #{associateBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="paidAmountBe != null">
        paid_amount_be = #{paidAmountBe,jdbcType=VARCHAR},
      </if>
      <if test="paidAmountAf != null">
        paid_amount_af = #{paidAmountAf,jdbcType=VARCHAR},
      </if>
      <if test="paidAmountDiff != null">
        paid_amount_diff = #{paidAmountDiff,jdbcType=DECIMAL},
      </if>
      <if test="paidAmountDiffPer != null">
        paid_amount_diff_per = #{paidAmountDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="nonResponsibleCostBe != null">
        non_responsible_cost_be = #{nonResponsibleCostBe,jdbcType=VARCHAR},
      </if>
      <if test="nonResponsibleCostAf != null">
        non_responsible_cost_af = #{nonResponsibleCostAf,jdbcType=VARCHAR},
      </if>
      <if test="nonResponsibleCostDiff != null">
        non_responsible_cost_diff = #{nonResponsibleCostDiff,jdbcType=DECIMAL},
      </if>
      <if test="nonResponsibleCostDiffPer != null">
        non_responsible_cost_diff_per = #{nonResponsibleCostDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="selfExpenseCategoryBe != null">
        self_expense_category_be = #{selfExpenseCategoryBe,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseCategoryAf != null">
        self_expense_category_af = #{selfExpenseCategoryAf,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseCategoryDiff != null">
        self_expense_category_diff = #{selfExpenseCategoryDiff,jdbcType=DECIMAL},
      </if>
      <if test="selfExpenseCategoryDiffPer != null">
        self_expense_category_diff_per = #{selfExpenseCategoryDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="selfExpenseBe != null">
        self_expense_be = #{selfExpenseBe,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseAf != null">
        self_expense_af = #{selfExpenseAf,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseDiff != null">
        self_expense_diff = #{selfExpenseDiff,jdbcType=DECIMAL},
      </if>
      <if test="selfExpenseDiffPer != null">
        self_expense_diff_per = #{selfExpenseDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="medicalInsurancePayBe != null">
        medical_insurance_pay_be = #{medicalInsurancePayBe,jdbcType=VARCHAR},
      </if>
      <if test="medicalInsurancePayAf != null">
        medical_insurance_pay_af = #{medicalInsurancePayAf,jdbcType=VARCHAR},
      </if>
      <if test="medicalInsurancePayDiff != null">
        medical_insurance_pay_diff = #{medicalInsurancePayDiff,jdbcType=DECIMAL},
      </if>
      <if test="medicalInsurancePayDiffPer != null">
        medical_insurance_pay_diff_per = #{medicalInsurancePayDiffPer,jdbcType=DECIMAL},
      </if>
      <if test="closeResultBe != null">
        close_result_be = #{closeResultBe,jdbcType=VARCHAR},
      </if>
      <if test="closeResultAf != null">
        close_result_af = #{closeResultAf,jdbcType=VARCHAR},
      </if>
      <if test="prdResult != null">
        prd_result = #{prdResult,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AutoClaimMvcDetailDO">
    update auto_claim_mvc_detail
    set report_no = #{reportNo,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      associate_batch_no = #{associateBatchNo,jdbcType=VARCHAR},
      paid_amount_be = #{paidAmountBe,jdbcType=VARCHAR},
      paid_amount_af = #{paidAmountAf,jdbcType=VARCHAR},
      paid_amount_diff = #{paidAmountDiff,jdbcType=DECIMAL},
      paid_amount_diff_per = #{paidAmountDiffPer,jdbcType=DECIMAL},
      non_responsible_cost_be = #{nonResponsibleCostBe,jdbcType=VARCHAR},
      non_responsible_cost_af = #{nonResponsibleCostAf,jdbcType=VARCHAR},
      non_responsible_cost_diff = #{nonResponsibleCostDiff,jdbcType=DECIMAL},
      non_responsible_cost_diff_per = #{nonResponsibleCostDiffPer,jdbcType=DECIMAL},
      self_expense_category_be = #{selfExpenseCategoryBe,jdbcType=VARCHAR},
      self_expense_category_af = #{selfExpenseCategoryAf,jdbcType=VARCHAR},
      self_expense_category_diff = #{selfExpenseCategoryDiff,jdbcType=DECIMAL},
      self_expense_category_diff_per = #{selfExpenseCategoryDiffPer,jdbcType=DECIMAL},
      self_expense_be = #{selfExpenseBe,jdbcType=VARCHAR},
      self_expense_af = #{selfExpenseAf,jdbcType=VARCHAR},
      self_expense_diff = #{selfExpenseDiff,jdbcType=DECIMAL},
      self_expense_diff_per = #{selfExpenseDiffPer,jdbcType=DECIMAL},
      medical_insurance_pay_be = #{medicalInsurancePayBe,jdbcType=VARCHAR},
      medical_insurance_pay_af = #{medicalInsurancePayAf,jdbcType=VARCHAR},
      medical_insurance_pay_diff = #{medicalInsurancePayDiff,jdbcType=DECIMAL},
      medical_insurance_pay_diff_per = #{medicalInsurancePayDiffPer,jdbcType=DECIMAL},
      close_result_be = #{closeResultBe,jdbcType=VARCHAR},
      close_result_af = #{closeResultAf,jdbcType=VARCHAR},
      prd_result = #{prdResult,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into auto_claim_mvc_detail
    (id, report_no, batch_no, associate_batch_no, paid_amount_be, paid_amount_af, paid_amount_diff, 
      paid_amount_diff_per, non_responsible_cost_be, non_responsible_cost_af, non_responsible_cost_diff, 
      non_responsible_cost_diff_per, self_expense_category_be, self_expense_category_af, 
      self_expense_category_diff, self_expense_category_diff_per, self_expense_be, self_expense_af, 
      self_expense_diff, self_expense_diff_per, medical_insurance_pay_be, medical_insurance_pay_af, 
      medical_insurance_pay_diff, medical_insurance_pay_diff_per, close_result_be, close_result_af, 
      prd_result, extra_info, gmt_created, gmt_modified, creator, modifier, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR}, 
        #{item.associateBatchNo,jdbcType=VARCHAR}, #{item.paidAmountBe,jdbcType=VARCHAR}, 
        #{item.paidAmountAf,jdbcType=VARCHAR}, #{item.paidAmountDiff,jdbcType=DECIMAL}, 
        #{item.paidAmountDiffPer,jdbcType=DECIMAL}, #{item.nonResponsibleCostBe,jdbcType=VARCHAR}, 
        #{item.nonResponsibleCostAf,jdbcType=VARCHAR}, #{item.nonResponsibleCostDiff,jdbcType=DECIMAL}, 
        #{item.nonResponsibleCostDiffPer,jdbcType=DECIMAL}, #{item.selfExpenseCategoryBe,jdbcType=VARCHAR}, 
        #{item.selfExpenseCategoryAf,jdbcType=VARCHAR}, #{item.selfExpenseCategoryDiff,jdbcType=DECIMAL}, 
        #{item.selfExpenseCategoryDiffPer,jdbcType=DECIMAL}, #{item.selfExpenseBe,jdbcType=VARCHAR}, 
        #{item.selfExpenseAf,jdbcType=VARCHAR}, #{item.selfExpenseDiff,jdbcType=DECIMAL}, 
        #{item.selfExpenseDiffPer,jdbcType=DECIMAL}, #{item.medicalInsurancePayBe,jdbcType=VARCHAR}, 
        #{item.medicalInsurancePayAf,jdbcType=VARCHAR}, #{item.medicalInsurancePayDiff,jdbcType=DECIMAL}, 
        #{item.medicalInsurancePayDiffPer,jdbcType=DECIMAL}, #{item.closeResultBe,jdbcType=VARCHAR}, 
        #{item.closeResultAf,jdbcType=VARCHAR}, #{item.prdResult,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into auto_claim_mvc_detail (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'batch_no'.toString() == column.value">
          #{item.batchNo,jdbcType=VARCHAR}
        </if>
        <if test="'associate_batch_no'.toString() == column.value">
          #{item.associateBatchNo,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount_be'.toString() == column.value">
          #{item.paidAmountBe,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount_af'.toString() == column.value">
          #{item.paidAmountAf,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount_diff'.toString() == column.value">
          #{item.paidAmountDiff,jdbcType=DECIMAL}
        </if>
        <if test="'paid_amount_diff_per'.toString() == column.value">
          #{item.paidAmountDiffPer,jdbcType=DECIMAL}
        </if>
        <if test="'non_responsible_cost_be'.toString() == column.value">
          #{item.nonResponsibleCostBe,jdbcType=VARCHAR}
        </if>
        <if test="'non_responsible_cost_af'.toString() == column.value">
          #{item.nonResponsibleCostAf,jdbcType=VARCHAR}
        </if>
        <if test="'non_responsible_cost_diff'.toString() == column.value">
          #{item.nonResponsibleCostDiff,jdbcType=DECIMAL}
        </if>
        <if test="'non_responsible_cost_diff_per'.toString() == column.value">
          #{item.nonResponsibleCostDiffPer,jdbcType=DECIMAL}
        </if>
        <if test="'self_expense_category_be'.toString() == column.value">
          #{item.selfExpenseCategoryBe,jdbcType=VARCHAR}
        </if>
        <if test="'self_expense_category_af'.toString() == column.value">
          #{item.selfExpenseCategoryAf,jdbcType=VARCHAR}
        </if>
        <if test="'self_expense_category_diff'.toString() == column.value">
          #{item.selfExpenseCategoryDiff,jdbcType=DECIMAL}
        </if>
        <if test="'self_expense_category_diff_per'.toString() == column.value">
          #{item.selfExpenseCategoryDiffPer,jdbcType=DECIMAL}
        </if>
        <if test="'self_expense_be'.toString() == column.value">
          #{item.selfExpenseBe,jdbcType=VARCHAR}
        </if>
        <if test="'self_expense_af'.toString() == column.value">
          #{item.selfExpenseAf,jdbcType=VARCHAR}
        </if>
        <if test="'self_expense_diff'.toString() == column.value">
          #{item.selfExpenseDiff,jdbcType=DECIMAL}
        </if>
        <if test="'self_expense_diff_per'.toString() == column.value">
          #{item.selfExpenseDiffPer,jdbcType=DECIMAL}
        </if>
        <if test="'medical_insurance_pay_be'.toString() == column.value">
          #{item.medicalInsurancePayBe,jdbcType=VARCHAR}
        </if>
        <if test="'medical_insurance_pay_af'.toString() == column.value">
          #{item.medicalInsurancePayAf,jdbcType=VARCHAR}
        </if>
        <if test="'medical_insurance_pay_diff'.toString() == column.value">
          #{item.medicalInsurancePayDiff,jdbcType=DECIMAL}
        </if>
        <if test="'medical_insurance_pay_diff_per'.toString() == column.value">
          #{item.medicalInsurancePayDiffPer,jdbcType=DECIMAL}
        </if>
        <if test="'close_result_be'.toString() == column.value">
          #{item.closeResultBe,jdbcType=VARCHAR}
        </if>
        <if test="'close_result_af'.toString() == column.value">
          #{item.closeResultAf,jdbcType=VARCHAR}
        </if>
        <if test="'prd_result'.toString() == column.value">
          #{item.prdResult,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>