<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.IdentificationAgencyMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.IdentificationAgencyDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="agency_name" jdbcType="VARCHAR" property="agencyName" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="credit_code" jdbcType="VARCHAR" property="creditCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="competent_authority" jdbcType="VARCHAR" property="competentAuthority" />
    <result column="established_time" jdbcType="VARCHAR" property="establishedTime" />
    <result column="charge_person" jdbcType="VARCHAR" property="chargePerson" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="business_scope" jdbcType="VARCHAR" property="businessScope" />
    <result column="judicial_appraiser" jdbcType="VARCHAR" property="judicialAppraiser" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="agency_introduction" jdbcType="VARCHAR" property="agencyIntroduction" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, agency_name, source_id, credit_code, address, competent_authority, established_time, 
    charge_person, phone, business_scope, judicial_appraiser, region, agency_introduction, 
    gmt_created, gmt_modified, creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.IdentificationAgencyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from identification_agency
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from identification_agency
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.IdentificationAgencyDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into identification_agency (agency_name, source_id, credit_code, 
      address, competent_authority, established_time, 
      charge_person, phone, business_scope, 
      judicial_appraiser, region, agency_introduction, 
      gmt_created, gmt_modified, creator, 
      modifier, is_deleted)
    values (#{agencyName,jdbcType=VARCHAR}, #{sourceId,jdbcType=VARCHAR}, #{creditCode,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{competentAuthority,jdbcType=VARCHAR}, #{establishedTime,jdbcType=VARCHAR}, 
      #{chargePerson,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{businessScope,jdbcType=VARCHAR}, 
      #{judicialAppraiser,jdbcType=VARCHAR}, #{region,jdbcType=VARCHAR}, #{agencyIntroduction,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.IdentificationAgencyDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into identification_agency
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="agencyName != null">
        agency_name,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="creditCode != null">
        credit_code,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="competentAuthority != null">
        competent_authority,
      </if>
      <if test="establishedTime != null">
        established_time,
      </if>
      <if test="chargePerson != null">
        charge_person,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="businessScope != null">
        business_scope,
      </if>
      <if test="judicialAppraiser != null">
        judicial_appraiser,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="agencyIntroduction != null">
        agency_introduction,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="agencyName != null">
        #{agencyName,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="creditCode != null">
        #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="competentAuthority != null">
        #{competentAuthority,jdbcType=VARCHAR},
      </if>
      <if test="establishedTime != null">
        #{establishedTime,jdbcType=VARCHAR},
      </if>
      <if test="chargePerson != null">
        #{chargePerson,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null">
        #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="judicialAppraiser != null">
        #{judicialAppraiser,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        #{region,jdbcType=VARCHAR},
      </if>
      <if test="agencyIntroduction != null">
        #{agencyIntroduction,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.IdentificationAgencyExample" resultType="java.lang.Long">
    select count(*) from identification_agency
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update identification_agency
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.agencyName != null">
        agency_name = #{record.agencyName,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.creditCode != null">
        credit_code = #{record.creditCode,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.competentAuthority != null">
        competent_authority = #{record.competentAuthority,jdbcType=VARCHAR},
      </if>
      <if test="record.establishedTime != null">
        established_time = #{record.establishedTime,jdbcType=VARCHAR},
      </if>
      <if test="record.chargePerson != null">
        charge_person = #{record.chargePerson,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.businessScope != null">
        business_scope = #{record.businessScope,jdbcType=VARCHAR},
      </if>
      <if test="record.judicialAppraiser != null">
        judicial_appraiser = #{record.judicialAppraiser,jdbcType=VARCHAR},
      </if>
      <if test="record.region != null">
        region = #{record.region,jdbcType=VARCHAR},
      </if>
      <if test="record.agencyIntroduction != null">
        agency_introduction = #{record.agencyIntroduction,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update identification_agency
    set id = #{record.id,jdbcType=BIGINT},
      agency_name = #{record.agencyName,jdbcType=VARCHAR},
      source_id = #{record.sourceId,jdbcType=VARCHAR},
      credit_code = #{record.creditCode,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      competent_authority = #{record.competentAuthority,jdbcType=VARCHAR},
      established_time = #{record.establishedTime,jdbcType=VARCHAR},
      charge_person = #{record.chargePerson,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      business_scope = #{record.businessScope,jdbcType=VARCHAR},
      judicial_appraiser = #{record.judicialAppraiser,jdbcType=VARCHAR},
      region = #{record.region,jdbcType=VARCHAR},
      agency_introduction = #{record.agencyIntroduction,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.IdentificationAgencyDO">
    update identification_agency
    <set>
      <if test="agencyName != null">
        agency_name = #{agencyName,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="creditCode != null">
        credit_code = #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="competentAuthority != null">
        competent_authority = #{competentAuthority,jdbcType=VARCHAR},
      </if>
      <if test="establishedTime != null">
        established_time = #{establishedTime,jdbcType=VARCHAR},
      </if>
      <if test="chargePerson != null">
        charge_person = #{chargePerson,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null">
        business_scope = #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="judicialAppraiser != null">
        judicial_appraiser = #{judicialAppraiser,jdbcType=VARCHAR},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=VARCHAR},
      </if>
      <if test="agencyIntroduction != null">
        agency_introduction = #{agencyIntroduction,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.IdentificationAgencyDO">
    update identification_agency
    set agency_name = #{agencyName,jdbcType=VARCHAR},
      source_id = #{sourceId,jdbcType=VARCHAR},
      credit_code = #{creditCode,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      competent_authority = #{competentAuthority,jdbcType=VARCHAR},
      established_time = #{establishedTime,jdbcType=VARCHAR},
      charge_person = #{chargePerson,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      business_scope = #{businessScope,jdbcType=VARCHAR},
      judicial_appraiser = #{judicialAppraiser,jdbcType=VARCHAR},
      region = #{region,jdbcType=VARCHAR},
      agency_introduction = #{agencyIntroduction,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into identification_agency
    (agency_name, source_id, credit_code, address, competent_authority, established_time, 
      charge_person, phone, business_scope, judicial_appraiser, region, agency_introduction, 
      gmt_created, gmt_modified, creator, modifier, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.agencyName,jdbcType=VARCHAR}, #{item.sourceId,jdbcType=VARCHAR}, #{item.creditCode,jdbcType=VARCHAR}, 
        #{item.address,jdbcType=VARCHAR}, #{item.competentAuthority,jdbcType=VARCHAR}, 
        #{item.establishedTime,jdbcType=VARCHAR}, #{item.chargePerson,jdbcType=VARCHAR}, 
        #{item.phone,jdbcType=VARCHAR}, #{item.businessScope,jdbcType=VARCHAR}, #{item.judicialAppraiser,jdbcType=VARCHAR}, 
        #{item.region,jdbcType=VARCHAR}, #{item.agencyIntroduction,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into identification_agency (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'agency_name'.toString() == column.value">
          #{item.agencyName,jdbcType=VARCHAR}
        </if>
        <if test="'source_id'.toString() == column.value">
          #{item.sourceId,jdbcType=VARCHAR}
        </if>
        <if test="'credit_code'.toString() == column.value">
          #{item.creditCode,jdbcType=VARCHAR}
        </if>
        <if test="'address'.toString() == column.value">
          #{item.address,jdbcType=VARCHAR}
        </if>
        <if test="'competent_authority'.toString() == column.value">
          #{item.competentAuthority,jdbcType=VARCHAR}
        </if>
        <if test="'established_time'.toString() == column.value">
          #{item.establishedTime,jdbcType=VARCHAR}
        </if>
        <if test="'charge_person'.toString() == column.value">
          #{item.chargePerson,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'business_scope'.toString() == column.value">
          #{item.businessScope,jdbcType=VARCHAR}
        </if>
        <if test="'judicial_appraiser'.toString() == column.value">
          #{item.judicialAppraiser,jdbcType=VARCHAR}
        </if>
        <if test="'region'.toString() == column.value">
          #{item.region,jdbcType=VARCHAR}
        </if>
        <if test="'agency_introduction'.toString() == column.value">
          #{item.agencyIntroduction,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>