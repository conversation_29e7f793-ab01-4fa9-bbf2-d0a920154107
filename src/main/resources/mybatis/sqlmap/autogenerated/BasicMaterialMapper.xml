<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.BasicMaterialMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.BasicMaterialDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pid" jdbcType="BIGINT" property="pid" />
    <result column="level" jdbcType="TINYINT" property="level" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="material_type" jdbcType="TINYINT" property="materialType" />
    <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl" />
    <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName" />
    <result column="describe_name" jdbcType="VARCHAR" property="describeName" />
    <result column="describe_content" jdbcType="VARCHAR" property="describeContent" />
    <result column="notice_content" jdbcType="VARCHAR" property="noticeContent" />
    <result column="email_content" jdbcType="VARCHAR" property="emailContent" />
    <result column="application_template" jdbcType="VARCHAR" property="applicationTemplate" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="examples_url" jdbcType="VARCHAR" property="examplesUrl" />
    <result column="examples_name" jdbcType="VARCHAR" property="examplesName" />
    <result column="example_info" jdbcType="VARCHAR" property="exampleInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, pid, level, material_name, material_type, attachment_url, attachment_name, describe_name, 
    describe_content, notice_content, email_content, application_template, creator, gmt_created, 
    modifier, gmt_modified, is_deleted, examples_url, examples_name, example_info
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.BasicMaterialExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_basic_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_basic_material
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.BasicMaterialDO">
    insert into cargo_basic_material (id, pid, level, 
      material_name, material_type, attachment_url, 
      attachment_name, describe_name, describe_content, 
      notice_content, email_content, application_template, 
      creator, gmt_created, modifier, 
      gmt_modified, is_deleted, examples_url, 
      examples_name, example_info)
    values (#{id,jdbcType=BIGINT}, #{pid,jdbcType=BIGINT}, #{level,jdbcType=TINYINT}, 
      #{materialName,jdbcType=VARCHAR}, #{materialType,jdbcType=TINYINT}, #{attachmentUrl,jdbcType=VARCHAR}, 
      #{attachmentName,jdbcType=VARCHAR}, #{describeName,jdbcType=VARCHAR}, #{describeContent,jdbcType=VARCHAR}, 
      #{noticeContent,jdbcType=VARCHAR}, #{emailContent,jdbcType=VARCHAR}, #{applicationTemplate,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, sysdate(), #{modifier,jdbcType=VARCHAR}, 
      sysdate(), #{isDeleted,jdbcType=CHAR}, #{examplesUrl,jdbcType=VARCHAR}, 
      #{examplesName,jdbcType=VARCHAR}, #{exampleInfo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.BasicMaterialDO">
    insert into cargo_basic_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="materialName != null">
        material_name,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="attachmentUrl != null">
        attachment_url,
      </if>
      <if test="attachmentName != null">
        attachment_name,
      </if>
      <if test="describeName != null">
        describe_name,
      </if>
      <if test="describeContent != null">
        describe_content,
      </if>
      <if test="noticeContent != null">
        notice_content,
      </if>
      <if test="emailContent != null">
        email_content,
      </if>
      <if test="applicationTemplate != null">
        application_template,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="examplesUrl != null">
        examples_url,
      </if>
      <if test="examplesName != null">
        examples_name,
      </if>
      <if test="exampleInfo != null">
        example_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        #{level,jdbcType=TINYINT},
      </if>
      <if test="materialName != null">
        #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=TINYINT},
      </if>
      <if test="attachmentUrl != null">
        #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="attachmentName != null">
        #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="describeName != null">
        #{describeName,jdbcType=VARCHAR},
      </if>
      <if test="describeContent != null">
        #{describeContent,jdbcType=VARCHAR},
      </if>
      <if test="noticeContent != null">
        #{noticeContent,jdbcType=VARCHAR},
      </if>
      <if test="emailContent != null">
        #{emailContent,jdbcType=VARCHAR},
      </if>
      <if test="applicationTemplate != null">
        #{applicationTemplate,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="examplesUrl != null">
        #{examplesUrl,jdbcType=VARCHAR},
      </if>
      <if test="examplesName != null">
        #{examplesName,jdbcType=VARCHAR},
      </if>
      <if test="exampleInfo != null">
        #{exampleInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.BasicMaterialExample" resultType="java.lang.Long">
    select count(*) from cargo_basic_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_basic_material
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.pid != null">
        pid = #{record.pid,jdbcType=BIGINT},
      </if>
      <if test="record.level != null">
        level = #{record.level,jdbcType=TINYINT},
      </if>
      <if test="record.materialName != null">
        material_name = #{record.materialName,jdbcType=VARCHAR},
      </if>
      <if test="record.materialType != null">
        material_type = #{record.materialType,jdbcType=TINYINT},
      </if>
      <if test="record.attachmentUrl != null">
        attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentName != null">
        attachment_name = #{record.attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.describeName != null">
        describe_name = #{record.describeName,jdbcType=VARCHAR},
      </if>
      <if test="record.describeContent != null">
        describe_content = #{record.describeContent,jdbcType=VARCHAR},
      </if>
      <if test="record.noticeContent != null">
        notice_content = #{record.noticeContent,jdbcType=VARCHAR},
      </if>
      <if test="record.emailContent != null">
        email_content = #{record.emailContent,jdbcType=VARCHAR},
      </if>
      <if test="record.applicationTemplate != null">
        application_template = #{record.applicationTemplate,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.examplesUrl != null">
        examples_url = #{record.examplesUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.examplesName != null">
        examples_name = #{record.examplesName,jdbcType=VARCHAR},
      </if>
      <if test="record.exampleInfo != null">
        example_info = #{record.exampleInfo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_basic_material
    set id = #{record.id,jdbcType=BIGINT},
      pid = #{record.pid,jdbcType=BIGINT},
      level = #{record.level,jdbcType=TINYINT},
      material_name = #{record.materialName,jdbcType=VARCHAR},
      material_type = #{record.materialType,jdbcType=TINYINT},
      attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      attachment_name = #{record.attachmentName,jdbcType=VARCHAR},
      describe_name = #{record.describeName,jdbcType=VARCHAR},
      describe_content = #{record.describeContent,jdbcType=VARCHAR},
      notice_content = #{record.noticeContent,jdbcType=VARCHAR},
      email_content = #{record.emailContent,jdbcType=VARCHAR},
      application_template = #{record.applicationTemplate,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      examples_url = #{record.examplesUrl,jdbcType=VARCHAR},
      examples_name = #{record.examplesName,jdbcType=VARCHAR},
      example_info = #{record.exampleInfo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.BasicMaterialDO">
    update cargo_basic_material
    <set>
      <if test="pid != null">
        pid = #{pid,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=TINYINT},
      </if>
      <if test="materialName != null">
        material_name = #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        material_type = #{materialType,jdbcType=TINYINT},
      </if>
      <if test="attachmentUrl != null">
        attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="attachmentName != null">
        attachment_name = #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="describeName != null">
        describe_name = #{describeName,jdbcType=VARCHAR},
      </if>
      <if test="describeContent != null">
        describe_content = #{describeContent,jdbcType=VARCHAR},
      </if>
      <if test="noticeContent != null">
        notice_content = #{noticeContent,jdbcType=VARCHAR},
      </if>
      <if test="emailContent != null">
        email_content = #{emailContent,jdbcType=VARCHAR},
      </if>
      <if test="applicationTemplate != null">
        application_template = #{applicationTemplate,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="examplesUrl != null">
        examples_url = #{examplesUrl,jdbcType=VARCHAR},
      </if>
      <if test="examplesName != null">
        examples_name = #{examplesName,jdbcType=VARCHAR},
      </if>
      <if test="exampleInfo != null">
        example_info = #{exampleInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.BasicMaterialDO">
    update cargo_basic_material
    set pid = #{pid,jdbcType=BIGINT},
      level = #{level,jdbcType=TINYINT},
      material_name = #{materialName,jdbcType=VARCHAR},
      material_type = #{materialType,jdbcType=TINYINT},
      attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      attachment_name = #{attachmentName,jdbcType=VARCHAR},
      describe_name = #{describeName,jdbcType=VARCHAR},
      describe_content = #{describeContent,jdbcType=VARCHAR},
      notice_content = #{noticeContent,jdbcType=VARCHAR},
      email_content = #{emailContent,jdbcType=VARCHAR},
      application_template = #{applicationTemplate,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR},
      examples_url = #{examplesUrl,jdbcType=VARCHAR},
      examples_name = #{examplesName,jdbcType=VARCHAR},
      example_info = #{exampleInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_basic_material
    (id, pid, level, material_name, material_type, attachment_url, attachment_name, describe_name, 
      describe_content, notice_content, email_content, application_template, creator, 
      gmt_created, modifier, gmt_modified, is_deleted, examples_url, examples_name, example_info
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.pid,jdbcType=BIGINT}, #{item.level,jdbcType=TINYINT}, 
        #{item.materialName,jdbcType=VARCHAR}, #{item.materialType,jdbcType=TINYINT}, #{item.attachmentUrl,jdbcType=VARCHAR}, 
        #{item.attachmentName,jdbcType=VARCHAR}, #{item.describeName,jdbcType=VARCHAR}, 
        #{item.describeContent,jdbcType=VARCHAR}, #{item.noticeContent,jdbcType=VARCHAR}, 
        #{item.emailContent,jdbcType=VARCHAR}, #{item.applicationTemplate,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}, #{item.examplesUrl,jdbcType=VARCHAR}, 
        #{item.examplesName,jdbcType=VARCHAR}, #{item.exampleInfo,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_basic_material (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'pid'.toString() == column.value">
          #{item.pid,jdbcType=BIGINT}
        </if>
        <if test="'level'.toString() == column.value">
          #{item.level,jdbcType=TINYINT}
        </if>
        <if test="'material_name'.toString() == column.value">
          #{item.materialName,jdbcType=VARCHAR}
        </if>
        <if test="'material_type'.toString() == column.value">
          #{item.materialType,jdbcType=TINYINT}
        </if>
        <if test="'attachment_url'.toString() == column.value">
          #{item.attachmentUrl,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_name'.toString() == column.value">
          #{item.attachmentName,jdbcType=VARCHAR}
        </if>
        <if test="'describe_name'.toString() == column.value">
          #{item.describeName,jdbcType=VARCHAR}
        </if>
        <if test="'describe_content'.toString() == column.value">
          #{item.describeContent,jdbcType=VARCHAR}
        </if>
        <if test="'notice_content'.toString() == column.value">
          #{item.noticeContent,jdbcType=VARCHAR}
        </if>
        <if test="'email_content'.toString() == column.value">
          #{item.emailContent,jdbcType=VARCHAR}
        </if>
        <if test="'application_template'.toString() == column.value">
          #{item.applicationTemplate,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'examples_url'.toString() == column.value">
          #{item.examplesUrl,jdbcType=VARCHAR}
        </if>
        <if test="'examples_name'.toString() == column.value">
          #{item.examplesName,jdbcType=VARCHAR}
        </if>
        <if test="'example_info'.toString() == column.value">
          #{item.exampleInfo,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>