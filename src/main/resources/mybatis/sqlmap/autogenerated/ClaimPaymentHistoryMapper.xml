<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimPaymentHistoryMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimPaymentHistoryDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="request_source" jdbcType="VARCHAR" property="requestSource" />
    <result column="idempotent_no" jdbcType="VARCHAR" property="idempotentNo" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="order_title" jdbcType="VARCHAR" property="orderTitle" />
    <result column="order_remark" jdbcType="VARCHAR" property="orderRemark" />
    <result column="pay_way" jdbcType="TINYINT" property="payWay" />
    <result column="fee_type" jdbcType="TINYINT" property="feeType" />
    <result column="payee_account" jdbcType="VARCHAR" property="payeeAccount" />
    <result column="payee_name" jdbcType="VARCHAR" property="payeeName" />
    <result column="amount" jdbcType="VARCHAR" property="amount" />
    <result column="exec_cnt" jdbcType="INTEGER" property="execCnt" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="pay_date" jdbcType="TIMESTAMP" property="payDate" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="payment_id" jdbcType="BIGINT" property="paymentId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, request_source, idempotent_no, biz_code, biz_no, order_no, trade_no, order_title, 
    order_remark, pay_way, fee_type, payee_account, payee_name, amount, exec_cnt, state, 
    pay_date, extra_info, remark, is_deleted, gmt_created, gmt_modified, creator, modifier, 
    payment_id
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPaymentHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_payment_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_payment_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimPaymentHistoryDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_payment_history (request_source, idempotent_no, biz_code, 
      biz_no, order_no, trade_no, 
      order_title, order_remark, pay_way, 
      fee_type, payee_account, payee_name, 
      amount, exec_cnt, state, 
      pay_date, extra_info, remark, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier, payment_id
      )
    values (#{requestSource,jdbcType=VARCHAR}, #{idempotentNo,jdbcType=VARCHAR}, #{bizCode,jdbcType=VARCHAR}, 
      #{bizNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{tradeNo,jdbcType=VARCHAR}, 
      #{orderTitle,jdbcType=VARCHAR}, #{orderRemark,jdbcType=VARCHAR}, #{payWay,jdbcType=TINYINT}, 
      #{feeType,jdbcType=TINYINT}, #{payeeAccount,jdbcType=VARCHAR}, #{payeeName,jdbcType=VARCHAR}, 
      #{amount,jdbcType=VARCHAR}, #{execCnt,jdbcType=INTEGER}, #{state,jdbcType=TINYINT}, 
      #{payDate,jdbcType=TIMESTAMP}, #{extraInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{paymentId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPaymentHistoryDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into claim_payment_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="requestSource != null">
        request_source,
      </if>
      <if test="idempotentNo != null">
        idempotent_no,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="orderTitle != null">
        order_title,
      </if>
      <if test="orderRemark != null">
        order_remark,
      </if>
      <if test="payWay != null">
        pay_way,
      </if>
      <if test="feeType != null">
        fee_type,
      </if>
      <if test="payeeAccount != null">
        payee_account,
      </if>
      <if test="payeeName != null">
        payee_name,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="execCnt != null">
        exec_cnt,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="payDate != null">
        pay_date,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="paymentId != null">
        payment_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="requestSource != null">
        #{requestSource,jdbcType=VARCHAR},
      </if>
      <if test="idempotentNo != null">
        #{idempotentNo,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="orderTitle != null">
        #{orderTitle,jdbcType=VARCHAR},
      </if>
      <if test="orderRemark != null">
        #{orderRemark,jdbcType=VARCHAR},
      </if>
      <if test="payWay != null">
        #{payWay,jdbcType=TINYINT},
      </if>
      <if test="feeType != null">
        #{feeType,jdbcType=TINYINT},
      </if>
      <if test="payeeAccount != null">
        #{payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=VARCHAR},
      </if>
      <if test="execCnt != null">
        #{execCnt,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=TINYINT},
      </if>
      <if test="payDate != null">
        #{payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="paymentId != null">
        #{paymentId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPaymentHistoryExample" resultType="java.lang.Long">
    select count(*) from claim_payment_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_payment_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.requestSource != null">
        request_source = #{record.requestSource,jdbcType=VARCHAR},
      </if>
      <if test="record.idempotentNo != null">
        idempotent_no = #{record.idempotentNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bizNo != null">
        biz_no = #{record.bizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeNo != null">
        trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderTitle != null">
        order_title = #{record.orderTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.orderRemark != null">
        order_remark = #{record.orderRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.payWay != null">
        pay_way = #{record.payWay,jdbcType=TINYINT},
      </if>
      <if test="record.feeType != null">
        fee_type = #{record.feeType,jdbcType=TINYINT},
      </if>
      <if test="record.payeeAccount != null">
        payee_account = #{record.payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeName != null">
        payee_name = #{record.payeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=VARCHAR},
      </if>
      <if test="record.execCnt != null">
        exec_cnt = #{record.execCnt,jdbcType=INTEGER},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=TINYINT},
      </if>
      <if test="record.payDate != null">
        pay_date = #{record.payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentId != null">
        payment_id = #{record.paymentId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_payment_history
    set id = #{record.id,jdbcType=BIGINT},
      request_source = #{record.requestSource,jdbcType=VARCHAR},
      idempotent_no = #{record.idempotentNo,jdbcType=VARCHAR},
      biz_code = #{record.bizCode,jdbcType=VARCHAR},
      biz_no = #{record.bizNo,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      order_title = #{record.orderTitle,jdbcType=VARCHAR},
      order_remark = #{record.orderRemark,jdbcType=VARCHAR},
      pay_way = #{record.payWay,jdbcType=TINYINT},
      fee_type = #{record.feeType,jdbcType=TINYINT},
      payee_account = #{record.payeeAccount,jdbcType=VARCHAR},
      payee_name = #{record.payeeName,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=VARCHAR},
      exec_cnt = #{record.execCnt,jdbcType=INTEGER},
      state = #{record.state,jdbcType=TINYINT},
      pay_date = #{record.payDate,jdbcType=TIMESTAMP},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      payment_id = #{record.paymentId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPaymentHistoryDO">
    update claim_payment_history
    <set>
      <if test="requestSource != null">
        request_source = #{requestSource,jdbcType=VARCHAR},
      </if>
      <if test="idempotentNo != null">
        idempotent_no = #{idempotentNo,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="orderTitle != null">
        order_title = #{orderTitle,jdbcType=VARCHAR},
      </if>
      <if test="orderRemark != null">
        order_remark = #{orderRemark,jdbcType=VARCHAR},
      </if>
      <if test="payWay != null">
        pay_way = #{payWay,jdbcType=TINYINT},
      </if>
      <if test="feeType != null">
        fee_type = #{feeType,jdbcType=TINYINT},
      </if>
      <if test="payeeAccount != null">
        payee_account = #{payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        payee_name = #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=VARCHAR},
      </if>
      <if test="execCnt != null">
        exec_cnt = #{execCnt,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=TINYINT},
      </if>
      <if test="payDate != null">
        pay_date = #{payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="paymentId != null">
        payment_id = #{paymentId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimPaymentHistoryDO">
    update claim_payment_history
    set request_source = #{requestSource,jdbcType=VARCHAR},
      idempotent_no = #{idempotentNo,jdbcType=VARCHAR},
      biz_code = #{bizCode,jdbcType=VARCHAR},
      biz_no = #{bizNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      order_title = #{orderTitle,jdbcType=VARCHAR},
      order_remark = #{orderRemark,jdbcType=VARCHAR},
      pay_way = #{payWay,jdbcType=TINYINT},
      fee_type = #{feeType,jdbcType=TINYINT},
      payee_account = #{payeeAccount,jdbcType=VARCHAR},
      payee_name = #{payeeName,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=VARCHAR},
      exec_cnt = #{execCnt,jdbcType=INTEGER},
      state = #{state,jdbcType=TINYINT},
      pay_date = #{payDate,jdbcType=TIMESTAMP},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      payment_id = #{paymentId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into claim_payment_history
    (request_source, idempotent_no, biz_code, biz_no, order_no, trade_no, order_title, 
      order_remark, pay_way, fee_type, payee_account, payee_name, amount, exec_cnt, state, 
      pay_date, extra_info, remark, is_deleted, gmt_created, gmt_modified, creator, modifier, 
      payment_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.requestSource,jdbcType=VARCHAR}, #{item.idempotentNo,jdbcType=VARCHAR}, #{item.bizCode,jdbcType=VARCHAR}, 
        #{item.bizNo,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=VARCHAR}, #{item.tradeNo,jdbcType=VARCHAR}, 
        #{item.orderTitle,jdbcType=VARCHAR}, #{item.orderRemark,jdbcType=VARCHAR}, #{item.payWay,jdbcType=TINYINT}, 
        #{item.feeType,jdbcType=TINYINT}, #{item.payeeAccount,jdbcType=VARCHAR}, #{item.payeeName,jdbcType=VARCHAR}, 
        #{item.amount,jdbcType=VARCHAR}, #{item.execCnt,jdbcType=INTEGER}, #{item.state,jdbcType=TINYINT}, 
        #{item.payDate,jdbcType=TIMESTAMP}, #{item.extraInfo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.paymentId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into claim_payment_history (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'request_source'.toString() == column.value">
          #{item.requestSource,jdbcType=VARCHAR}
        </if>
        <if test="'idempotent_no'.toString() == column.value">
          #{item.idempotentNo,jdbcType=VARCHAR}
        </if>
        <if test="'biz_code'.toString() == column.value">
          #{item.bizCode,jdbcType=VARCHAR}
        </if>
        <if test="'biz_no'.toString() == column.value">
          #{item.bizNo,jdbcType=VARCHAR}
        </if>
        <if test="'order_no'.toString() == column.value">
          #{item.orderNo,jdbcType=VARCHAR}
        </if>
        <if test="'trade_no'.toString() == column.value">
          #{item.tradeNo,jdbcType=VARCHAR}
        </if>
        <if test="'order_title'.toString() == column.value">
          #{item.orderTitle,jdbcType=VARCHAR}
        </if>
        <if test="'order_remark'.toString() == column.value">
          #{item.orderRemark,jdbcType=VARCHAR}
        </if>
        <if test="'pay_way'.toString() == column.value">
          #{item.payWay,jdbcType=TINYINT}
        </if>
        <if test="'fee_type'.toString() == column.value">
          #{item.feeType,jdbcType=TINYINT}
        </if>
        <if test="'payee_account'.toString() == column.value">
          #{item.payeeAccount,jdbcType=VARCHAR}
        </if>
        <if test="'payee_name'.toString() == column.value">
          #{item.payeeName,jdbcType=VARCHAR}
        </if>
        <if test="'amount'.toString() == column.value">
          #{item.amount,jdbcType=VARCHAR}
        </if>
        <if test="'exec_cnt'.toString() == column.value">
          #{item.execCnt,jdbcType=INTEGER}
        </if>
        <if test="'state'.toString() == column.value">
          #{item.state,jdbcType=TINYINT}
        </if>
        <if test="'pay_date'.toString() == column.value">
          #{item.payDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'payment_id'.toString() == column.value">
          #{item.paymentId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>