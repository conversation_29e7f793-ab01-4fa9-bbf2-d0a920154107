<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.CwxSegmentationClaimBasicMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.CwxSegmentationClaimBasicDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="label_ago_settled_claim" jdbcType="VARCHAR" property="labelAgoSettledClaim" />
    <result column="label_refuse" jdbcType="VARCHAR" property="labelRefuse" />
    <result column="label_agoaccnt" jdbcType="VARCHAR" property="labelAgoaccnt" />
    <result column="label_ago_policyt_tb_cnt" jdbcType="VARCHAR" property="labelAgoPolicytTbCnt" />
    <result column="label_black" jdbcType="VARCHAR" property="labelBlack" />
    <result column="label_kesu" jdbcType="VARCHAR" property="labelKesu" />
    <result column="policy_type" jdbcType="VARCHAR" property="policyType" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_no, label_ago_settled_claim, label_refuse, label_agoaccnt, label_ago_policyt_tb_cnt, 
    label_black, label_kesu, policy_type, gmt_created, gmt_modified, creator, modifier, 
    is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.CwxSegmentationClaimBasicExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cdm_cwx_segmentation_claim_basic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cdm_cwx_segmentation_claim_basic
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.CwxSegmentationClaimBasicDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cdm_cwx_segmentation_claim_basic (policy_no, label_ago_settled_claim, label_refuse, 
      label_agoaccnt, label_ago_policyt_tb_cnt, label_black, 
      label_kesu, policy_type, gmt_created, 
      gmt_modified, creator, modifier, 
      is_deleted)
    values (#{policyNo,jdbcType=VARCHAR}, #{labelAgoSettledClaim,jdbcType=VARCHAR}, #{labelRefuse,jdbcType=VARCHAR}, 
      #{labelAgoaccnt,jdbcType=VARCHAR}, #{labelAgoPolicytTbCnt,jdbcType=VARCHAR}, #{labelBlack,jdbcType=VARCHAR}, 
      #{labelKesu,jdbcType=VARCHAR}, #{policyType,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.CwxSegmentationClaimBasicDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cdm_cwx_segmentation_claim_basic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="labelAgoSettledClaim != null">
        label_ago_settled_claim,
      </if>
      <if test="labelRefuse != null">
        label_refuse,
      </if>
      <if test="labelAgoaccnt != null">
        label_agoaccnt,
      </if>
      <if test="labelAgoPolicytTbCnt != null">
        label_ago_policyt_tb_cnt,
      </if>
      <if test="labelBlack != null">
        label_black,
      </if>
      <if test="labelKesu != null">
        label_kesu,
      </if>
      <if test="policyType != null">
        policy_type,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="labelAgoSettledClaim != null">
        #{labelAgoSettledClaim,jdbcType=VARCHAR},
      </if>
      <if test="labelRefuse != null">
        #{labelRefuse,jdbcType=VARCHAR},
      </if>
      <if test="labelAgoaccnt != null">
        #{labelAgoaccnt,jdbcType=VARCHAR},
      </if>
      <if test="labelAgoPolicytTbCnt != null">
        #{labelAgoPolicytTbCnt,jdbcType=VARCHAR},
      </if>
      <if test="labelBlack != null">
        #{labelBlack,jdbcType=VARCHAR},
      </if>
      <if test="labelKesu != null">
        #{labelKesu,jdbcType=VARCHAR},
      </if>
      <if test="policyType != null">
        #{policyType,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.CwxSegmentationClaimBasicExample" resultType="java.lang.Long">
    select count(*) from cdm_cwx_segmentation_claim_basic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cdm_cwx_segmentation_claim_basic
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.labelAgoSettledClaim != null">
        label_ago_settled_claim = #{record.labelAgoSettledClaim,jdbcType=VARCHAR},
      </if>
      <if test="record.labelRefuse != null">
        label_refuse = #{record.labelRefuse,jdbcType=VARCHAR},
      </if>
      <if test="record.labelAgoaccnt != null">
        label_agoaccnt = #{record.labelAgoaccnt,jdbcType=VARCHAR},
      </if>
      <if test="record.labelAgoPolicytTbCnt != null">
        label_ago_policyt_tb_cnt = #{record.labelAgoPolicytTbCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.labelBlack != null">
        label_black = #{record.labelBlack,jdbcType=VARCHAR},
      </if>
      <if test="record.labelKesu != null">
        label_kesu = #{record.labelKesu,jdbcType=VARCHAR},
      </if>
      <if test="record.policyType != null">
        policy_type = #{record.policyType,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cdm_cwx_segmentation_claim_basic
    set id = #{record.id,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      label_ago_settled_claim = #{record.labelAgoSettledClaim,jdbcType=VARCHAR},
      label_refuse = #{record.labelRefuse,jdbcType=VARCHAR},
      label_agoaccnt = #{record.labelAgoaccnt,jdbcType=VARCHAR},
      label_ago_policyt_tb_cnt = #{record.labelAgoPolicytTbCnt,jdbcType=VARCHAR},
      label_black = #{record.labelBlack,jdbcType=VARCHAR},
      label_kesu = #{record.labelKesu,jdbcType=VARCHAR},
      policy_type = #{record.policyType,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.CwxSegmentationClaimBasicDO">
    update cdm_cwx_segmentation_claim_basic
    <set>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="labelAgoSettledClaim != null">
        label_ago_settled_claim = #{labelAgoSettledClaim,jdbcType=VARCHAR},
      </if>
      <if test="labelRefuse != null">
        label_refuse = #{labelRefuse,jdbcType=VARCHAR},
      </if>
      <if test="labelAgoaccnt != null">
        label_agoaccnt = #{labelAgoaccnt,jdbcType=VARCHAR},
      </if>
      <if test="labelAgoPolicytTbCnt != null">
        label_ago_policyt_tb_cnt = #{labelAgoPolicytTbCnt,jdbcType=VARCHAR},
      </if>
      <if test="labelBlack != null">
        label_black = #{labelBlack,jdbcType=VARCHAR},
      </if>
      <if test="labelKesu != null">
        label_kesu = #{labelKesu,jdbcType=VARCHAR},
      </if>
      <if test="policyType != null">
        policy_type = #{policyType,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.CwxSegmentationClaimBasicDO">
    update cdm_cwx_segmentation_claim_basic
    set policy_no = #{policyNo,jdbcType=VARCHAR},
      label_ago_settled_claim = #{labelAgoSettledClaim,jdbcType=VARCHAR},
      label_refuse = #{labelRefuse,jdbcType=VARCHAR},
      label_agoaccnt = #{labelAgoaccnt,jdbcType=VARCHAR},
      label_ago_policyt_tb_cnt = #{labelAgoPolicytTbCnt,jdbcType=VARCHAR},
      label_black = #{labelBlack,jdbcType=VARCHAR},
      label_kesu = #{labelKesu,jdbcType=VARCHAR},
      policy_type = #{policyType,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into cdm_cwx_segmentation_claim_basic
    (policy_no, label_ago_settled_claim, label_refuse, label_agoaccnt, label_ago_policyt_tb_cnt, 
      label_black, label_kesu, policy_type, gmt_created, gmt_modified, creator, modifier, 
      is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.policyNo,jdbcType=VARCHAR}, #{item.labelAgoSettledClaim,jdbcType=VARCHAR}, 
        #{item.labelRefuse,jdbcType=VARCHAR}, #{item.labelAgoaccnt,jdbcType=VARCHAR}, #{item.labelAgoPolicytTbCnt,jdbcType=VARCHAR}, 
        #{item.labelBlack,jdbcType=VARCHAR}, #{item.labelKesu,jdbcType=VARCHAR}, #{item.policyType,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into cdm_cwx_segmentation_claim_basic (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'label_ago_settled_claim'.toString() == column.value">
          #{item.labelAgoSettledClaim,jdbcType=VARCHAR}
        </if>
        <if test="'label_refuse'.toString() == column.value">
          #{item.labelRefuse,jdbcType=VARCHAR}
        </if>
        <if test="'label_agoaccnt'.toString() == column.value">
          #{item.labelAgoaccnt,jdbcType=VARCHAR}
        </if>
        <if test="'label_ago_policyt_tb_cnt'.toString() == column.value">
          #{item.labelAgoPolicytTbCnt,jdbcType=VARCHAR}
        </if>
        <if test="'label_black'.toString() == column.value">
          #{item.labelBlack,jdbcType=VARCHAR}
        </if>
        <if test="'label_kesu'.toString() == column.value">
          #{item.labelKesu,jdbcType=VARCHAR}
        </if>
        <if test="'policy_type'.toString() == column.value">
          #{item.policyType,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>