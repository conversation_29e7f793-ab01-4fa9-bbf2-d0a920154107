<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimPushInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimPushInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="push_service_name" jdbcType="VARCHAR" property="pushServiceName" />
    <result column="push_status" jdbcType="VARCHAR" property="pushStatus" />
    <result column="didi_case_no" jdbcType="VARCHAR" property="didiCaseNo" />
    <result column="inscompany_case_no" jdbcType="VARCHAR" property="inscompanyCaseNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="case_info" jdbcType="VARCHAR" property="caseInfo" />
    <result column="case_status_info" jdbcType="VARCHAR" property="caseStatusInfo" />
    <result column="reporter_info" jdbcType="VARCHAR" property="reporterInfo" />
    <result column="customer_info" jdbcType="VARCHAR" property="customerInfo" />
    <result column="contact_info" jdbcType="VARCHAR" property="contactInfo" />
    <result column="family_policy_info" jdbcType="VARCHAR" property="familyPolicyInfo" />
    <result column="account_info" jdbcType="VARCHAR" property="accountInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="pay_info" jdbcType="VARCHAR" property="payInfo" />
    <result column="response_json" jdbcType="VARCHAR" property="responseJson" />
    <result column="batch_claim_bill_no" jdbcType="VARCHAR" property="batchClaimBillNo" />
    <result column="report_date" jdbcType="TIMESTAMP" property="reportDate" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.zhongan.lincoln.dal.domain.ClaimPushInfoDO">
    <result column="pic_info" jdbcType="LONGVARCHAR" property="picInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, push_service_name, push_status, didi_case_no, inscompany_case_no, policy_no, 
    case_info, case_status_info, reporter_info, customer_info, contact_info, family_policy_info, 
    account_info, creator, gmt_created, modifier, gmt_modified, is_deleted, pay_info, 
    response_json, batch_claim_bill_no, report_date
  </sql>
  <sql id="Blob_Column_List">
    pic_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.zhongan.lincoln.dal.domain.ClaimPushInfoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from cargo_claim_push_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPushInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_claim_push_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from cargo_claim_push_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimPushInfoDO">
    insert into cargo_claim_push_info (id, push_service_name, push_status, 
      didi_case_no, inscompany_case_no, policy_no, 
      case_info, case_status_info, reporter_info, 
      customer_info, contact_info, family_policy_info, 
      account_info, creator, gmt_created, 
      modifier, gmt_modified, is_deleted, 
      pay_info, response_json, batch_claim_bill_no, 
      report_date, pic_info)
    values (#{id,jdbcType=BIGINT}, #{pushServiceName,jdbcType=VARCHAR}, #{pushStatus,jdbcType=VARCHAR}, 
      #{didiCaseNo,jdbcType=VARCHAR}, #{inscompanyCaseNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, 
      #{caseInfo,jdbcType=VARCHAR}, #{caseStatusInfo,jdbcType=VARCHAR}, #{reporterInfo,jdbcType=VARCHAR}, 
      #{customerInfo,jdbcType=VARCHAR}, #{contactInfo,jdbcType=VARCHAR}, #{familyPolicyInfo,jdbcType=VARCHAR}, 
      #{accountInfo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, sysdate(), 
      #{modifier,jdbcType=VARCHAR}, sysdate(), #{isDeleted,jdbcType=CHAR}, 
      #{payInfo,jdbcType=VARCHAR}, #{responseJson,jdbcType=VARCHAR}, #{batchClaimBillNo,jdbcType=VARCHAR}, 
      #{reportDate,jdbcType=TIMESTAMP}, #{picInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPushInfoDO">
    insert into cargo_claim_push_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="pushServiceName != null">
        push_service_name,
      </if>
      <if test="pushStatus != null">
        push_status,
      </if>
      <if test="didiCaseNo != null">
        didi_case_no,
      </if>
      <if test="inscompanyCaseNo != null">
        inscompany_case_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="caseInfo != null">
        case_info,
      </if>
      <if test="caseStatusInfo != null">
        case_status_info,
      </if>
      <if test="reporterInfo != null">
        reporter_info,
      </if>
      <if test="customerInfo != null">
        customer_info,
      </if>
      <if test="contactInfo != null">
        contact_info,
      </if>
      <if test="familyPolicyInfo != null">
        family_policy_info,
      </if>
      <if test="accountInfo != null">
        account_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="payInfo != null">
        pay_info,
      </if>
      <if test="responseJson != null">
        response_json,
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="picInfo != null">
        pic_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="pushServiceName != null">
        #{pushServiceName,jdbcType=VARCHAR},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=VARCHAR},
      </if>
      <if test="didiCaseNo != null">
        #{didiCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="inscompanyCaseNo != null">
        #{inscompanyCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="caseInfo != null">
        #{caseInfo,jdbcType=VARCHAR},
      </if>
      <if test="caseStatusInfo != null">
        #{caseStatusInfo,jdbcType=VARCHAR},
      </if>
      <if test="reporterInfo != null">
        #{reporterInfo,jdbcType=VARCHAR},
      </if>
      <if test="customerInfo != null">
        #{customerInfo,jdbcType=VARCHAR},
      </if>
      <if test="contactInfo != null">
        #{contactInfo,jdbcType=VARCHAR},
      </if>
      <if test="familyPolicyInfo != null">
        #{familyPolicyInfo,jdbcType=VARCHAR},
      </if>
      <if test="accountInfo != null">
        #{accountInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="payInfo != null">
        #{payInfo,jdbcType=VARCHAR},
      </if>
      <if test="responseJson != null">
        #{responseJson,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillNo != null">
        #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="picInfo != null">
        #{picInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPushInfoExample" resultType="java.lang.Long">
    select count(*) from cargo_claim_push_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_claim_push_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.pushServiceName != null">
        push_service_name = #{record.pushServiceName,jdbcType=VARCHAR},
      </if>
      <if test="record.pushStatus != null">
        push_status = #{record.pushStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.didiCaseNo != null">
        didi_case_no = #{record.didiCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.inscompanyCaseNo != null">
        inscompany_case_no = #{record.inscompanyCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.caseInfo != null">
        case_info = #{record.caseInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.caseStatusInfo != null">
        case_status_info = #{record.caseStatusInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.reporterInfo != null">
        reporter_info = #{record.reporterInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.customerInfo != null">
        customer_info = #{record.customerInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.contactInfo != null">
        contact_info = #{record.contactInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.familyPolicyInfo != null">
        family_policy_info = #{record.familyPolicyInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.accountInfo != null">
        account_info = #{record.accountInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.payInfo != null">
        pay_info = #{record.payInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.responseJson != null">
        response_json = #{record.responseJson,jdbcType=VARCHAR},
      </if>
      <if test="record.batchClaimBillNo != null">
        batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDate != null">
        report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.picInfo != null">
        pic_info = #{record.picInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update cargo_claim_push_info
    set id = #{record.id,jdbcType=BIGINT},
      push_service_name = #{record.pushServiceName,jdbcType=VARCHAR},
      push_status = #{record.pushStatus,jdbcType=VARCHAR},
      didi_case_no = #{record.didiCaseNo,jdbcType=VARCHAR},
      inscompany_case_no = #{record.inscompanyCaseNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      case_info = #{record.caseInfo,jdbcType=VARCHAR},
      case_status_info = #{record.caseStatusInfo,jdbcType=VARCHAR},
      reporter_info = #{record.reporterInfo,jdbcType=VARCHAR},
      customer_info = #{record.customerInfo,jdbcType=VARCHAR},
      contact_info = #{record.contactInfo,jdbcType=VARCHAR},
      family_policy_info = #{record.familyPolicyInfo,jdbcType=VARCHAR},
      account_info = #{record.accountInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      pay_info = #{record.payInfo,jdbcType=VARCHAR},
      response_json = #{record.responseJson,jdbcType=VARCHAR},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      pic_info = #{record.picInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_claim_push_info
    set id = #{record.id,jdbcType=BIGINT},
      push_service_name = #{record.pushServiceName,jdbcType=VARCHAR},
      push_status = #{record.pushStatus,jdbcType=VARCHAR},
      didi_case_no = #{record.didiCaseNo,jdbcType=VARCHAR},
      inscompany_case_no = #{record.inscompanyCaseNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      case_info = #{record.caseInfo,jdbcType=VARCHAR},
      case_status_info = #{record.caseStatusInfo,jdbcType=VARCHAR},
      reporter_info = #{record.reporterInfo,jdbcType=VARCHAR},
      customer_info = #{record.customerInfo,jdbcType=VARCHAR},
      contact_info = #{record.contactInfo,jdbcType=VARCHAR},
      family_policy_info = #{record.familyPolicyInfo,jdbcType=VARCHAR},
      account_info = #{record.accountInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      pay_info = #{record.payInfo,jdbcType=VARCHAR},
      response_json = #{record.responseJson,jdbcType=VARCHAR},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      report_date = #{record.reportDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPushInfoDO">
    update cargo_claim_push_info
    <set>
      <if test="pushServiceName != null">
        push_service_name = #{pushServiceName,jdbcType=VARCHAR},
      </if>
      <if test="pushStatus != null">
        push_status = #{pushStatus,jdbcType=VARCHAR},
      </if>
      <if test="didiCaseNo != null">
        didi_case_no = #{didiCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="inscompanyCaseNo != null">
        inscompany_case_no = #{inscompanyCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="caseInfo != null">
        case_info = #{caseInfo,jdbcType=VARCHAR},
      </if>
      <if test="caseStatusInfo != null">
        case_status_info = #{caseStatusInfo,jdbcType=VARCHAR},
      </if>
      <if test="reporterInfo != null">
        reporter_info = #{reporterInfo,jdbcType=VARCHAR},
      </if>
      <if test="customerInfo != null">
        customer_info = #{customerInfo,jdbcType=VARCHAR},
      </if>
      <if test="contactInfo != null">
        contact_info = #{contactInfo,jdbcType=VARCHAR},
      </if>
      <if test="familyPolicyInfo != null">
        family_policy_info = #{familyPolicyInfo,jdbcType=VARCHAR},
      </if>
      <if test="accountInfo != null">
        account_info = #{accountInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="payInfo != null">
        pay_info = #{payInfo,jdbcType=VARCHAR},
      </if>
      <if test="responseJson != null">
        response_json = #{responseJson,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="picInfo != null">
        pic_info = #{picInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.zhongan.lincoln.dal.domain.ClaimPushInfoDO">
    update cargo_claim_push_info
    set push_service_name = #{pushServiceName,jdbcType=VARCHAR},
      push_status = #{pushStatus,jdbcType=VARCHAR},
      didi_case_no = #{didiCaseNo,jdbcType=VARCHAR},
      inscompany_case_no = #{inscompanyCaseNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      case_info = #{caseInfo,jdbcType=VARCHAR},
      case_status_info = #{caseStatusInfo,jdbcType=VARCHAR},
      reporter_info = #{reporterInfo,jdbcType=VARCHAR},
      customer_info = #{customerInfo,jdbcType=VARCHAR},
      contact_info = #{contactInfo,jdbcType=VARCHAR},
      family_policy_info = #{familyPolicyInfo,jdbcType=VARCHAR},
      account_info = #{accountInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR},
      pay_info = #{payInfo,jdbcType=VARCHAR},
      response_json = #{responseJson,jdbcType=VARCHAR},
      batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      report_date = #{reportDate,jdbcType=TIMESTAMP},
      pic_info = #{picInfo,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimPushInfoDO">
    update cargo_claim_push_info
    set push_service_name = #{pushServiceName,jdbcType=VARCHAR},
      push_status = #{pushStatus,jdbcType=VARCHAR},
      didi_case_no = #{didiCaseNo,jdbcType=VARCHAR},
      inscompany_case_no = #{inscompanyCaseNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      case_info = #{caseInfo,jdbcType=VARCHAR},
      case_status_info = #{caseStatusInfo,jdbcType=VARCHAR},
      reporter_info = #{reporterInfo,jdbcType=VARCHAR},
      customer_info = #{customerInfo,jdbcType=VARCHAR},
      contact_info = #{contactInfo,jdbcType=VARCHAR},
      family_policy_info = #{familyPolicyInfo,jdbcType=VARCHAR},
      account_info = #{accountInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR},
      pay_info = #{payInfo,jdbcType=VARCHAR},
      response_json = #{responseJson,jdbcType=VARCHAR},
      batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      report_date = #{reportDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_claim_push_info
    (id, push_service_name, push_status, didi_case_no, inscompany_case_no, policy_no, 
      case_info, case_status_info, reporter_info, customer_info, contact_info, family_policy_info, 
      account_info, creator, gmt_created, modifier, gmt_modified, is_deleted, pay_info, 
      response_json, batch_claim_bill_no, report_date, pic_info)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.pushServiceName,jdbcType=VARCHAR}, #{item.pushStatus,jdbcType=VARCHAR}, 
        #{item.didiCaseNo,jdbcType=VARCHAR}, #{item.inscompanyCaseNo,jdbcType=VARCHAR}, 
        #{item.policyNo,jdbcType=VARCHAR}, #{item.caseInfo,jdbcType=VARCHAR}, #{item.caseStatusInfo,jdbcType=VARCHAR}, 
        #{item.reporterInfo,jdbcType=VARCHAR}, #{item.customerInfo,jdbcType=VARCHAR}, #{item.contactInfo,jdbcType=VARCHAR}, 
        #{item.familyPolicyInfo,jdbcType=VARCHAR}, #{item.accountInfo,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}, #{item.payInfo,jdbcType=VARCHAR}, 
        #{item.responseJson,jdbcType=VARCHAR}, #{item.batchClaimBillNo,jdbcType=VARCHAR}, 
        #{item.reportDate,jdbcType=TIMESTAMP}, #{item.picInfo,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_claim_push_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'push_service_name'.toString() == column.value">
          #{item.pushServiceName,jdbcType=VARCHAR}
        </if>
        <if test="'push_status'.toString() == column.value">
          #{item.pushStatus,jdbcType=VARCHAR}
        </if>
        <if test="'didi_case_no'.toString() == column.value">
          #{item.didiCaseNo,jdbcType=VARCHAR}
        </if>
        <if test="'inscompany_case_no'.toString() == column.value">
          #{item.inscompanyCaseNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'case_info'.toString() == column.value">
          #{item.caseInfo,jdbcType=VARCHAR}
        </if>
        <if test="'case_status_info'.toString() == column.value">
          #{item.caseStatusInfo,jdbcType=VARCHAR}
        </if>
        <if test="'reporter_info'.toString() == column.value">
          #{item.reporterInfo,jdbcType=VARCHAR}
        </if>
        <if test="'customer_info'.toString() == column.value">
          #{item.customerInfo,jdbcType=VARCHAR}
        </if>
        <if test="'contact_info'.toString() == column.value">
          #{item.contactInfo,jdbcType=VARCHAR}
        </if>
        <if test="'family_policy_info'.toString() == column.value">
          #{item.familyPolicyInfo,jdbcType=VARCHAR}
        </if>
        <if test="'account_info'.toString() == column.value">
          #{item.accountInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'pay_info'.toString() == column.value">
          #{item.payInfo,jdbcType=VARCHAR}
        </if>
        <if test="'response_json'.toString() == column.value">
          #{item.responseJson,jdbcType=VARCHAR}
        </if>
        <if test="'batch_claim_bill_no'.toString() == column.value">
          #{item.batchClaimBillNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_date'.toString() == column.value">
          #{item.reportDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'pic_info'.toString() == column.value">
          #{item.picInfo,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>