<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.LossCoreRelationMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.LossCoreRelationDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="traffic_insurance_type" jdbcType="VARCHAR" property="trafficInsuranceType" />
    <result column="front_loss_code" jdbcType="VARCHAR" property="frontLossCode" />
    <result column="front_loss_name" jdbcType="VARCHAR" property="frontLossName" />
    <result column="core_loss_code" jdbcType="VARCHAR" property="coreLossCode" />
    <result column="core_loss_name" jdbcType="VARCHAR" property="coreLossName" />
    <result column="order_by_param" jdbcType="VARCHAR" property="orderByParam" />
    <result column="un_support_self_claim" jdbcType="CHAR" property="unSupportSelfClaim" />
    <result column="campaign_package_id" jdbcType="VARCHAR" property="campaignPackageId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, traffic_insurance_type, front_loss_code, front_loss_name, core_loss_code, core_loss_name, 
    order_by_param, un_support_self_claim, campaign_package_id, creator, modifier, gmt_created, 
    gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.LossCoreRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from loss_core_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from loss_core_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.LossCoreRelationDO">
    insert into loss_core_relation (id, traffic_insurance_type, front_loss_code, 
      front_loss_name, core_loss_code, core_loss_name, 
      order_by_param, un_support_self_claim, campaign_package_id, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{trafficInsuranceType,jdbcType=VARCHAR}, #{frontLossCode,jdbcType=VARCHAR}, 
      #{frontLossName,jdbcType=VARCHAR}, #{coreLossCode,jdbcType=VARCHAR}, #{coreLossName,jdbcType=VARCHAR}, 
      #{orderByParam,jdbcType=VARCHAR}, #{unSupportSelfClaim,jdbcType=CHAR}, #{campaignPackageId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.LossCoreRelationDO">
    insert into loss_core_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="trafficInsuranceType != null">
        traffic_insurance_type,
      </if>
      <if test="frontLossCode != null">
        front_loss_code,
      </if>
      <if test="frontLossName != null">
        front_loss_name,
      </if>
      <if test="coreLossCode != null">
        core_loss_code,
      </if>
      <if test="coreLossName != null">
        core_loss_name,
      </if>
      <if test="orderByParam != null">
        order_by_param,
      </if>
      <if test="unSupportSelfClaim != null">
        un_support_self_claim,
      </if>
      <if test="campaignPackageId != null">
        campaign_package_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="trafficInsuranceType != null">
        #{trafficInsuranceType,jdbcType=VARCHAR},
      </if>
      <if test="frontLossCode != null">
        #{frontLossCode,jdbcType=VARCHAR},
      </if>
      <if test="frontLossName != null">
        #{frontLossName,jdbcType=VARCHAR},
      </if>
      <if test="coreLossCode != null">
        #{coreLossCode,jdbcType=VARCHAR},
      </if>
      <if test="coreLossName != null">
        #{coreLossName,jdbcType=VARCHAR},
      </if>
      <if test="orderByParam != null">
        #{orderByParam,jdbcType=VARCHAR},
      </if>
      <if test="unSupportSelfClaim != null">
        #{unSupportSelfClaim,jdbcType=CHAR},
      </if>
      <if test="campaignPackageId != null">
        #{campaignPackageId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.LossCoreRelationExample" resultType="java.lang.Long">
    select count(*) from loss_core_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update loss_core_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.trafficInsuranceType != null">
        traffic_insurance_type = #{record.trafficInsuranceType,jdbcType=VARCHAR},
      </if>
      <if test="record.frontLossCode != null">
        front_loss_code = #{record.frontLossCode,jdbcType=VARCHAR},
      </if>
      <if test="record.frontLossName != null">
        front_loss_name = #{record.frontLossName,jdbcType=VARCHAR},
      </if>
      <if test="record.coreLossCode != null">
        core_loss_code = #{record.coreLossCode,jdbcType=VARCHAR},
      </if>
      <if test="record.coreLossName != null">
        core_loss_name = #{record.coreLossName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderByParam != null">
        order_by_param = #{record.orderByParam,jdbcType=VARCHAR},
      </if>
      <if test="record.unSupportSelfClaim != null">
        un_support_self_claim = #{record.unSupportSelfClaim,jdbcType=CHAR},
      </if>
      <if test="record.campaignPackageId != null">
        campaign_package_id = #{record.campaignPackageId,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update loss_core_relation
    set id = #{record.id,jdbcType=BIGINT},
      traffic_insurance_type = #{record.trafficInsuranceType,jdbcType=VARCHAR},
      front_loss_code = #{record.frontLossCode,jdbcType=VARCHAR},
      front_loss_name = #{record.frontLossName,jdbcType=VARCHAR},
      core_loss_code = #{record.coreLossCode,jdbcType=VARCHAR},
      core_loss_name = #{record.coreLossName,jdbcType=VARCHAR},
      order_by_param = #{record.orderByParam,jdbcType=VARCHAR},
      un_support_self_claim = #{record.unSupportSelfClaim,jdbcType=CHAR},
      campaign_package_id = #{record.campaignPackageId,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.LossCoreRelationDO">
    update loss_core_relation
    <set>
      <if test="trafficInsuranceType != null">
        traffic_insurance_type = #{trafficInsuranceType,jdbcType=VARCHAR},
      </if>
      <if test="frontLossCode != null">
        front_loss_code = #{frontLossCode,jdbcType=VARCHAR},
      </if>
      <if test="frontLossName != null">
        front_loss_name = #{frontLossName,jdbcType=VARCHAR},
      </if>
      <if test="coreLossCode != null">
        core_loss_code = #{coreLossCode,jdbcType=VARCHAR},
      </if>
      <if test="coreLossName != null">
        core_loss_name = #{coreLossName,jdbcType=VARCHAR},
      </if>
      <if test="orderByParam != null">
        order_by_param = #{orderByParam,jdbcType=VARCHAR},
      </if>
      <if test="unSupportSelfClaim != null">
        un_support_self_claim = #{unSupportSelfClaim,jdbcType=CHAR},
      </if>
      <if test="campaignPackageId != null">
        campaign_package_id = #{campaignPackageId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.LossCoreRelationDO">
    update loss_core_relation
    set traffic_insurance_type = #{trafficInsuranceType,jdbcType=VARCHAR},
      front_loss_code = #{frontLossCode,jdbcType=VARCHAR},
      front_loss_name = #{frontLossName,jdbcType=VARCHAR},
      core_loss_code = #{coreLossCode,jdbcType=VARCHAR},
      core_loss_name = #{coreLossName,jdbcType=VARCHAR},
      order_by_param = #{orderByParam,jdbcType=VARCHAR},
      un_support_self_claim = #{unSupportSelfClaim,jdbcType=CHAR},
      campaign_package_id = #{campaignPackageId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into loss_core_relation
    (id, traffic_insurance_type, front_loss_code, front_loss_name, core_loss_code, core_loss_name, 
      order_by_param, un_support_self_claim, campaign_package_id, creator, modifier, 
      gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.trafficInsuranceType,jdbcType=VARCHAR}, #{item.frontLossCode,jdbcType=VARCHAR}, 
        #{item.frontLossName,jdbcType=VARCHAR}, #{item.coreLossCode,jdbcType=VARCHAR}, 
        #{item.coreLossName,jdbcType=VARCHAR}, #{item.orderByParam,jdbcType=VARCHAR}, #{item.unSupportSelfClaim,jdbcType=CHAR}, 
        #{item.campaignPackageId,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into loss_core_relation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'traffic_insurance_type'.toString() == column.value">
          #{item.trafficInsuranceType,jdbcType=VARCHAR}
        </if>
        <if test="'front_loss_code'.toString() == column.value">
          #{item.frontLossCode,jdbcType=VARCHAR}
        </if>
        <if test="'front_loss_name'.toString() == column.value">
          #{item.frontLossName,jdbcType=VARCHAR}
        </if>
        <if test="'core_loss_code'.toString() == column.value">
          #{item.coreLossCode,jdbcType=VARCHAR}
        </if>
        <if test="'core_loss_name'.toString() == column.value">
          #{item.coreLossName,jdbcType=VARCHAR}
        </if>
        <if test="'order_by_param'.toString() == column.value">
          #{item.orderByParam,jdbcType=VARCHAR}
        </if>
        <if test="'un_support_self_claim'.toString() == column.value">
          #{item.unSupportSelfClaim,jdbcType=CHAR}
        </if>
        <if test="'campaign_package_id'.toString() == column.value">
          #{item.campaignPackageId,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>