<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.TpaClaimCostItemMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.TpaClaimCostItemDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cost_no" jdbcType="VARCHAR" property="costNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="oa_no" jdbcType="VARCHAR" property="oaNo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_type" jdbcType="CHAR" property="companyType" />
    <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="submit_type" jdbcType="VARCHAR" property="submitType" />
    <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="paid_time" jdbcType="TIMESTAMP" property="paidTime" />
    <result column="claim_type" jdbcType="VARCHAR" property="claimType" />
    <result column="claim_price" jdbcType="DECIMAL" property="claimPrice" />
    <result column="outbound_price" jdbcType="DECIMAL" property="outboundPrice" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="tag" jdbcType="TINYINT" property="tag" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, cost_no, report_no, policy_no, batch_no, invoice_no, oa_no, status, company_id, 
    company_type, employee_id, submit_time, submit_type, effective_time, pay_time, paid_time, 
    claim_type, claim_price, outbound_price, total_amount, tag, extra_info, gmt_created, 
    gmt_modified, creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimCostItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpa_claim_cost_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tpa_claim_cost_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimCostItemDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpa_claim_cost_item (cost_no, report_no, policy_no, 
      batch_no, invoice_no, oa_no, 
      status, company_id, company_type, 
      employee_id, submit_time, submit_type, 
      effective_time, pay_time, paid_time, 
      claim_type, claim_price, outbound_price, 
      total_amount, tag, extra_info, 
      gmt_created, gmt_modified, creator, 
      modifier, is_deleted)
    values (#{costNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, 
      #{batchNo,jdbcType=VARCHAR}, #{invoiceNo,jdbcType=VARCHAR}, #{oaNo,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{companyId,jdbcType=BIGINT}, #{companyType,jdbcType=CHAR}, 
      #{employeeId,jdbcType=BIGINT}, #{submitTime,jdbcType=TIMESTAMP}, #{submitType,jdbcType=VARCHAR}, 
      #{effectiveTime,jdbcType=TIMESTAMP}, #{payTime,jdbcType=TIMESTAMP}, #{paidTime,jdbcType=TIMESTAMP}, 
      #{claimType,jdbcType=VARCHAR}, #{claimPrice,jdbcType=DECIMAL}, #{outboundPrice,jdbcType=DECIMAL}, 
      #{totalAmount,jdbcType=DECIMAL}, #{tag,jdbcType=TINYINT}, #{extraInfo,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimCostItemDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpa_claim_cost_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="costNo != null">
        cost_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="invoiceNo != null">
        invoice_no,
      </if>
      <if test="oaNo != null">
        oa_no,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyType != null">
        company_type,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="submitType != null">
        submit_type,
      </if>
      <if test="effectiveTime != null">
        effective_time,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="paidTime != null">
        paid_time,
      </if>
      <if test="claimType != null">
        claim_type,
      </if>
      <if test="claimPrice != null">
        claim_price,
      </if>
      <if test="outboundPrice != null">
        outbound_price,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="tag != null">
        tag,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="costNo != null">
        #{costNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="oaNo != null">
        #{oaNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyType != null">
        #{companyType,jdbcType=CHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=BIGINT},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="submitType != null">
        #{submitType,jdbcType=VARCHAR},
      </if>
      <if test="effectiveTime != null">
        #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paidTime != null">
        #{paidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="claimType != null">
        #{claimType,jdbcType=VARCHAR},
      </if>
      <if test="claimPrice != null">
        #{claimPrice,jdbcType=DECIMAL},
      </if>
      <if test="outboundPrice != null">
        #{outboundPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=TINYINT},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimCostItemExample" resultType="java.lang.Long">
    select count(*) from tpa_claim_cost_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tpa_claim_cost_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.costNo != null">
        cost_no = #{record.costNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceNo != null">
        invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.oaNo != null">
        oa_no = #{record.oaNo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.companyType != null">
        company_type = #{record.companyType,jdbcType=CHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=BIGINT},
      </if>
      <if test="record.submitTime != null">
        submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.submitType != null">
        submit_type = #{record.submitType,jdbcType=VARCHAR},
      </if>
      <if test="record.effectiveTime != null">
        effective_time = #{record.effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.paidTime != null">
        paid_time = #{record.paidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.claimType != null">
        claim_type = #{record.claimType,jdbcType=VARCHAR},
      </if>
      <if test="record.claimPrice != null">
        claim_price = #{record.claimPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.outboundPrice != null">
        outbound_price = #{record.outboundPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.totalAmount != null">
        total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.tag != null">
        tag = #{record.tag,jdbcType=TINYINT},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tpa_claim_cost_item
    set id = #{record.id,jdbcType=BIGINT},
      cost_no = #{record.costNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      oa_no = #{record.oaNo,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      company_type = #{record.companyType,jdbcType=CHAR},
      employee_id = #{record.employeeId,jdbcType=BIGINT},
      submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      submit_type = #{record.submitType,jdbcType=VARCHAR},
      effective_time = #{record.effectiveTime,jdbcType=TIMESTAMP},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      paid_time = #{record.paidTime,jdbcType=TIMESTAMP},
      claim_type = #{record.claimType,jdbcType=VARCHAR},
      claim_price = #{record.claimPrice,jdbcType=DECIMAL},
      outbound_price = #{record.outboundPrice,jdbcType=DECIMAL},
      total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      tag = #{record.tag,jdbcType=TINYINT},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimCostItemDO">
    update tpa_claim_cost_item
    <set>
      <if test="costNo != null">
        cost_no = #{costNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="oaNo != null">
        oa_no = #{oaNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="companyType != null">
        company_type = #{companyType,jdbcType=CHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=BIGINT},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="submitType != null">
        submit_type = #{submitType,jdbcType=VARCHAR},
      </if>
      <if test="effectiveTime != null">
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paidTime != null">
        paid_time = #{paidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="claimType != null">
        claim_type = #{claimType,jdbcType=VARCHAR},
      </if>
      <if test="claimPrice != null">
        claim_price = #{claimPrice,jdbcType=DECIMAL},
      </if>
      <if test="outboundPrice != null">
        outbound_price = #{outboundPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="tag != null">
        tag = #{tag,jdbcType=TINYINT},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimCostItemDO">
    update tpa_claim_cost_item
    set cost_no = #{costNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      oa_no = #{oaNo,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=BIGINT},
      company_type = #{companyType,jdbcType=CHAR},
      employee_id = #{employeeId,jdbcType=BIGINT},
      submit_time = #{submitTime,jdbcType=TIMESTAMP},
      submit_type = #{submitType,jdbcType=VARCHAR},
      effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      paid_time = #{paidTime,jdbcType=TIMESTAMP},
      claim_type = #{claimType,jdbcType=VARCHAR},
      claim_price = #{claimPrice,jdbcType=DECIMAL},
      outbound_price = #{outboundPrice,jdbcType=DECIMAL},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      tag = #{tag,jdbcType=TINYINT},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into tpa_claim_cost_item
    (cost_no, report_no, policy_no, batch_no, invoice_no, oa_no, status, company_id, 
      company_type, employee_id, submit_time, submit_type, effective_time, pay_time, 
      paid_time, claim_type, claim_price, outbound_price, total_amount, tag, extra_info, 
      gmt_created, gmt_modified, creator, modifier, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.costNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.batchNo,jdbcType=VARCHAR}, #{item.invoiceNo,jdbcType=VARCHAR}, #{item.oaNo,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=TINYINT}, #{item.companyId,jdbcType=BIGINT}, #{item.companyType,jdbcType=CHAR}, 
        #{item.employeeId,jdbcType=BIGINT}, #{item.submitTime,jdbcType=TIMESTAMP}, #{item.submitType,jdbcType=VARCHAR}, 
        #{item.effectiveTime,jdbcType=TIMESTAMP}, #{item.payTime,jdbcType=TIMESTAMP}, #{item.paidTime,jdbcType=TIMESTAMP}, 
        #{item.claimType,jdbcType=VARCHAR}, #{item.claimPrice,jdbcType=DECIMAL}, #{item.outboundPrice,jdbcType=DECIMAL}, 
        #{item.totalAmount,jdbcType=DECIMAL}, #{item.tag,jdbcType=TINYINT}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into tpa_claim_cost_item (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'cost_no'.toString() == column.value">
          #{item.costNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'batch_no'.toString() == column.value">
          #{item.batchNo,jdbcType=VARCHAR}
        </if>
        <if test="'invoice_no'.toString() == column.value">
          #{item.invoiceNo,jdbcType=VARCHAR}
        </if>
        <if test="'oa_no'.toString() == column.value">
          #{item.oaNo,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'company_id'.toString() == column.value">
          #{item.companyId,jdbcType=BIGINT}
        </if>
        <if test="'company_type'.toString() == column.value">
          #{item.companyType,jdbcType=CHAR}
        </if>
        <if test="'employee_id'.toString() == column.value">
          #{item.employeeId,jdbcType=BIGINT}
        </if>
        <if test="'submit_time'.toString() == column.value">
          #{item.submitTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'submit_type'.toString() == column.value">
          #{item.submitType,jdbcType=VARCHAR}
        </if>
        <if test="'effective_time'.toString() == column.value">
          #{item.effectiveTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'pay_time'.toString() == column.value">
          #{item.payTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'paid_time'.toString() == column.value">
          #{item.paidTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'claim_type'.toString() == column.value">
          #{item.claimType,jdbcType=VARCHAR}
        </if>
        <if test="'claim_price'.toString() == column.value">
          #{item.claimPrice,jdbcType=DECIMAL}
        </if>
        <if test="'outbound_price'.toString() == column.value">
          #{item.outboundPrice,jdbcType=DECIMAL}
        </if>
        <if test="'total_amount'.toString() == column.value">
          #{item.totalAmount,jdbcType=DECIMAL}
        </if>
        <if test="'tag'.toString() == column.value">
          #{item.tag,jdbcType=TINYINT}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>