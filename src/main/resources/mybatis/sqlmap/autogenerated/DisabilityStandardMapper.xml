<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.DisabilityStandardMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.DisabilityStandardDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="disability_level" jdbcType="VARCHAR" property="disabilityLevel" />
    <result column="tree_level" jdbcType="VARCHAR" property="treeLevel" />
    <result column="parentCode" jdbcType="VARCHAR" property="parentcode" />
    <result column="appraisal_standard" jdbcType="VARCHAR" property="appraisalStandard" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, code, disability_level, tree_level, parentCode, appraisal_standard, creator, 
    gmt_created, modifier, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.DisabilityStandardExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_disability_standard_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_disability_standard_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.DisabilityStandardDO">
    insert into claim_disability_standard_info (id, name, code, 
      disability_level, tree_level, parentCode, 
      appraisal_standard, creator, gmt_created, 
      modifier, gmt_modified, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{disabilityLevel,jdbcType=VARCHAR}, #{treeLevel,jdbcType=VARCHAR}, #{parentcode,jdbcType=VARCHAR}, 
      #{appraisalStandard,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, sysdate(), 
      #{modifier,jdbcType=VARCHAR}, sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.DisabilityStandardDO">
    insert into claim_disability_standard_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="disabilityLevel != null">
        disability_level,
      </if>
      <if test="treeLevel != null">
        tree_level,
      </if>
      <if test="parentcode != null">
        parentCode,
      </if>
      <if test="appraisalStandard != null">
        appraisal_standard,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="disabilityLevel != null">
        #{disabilityLevel,jdbcType=VARCHAR},
      </if>
      <if test="treeLevel != null">
        #{treeLevel,jdbcType=VARCHAR},
      </if>
      <if test="parentcode != null">
        #{parentcode,jdbcType=VARCHAR},
      </if>
      <if test="appraisalStandard != null">
        #{appraisalStandard,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.DisabilityStandardExample" resultType="java.lang.Long">
    select count(*) from claim_disability_standard_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_disability_standard_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.disabilityLevel != null">
        disability_level = #{record.disabilityLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.treeLevel != null">
        tree_level = #{record.treeLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.parentcode != null">
        parentCode = #{record.parentcode,jdbcType=VARCHAR},
      </if>
      <if test="record.appraisalStandard != null">
        appraisal_standard = #{record.appraisalStandard,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_disability_standard_info
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      disability_level = #{record.disabilityLevel,jdbcType=VARCHAR},
      tree_level = #{record.treeLevel,jdbcType=VARCHAR},
      parentCode = #{record.parentcode,jdbcType=VARCHAR},
      appraisal_standard = #{record.appraisalStandard,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.DisabilityStandardDO">
    update claim_disability_standard_info
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="disabilityLevel != null">
        disability_level = #{disabilityLevel,jdbcType=VARCHAR},
      </if>
      <if test="treeLevel != null">
        tree_level = #{treeLevel,jdbcType=VARCHAR},
      </if>
      <if test="parentcode != null">
        parentCode = #{parentcode,jdbcType=VARCHAR},
      </if>
      <if test="appraisalStandard != null">
        appraisal_standard = #{appraisalStandard,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.DisabilityStandardDO">
    update claim_disability_standard_info
    set name = #{name,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      disability_level = #{disabilityLevel,jdbcType=VARCHAR},
      tree_level = #{treeLevel,jdbcType=VARCHAR},
      parentCode = #{parentcode,jdbcType=VARCHAR},
      appraisal_standard = #{appraisalStandard,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_disability_standard_info
    (id, name, code, disability_level, tree_level, parentCode, appraisal_standard, creator, 
      gmt_created, modifier, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.name,jdbcType=VARCHAR}, #{item.code,jdbcType=VARCHAR}, 
        #{item.disabilityLevel,jdbcType=VARCHAR}, #{item.treeLevel,jdbcType=VARCHAR}, #{item.parentcode,jdbcType=VARCHAR}, 
        #{item.appraisalStandard,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_disability_standard_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'code'.toString() == column.value">
          #{item.code,jdbcType=VARCHAR}
        </if>
        <if test="'disability_level'.toString() == column.value">
          #{item.disabilityLevel,jdbcType=VARCHAR}
        </if>
        <if test="'tree_level'.toString() == column.value">
          #{item.treeLevel,jdbcType=VARCHAR}
        </if>
        <if test="'parentCode'.toString() == column.value">
          #{item.parentcode,jdbcType=VARCHAR}
        </if>
        <if test="'appraisal_standard'.toString() == column.value">
          #{item.appraisalStandard,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>