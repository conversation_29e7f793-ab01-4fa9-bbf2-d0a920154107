<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimPreReportMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimPreReportDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pre_report_no" jdbcType="VARCHAR" property="preReportNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="campaign_def_id" jdbcType="BIGINT" property="campaignDefId" />
    <result column="campaign_full_name" jdbcType="VARCHAR" property="campaignFullName" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="insurant_name" jdbcType="VARCHAR" property="insurantName" />
    <result column="insurant_cert_no" jdbcType="VARCHAR" property="insurantCertNo" />
    <result column="insurant_cert_type" jdbcType="VARCHAR" property="insurantCertType" />
    <result column="report_date" jdbcType="TIMESTAMP" property="reportDate" />
    <result column="accident_date" jdbcType="TIMESTAMP" property="accidentDate" />
    <result column="report_amount" jdbcType="VARCHAR" property="reportAmount" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, pre_report_no, report_no, policy_no, campaign_def_id, campaign_full_name, package_def_id, 
    product_name, channel_id, channel_name, insurant_name, insurant_cert_no, insurant_cert_type, 
    report_date, accident_date, report_amount, extra_info, remark, is_deleted, gmt_created, 
    gmt_modified, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPreReportExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_claim_pre_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_claim_pre_report
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimPreReportDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cargo_claim_pre_report (pre_report_no, report_no, policy_no, 
      campaign_def_id, campaign_full_name, package_def_id, 
      product_name, channel_id, channel_name, 
      insurant_name, insurant_cert_no, insurant_cert_type, 
      report_date, accident_date, report_amount, 
      extra_info, remark, is_deleted, 
      gmt_created, gmt_modified, creator, 
      modifier)
    values (#{preReportNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, 
      #{campaignDefId,jdbcType=BIGINT}, #{campaignFullName,jdbcType=VARCHAR}, #{packageDefId,jdbcType=BIGINT}, 
      #{productName,jdbcType=VARCHAR}, #{channelId,jdbcType=BIGINT}, #{channelName,jdbcType=VARCHAR}, 
      #{insurantName,jdbcType=VARCHAR}, #{insurantCertNo,jdbcType=VARCHAR}, #{insurantCertType,jdbcType=VARCHAR}, 
      #{reportDate,jdbcType=TIMESTAMP}, #{accidentDate,jdbcType=TIMESTAMP}, #{reportAmount,jdbcType=VARCHAR}, 
      #{extraInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPreReportDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cargo_claim_pre_report
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="preReportNo != null">
        pre_report_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="campaignFullName != null">
        campaign_full_name,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="channelName != null">
        channel_name,
      </if>
      <if test="insurantName != null">
        insurant_name,
      </if>
      <if test="insurantCertNo != null">
        insurant_cert_no,
      </if>
      <if test="insurantCertType != null">
        insurant_cert_type,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="accidentDate != null">
        accident_date,
      </if>
      <if test="reportAmount != null">
        report_amount,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="preReportNo != null">
        #{preReportNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="campaignFullName != null">
        #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="channelName != null">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="insurantName != null">
        #{insurantName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertNo != null">
        #{insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertType != null">
        #{insurantCertType,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="accidentDate != null">
        #{accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportAmount != null">
        #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPreReportExample" resultType="java.lang.Long">
    select count(*) from cargo_claim_pre_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_claim_pre_report
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.preReportNo != null">
        pre_report_no = #{record.preReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="record.campaignFullName != null">
        campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.channelName != null">
        channel_name = #{record.channelName,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantName != null">
        insurant_name = #{record.insurantName,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCertNo != null">
        insurant_cert_no = #{record.insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCertType != null">
        insurant_cert_type = #{record.insurantCertType,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDate != null">
        report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accidentDate != null">
        accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportAmount != null">
        report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_claim_pre_report
    set id = #{record.id,jdbcType=BIGINT},
      pre_report_no = #{record.preReportNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      product_name = #{record.productName,jdbcType=VARCHAR},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      channel_name = #{record.channelName,jdbcType=VARCHAR},
      insurant_name = #{record.insurantName,jdbcType=VARCHAR},
      insurant_cert_no = #{record.insurantCertNo,jdbcType=VARCHAR},
      insurant_cert_type = #{record.insurantCertType,jdbcType=VARCHAR},
      report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPreReportDO">
    update cargo_claim_pre_report
    <set>
      <if test="preReportNo != null">
        pre_report_no = #{preReportNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="campaignFullName != null">
        campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="channelName != null">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="insurantName != null">
        insurant_name = #{insurantName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertNo != null">
        insurant_cert_no = #{insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertType != null">
        insurant_cert_type = #{insurantCertType,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="accidentDate != null">
        accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reportAmount != null">
        report_amount = #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimPreReportDO">
    update cargo_claim_pre_report
    set pre_report_no = #{preReportNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      package_def_id = #{packageDefId,jdbcType=BIGINT},
      product_name = #{productName,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=BIGINT},
      channel_name = #{channelName,jdbcType=VARCHAR},
      insurant_name = #{insurantName,jdbcType=VARCHAR},
      insurant_cert_no = #{insurantCertNo,jdbcType=VARCHAR},
      insurant_cert_type = #{insurantCertType,jdbcType=VARCHAR},
      report_date = #{reportDate,jdbcType=TIMESTAMP},
      accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      report_amount = #{reportAmount,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into cargo_claim_pre_report
    (pre_report_no, report_no, policy_no, campaign_def_id, campaign_full_name, package_def_id, 
      product_name, channel_id, channel_name, insurant_name, insurant_cert_no, insurant_cert_type, 
      report_date, accident_date, report_amount, extra_info, remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.preReportNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.campaignDefId,jdbcType=BIGINT}, #{item.campaignFullName,jdbcType=VARCHAR}, 
        #{item.packageDefId,jdbcType=BIGINT}, #{item.productName,jdbcType=VARCHAR}, #{item.channelId,jdbcType=BIGINT}, 
        #{item.channelName,jdbcType=VARCHAR}, #{item.insurantName,jdbcType=VARCHAR}, #{item.insurantCertNo,jdbcType=VARCHAR}, 
        #{item.insurantCertType,jdbcType=VARCHAR}, #{item.reportDate,jdbcType=TIMESTAMP}, 
        #{item.accidentDate,jdbcType=TIMESTAMP}, #{item.reportAmount,jdbcType=VARCHAR}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into cargo_claim_pre_report (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'pre_report_no'.toString() == column.value">
          #{item.preReportNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=BIGINT}
        </if>
        <if test="'campaign_full_name'.toString() == column.value">
          #{item.campaignFullName,jdbcType=VARCHAR}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'channel_name'.toString() == column.value">
          #{item.channelName,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_name'.toString() == column.value">
          #{item.insurantName,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_cert_no'.toString() == column.value">
          #{item.insurantCertNo,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_cert_type'.toString() == column.value">
          #{item.insurantCertType,jdbcType=VARCHAR}
        </if>
        <if test="'report_date'.toString() == column.value">
          #{item.reportDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'accident_date'.toString() == column.value">
          #{item.accidentDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'report_amount'.toString() == column.value">
          #{item.reportAmount,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>