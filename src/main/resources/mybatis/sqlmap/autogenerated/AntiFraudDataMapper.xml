<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AntiFraudDataMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AntiFraudDataDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="insurance_company" jdbcType="VARCHAR" property="insuranceCompany" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="policy_validity" jdbcType="VARCHAR" property="policyValidity" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_holder_name" jdbcType="VARCHAR" property="policyHolderName" />
    <result column="policy_holder_certificate_no" jdbcType="VARCHAR" property="policyHolderCertificateNo" />
    <result column="policy_holder_phone" jdbcType="VARCHAR" property="policyHolderPhone" />
    <result column="purchase_time" jdbcType="VARCHAR" property="purchaseTime" />
    <result column="premium" jdbcType="VARCHAR" property="premium" />
    <result column="insured_name" jdbcType="VARCHAR" property="insuredName" />
    <result column="insured_certificate_no" jdbcType="VARCHAR" property="insuredCertificateNo" />
    <result column="insured_phone" jdbcType="VARCHAR" property="insuredPhone" />
    <result column="flight_date" jdbcType="VARCHAR" property="flightDate" />
    <result column="flight_no" jdbcType="VARCHAR" property="flightNo" />
    <result column="flight_dep_city" jdbcType="VARCHAR" property="flightDepCity" />
    <result column="flight_arr_city" jdbcType="VARCHAR" property="flightArrCity" />
    <result column="ticket" jdbcType="VARCHAR" property="ticket" />
    <result column="report_date" jdbcType="VARCHAR" property="reportDate" />
    <result column="reportor_name" jdbcType="VARCHAR" property="reportorName" />
    <result column="reportor_certificate_no" jdbcType="VARCHAR" property="reportorCertificateNo" />
    <result column="reportor_phone" jdbcType="VARCHAR" property="reportorPhone" />
    <result column="payeer_account" jdbcType="VARCHAR" property="payeerAccount" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="payeer_account_bank" jdbcType="VARCHAR" property="payeerAccountBank" />
    <result column="pay_time" jdbcType="VARCHAR" property="payTime" />
    <result column="pay_ammount" jdbcType="VARCHAR" property="payAmmount" />
    <result column="accident_reason" jdbcType="VARCHAR" property="accidentReason" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, insurance_company, product_name, policy_validity, policy_no, report_no, policy_holder_name, 
    policy_holder_certificate_no, policy_holder_phone, purchase_time, premium, insured_name, 
    insured_certificate_no, insured_phone, flight_date, flight_no, flight_dep_city, flight_arr_city, 
    ticket, report_date, reportor_name, reportor_certificate_no, reportor_phone, payeer_account, 
    trade_no, payeer_account_bank, pay_time, pay_ammount, accident_reason, creator, gmt_created, 
    modifier, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AntiFraudDataExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from anti_fraud_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from anti_fraud_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AntiFraudDataDO">
    insert into anti_fraud_data (id, insurance_company, product_name, 
      policy_validity, policy_no, report_no, 
      policy_holder_name, policy_holder_certificate_no, 
      policy_holder_phone, purchase_time, premium, 
      insured_name, insured_certificate_no, insured_phone, 
      flight_date, flight_no, flight_dep_city, 
      flight_arr_city, ticket, report_date, 
      reportor_name, reportor_certificate_no, reportor_phone, 
      payeer_account, trade_no, payeer_account_bank, 
      pay_time, pay_ammount, accident_reason, 
      creator, gmt_created, modifier, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{insuranceCompany,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, 
      #{policyValidity,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{policyHolderName,jdbcType=VARCHAR}, #{policyHolderCertificateNo,jdbcType=VARCHAR}, 
      #{policyHolderPhone,jdbcType=VARCHAR}, #{purchaseTime,jdbcType=VARCHAR}, #{premium,jdbcType=VARCHAR}, 
      #{insuredName,jdbcType=VARCHAR}, #{insuredCertificateNo,jdbcType=VARCHAR}, #{insuredPhone,jdbcType=VARCHAR}, 
      #{flightDate,jdbcType=VARCHAR}, #{flightNo,jdbcType=VARCHAR}, #{flightDepCity,jdbcType=VARCHAR}, 
      #{flightArrCity,jdbcType=VARCHAR}, #{ticket,jdbcType=VARCHAR}, #{reportDate,jdbcType=VARCHAR}, 
      #{reportorName,jdbcType=VARCHAR}, #{reportorCertificateNo,jdbcType=VARCHAR}, #{reportorPhone,jdbcType=VARCHAR}, 
      #{payeerAccount,jdbcType=VARCHAR}, #{tradeNo,jdbcType=VARCHAR}, #{payeerAccountBank,jdbcType=VARCHAR}, 
      #{payTime,jdbcType=VARCHAR}, #{payAmmount,jdbcType=VARCHAR}, #{accidentReason,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, sysdate(), #{modifier,jdbcType=VARCHAR}, 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AntiFraudDataDO">
    insert into anti_fraud_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="insuranceCompany != null">
        insurance_company,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="policyValidity != null">
        policy_validity,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyHolderName != null">
        policy_holder_name,
      </if>
      <if test="policyHolderCertificateNo != null">
        policy_holder_certificate_no,
      </if>
      <if test="policyHolderPhone != null">
        policy_holder_phone,
      </if>
      <if test="purchaseTime != null">
        purchase_time,
      </if>
      <if test="premium != null">
        premium,
      </if>
      <if test="insuredName != null">
        insured_name,
      </if>
      <if test="insuredCertificateNo != null">
        insured_certificate_no,
      </if>
      <if test="insuredPhone != null">
        insured_phone,
      </if>
      <if test="flightDate != null">
        flight_date,
      </if>
      <if test="flightNo != null">
        flight_no,
      </if>
      <if test="flightDepCity != null">
        flight_dep_city,
      </if>
      <if test="flightArrCity != null">
        flight_arr_city,
      </if>
      <if test="ticket != null">
        ticket,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="reportorName != null">
        reportor_name,
      </if>
      <if test="reportorCertificateNo != null">
        reportor_certificate_no,
      </if>
      <if test="reportorPhone != null">
        reportor_phone,
      </if>
      <if test="payeerAccount != null">
        payeer_account,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="payeerAccountBank != null">
        payeer_account_bank,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="payAmmount != null">
        pay_ammount,
      </if>
      <if test="accidentReason != null">
        accident_reason,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="insuranceCompany != null">
        #{insuranceCompany,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="policyValidity != null">
        #{policyValidity,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderName != null">
        #{policyHolderName,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderCertificateNo != null">
        #{policyHolderCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderPhone != null">
        #{policyHolderPhone,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        #{purchaseTime,jdbcType=VARCHAR},
      </if>
      <if test="premium != null">
        #{premium,jdbcType=VARCHAR},
      </if>
      <if test="insuredName != null">
        #{insuredName,jdbcType=VARCHAR},
      </if>
      <if test="insuredCertificateNo != null">
        #{insuredCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="insuredPhone != null">
        #{insuredPhone,jdbcType=VARCHAR},
      </if>
      <if test="flightDate != null">
        #{flightDate,jdbcType=VARCHAR},
      </if>
      <if test="flightNo != null">
        #{flightNo,jdbcType=VARCHAR},
      </if>
      <if test="flightDepCity != null">
        #{flightDepCity,jdbcType=VARCHAR},
      </if>
      <if test="flightArrCity != null">
        #{flightArrCity,jdbcType=VARCHAR},
      </if>
      <if test="ticket != null">
        #{ticket,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=VARCHAR},
      </if>
      <if test="reportorName != null">
        #{reportorName,jdbcType=VARCHAR},
      </if>
      <if test="reportorCertificateNo != null">
        #{reportorCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="reportorPhone != null">
        #{reportorPhone,jdbcType=VARCHAR},
      </if>
      <if test="payeerAccount != null">
        #{payeerAccount,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="payeerAccountBank != null">
        #{payeerAccountBank,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=VARCHAR},
      </if>
      <if test="payAmmount != null">
        #{payAmmount,jdbcType=VARCHAR},
      </if>
      <if test="accidentReason != null">
        #{accidentReason,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AntiFraudDataExample" resultType="java.lang.Long">
    select count(*) from anti_fraud_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update anti_fraud_data
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.insuranceCompany != null">
        insurance_company = #{record.insuranceCompany,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.policyValidity != null">
        policy_validity = #{record.policyValidity,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyHolderName != null">
        policy_holder_name = #{record.policyHolderName,jdbcType=VARCHAR},
      </if>
      <if test="record.policyHolderCertificateNo != null">
        policy_holder_certificate_no = #{record.policyHolderCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyHolderPhone != null">
        policy_holder_phone = #{record.policyHolderPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseTime != null">
        purchase_time = #{record.purchaseTime,jdbcType=VARCHAR},
      </if>
      <if test="record.premium != null">
        premium = #{record.premium,jdbcType=VARCHAR},
      </if>
      <if test="record.insuredName != null">
        insured_name = #{record.insuredName,jdbcType=VARCHAR},
      </if>
      <if test="record.insuredCertificateNo != null">
        insured_certificate_no = #{record.insuredCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="record.insuredPhone != null">
        insured_phone = #{record.insuredPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.flightDate != null">
        flight_date = #{record.flightDate,jdbcType=VARCHAR},
      </if>
      <if test="record.flightNo != null">
        flight_no = #{record.flightNo,jdbcType=VARCHAR},
      </if>
      <if test="record.flightDepCity != null">
        flight_dep_city = #{record.flightDepCity,jdbcType=VARCHAR},
      </if>
      <if test="record.flightArrCity != null">
        flight_arr_city = #{record.flightArrCity,jdbcType=VARCHAR},
      </if>
      <if test="record.ticket != null">
        ticket = #{record.ticket,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDate != null">
        report_date = #{record.reportDate,jdbcType=VARCHAR},
      </if>
      <if test="record.reportorName != null">
        reportor_name = #{record.reportorName,jdbcType=VARCHAR},
      </if>
      <if test="record.reportorCertificateNo != null">
        reportor_certificate_no = #{record.reportorCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportorPhone != null">
        reportor_phone = #{record.reportorPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.payeerAccount != null">
        payeer_account = #{record.payeerAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeNo != null">
        trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.payeerAccountBank != null">
        payeer_account_bank = #{record.payeerAccountBank,jdbcType=VARCHAR},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=VARCHAR},
      </if>
      <if test="record.payAmmount != null">
        pay_ammount = #{record.payAmmount,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentReason != null">
        accident_reason = #{record.accidentReason,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update anti_fraud_data
    set id = #{record.id,jdbcType=BIGINT},
      insurance_company = #{record.insuranceCompany,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      policy_validity = #{record.policyValidity,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_holder_name = #{record.policyHolderName,jdbcType=VARCHAR},
      policy_holder_certificate_no = #{record.policyHolderCertificateNo,jdbcType=VARCHAR},
      policy_holder_phone = #{record.policyHolderPhone,jdbcType=VARCHAR},
      purchase_time = #{record.purchaseTime,jdbcType=VARCHAR},
      premium = #{record.premium,jdbcType=VARCHAR},
      insured_name = #{record.insuredName,jdbcType=VARCHAR},
      insured_certificate_no = #{record.insuredCertificateNo,jdbcType=VARCHAR},
      insured_phone = #{record.insuredPhone,jdbcType=VARCHAR},
      flight_date = #{record.flightDate,jdbcType=VARCHAR},
      flight_no = #{record.flightNo,jdbcType=VARCHAR},
      flight_dep_city = #{record.flightDepCity,jdbcType=VARCHAR},
      flight_arr_city = #{record.flightArrCity,jdbcType=VARCHAR},
      ticket = #{record.ticket,jdbcType=VARCHAR},
      report_date = #{record.reportDate,jdbcType=VARCHAR},
      reportor_name = #{record.reportorName,jdbcType=VARCHAR},
      reportor_certificate_no = #{record.reportorCertificateNo,jdbcType=VARCHAR},
      reportor_phone = #{record.reportorPhone,jdbcType=VARCHAR},
      payeer_account = #{record.payeerAccount,jdbcType=VARCHAR},
      trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      payeer_account_bank = #{record.payeerAccountBank,jdbcType=VARCHAR},
      pay_time = #{record.payTime,jdbcType=VARCHAR},
      pay_ammount = #{record.payAmmount,jdbcType=VARCHAR},
      accident_reason = #{record.accidentReason,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AntiFraudDataDO">
    update anti_fraud_data
    <set>
      <if test="insuranceCompany != null">
        insurance_company = #{insuranceCompany,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="policyValidity != null">
        policy_validity = #{policyValidity,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderName != null">
        policy_holder_name = #{policyHolderName,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderCertificateNo != null">
        policy_holder_certificate_no = #{policyHolderCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderPhone != null">
        policy_holder_phone = #{policyHolderPhone,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        purchase_time = #{purchaseTime,jdbcType=VARCHAR},
      </if>
      <if test="premium != null">
        premium = #{premium,jdbcType=VARCHAR},
      </if>
      <if test="insuredName != null">
        insured_name = #{insuredName,jdbcType=VARCHAR},
      </if>
      <if test="insuredCertificateNo != null">
        insured_certificate_no = #{insuredCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="insuredPhone != null">
        insured_phone = #{insuredPhone,jdbcType=VARCHAR},
      </if>
      <if test="flightDate != null">
        flight_date = #{flightDate,jdbcType=VARCHAR},
      </if>
      <if test="flightNo != null">
        flight_no = #{flightNo,jdbcType=VARCHAR},
      </if>
      <if test="flightDepCity != null">
        flight_dep_city = #{flightDepCity,jdbcType=VARCHAR},
      </if>
      <if test="flightArrCity != null">
        flight_arr_city = #{flightArrCity,jdbcType=VARCHAR},
      </if>
      <if test="ticket != null">
        ticket = #{ticket,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=VARCHAR},
      </if>
      <if test="reportorName != null">
        reportor_name = #{reportorName,jdbcType=VARCHAR},
      </if>
      <if test="reportorCertificateNo != null">
        reportor_certificate_no = #{reportorCertificateNo,jdbcType=VARCHAR},
      </if>
      <if test="reportorPhone != null">
        reportor_phone = #{reportorPhone,jdbcType=VARCHAR},
      </if>
      <if test="payeerAccount != null">
        payeer_account = #{payeerAccount,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="payeerAccountBank != null">
        payeer_account_bank = #{payeerAccountBank,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=VARCHAR},
      </if>
      <if test="payAmmount != null">
        pay_ammount = #{payAmmount,jdbcType=VARCHAR},
      </if>
      <if test="accidentReason != null">
        accident_reason = #{accidentReason,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AntiFraudDataDO">
    update anti_fraud_data
    set insurance_company = #{insuranceCompany,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      policy_validity = #{policyValidity,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      policy_holder_name = #{policyHolderName,jdbcType=VARCHAR},
      policy_holder_certificate_no = #{policyHolderCertificateNo,jdbcType=VARCHAR},
      policy_holder_phone = #{policyHolderPhone,jdbcType=VARCHAR},
      purchase_time = #{purchaseTime,jdbcType=VARCHAR},
      premium = #{premium,jdbcType=VARCHAR},
      insured_name = #{insuredName,jdbcType=VARCHAR},
      insured_certificate_no = #{insuredCertificateNo,jdbcType=VARCHAR},
      insured_phone = #{insuredPhone,jdbcType=VARCHAR},
      flight_date = #{flightDate,jdbcType=VARCHAR},
      flight_no = #{flightNo,jdbcType=VARCHAR},
      flight_dep_city = #{flightDepCity,jdbcType=VARCHAR},
      flight_arr_city = #{flightArrCity,jdbcType=VARCHAR},
      ticket = #{ticket,jdbcType=VARCHAR},
      report_date = #{reportDate,jdbcType=VARCHAR},
      reportor_name = #{reportorName,jdbcType=VARCHAR},
      reportor_certificate_no = #{reportorCertificateNo,jdbcType=VARCHAR},
      reportor_phone = #{reportorPhone,jdbcType=VARCHAR},
      payeer_account = #{payeerAccount,jdbcType=VARCHAR},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      payeer_account_bank = #{payeerAccountBank,jdbcType=VARCHAR},
      pay_time = #{payTime,jdbcType=VARCHAR},
      pay_ammount = #{payAmmount,jdbcType=VARCHAR},
      accident_reason = #{accidentReason,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into anti_fraud_data
    (id, insurance_company, product_name, policy_validity, policy_no, report_no, policy_holder_name, 
      policy_holder_certificate_no, policy_holder_phone, purchase_time, premium, insured_name, 
      insured_certificate_no, insured_phone, flight_date, flight_no, flight_dep_city, 
      flight_arr_city, ticket, report_date, reportor_name, reportor_certificate_no, reportor_phone, 
      payeer_account, trade_no, payeer_account_bank, pay_time, pay_ammount, accident_reason, 
      creator, gmt_created, modifier, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.insuranceCompany,jdbcType=VARCHAR}, #{item.productName,jdbcType=VARCHAR}, 
        #{item.policyValidity,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.policyHolderName,jdbcType=VARCHAR}, #{item.policyHolderCertificateNo,jdbcType=VARCHAR}, 
        #{item.policyHolderPhone,jdbcType=VARCHAR}, #{item.purchaseTime,jdbcType=VARCHAR}, 
        #{item.premium,jdbcType=VARCHAR}, #{item.insuredName,jdbcType=VARCHAR}, #{item.insuredCertificateNo,jdbcType=VARCHAR}, 
        #{item.insuredPhone,jdbcType=VARCHAR}, #{item.flightDate,jdbcType=VARCHAR}, #{item.flightNo,jdbcType=VARCHAR}, 
        #{item.flightDepCity,jdbcType=VARCHAR}, #{item.flightArrCity,jdbcType=VARCHAR}, 
        #{item.ticket,jdbcType=VARCHAR}, #{item.reportDate,jdbcType=VARCHAR}, #{item.reportorName,jdbcType=VARCHAR}, 
        #{item.reportorCertificateNo,jdbcType=VARCHAR}, #{item.reportorPhone,jdbcType=VARCHAR}, 
        #{item.payeerAccount,jdbcType=VARCHAR}, #{item.tradeNo,jdbcType=VARCHAR}, #{item.payeerAccountBank,jdbcType=VARCHAR}, 
        #{item.payTime,jdbcType=VARCHAR}, #{item.payAmmount,jdbcType=VARCHAR}, #{item.accidentReason,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into anti_fraud_data (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'insurance_company'.toString() == column.value">
          #{item.insuranceCompany,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'policy_validity'.toString() == column.value">
          #{item.policyValidity,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_holder_name'.toString() == column.value">
          #{item.policyHolderName,jdbcType=VARCHAR}
        </if>
        <if test="'policy_holder_certificate_no'.toString() == column.value">
          #{item.policyHolderCertificateNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_holder_phone'.toString() == column.value">
          #{item.policyHolderPhone,jdbcType=VARCHAR}
        </if>
        <if test="'purchase_time'.toString() == column.value">
          #{item.purchaseTime,jdbcType=VARCHAR}
        </if>
        <if test="'premium'.toString() == column.value">
          #{item.premium,jdbcType=VARCHAR}
        </if>
        <if test="'insured_name'.toString() == column.value">
          #{item.insuredName,jdbcType=VARCHAR}
        </if>
        <if test="'insured_certificate_no'.toString() == column.value">
          #{item.insuredCertificateNo,jdbcType=VARCHAR}
        </if>
        <if test="'insured_phone'.toString() == column.value">
          #{item.insuredPhone,jdbcType=VARCHAR}
        </if>
        <if test="'flight_date'.toString() == column.value">
          #{item.flightDate,jdbcType=VARCHAR}
        </if>
        <if test="'flight_no'.toString() == column.value">
          #{item.flightNo,jdbcType=VARCHAR}
        </if>
        <if test="'flight_dep_city'.toString() == column.value">
          #{item.flightDepCity,jdbcType=VARCHAR}
        </if>
        <if test="'flight_arr_city'.toString() == column.value">
          #{item.flightArrCity,jdbcType=VARCHAR}
        </if>
        <if test="'ticket'.toString() == column.value">
          #{item.ticket,jdbcType=VARCHAR}
        </if>
        <if test="'report_date'.toString() == column.value">
          #{item.reportDate,jdbcType=VARCHAR}
        </if>
        <if test="'reportor_name'.toString() == column.value">
          #{item.reportorName,jdbcType=VARCHAR}
        </if>
        <if test="'reportor_certificate_no'.toString() == column.value">
          #{item.reportorCertificateNo,jdbcType=VARCHAR}
        </if>
        <if test="'reportor_phone'.toString() == column.value">
          #{item.reportorPhone,jdbcType=VARCHAR}
        </if>
        <if test="'payeer_account'.toString() == column.value">
          #{item.payeerAccount,jdbcType=VARCHAR}
        </if>
        <if test="'trade_no'.toString() == column.value">
          #{item.tradeNo,jdbcType=VARCHAR}
        </if>
        <if test="'payeer_account_bank'.toString() == column.value">
          #{item.payeerAccountBank,jdbcType=VARCHAR}
        </if>
        <if test="'pay_time'.toString() == column.value">
          #{item.payTime,jdbcType=VARCHAR}
        </if>
        <if test="'pay_ammount'.toString() == column.value">
          #{item.payAmmount,jdbcType=VARCHAR}
        </if>
        <if test="'accident_reason'.toString() == column.value">
          #{item.accidentReason,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>