<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.LargeAmtOnlinePayMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.LargeAmtOnlinePayDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bank_info_id" jdbcType="BIGINT" property="bankInfoId" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="claim_status" jdbcType="INTEGER" property="claimStatus" />
    <result column="claim_status_name" jdbcType="VARCHAR" property="claimStatusName" />
    <result column="paid_amount" jdbcType="DECIMAL" property="paidAmount" />
    <result column="close_date" jdbcType="TIMESTAMP" property="closeDate" />
    <result column="insurant_name" jdbcType="VARCHAR" property="insurantName" />
    <result column="settlement_operator" jdbcType="VARCHAR" property="settlementOperator" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="insurance_type" jdbcType="VARCHAR" property="insuranceType" />
    <result column="pay_state" jdbcType="VARCHAR" property="payState" />
    <result column="print_count" jdbcType="INTEGER" property="printCount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bank_info_id, policy_no, report_no, claim_status, claim_status_name, paid_amount, 
    close_date, insurant_name, settlement_operator, package_def_id, product_name, insurance_type, 
    pay_state, print_count, remark, gmt_created, gmt_modified, creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.LargeAmtOnlinePayExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from large_amt_online_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from large_amt_online_pay
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.LargeAmtOnlinePayDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into large_amt_online_pay (bank_info_id, policy_no, report_no, 
      claim_status, claim_status_name, paid_amount, 
      close_date, insurant_name, settlement_operator, 
      package_def_id, product_name, insurance_type, 
      pay_state, print_count, remark, 
      gmt_created, gmt_modified, creator, 
      modifier, is_deleted)
    values (#{bankInfoId,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{claimStatus,jdbcType=INTEGER}, #{claimStatusName,jdbcType=VARCHAR}, #{paidAmount,jdbcType=DECIMAL}, 
      #{closeDate,jdbcType=TIMESTAMP}, #{insurantName,jdbcType=VARCHAR}, #{settlementOperator,jdbcType=VARCHAR}, 
      #{packageDefId,jdbcType=BIGINT}, #{productName,jdbcType=VARCHAR}, #{insuranceType,jdbcType=VARCHAR}, 
      #{payState,jdbcType=VARCHAR}, #{printCount,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.LargeAmtOnlinePayDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into large_amt_online_pay
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bankInfoId != null">
        bank_info_id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="claimStatus != null">
        claim_status,
      </if>
      <if test="claimStatusName != null">
        claim_status_name,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="closeDate != null">
        close_date,
      </if>
      <if test="insurantName != null">
        insurant_name,
      </if>
      <if test="settlementOperator != null">
        settlement_operator,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="insuranceType != null">
        insurance_type,
      </if>
      <if test="payState != null">
        pay_state,
      </if>
      <if test="printCount != null">
        print_count,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bankInfoId != null">
        #{bankInfoId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        #{claimStatus,jdbcType=INTEGER},
      </if>
      <if test="claimStatusName != null">
        #{claimStatusName,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="closeDate != null">
        #{closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="insurantName != null">
        #{insurantName,jdbcType=VARCHAR},
      </if>
      <if test="settlementOperator != null">
        #{settlementOperator,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="insuranceType != null">
        #{insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        #{payState,jdbcType=VARCHAR},
      </if>
      <if test="printCount != null">
        #{printCount,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.LargeAmtOnlinePayExample" resultType="java.lang.Long">
    select count(*) from large_amt_online_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update large_amt_online_pay
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bankInfoId != null">
        bank_info_id = #{record.bankInfoId,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimStatus != null">
        claim_status = #{record.claimStatus,jdbcType=INTEGER},
      </if>
      <if test="record.claimStatusName != null">
        claim_status_name = #{record.claimStatusName,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.closeDate != null">
        close_date = #{record.closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.insurantName != null">
        insurant_name = #{record.insurantName,jdbcType=VARCHAR},
      </if>
      <if test="record.settlementOperator != null">
        settlement_operator = #{record.settlementOperator,jdbcType=VARCHAR},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceType != null">
        insurance_type = #{record.insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="record.payState != null">
        pay_state = #{record.payState,jdbcType=VARCHAR},
      </if>
      <if test="record.printCount != null">
        print_count = #{record.printCount,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update large_amt_online_pay
    set id = #{record.id,jdbcType=BIGINT},
      bank_info_id = #{record.bankInfoId,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      claim_status = #{record.claimStatus,jdbcType=INTEGER},
      claim_status_name = #{record.claimStatusName,jdbcType=VARCHAR},
      paid_amount = #{record.paidAmount,jdbcType=DECIMAL},
      close_date = #{record.closeDate,jdbcType=TIMESTAMP},
      insurant_name = #{record.insurantName,jdbcType=VARCHAR},
      settlement_operator = #{record.settlementOperator,jdbcType=VARCHAR},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      product_name = #{record.productName,jdbcType=VARCHAR},
      insurance_type = #{record.insuranceType,jdbcType=VARCHAR},
      pay_state = #{record.payState,jdbcType=VARCHAR},
      print_count = #{record.printCount,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.LargeAmtOnlinePayDO">
    update large_amt_online_pay
    <set>
      <if test="bankInfoId != null">
        bank_info_id = #{bankInfoId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        claim_status = #{claimStatus,jdbcType=INTEGER},
      </if>
      <if test="claimStatusName != null">
        claim_status_name = #{claimStatusName,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="closeDate != null">
        close_date = #{closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="insurantName != null">
        insurant_name = #{insurantName,jdbcType=VARCHAR},
      </if>
      <if test="settlementOperator != null">
        settlement_operator = #{settlementOperator,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="insuranceType != null">
        insurance_type = #{insuranceType,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        pay_state = #{payState,jdbcType=VARCHAR},
      </if>
      <if test="printCount != null">
        print_count = #{printCount,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.LargeAmtOnlinePayDO">
    update large_amt_online_pay
    set bank_info_id = #{bankInfoId,jdbcType=BIGINT},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      claim_status = #{claimStatus,jdbcType=INTEGER},
      claim_status_name = #{claimStatusName,jdbcType=VARCHAR},
      paid_amount = #{paidAmount,jdbcType=DECIMAL},
      close_date = #{closeDate,jdbcType=TIMESTAMP},
      insurant_name = #{insurantName,jdbcType=VARCHAR},
      settlement_operator = #{settlementOperator,jdbcType=VARCHAR},
      package_def_id = #{packageDefId,jdbcType=BIGINT},
      product_name = #{productName,jdbcType=VARCHAR},
      insurance_type = #{insuranceType,jdbcType=VARCHAR},
      pay_state = #{payState,jdbcType=VARCHAR},
      print_count = #{printCount,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into large_amt_online_pay
    (bank_info_id, policy_no, report_no, claim_status, claim_status_name, paid_amount, 
      close_date, insurant_name, settlement_operator, package_def_id, product_name, insurance_type, 
      pay_state, print_count, remark, gmt_created, gmt_modified, creator, modifier, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bankInfoId,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.claimStatus,jdbcType=INTEGER}, #{item.claimStatusName,jdbcType=VARCHAR}, 
        #{item.paidAmount,jdbcType=DECIMAL}, #{item.closeDate,jdbcType=TIMESTAMP}, #{item.insurantName,jdbcType=VARCHAR}, 
        #{item.settlementOperator,jdbcType=VARCHAR}, #{item.packageDefId,jdbcType=BIGINT}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.insuranceType,jdbcType=VARCHAR}, #{item.payState,jdbcType=VARCHAR}, 
        #{item.printCount,jdbcType=INTEGER}, #{item.remark,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into large_amt_online_pay (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'bank_info_id'.toString() == column.value">
          #{item.bankInfoId,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'claim_status'.toString() == column.value">
          #{item.claimStatus,jdbcType=INTEGER}
        </if>
        <if test="'claim_status_name'.toString() == column.value">
          #{item.claimStatusName,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount'.toString() == column.value">
          #{item.paidAmount,jdbcType=DECIMAL}
        </if>
        <if test="'close_date'.toString() == column.value">
          #{item.closeDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'insurant_name'.toString() == column.value">
          #{item.insurantName,jdbcType=VARCHAR}
        </if>
        <if test="'settlement_operator'.toString() == column.value">
          #{item.settlementOperator,jdbcType=VARCHAR}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'insurance_type'.toString() == column.value">
          #{item.insuranceType,jdbcType=VARCHAR}
        </if>
        <if test="'pay_state'.toString() == column.value">
          #{item.payState,jdbcType=VARCHAR}
        </if>
        <if test="'print_count'.toString() == column.value">
          #{item.printCount,jdbcType=INTEGER}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>