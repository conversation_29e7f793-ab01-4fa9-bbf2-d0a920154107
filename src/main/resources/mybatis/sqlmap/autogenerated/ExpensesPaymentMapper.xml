<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ExpensesPaymentMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ExpensesPaymentDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="payment_no" jdbcType="VARCHAR" property="paymentNo" />
    <result column="assess_payment_no" jdbcType="VARCHAR" property="assessPaymentNo" />
    <result column="deduct_batch_no" jdbcType="VARCHAR" property="deductBatchNo" />
    <result column="expenses_type" jdbcType="TINYINT" property="expensesType" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="bank_account_name" jdbcType="VARCHAR" property="bankAccountName" />
    <result column="bank_branch_name" jdbcType="VARCHAR" property="bankBranchName" />
    <result column="expenses_amount" jdbcType="DECIMAL" property="expensesAmount" />
    <result column="applicant" jdbcType="VARCHAR" property="applicant" />
    <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate" />
    <result column="apply_form" jdbcType="VARCHAR" property="applyForm" />
    <result column="oa_no" jdbcType="VARCHAR" property="oaNo" />
    <result column="oa_date" jdbcType="TIMESTAMP" property="oaDate" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="pay_date" jdbcType="TIMESTAMP" property="payDate" />
    <result column="paid_date" jdbcType="TIMESTAMP" property="paidDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="claim_validate_status" jdbcType="TINYINT" property="claimValidateStatus" />
    <result column="invoice_check_data" jdbcType="VARCHAR" property="invoiceCheckData" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, title, payment_no, assess_payment_no, deduct_batch_no, expenses_type, bank_account_no, 
    bank_account_name, bank_branch_name, expenses_amount, applicant, apply_date, apply_form, 
    oa_no, oa_date, trade_no, pay_amount, pay_date, paid_date, status, claim_validate_status, 
    invoice_check_data, remark, creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ExpensesPaymentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from expenses_payment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from expenses_payment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ExpensesPaymentDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into expenses_payment (title, payment_no, assess_payment_no, 
      deduct_batch_no, expenses_type, bank_account_no, 
      bank_account_name, bank_branch_name, expenses_amount, 
      applicant, apply_date, apply_form, 
      oa_no, oa_date, trade_no, 
      pay_amount, pay_date, paid_date, 
      status, claim_validate_status, invoice_check_data, 
      remark, creator, modifier, 
      gmt_created, gmt_modified, is_deleted
      )
    values (#{title,jdbcType=VARCHAR}, #{paymentNo,jdbcType=VARCHAR}, #{assessPaymentNo,jdbcType=VARCHAR}, 
      #{deductBatchNo,jdbcType=VARCHAR}, #{expensesType,jdbcType=TINYINT}, #{bankAccountNo,jdbcType=VARCHAR}, 
      #{bankAccountName,jdbcType=VARCHAR}, #{bankBranchName,jdbcType=VARCHAR}, #{expensesAmount,jdbcType=DECIMAL}, 
      #{applicant,jdbcType=VARCHAR}, #{applyDate,jdbcType=TIMESTAMP}, #{applyForm,jdbcType=VARCHAR}, 
      #{oaNo,jdbcType=VARCHAR}, #{oaDate,jdbcType=TIMESTAMP}, #{tradeNo,jdbcType=VARCHAR}, 
      #{payAmount,jdbcType=DECIMAL}, #{payDate,jdbcType=TIMESTAMP}, #{paidDate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{claimValidateStatus,jdbcType=TINYINT}, #{invoiceCheckData,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ExpensesPaymentDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into expenses_payment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="title != null">
        title,
      </if>
      <if test="paymentNo != null">
        payment_no,
      </if>
      <if test="assessPaymentNo != null">
        assess_payment_no,
      </if>
      <if test="deductBatchNo != null">
        deduct_batch_no,
      </if>
      <if test="expensesType != null">
        expenses_type,
      </if>
      <if test="bankAccountNo != null">
        bank_account_no,
      </if>
      <if test="bankAccountName != null">
        bank_account_name,
      </if>
      <if test="bankBranchName != null">
        bank_branch_name,
      </if>
      <if test="expensesAmount != null">
        expenses_amount,
      </if>
      <if test="applicant != null">
        applicant,
      </if>
      <if test="applyDate != null">
        apply_date,
      </if>
      <if test="applyForm != null">
        apply_form,
      </if>
      <if test="oaNo != null">
        oa_no,
      </if>
      <if test="oaDate != null">
        oa_date,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="payDate != null">
        pay_date,
      </if>
      <if test="paidDate != null">
        paid_date,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="claimValidateStatus != null">
        claim_validate_status,
      </if>
      <if test="invoiceCheckData != null">
        invoice_check_data,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null">
        #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="assessPaymentNo != null">
        #{assessPaymentNo,jdbcType=VARCHAR},
      </if>
      <if test="deductBatchNo != null">
        #{deductBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="expensesType != null">
        #{expensesType,jdbcType=TINYINT},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountName != null">
        #{bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="bankBranchName != null">
        #{bankBranchName,jdbcType=VARCHAR},
      </if>
      <if test="expensesAmount != null">
        #{expensesAmount,jdbcType=DECIMAL},
      </if>
      <if test="applicant != null">
        #{applicant,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        #{applyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="applyForm != null">
        #{applyForm,jdbcType=VARCHAR},
      </if>
      <if test="oaNo != null">
        #{oaNo,jdbcType=VARCHAR},
      </if>
      <if test="oaDate != null">
        #{oaDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="payDate != null">
        #{payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paidDate != null">
        #{paidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="claimValidateStatus != null">
        #{claimValidateStatus,jdbcType=TINYINT},
      </if>
      <if test="invoiceCheckData != null">
        #{invoiceCheckData,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ExpensesPaymentExample" resultType="java.lang.Long">
    select count(*) from expenses_payment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update expenses_payment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentNo != null">
        payment_no = #{record.paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="record.assessPaymentNo != null">
        assess_payment_no = #{record.assessPaymentNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deductBatchNo != null">
        deduct_batch_no = #{record.deductBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.expensesType != null">
        expenses_type = #{record.expensesType,jdbcType=TINYINT},
      </if>
      <if test="record.bankAccountNo != null">
        bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccountName != null">
        bank_account_name = #{record.bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankBranchName != null">
        bank_branch_name = #{record.bankBranchName,jdbcType=VARCHAR},
      </if>
      <if test="record.expensesAmount != null">
        expenses_amount = #{record.expensesAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.applicant != null">
        applicant = #{record.applicant,jdbcType=VARCHAR},
      </if>
      <if test="record.applyDate != null">
        apply_date = #{record.applyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.applyForm != null">
        apply_form = #{record.applyForm,jdbcType=VARCHAR},
      </if>
      <if test="record.oaNo != null">
        oa_no = #{record.oaNo,jdbcType=VARCHAR},
      </if>
      <if test="record.oaDate != null">
        oa_date = #{record.oaDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tradeNo != null">
        trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.payAmount != null">
        pay_amount = #{record.payAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.payDate != null">
        pay_date = #{record.payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.paidDate != null">
        paid_date = #{record.paidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.claimValidateStatus != null">
        claim_validate_status = #{record.claimValidateStatus,jdbcType=TINYINT},
      </if>
      <if test="record.invoiceCheckData != null">
        invoice_check_data = #{record.invoiceCheckData,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update expenses_payment
    set id = #{record.id,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      payment_no = #{record.paymentNo,jdbcType=VARCHAR},
      assess_payment_no = #{record.assessPaymentNo,jdbcType=VARCHAR},
      deduct_batch_no = #{record.deductBatchNo,jdbcType=VARCHAR},
      expenses_type = #{record.expensesType,jdbcType=TINYINT},
      bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      bank_account_name = #{record.bankAccountName,jdbcType=VARCHAR},
      bank_branch_name = #{record.bankBranchName,jdbcType=VARCHAR},
      expenses_amount = #{record.expensesAmount,jdbcType=DECIMAL},
      applicant = #{record.applicant,jdbcType=VARCHAR},
      apply_date = #{record.applyDate,jdbcType=TIMESTAMP},
      apply_form = #{record.applyForm,jdbcType=VARCHAR},
      oa_no = #{record.oaNo,jdbcType=VARCHAR},
      oa_date = #{record.oaDate,jdbcType=TIMESTAMP},
      trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      pay_amount = #{record.payAmount,jdbcType=DECIMAL},
      pay_date = #{record.payDate,jdbcType=TIMESTAMP},
      paid_date = #{record.paidDate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      claim_validate_status = #{record.claimValidateStatus,jdbcType=TINYINT},
      invoice_check_data = #{record.invoiceCheckData,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ExpensesPaymentDO">
    update expenses_payment
    <set>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null">
        payment_no = #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="assessPaymentNo != null">
        assess_payment_no = #{assessPaymentNo,jdbcType=VARCHAR},
      </if>
      <if test="deductBatchNo != null">
        deduct_batch_no = #{deductBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="expensesType != null">
        expenses_type = #{expensesType,jdbcType=TINYINT},
      </if>
      <if test="bankAccountNo != null">
        bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountName != null">
        bank_account_name = #{bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="bankBranchName != null">
        bank_branch_name = #{bankBranchName,jdbcType=VARCHAR},
      </if>
      <if test="expensesAmount != null">
        expenses_amount = #{expensesAmount,jdbcType=DECIMAL},
      </if>
      <if test="applicant != null">
        applicant = #{applicant,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        apply_date = #{applyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="applyForm != null">
        apply_form = #{applyForm,jdbcType=VARCHAR},
      </if>
      <if test="oaNo != null">
        oa_no = #{oaNo,jdbcType=VARCHAR},
      </if>
      <if test="oaDate != null">
        oa_date = #{oaDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="payDate != null">
        pay_date = #{payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paidDate != null">
        paid_date = #{paidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="claimValidateStatus != null">
        claim_validate_status = #{claimValidateStatus,jdbcType=TINYINT},
      </if>
      <if test="invoiceCheckData != null">
        invoice_check_data = #{invoiceCheckData,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ExpensesPaymentDO">
    update expenses_payment
    set title = #{title,jdbcType=VARCHAR},
      payment_no = #{paymentNo,jdbcType=VARCHAR},
      assess_payment_no = #{assessPaymentNo,jdbcType=VARCHAR},
      deduct_batch_no = #{deductBatchNo,jdbcType=VARCHAR},
      expenses_type = #{expensesType,jdbcType=TINYINT},
      bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      bank_account_name = #{bankAccountName,jdbcType=VARCHAR},
      bank_branch_name = #{bankBranchName,jdbcType=VARCHAR},
      expenses_amount = #{expensesAmount,jdbcType=DECIMAL},
      applicant = #{applicant,jdbcType=VARCHAR},
      apply_date = #{applyDate,jdbcType=TIMESTAMP},
      apply_form = #{applyForm,jdbcType=VARCHAR},
      oa_no = #{oaNo,jdbcType=VARCHAR},
      oa_date = #{oaDate,jdbcType=TIMESTAMP},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      pay_amount = #{payAmount,jdbcType=DECIMAL},
      pay_date = #{payDate,jdbcType=TIMESTAMP},
      paid_date = #{paidDate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      claim_validate_status = #{claimValidateStatus,jdbcType=TINYINT},
      invoice_check_data = #{invoiceCheckData,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into expenses_payment
    (title, payment_no, assess_payment_no, deduct_batch_no, expenses_type, bank_account_no, 
      bank_account_name, bank_branch_name, expenses_amount, applicant, apply_date, apply_form, 
      oa_no, oa_date, trade_no, pay_amount, pay_date, paid_date, status, claim_validate_status, 
      invoice_check_data, remark, creator, modifier, gmt_created, gmt_modified, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.title,jdbcType=VARCHAR}, #{item.paymentNo,jdbcType=VARCHAR}, #{item.assessPaymentNo,jdbcType=VARCHAR}, 
        #{item.deductBatchNo,jdbcType=VARCHAR}, #{item.expensesType,jdbcType=TINYINT}, 
        #{item.bankAccountNo,jdbcType=VARCHAR}, #{item.bankAccountName,jdbcType=VARCHAR}, 
        #{item.bankBranchName,jdbcType=VARCHAR}, #{item.expensesAmount,jdbcType=DECIMAL}, 
        #{item.applicant,jdbcType=VARCHAR}, #{item.applyDate,jdbcType=TIMESTAMP}, #{item.applyForm,jdbcType=VARCHAR}, 
        #{item.oaNo,jdbcType=VARCHAR}, #{item.oaDate,jdbcType=TIMESTAMP}, #{item.tradeNo,jdbcType=VARCHAR}, 
        #{item.payAmount,jdbcType=DECIMAL}, #{item.payDate,jdbcType=TIMESTAMP}, #{item.paidDate,jdbcType=TIMESTAMP}, 
        #{item.status,jdbcType=TINYINT}, #{item.claimValidateStatus,jdbcType=TINYINT}, 
        #{item.invoiceCheckData,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into expenses_payment (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'title'.toString() == column.value">
          #{item.title,jdbcType=VARCHAR}
        </if>
        <if test="'payment_no'.toString() == column.value">
          #{item.paymentNo,jdbcType=VARCHAR}
        </if>
        <if test="'assess_payment_no'.toString() == column.value">
          #{item.assessPaymentNo,jdbcType=VARCHAR}
        </if>
        <if test="'deduct_batch_no'.toString() == column.value">
          #{item.deductBatchNo,jdbcType=VARCHAR}
        </if>
        <if test="'expenses_type'.toString() == column.value">
          #{item.expensesType,jdbcType=TINYINT}
        </if>
        <if test="'bank_account_no'.toString() == column.value">
          #{item.bankAccountNo,jdbcType=VARCHAR}
        </if>
        <if test="'bank_account_name'.toString() == column.value">
          #{item.bankAccountName,jdbcType=VARCHAR}
        </if>
        <if test="'bank_branch_name'.toString() == column.value">
          #{item.bankBranchName,jdbcType=VARCHAR}
        </if>
        <if test="'expenses_amount'.toString() == column.value">
          #{item.expensesAmount,jdbcType=DECIMAL}
        </if>
        <if test="'applicant'.toString() == column.value">
          #{item.applicant,jdbcType=VARCHAR}
        </if>
        <if test="'apply_date'.toString() == column.value">
          #{item.applyDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'apply_form'.toString() == column.value">
          #{item.applyForm,jdbcType=VARCHAR}
        </if>
        <if test="'oa_no'.toString() == column.value">
          #{item.oaNo,jdbcType=VARCHAR}
        </if>
        <if test="'oa_date'.toString() == column.value">
          #{item.oaDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'trade_no'.toString() == column.value">
          #{item.tradeNo,jdbcType=VARCHAR}
        </if>
        <if test="'pay_amount'.toString() == column.value">
          #{item.payAmount,jdbcType=DECIMAL}
        </if>
        <if test="'pay_date'.toString() == column.value">
          #{item.payDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'paid_date'.toString() == column.value">
          #{item.paidDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'claim_validate_status'.toString() == column.value">
          #{item.claimValidateStatus,jdbcType=TINYINT}
        </if>
        <if test="'invoice_check_data'.toString() == column.value">
          #{item.invoiceCheckData,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>