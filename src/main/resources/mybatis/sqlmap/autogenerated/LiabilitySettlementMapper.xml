<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.LiabilitySettlementMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.LiabilitySettlementDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="source_policy_id" jdbcType="BIGINT" property="sourcePolicyId" />
    <result column="source_policy_product_id" jdbcType="BIGINT" property="sourcePolicyProductId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="liability_id" jdbcType="BIGINT" property="liabilityId" />
    <result column="liability_code" jdbcType="VARCHAR" property="liabilityCode" />
    <result column="liability_name" jdbcType="VARCHAR" property="liabilityName" />
    <result column="clause_name" jdbcType="VARCHAR" property="clauseName" />
    <result column="clause_url_addr" jdbcType="VARCHAR" property="clauseUrlAddr" />
    <result column="amount_type" jdbcType="VARCHAR" property="amountType" />
    <result column="effective_date" jdbcType="TIMESTAMP" property="effectiveDate" />
    <result column="expiry_date" jdbcType="TIMESTAMP" property="expiryDate" />
    <result column="channel_policy_end_time" jdbcType="TIMESTAMP" property="channelPolicyEndTime" />
    <result column="sum_insured" jdbcType="VARCHAR" property="sumInsured" />
    <result column="unused_sum_insured" jdbcType="VARCHAR" property="unusedSumInsured" />
    <result column="reserve_amount_curr" jdbcType="VARCHAR" property="reserveAmountCurr" />
    <result column="reserve_amount" jdbcType="VARCHAR" property="reserveAmount" />
    <result column="paid_amount_curr" jdbcType="VARCHAR" property="paidAmountCurr" />
    <result column="paid_amount" jdbcType="VARCHAR" property="paidAmount" />
    <result column="derogation_amount" jdbcType="VARCHAR" property="derogationAmount" />
    <result column="deductible" jdbcType="VARCHAR" property="deductible" />
    <result column="out_deductible" jdbcType="VARCHAR" property="outDeductible" />
    <result column="ordinary_deductible" jdbcType="VARCHAR" property="ordinaryDeductible" />
    <result column="ordinary_out_deductible" jdbcType="VARCHAR" property="ordinaryOutDeductible" />
    <result column="deduct_deductible" jdbcType="VARCHAR" property="deductDeductible" />
    <result column="deduct_out_deductible" jdbcType="VARCHAR" property="deductOutDeductible" />
    <result column="ordinary_deduct_deductible" jdbcType="VARCHAR" property="ordinaryDeductDeductible" />
    <result column="ordinary_deduct_out_deductible" jdbcType="VARCHAR" property="ordinaryDeductOutDeductible" />
    <result column="settlement_type" jdbcType="VARCHAR" property="settlementType" />
    <result column="cumulative_paid_supply_days" jdbcType="VARCHAR" property="cumulativePaidSupplyDays" />
    <result column="medical_billing_supply_days_count" jdbcType="VARCHAR" property="medicalBillingSupplyDaysCount" />
    <result column="treatment_count" jdbcType="VARCHAR" property="treatmentCount" />
    <result column="settlement_result" jdbcType="VARCHAR" property="settlementResult" />
    <result column="audit_result_show" jdbcType="VARCHAR" property="auditResultShow" />
    <result column="refusal_reason" jdbcType="VARCHAR" property="refusalReason" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="zero_paid_reason" jdbcType="VARCHAR" property="zeroPaidReason" />
    <result column="expense_reserve_amount" jdbcType="VARCHAR" property="expenseReserveAmount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, policy_no, source_policy_id, source_policy_product_id, product_id, 
    product_code, product_name, liability_id, liability_code, liability_name, clause_name, 
    clause_url_addr, amount_type, effective_date, expiry_date, channel_policy_end_time, 
    sum_insured, unused_sum_insured, reserve_amount_curr, reserve_amount, paid_amount_curr, 
    paid_amount, derogation_amount, deductible, out_deductible, ordinary_deductible, 
    ordinary_out_deductible, deduct_deductible, deduct_out_deductible, ordinary_deduct_deductible, 
    ordinary_deduct_out_deductible, settlement_type, cumulative_paid_supply_days, medical_billing_supply_days_count, 
    treatment_count, settlement_result, audit_result_show, refusal_reason, is_deleted, 
    gmt_created, gmt_modified, creator, modifier, zero_paid_reason, expense_reserve_amount
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.LiabilitySettlementExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_hospital_liability_settlement_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_hospital_liability_settlement_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.LiabilitySettlementDO">
    insert into claim_hospital_liability_settlement_info (id, report_no, policy_no, 
      source_policy_id, source_policy_product_id, product_id, 
      product_code, product_name, liability_id, 
      liability_code, liability_name, clause_name, 
      clause_url_addr, amount_type, effective_date, 
      expiry_date, channel_policy_end_time, sum_insured, 
      unused_sum_insured, reserve_amount_curr, reserve_amount, 
      paid_amount_curr, paid_amount, derogation_amount, 
      deductible, out_deductible, ordinary_deductible, 
      ordinary_out_deductible, deduct_deductible, 
      deduct_out_deductible, ordinary_deduct_deductible, 
      ordinary_deduct_out_deductible, settlement_type, 
      cumulative_paid_supply_days, medical_billing_supply_days_count, 
      treatment_count, settlement_result, audit_result_show, 
      refusal_reason, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, 
      zero_paid_reason, expense_reserve_amount)
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, 
      #{sourcePolicyId,jdbcType=BIGINT}, #{sourcePolicyProductId,jdbcType=BIGINT}, #{productId,jdbcType=BIGINT}, 
      #{productCode,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{liabilityId,jdbcType=BIGINT}, 
      #{liabilityCode,jdbcType=VARCHAR}, #{liabilityName,jdbcType=VARCHAR}, #{clauseName,jdbcType=VARCHAR}, 
      #{clauseUrlAddr,jdbcType=VARCHAR}, #{amountType,jdbcType=VARCHAR}, #{effectiveDate,jdbcType=TIMESTAMP}, 
      #{expiryDate,jdbcType=TIMESTAMP}, #{channelPolicyEndTime,jdbcType=TIMESTAMP}, #{sumInsured,jdbcType=VARCHAR}, 
      #{unusedSumInsured,jdbcType=VARCHAR}, #{reserveAmountCurr,jdbcType=VARCHAR}, #{reserveAmount,jdbcType=VARCHAR}, 
      #{paidAmountCurr,jdbcType=VARCHAR}, #{paidAmount,jdbcType=VARCHAR}, #{derogationAmount,jdbcType=VARCHAR}, 
      #{deductible,jdbcType=VARCHAR}, #{outDeductible,jdbcType=VARCHAR}, #{ordinaryDeductible,jdbcType=VARCHAR}, 
      #{ordinaryOutDeductible,jdbcType=VARCHAR}, #{deductDeductible,jdbcType=VARCHAR}, 
      #{deductOutDeductible,jdbcType=VARCHAR}, #{ordinaryDeductDeductible,jdbcType=VARCHAR}, 
      #{ordinaryDeductOutDeductible,jdbcType=VARCHAR}, #{settlementType,jdbcType=VARCHAR}, 
      #{cumulativePaidSupplyDays,jdbcType=VARCHAR}, #{medicalBillingSupplyDaysCount,jdbcType=VARCHAR}, 
      #{treatmentCount,jdbcType=VARCHAR}, #{settlementResult,jdbcType=VARCHAR}, #{auditResultShow,jdbcType=VARCHAR}, 
      #{refusalReason,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{zeroPaidReason,jdbcType=VARCHAR}, #{expenseReserveAmount,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.LiabilitySettlementDO">
    insert into claim_hospital_liability_settlement_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="sourcePolicyId != null">
        source_policy_id,
      </if>
      <if test="sourcePolicyProductId != null">
        source_policy_product_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productCode != null">
        product_code,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="liabilityId != null">
        liability_id,
      </if>
      <if test="liabilityCode != null">
        liability_code,
      </if>
      <if test="liabilityName != null">
        liability_name,
      </if>
      <if test="clauseName != null">
        clause_name,
      </if>
      <if test="clauseUrlAddr != null">
        clause_url_addr,
      </if>
      <if test="amountType != null">
        amount_type,
      </if>
      <if test="effectiveDate != null">
        effective_date,
      </if>
      <if test="expiryDate != null">
        expiry_date,
      </if>
      <if test="channelPolicyEndTime != null">
        channel_policy_end_time,
      </if>
      <if test="sumInsured != null">
        sum_insured,
      </if>
      <if test="unusedSumInsured != null">
        unused_sum_insured,
      </if>
      <if test="reserveAmountCurr != null">
        reserve_amount_curr,
      </if>
      <if test="reserveAmount != null">
        reserve_amount,
      </if>
      <if test="paidAmountCurr != null">
        paid_amount_curr,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="derogationAmount != null">
        derogation_amount,
      </if>
      <if test="deductible != null">
        deductible,
      </if>
      <if test="outDeductible != null">
        out_deductible,
      </if>
      <if test="ordinaryDeductible != null">
        ordinary_deductible,
      </if>
      <if test="ordinaryOutDeductible != null">
        ordinary_out_deductible,
      </if>
      <if test="deductDeductible != null">
        deduct_deductible,
      </if>
      <if test="deductOutDeductible != null">
        deduct_out_deductible,
      </if>
      <if test="ordinaryDeductDeductible != null">
        ordinary_deduct_deductible,
      </if>
      <if test="ordinaryDeductOutDeductible != null">
        ordinary_deduct_out_deductible,
      </if>
      <if test="settlementType != null">
        settlement_type,
      </if>
      <if test="cumulativePaidSupplyDays != null">
        cumulative_paid_supply_days,
      </if>
      <if test="medicalBillingSupplyDaysCount != null">
        medical_billing_supply_days_count,
      </if>
      <if test="treatmentCount != null">
        treatment_count,
      </if>
      <if test="settlementResult != null">
        settlement_result,
      </if>
      <if test="auditResultShow != null">
        audit_result_show,
      </if>
      <if test="refusalReason != null">
        refusal_reason,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="zeroPaidReason != null">
        zero_paid_reason,
      </if>
      <if test="expenseReserveAmount != null">
        expense_reserve_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="sourcePolicyId != null">
        #{sourcePolicyId,jdbcType=BIGINT},
      </if>
      <if test="sourcePolicyProductId != null">
        #{sourcePolicyProductId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityId != null">
        #{liabilityId,jdbcType=BIGINT},
      </if>
      <if test="liabilityCode != null">
        #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="liabilityName != null">
        #{liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="clauseName != null">
        #{clauseName,jdbcType=VARCHAR},
      </if>
      <if test="clauseUrlAddr != null">
        #{clauseUrlAddr,jdbcType=VARCHAR},
      </if>
      <if test="amountType != null">
        #{amountType,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDate != null">
        #{effectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expiryDate != null">
        #{expiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="channelPolicyEndTime != null">
        #{channelPolicyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sumInsured != null">
        #{sumInsured,jdbcType=VARCHAR},
      </if>
      <if test="unusedSumInsured != null">
        #{unusedSumInsured,jdbcType=VARCHAR},
      </if>
      <if test="reserveAmountCurr != null">
        #{reserveAmountCurr,jdbcType=VARCHAR},
      </if>
      <if test="reserveAmount != null">
        #{reserveAmount,jdbcType=VARCHAR},
      </if>
      <if test="paidAmountCurr != null">
        #{paidAmountCurr,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="derogationAmount != null">
        #{derogationAmount,jdbcType=VARCHAR},
      </if>
      <if test="deductible != null">
        #{deductible,jdbcType=VARCHAR},
      </if>
      <if test="outDeductible != null">
        #{outDeductible,jdbcType=VARCHAR},
      </if>
      <if test="ordinaryDeductible != null">
        #{ordinaryDeductible,jdbcType=VARCHAR},
      </if>
      <if test="ordinaryOutDeductible != null">
        #{ordinaryOutDeductible,jdbcType=VARCHAR},
      </if>
      <if test="deductDeductible != null">
        #{deductDeductible,jdbcType=VARCHAR},
      </if>
      <if test="deductOutDeductible != null">
        #{deductOutDeductible,jdbcType=VARCHAR},
      </if>
      <if test="ordinaryDeductDeductible != null">
        #{ordinaryDeductDeductible,jdbcType=VARCHAR},
      </if>
      <if test="ordinaryDeductOutDeductible != null">
        #{ordinaryDeductOutDeductible,jdbcType=VARCHAR},
      </if>
      <if test="settlementType != null">
        #{settlementType,jdbcType=VARCHAR},
      </if>
      <if test="cumulativePaidSupplyDays != null">
        #{cumulativePaidSupplyDays,jdbcType=VARCHAR},
      </if>
      <if test="medicalBillingSupplyDaysCount != null">
        #{medicalBillingSupplyDaysCount,jdbcType=VARCHAR},
      </if>
      <if test="treatmentCount != null">
        #{treatmentCount,jdbcType=VARCHAR},
      </if>
      <if test="settlementResult != null">
        #{settlementResult,jdbcType=VARCHAR},
      </if>
      <if test="auditResultShow != null">
        #{auditResultShow,jdbcType=VARCHAR},
      </if>
      <if test="refusalReason != null">
        #{refusalReason,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="zeroPaidReason != null">
        #{zeroPaidReason,jdbcType=VARCHAR},
      </if>
      <if test="expenseReserveAmount != null">
        #{expenseReserveAmount,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.LiabilitySettlementExample" resultType="java.lang.Long">
    select count(*) from claim_hospital_liability_settlement_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_hospital_liability_settlement_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePolicyId != null">
        source_policy_id = #{record.sourcePolicyId,jdbcType=BIGINT},
      </if>
      <if test="record.sourcePolicyProductId != null">
        source_policy_product_id = #{record.sourcePolicyProductId,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.productCode != null">
        product_code = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityId != null">
        liability_id = #{record.liabilityId,jdbcType=BIGINT},
      </if>
      <if test="record.liabilityCode != null">
        liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityName != null">
        liability_name = #{record.liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="record.clauseName != null">
        clause_name = #{record.clauseName,jdbcType=VARCHAR},
      </if>
      <if test="record.clauseUrlAddr != null">
        clause_url_addr = #{record.clauseUrlAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.amountType != null">
        amount_type = #{record.amountType,jdbcType=VARCHAR},
      </if>
      <if test="record.effectiveDate != null">
        effective_date = #{record.effectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expiryDate != null">
        expiry_date = #{record.expiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.channelPolicyEndTime != null">
        channel_policy_end_time = #{record.channelPolicyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sumInsured != null">
        sum_insured = #{record.sumInsured,jdbcType=VARCHAR},
      </if>
      <if test="record.unusedSumInsured != null">
        unused_sum_insured = #{record.unusedSumInsured,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveAmountCurr != null">
        reserve_amount_curr = #{record.reserveAmountCurr,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveAmount != null">
        reserve_amount = #{record.reserveAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmountCurr != null">
        paid_amount_curr = #{record.paidAmountCurr,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.derogationAmount != null">
        derogation_amount = #{record.derogationAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.deductible != null">
        deductible = #{record.deductible,jdbcType=VARCHAR},
      </if>
      <if test="record.outDeductible != null">
        out_deductible = #{record.outDeductible,jdbcType=VARCHAR},
      </if>
      <if test="record.ordinaryDeductible != null">
        ordinary_deductible = #{record.ordinaryDeductible,jdbcType=VARCHAR},
      </if>
      <if test="record.ordinaryOutDeductible != null">
        ordinary_out_deductible = #{record.ordinaryOutDeductible,jdbcType=VARCHAR},
      </if>
      <if test="record.deductDeductible != null">
        deduct_deductible = #{record.deductDeductible,jdbcType=VARCHAR},
      </if>
      <if test="record.deductOutDeductible != null">
        deduct_out_deductible = #{record.deductOutDeductible,jdbcType=VARCHAR},
      </if>
      <if test="record.ordinaryDeductDeductible != null">
        ordinary_deduct_deductible = #{record.ordinaryDeductDeductible,jdbcType=VARCHAR},
      </if>
      <if test="record.ordinaryDeductOutDeductible != null">
        ordinary_deduct_out_deductible = #{record.ordinaryDeductOutDeductible,jdbcType=VARCHAR},
      </if>
      <if test="record.settlementType != null">
        settlement_type = #{record.settlementType,jdbcType=VARCHAR},
      </if>
      <if test="record.cumulativePaidSupplyDays != null">
        cumulative_paid_supply_days = #{record.cumulativePaidSupplyDays,jdbcType=VARCHAR},
      </if>
      <if test="record.medicalBillingSupplyDaysCount != null">
        medical_billing_supply_days_count = #{record.medicalBillingSupplyDaysCount,jdbcType=VARCHAR},
      </if>
      <if test="record.treatmentCount != null">
        treatment_count = #{record.treatmentCount,jdbcType=VARCHAR},
      </if>
      <if test="record.settlementResult != null">
        settlement_result = #{record.settlementResult,jdbcType=VARCHAR},
      </if>
      <if test="record.auditResultShow != null">
        audit_result_show = #{record.auditResultShow,jdbcType=VARCHAR},
      </if>
      <if test="record.refusalReason != null">
        refusal_reason = #{record.refusalReason,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.zeroPaidReason != null">
        zero_paid_reason = #{record.zeroPaidReason,jdbcType=VARCHAR},
      </if>
      <if test="record.expenseReserveAmount != null">
        expense_reserve_amount = #{record.expenseReserveAmount,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_hospital_liability_settlement_info
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      source_policy_id = #{record.sourcePolicyId,jdbcType=BIGINT},
      source_policy_product_id = #{record.sourcePolicyProductId,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=BIGINT},
      product_code = #{record.productCode,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      liability_id = #{record.liabilityId,jdbcType=BIGINT},
      liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      liability_name = #{record.liabilityName,jdbcType=VARCHAR},
      clause_name = #{record.clauseName,jdbcType=VARCHAR},
      clause_url_addr = #{record.clauseUrlAddr,jdbcType=VARCHAR},
      amount_type = #{record.amountType,jdbcType=VARCHAR},
      effective_date = #{record.effectiveDate,jdbcType=TIMESTAMP},
      expiry_date = #{record.expiryDate,jdbcType=TIMESTAMP},
      channel_policy_end_time = #{record.channelPolicyEndTime,jdbcType=TIMESTAMP},
      sum_insured = #{record.sumInsured,jdbcType=VARCHAR},
      unused_sum_insured = #{record.unusedSumInsured,jdbcType=VARCHAR},
      reserve_amount_curr = #{record.reserveAmountCurr,jdbcType=VARCHAR},
      reserve_amount = #{record.reserveAmount,jdbcType=VARCHAR},
      paid_amount_curr = #{record.paidAmountCurr,jdbcType=VARCHAR},
      paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      derogation_amount = #{record.derogationAmount,jdbcType=VARCHAR},
      deductible = #{record.deductible,jdbcType=VARCHAR},
      out_deductible = #{record.outDeductible,jdbcType=VARCHAR},
      ordinary_deductible = #{record.ordinaryDeductible,jdbcType=VARCHAR},
      ordinary_out_deductible = #{record.ordinaryOutDeductible,jdbcType=VARCHAR},
      deduct_deductible = #{record.deductDeductible,jdbcType=VARCHAR},
      deduct_out_deductible = #{record.deductOutDeductible,jdbcType=VARCHAR},
      ordinary_deduct_deductible = #{record.ordinaryDeductDeductible,jdbcType=VARCHAR},
      ordinary_deduct_out_deductible = #{record.ordinaryDeductOutDeductible,jdbcType=VARCHAR},
      settlement_type = #{record.settlementType,jdbcType=VARCHAR},
      cumulative_paid_supply_days = #{record.cumulativePaidSupplyDays,jdbcType=VARCHAR},
      medical_billing_supply_days_count = #{record.medicalBillingSupplyDaysCount,jdbcType=VARCHAR},
      treatment_count = #{record.treatmentCount,jdbcType=VARCHAR},
      settlement_result = #{record.settlementResult,jdbcType=VARCHAR},
      audit_result_show = #{record.auditResultShow,jdbcType=VARCHAR},
      refusal_reason = #{record.refusalReason,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      zero_paid_reason = #{record.zeroPaidReason,jdbcType=VARCHAR},
      expense_reserve_amount = #{record.expenseReserveAmount,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.LiabilitySettlementDO">
    update claim_hospital_liability_settlement_info
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="sourcePolicyId != null">
        source_policy_id = #{sourcePolicyId,jdbcType=BIGINT},
      </if>
      <if test="sourcePolicyProductId != null">
        source_policy_product_id = #{sourcePolicyProductId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="productCode != null">
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityId != null">
        liability_id = #{liabilityId,jdbcType=BIGINT},
      </if>
      <if test="liabilityCode != null">
        liability_code = #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="liabilityName != null">
        liability_name = #{liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="clauseName != null">
        clause_name = #{clauseName,jdbcType=VARCHAR},
      </if>
      <if test="clauseUrlAddr != null">
        clause_url_addr = #{clauseUrlAddr,jdbcType=VARCHAR},
      </if>
      <if test="amountType != null">
        amount_type = #{amountType,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDate != null">
        effective_date = #{effectiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expiryDate != null">
        expiry_date = #{expiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="channelPolicyEndTime != null">
        channel_policy_end_time = #{channelPolicyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sumInsured != null">
        sum_insured = #{sumInsured,jdbcType=VARCHAR},
      </if>
      <if test="unusedSumInsured != null">
        unused_sum_insured = #{unusedSumInsured,jdbcType=VARCHAR},
      </if>
      <if test="reserveAmountCurr != null">
        reserve_amount_curr = #{reserveAmountCurr,jdbcType=VARCHAR},
      </if>
      <if test="reserveAmount != null">
        reserve_amount = #{reserveAmount,jdbcType=VARCHAR},
      </if>
      <if test="paidAmountCurr != null">
        paid_amount_curr = #{paidAmountCurr,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="derogationAmount != null">
        derogation_amount = #{derogationAmount,jdbcType=VARCHAR},
      </if>
      <if test="deductible != null">
        deductible = #{deductible,jdbcType=VARCHAR},
      </if>
      <if test="outDeductible != null">
        out_deductible = #{outDeductible,jdbcType=VARCHAR},
      </if>
      <if test="ordinaryDeductible != null">
        ordinary_deductible = #{ordinaryDeductible,jdbcType=VARCHAR},
      </if>
      <if test="ordinaryOutDeductible != null">
        ordinary_out_deductible = #{ordinaryOutDeductible,jdbcType=VARCHAR},
      </if>
      <if test="deductDeductible != null">
        deduct_deductible = #{deductDeductible,jdbcType=VARCHAR},
      </if>
      <if test="deductOutDeductible != null">
        deduct_out_deductible = #{deductOutDeductible,jdbcType=VARCHAR},
      </if>
      <if test="ordinaryDeductDeductible != null">
        ordinary_deduct_deductible = #{ordinaryDeductDeductible,jdbcType=VARCHAR},
      </if>
      <if test="ordinaryDeductOutDeductible != null">
        ordinary_deduct_out_deductible = #{ordinaryDeductOutDeductible,jdbcType=VARCHAR},
      </if>
      <if test="settlementType != null">
        settlement_type = #{settlementType,jdbcType=VARCHAR},
      </if>
      <if test="cumulativePaidSupplyDays != null">
        cumulative_paid_supply_days = #{cumulativePaidSupplyDays,jdbcType=VARCHAR},
      </if>
      <if test="medicalBillingSupplyDaysCount != null">
        medical_billing_supply_days_count = #{medicalBillingSupplyDaysCount,jdbcType=VARCHAR},
      </if>
      <if test="treatmentCount != null">
        treatment_count = #{treatmentCount,jdbcType=VARCHAR},
      </if>
      <if test="settlementResult != null">
        settlement_result = #{settlementResult,jdbcType=VARCHAR},
      </if>
      <if test="auditResultShow != null">
        audit_result_show = #{auditResultShow,jdbcType=VARCHAR},
      </if>
      <if test="refusalReason != null">
        refusal_reason = #{refusalReason,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="zeroPaidReason != null">
        zero_paid_reason = #{zeroPaidReason,jdbcType=VARCHAR},
      </if>
      <if test="expenseReserveAmount != null">
        expense_reserve_amount = #{expenseReserveAmount,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.LiabilitySettlementDO">
    update claim_hospital_liability_settlement_info
    set report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      source_policy_id = #{sourcePolicyId,jdbcType=BIGINT},
      source_policy_product_id = #{sourcePolicyProductId,jdbcType=BIGINT},
      product_id = #{productId,jdbcType=BIGINT},
      product_code = #{productCode,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      liability_id = #{liabilityId,jdbcType=BIGINT},
      liability_code = #{liabilityCode,jdbcType=VARCHAR},
      liability_name = #{liabilityName,jdbcType=VARCHAR},
      clause_name = #{clauseName,jdbcType=VARCHAR},
      clause_url_addr = #{clauseUrlAddr,jdbcType=VARCHAR},
      amount_type = #{amountType,jdbcType=VARCHAR},
      effective_date = #{effectiveDate,jdbcType=TIMESTAMP},
      expiry_date = #{expiryDate,jdbcType=TIMESTAMP},
      channel_policy_end_time = #{channelPolicyEndTime,jdbcType=TIMESTAMP},
      sum_insured = #{sumInsured,jdbcType=VARCHAR},
      unused_sum_insured = #{unusedSumInsured,jdbcType=VARCHAR},
      reserve_amount_curr = #{reserveAmountCurr,jdbcType=VARCHAR},
      reserve_amount = #{reserveAmount,jdbcType=VARCHAR},
      paid_amount_curr = #{paidAmountCurr,jdbcType=VARCHAR},
      paid_amount = #{paidAmount,jdbcType=VARCHAR},
      derogation_amount = #{derogationAmount,jdbcType=VARCHAR},
      deductible = #{deductible,jdbcType=VARCHAR},
      out_deductible = #{outDeductible,jdbcType=VARCHAR},
      ordinary_deductible = #{ordinaryDeductible,jdbcType=VARCHAR},
      ordinary_out_deductible = #{ordinaryOutDeductible,jdbcType=VARCHAR},
      deduct_deductible = #{deductDeductible,jdbcType=VARCHAR},
      deduct_out_deductible = #{deductOutDeductible,jdbcType=VARCHAR},
      ordinary_deduct_deductible = #{ordinaryDeductDeductible,jdbcType=VARCHAR},
      ordinary_deduct_out_deductible = #{ordinaryDeductOutDeductible,jdbcType=VARCHAR},
      settlement_type = #{settlementType,jdbcType=VARCHAR},
      cumulative_paid_supply_days = #{cumulativePaidSupplyDays,jdbcType=VARCHAR},
      medical_billing_supply_days_count = #{medicalBillingSupplyDaysCount,jdbcType=VARCHAR},
      treatment_count = #{treatmentCount,jdbcType=VARCHAR},
      settlement_result = #{settlementResult,jdbcType=VARCHAR},
      audit_result_show = #{auditResultShow,jdbcType=VARCHAR},
      refusal_reason = #{refusalReason,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      zero_paid_reason = #{zeroPaidReason,jdbcType=VARCHAR},
      expense_reserve_amount = #{expenseReserveAmount,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_hospital_liability_settlement_info
    (id, report_no, policy_no, source_policy_id, source_policy_product_id, product_id, 
      product_code, product_name, liability_id, liability_code, liability_name, clause_name, 
      clause_url_addr, amount_type, effective_date, expiry_date, channel_policy_end_time, 
      sum_insured, unused_sum_insured, reserve_amount_curr, reserve_amount, paid_amount_curr, 
      paid_amount, derogation_amount, deductible, out_deductible, ordinary_deductible, 
      ordinary_out_deductible, deduct_deductible, deduct_out_deductible, ordinary_deduct_deductible, 
      ordinary_deduct_out_deductible, settlement_type, cumulative_paid_supply_days, medical_billing_supply_days_count, 
      treatment_count, settlement_result, audit_result_show, refusal_reason, is_deleted, 
      gmt_created, gmt_modified, creator, modifier, zero_paid_reason, expense_reserve_amount
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.sourcePolicyId,jdbcType=BIGINT}, #{item.sourcePolicyProductId,jdbcType=BIGINT}, 
        #{item.productId,jdbcType=BIGINT}, #{item.productCode,jdbcType=VARCHAR}, #{item.productName,jdbcType=VARCHAR}, 
        #{item.liabilityId,jdbcType=BIGINT}, #{item.liabilityCode,jdbcType=VARCHAR}, #{item.liabilityName,jdbcType=VARCHAR}, 
        #{item.clauseName,jdbcType=VARCHAR}, #{item.clauseUrlAddr,jdbcType=VARCHAR}, #{item.amountType,jdbcType=VARCHAR}, 
        #{item.effectiveDate,jdbcType=TIMESTAMP}, #{item.expiryDate,jdbcType=TIMESTAMP}, 
        #{item.channelPolicyEndTime,jdbcType=TIMESTAMP}, #{item.sumInsured,jdbcType=VARCHAR}, 
        #{item.unusedSumInsured,jdbcType=VARCHAR}, #{item.reserveAmountCurr,jdbcType=VARCHAR}, 
        #{item.reserveAmount,jdbcType=VARCHAR}, #{item.paidAmountCurr,jdbcType=VARCHAR}, 
        #{item.paidAmount,jdbcType=VARCHAR}, #{item.derogationAmount,jdbcType=VARCHAR}, 
        #{item.deductible,jdbcType=VARCHAR}, #{item.outDeductible,jdbcType=VARCHAR}, #{item.ordinaryDeductible,jdbcType=VARCHAR}, 
        #{item.ordinaryOutDeductible,jdbcType=VARCHAR}, #{item.deductDeductible,jdbcType=VARCHAR}, 
        #{item.deductOutDeductible,jdbcType=VARCHAR}, #{item.ordinaryDeductDeductible,jdbcType=VARCHAR}, 
        #{item.ordinaryDeductOutDeductible,jdbcType=VARCHAR}, #{item.settlementType,jdbcType=VARCHAR}, 
        #{item.cumulativePaidSupplyDays,jdbcType=VARCHAR}, #{item.medicalBillingSupplyDaysCount,jdbcType=VARCHAR}, 
        #{item.treatmentCount,jdbcType=VARCHAR}, #{item.settlementResult,jdbcType=VARCHAR}, 
        #{item.auditResultShow,jdbcType=VARCHAR}, #{item.refusalReason,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.zeroPaidReason,jdbcType=VARCHAR}, 
        #{item.expenseReserveAmount,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_hospital_liability_settlement_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'source_policy_id'.toString() == column.value">
          #{item.sourcePolicyId,jdbcType=BIGINT}
        </if>
        <if test="'source_policy_product_id'.toString() == column.value">
          #{item.sourcePolicyProductId,jdbcType=BIGINT}
        </if>
        <if test="'product_id'.toString() == column.value">
          #{item.productId,jdbcType=BIGINT}
        </if>
        <if test="'product_code'.toString() == column.value">
          #{item.productCode,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'liability_id'.toString() == column.value">
          #{item.liabilityId,jdbcType=BIGINT}
        </if>
        <if test="'liability_code'.toString() == column.value">
          #{item.liabilityCode,jdbcType=VARCHAR}
        </if>
        <if test="'liability_name'.toString() == column.value">
          #{item.liabilityName,jdbcType=VARCHAR}
        </if>
        <if test="'clause_name'.toString() == column.value">
          #{item.clauseName,jdbcType=VARCHAR}
        </if>
        <if test="'clause_url_addr'.toString() == column.value">
          #{item.clauseUrlAddr,jdbcType=VARCHAR}
        </if>
        <if test="'amount_type'.toString() == column.value">
          #{item.amountType,jdbcType=VARCHAR}
        </if>
        <if test="'effective_date'.toString() == column.value">
          #{item.effectiveDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'expiry_date'.toString() == column.value">
          #{item.expiryDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'channel_policy_end_time'.toString() == column.value">
          #{item.channelPolicyEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'sum_insured'.toString() == column.value">
          #{item.sumInsured,jdbcType=VARCHAR}
        </if>
        <if test="'unused_sum_insured'.toString() == column.value">
          #{item.unusedSumInsured,jdbcType=VARCHAR}
        </if>
        <if test="'reserve_amount_curr'.toString() == column.value">
          #{item.reserveAmountCurr,jdbcType=VARCHAR}
        </if>
        <if test="'reserve_amount'.toString() == column.value">
          #{item.reserveAmount,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount_curr'.toString() == column.value">
          #{item.paidAmountCurr,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount'.toString() == column.value">
          #{item.paidAmount,jdbcType=VARCHAR}
        </if>
        <if test="'derogation_amount'.toString() == column.value">
          #{item.derogationAmount,jdbcType=VARCHAR}
        </if>
        <if test="'deductible'.toString() == column.value">
          #{item.deductible,jdbcType=VARCHAR}
        </if>
        <if test="'out_deductible'.toString() == column.value">
          #{item.outDeductible,jdbcType=VARCHAR}
        </if>
        <if test="'ordinary_deductible'.toString() == column.value">
          #{item.ordinaryDeductible,jdbcType=VARCHAR}
        </if>
        <if test="'ordinary_out_deductible'.toString() == column.value">
          #{item.ordinaryOutDeductible,jdbcType=VARCHAR}
        </if>
        <if test="'deduct_deductible'.toString() == column.value">
          #{item.deductDeductible,jdbcType=VARCHAR}
        </if>
        <if test="'deduct_out_deductible'.toString() == column.value">
          #{item.deductOutDeductible,jdbcType=VARCHAR}
        </if>
        <if test="'ordinary_deduct_deductible'.toString() == column.value">
          #{item.ordinaryDeductDeductible,jdbcType=VARCHAR}
        </if>
        <if test="'ordinary_deduct_out_deductible'.toString() == column.value">
          #{item.ordinaryDeductOutDeductible,jdbcType=VARCHAR}
        </if>
        <if test="'settlement_type'.toString() == column.value">
          #{item.settlementType,jdbcType=VARCHAR}
        </if>
        <if test="'cumulative_paid_supply_days'.toString() == column.value">
          #{item.cumulativePaidSupplyDays,jdbcType=VARCHAR}
        </if>
        <if test="'medical_billing_supply_days_count'.toString() == column.value">
          #{item.medicalBillingSupplyDaysCount,jdbcType=VARCHAR}
        </if>
        <if test="'treatment_count'.toString() == column.value">
          #{item.treatmentCount,jdbcType=VARCHAR}
        </if>
        <if test="'settlement_result'.toString() == column.value">
          #{item.settlementResult,jdbcType=VARCHAR}
        </if>
        <if test="'audit_result_show'.toString() == column.value">
          #{item.auditResultShow,jdbcType=VARCHAR}
        </if>
        <if test="'refusal_reason'.toString() == column.value">
          #{item.refusalReason,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'zero_paid_reason'.toString() == column.value">
          #{item.zeroPaidReason,jdbcType=VARCHAR}
        </if>
        <if test="'expense_reserve_amount'.toString() == column.value">
          #{item.expenseReserveAmount,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>