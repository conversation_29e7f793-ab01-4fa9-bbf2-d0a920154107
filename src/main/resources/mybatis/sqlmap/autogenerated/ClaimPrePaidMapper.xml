<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimPrePaidMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimPrePaidDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dispatcher_no" jdbcType="VARCHAR" property="dispatcherNo" />
    <result column="indemnity_type" jdbcType="TINYINT" property="indemnityType" />
    <result column="liability_code" jdbcType="VARCHAR" property="liabilityCode" />
    <result column="liability_name" jdbcType="VARCHAR" property="liabilityName" />
    <result column="indemnity_amount" jdbcType="VARCHAR" property="indemnityAmount" />
    <result column="account_type" jdbcType="TINYINT" property="accountType" />
    <result column="payee_bank" jdbcType="VARCHAR" property="payeeBank" />
    <result column="payee_bank_name" jdbcType="VARCHAR" property="payeeBankName" />
    <result column="payee_account" jdbcType="VARCHAR" property="payeeAccount" />
    <result column="payee_name" jdbcType="VARCHAR" property="payeeName" />
    <result column="payee_bank_area" jdbcType="VARCHAR" property="payeeBankArea" />
    <result column="payee_bank_code" jdbcType="VARCHAR" property="payeeBankCode" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="batch_claim_bill_no" jdbcType="VARCHAR" property="batchClaimBillNo" />
    <result column="product_policy_id" jdbcType="BIGINT" property="productPolicyId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="liability_id" jdbcType="BIGINT" property="liabilityId" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="policy_id" jdbcType="BIGINT" property="policyId" />
    <result column="indemnity_amount_curr" jdbcType="VARCHAR" property="indemnityAmountCurr" />
    <result column="payment_way" jdbcType="INTEGER" property="paymentWay" />
    <result column="scope" jdbcType="VARCHAR" property="scope" />
    <result column="application_desc" jdbcType="VARCHAR" property="applicationDesc" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_paid" jdbcType="CHAR" property="isPaid" />
    <result column="settlement_id" jdbcType="VARCHAR" property="settlementId" />
    <result column="payeecert_type" jdbcType="VARCHAR" property="payeecertType" />
    <result column="payeecert_no" jdbcType="VARCHAR" property="payeecertNo" />
    <result column="audit_status" jdbcType="VARCHAR" property="auditStatus" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="bank_info_id" jdbcType="BIGINT" property="bankInfoId" />
    <result column="claim_status" jdbcType="TINYINT" property="claimStatus" />
    <result column="pre_paid_amount" jdbcType="VARCHAR" property="prePaidAmount" />
    <result column="pre_paid_reason" jdbcType="VARCHAR" property="prePaidReason" />
    <result column="audit_no_pass_reason" jdbcType="VARCHAR" property="auditNoPassReason" />
    <result column="is_offline_compensation" jdbcType="CHAR" property="isOfflineCompensation" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="is_authorized_enterprise" jdbcType="VARCHAR" property="isAuthorizedEnterprise" />
    <result column="claim_no" jdbcType="VARCHAR" property="claimNo" />
    <result column="uniq_id" jdbcType="VARCHAR" property="uniqId" />
    <result column="authorized_use_id" jdbcType="VARCHAR" property="authorizedUseId" />
    <result column="authorized_phone" jdbcType="VARCHAR" property="authorizedPhone" />
    <result column="authorized_certi_no" jdbcType="VARCHAR" property="authorizedCertiNo" />
    <result column="hospital_id" jdbcType="VARCHAR" property="hospitalId" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
    <result column="pay_no" jdbcType="VARCHAR" property="payNo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dispatcher_no, indemnity_type, liability_code, liability_name, indemnity_amount, 
    account_type, payee_bank, payee_bank_name, payee_account, payee_name, payee_bank_area, 
    payee_bank_code, report_no, batch_claim_bill_no, product_policy_id, product_id, product_code, 
    product_name, liability_id, policy_no, policy_id, indemnity_amount_curr, payment_way, 
    scope, application_desc, remark, is_paid, settlement_id, payeecert_type, payeecert_no, 
    audit_status, is_deleted, gmt_created, gmt_modified, creator, modifier, bank_info_id, 
    claim_status, pre_paid_amount, pre_paid_reason, audit_no_pass_reason, is_offline_compensation, 
    extra_info, is_authorized_enterprise, claim_no, uniq_id, authorized_use_id, authorized_phone, 
    authorized_certi_no, hospital_id, hospital_name, pay_no
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPrePaidExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_claim_pre_paid_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_claim_pre_paid_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimPrePaidDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cargo_claim_pre_paid_info (dispatcher_no, indemnity_type, liability_code, 
      liability_name, indemnity_amount, account_type, 
      payee_bank, payee_bank_name, payee_account, 
      payee_name, payee_bank_area, payee_bank_code, 
      report_no, batch_claim_bill_no, product_policy_id, 
      product_id, product_code, product_name, 
      liability_id, policy_no, policy_id, 
      indemnity_amount_curr, payment_way, scope, 
      application_desc, remark, is_paid, 
      settlement_id, payeecert_type, payeecert_no, 
      audit_status, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, 
      bank_info_id, claim_status, pre_paid_amount, 
      pre_paid_reason, audit_no_pass_reason, is_offline_compensation, 
      extra_info, is_authorized_enterprise, claim_no, 
      uniq_id, authorized_use_id, authorized_phone, 
      authorized_certi_no, hospital_id, hospital_name, 
      pay_no)
    values (#{dispatcherNo,jdbcType=VARCHAR}, #{indemnityType,jdbcType=TINYINT}, #{liabilityCode,jdbcType=VARCHAR}, 
      #{liabilityName,jdbcType=VARCHAR}, #{indemnityAmount,jdbcType=VARCHAR}, #{accountType,jdbcType=TINYINT}, 
      #{payeeBank,jdbcType=VARCHAR}, #{payeeBankName,jdbcType=VARCHAR}, #{payeeAccount,jdbcType=VARCHAR}, 
      #{payeeName,jdbcType=VARCHAR}, #{payeeBankArea,jdbcType=VARCHAR}, #{payeeBankCode,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{batchClaimBillNo,jdbcType=VARCHAR}, #{productPolicyId,jdbcType=BIGINT}, 
      #{productId,jdbcType=BIGINT}, #{productCode,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, 
      #{liabilityId,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, #{policyId,jdbcType=BIGINT}, 
      #{indemnityAmountCurr,jdbcType=VARCHAR}, #{paymentWay,jdbcType=INTEGER}, #{scope,jdbcType=VARCHAR}, 
      #{applicationDesc,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isPaid,jdbcType=CHAR}, 
      #{settlementId,jdbcType=VARCHAR}, #{payeecertType,jdbcType=VARCHAR}, #{payeecertNo,jdbcType=VARCHAR}, 
      #{auditStatus,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{bankInfoId,jdbcType=BIGINT}, #{claimStatus,jdbcType=TINYINT}, #{prePaidAmount,jdbcType=VARCHAR}, 
      #{prePaidReason,jdbcType=VARCHAR}, #{auditNoPassReason,jdbcType=VARCHAR}, #{isOfflineCompensation,jdbcType=CHAR}, 
      #{extraInfo,jdbcType=VARCHAR}, #{isAuthorizedEnterprise,jdbcType=VARCHAR}, #{claimNo,jdbcType=VARCHAR}, 
      #{uniqId,jdbcType=VARCHAR}, #{authorizedUseId,jdbcType=VARCHAR}, #{authorizedPhone,jdbcType=VARCHAR}, 
      #{authorizedCertiNo,jdbcType=VARCHAR}, #{hospitalId,jdbcType=VARCHAR}, #{hospitalName,jdbcType=VARCHAR}, 
      #{payNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPrePaidDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cargo_claim_pre_paid_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dispatcherNo != null">
        dispatcher_no,
      </if>
      <if test="indemnityType != null">
        indemnity_type,
      </if>
      <if test="liabilityCode != null">
        liability_code,
      </if>
      <if test="liabilityName != null">
        liability_name,
      </if>
      <if test="indemnityAmount != null">
        indemnity_amount,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="payeeBank != null">
        payee_bank,
      </if>
      <if test="payeeBankName != null">
        payee_bank_name,
      </if>
      <if test="payeeAccount != null">
        payee_account,
      </if>
      <if test="payeeName != null">
        payee_name,
      </if>
      <if test="payeeBankArea != null">
        payee_bank_area,
      </if>
      <if test="payeeBankCode != null">
        payee_bank_code,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no,
      </if>
      <if test="productPolicyId != null">
        product_policy_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productCode != null">
        product_code,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="liabilityId != null">
        liability_id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="policyId != null">
        policy_id,
      </if>
      <if test="indemnityAmountCurr != null">
        indemnity_amount_curr,
      </if>
      <if test="paymentWay != null">
        payment_way,
      </if>
      <if test="scope != null">
        scope,
      </if>
      <if test="applicationDesc != null">
        application_desc,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isPaid != null">
        is_paid,
      </if>
      <if test="settlementId != null">
        settlement_id,
      </if>
      <if test="payeecertType != null">
        payeecert_type,
      </if>
      <if test="payeecertNo != null">
        payeecert_no,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="bankInfoId != null">
        bank_info_id,
      </if>
      <if test="claimStatus != null">
        claim_status,
      </if>
      <if test="prePaidAmount != null">
        pre_paid_amount,
      </if>
      <if test="prePaidReason != null">
        pre_paid_reason,
      </if>
      <if test="auditNoPassReason != null">
        audit_no_pass_reason,
      </if>
      <if test="isOfflineCompensation != null">
        is_offline_compensation,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="isAuthorizedEnterprise != null">
        is_authorized_enterprise,
      </if>
      <if test="claimNo != null">
        claim_no,
      </if>
      <if test="uniqId != null">
        uniq_id,
      </if>
      <if test="authorizedUseId != null">
        authorized_use_id,
      </if>
      <if test="authorizedPhone != null">
        authorized_phone,
      </if>
      <if test="authorizedCertiNo != null">
        authorized_certi_no,
      </if>
      <if test="hospitalId != null">
        hospital_id,
      </if>
      <if test="hospitalName != null">
        hospital_name,
      </if>
      <if test="payNo != null">
        pay_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dispatcherNo != null">
        #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="indemnityType != null">
        #{indemnityType,jdbcType=TINYINT},
      </if>
      <if test="liabilityCode != null">
        #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="liabilityName != null">
        #{liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="indemnityAmount != null">
        #{indemnityAmount,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=TINYINT},
      </if>
      <if test="payeeBank != null">
        #{payeeBank,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankName != null">
        #{payeeBankName,jdbcType=VARCHAR},
      </if>
      <if test="payeeAccount != null">
        #{payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankArea != null">
        #{payeeBankArea,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankCode != null">
        #{payeeBankCode,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillNo != null">
        #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="productPolicyId != null">
        #{productPolicyId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityId != null">
        #{liabilityId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        #{policyId,jdbcType=BIGINT},
      </if>
      <if test="indemnityAmountCurr != null">
        #{indemnityAmountCurr,jdbcType=VARCHAR},
      </if>
      <if test="paymentWay != null">
        #{paymentWay,jdbcType=INTEGER},
      </if>
      <if test="scope != null">
        #{scope,jdbcType=VARCHAR},
      </if>
      <if test="applicationDesc != null">
        #{applicationDesc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isPaid != null">
        #{isPaid,jdbcType=CHAR},
      </if>
      <if test="settlementId != null">
        #{settlementId,jdbcType=VARCHAR},
      </if>
      <if test="payeecertType != null">
        #{payeecertType,jdbcType=VARCHAR},
      </if>
      <if test="payeecertNo != null">
        #{payeecertNo,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="bankInfoId != null">
        #{bankInfoId,jdbcType=BIGINT},
      </if>
      <if test="claimStatus != null">
        #{claimStatus,jdbcType=TINYINT},
      </if>
      <if test="prePaidAmount != null">
        #{prePaidAmount,jdbcType=VARCHAR},
      </if>
      <if test="prePaidReason != null">
        #{prePaidReason,jdbcType=VARCHAR},
      </if>
      <if test="auditNoPassReason != null">
        #{auditNoPassReason,jdbcType=VARCHAR},
      </if>
      <if test="isOfflineCompensation != null">
        #{isOfflineCompensation,jdbcType=CHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="isAuthorizedEnterprise != null">
        #{isAuthorizedEnterprise,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="uniqId != null">
        #{uniqId,jdbcType=VARCHAR},
      </if>
      <if test="authorizedUseId != null">
        #{authorizedUseId,jdbcType=VARCHAR},
      </if>
      <if test="authorizedPhone != null">
        #{authorizedPhone,jdbcType=VARCHAR},
      </if>
      <if test="authorizedCertiNo != null">
        #{authorizedCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="hospitalId != null">
        #{hospitalId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="payNo != null">
        #{payNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPrePaidExample" resultType="java.lang.Long">
    select count(*) from cargo_claim_pre_paid_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_claim_pre_paid_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dispatcherNo != null">
        dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="record.indemnityType != null">
        indemnity_type = #{record.indemnityType,jdbcType=TINYINT},
      </if>
      <if test="record.liabilityCode != null">
        liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityName != null">
        liability_name = #{record.liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="record.indemnityAmount != null">
        indemnity_amount = #{record.indemnityAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=TINYINT},
      </if>
      <if test="record.payeeBank != null">
        payee_bank = #{record.payeeBank,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeBankName != null">
        payee_bank_name = #{record.payeeBankName,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeAccount != null">
        payee_account = #{record.payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeName != null">
        payee_name = #{record.payeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeBankArea != null">
        payee_bank_area = #{record.payeeBankArea,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeBankCode != null">
        payee_bank_code = #{record.payeeBankCode,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchClaimBillNo != null">
        batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.productPolicyId != null">
        product_policy_id = #{record.productPolicyId,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.productCode != null">
        product_code = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityId != null">
        liability_id = #{record.liabilityId,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyId != null">
        policy_id = #{record.policyId,jdbcType=BIGINT},
      </if>
      <if test="record.indemnityAmountCurr != null">
        indemnity_amount_curr = #{record.indemnityAmountCurr,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentWay != null">
        payment_way = #{record.paymentWay,jdbcType=INTEGER},
      </if>
      <if test="record.scope != null">
        scope = #{record.scope,jdbcType=VARCHAR},
      </if>
      <if test="record.applicationDesc != null">
        application_desc = #{record.applicationDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isPaid != null">
        is_paid = #{record.isPaid,jdbcType=CHAR},
      </if>
      <if test="record.settlementId != null">
        settlement_id = #{record.settlementId,jdbcType=VARCHAR},
      </if>
      <if test="record.payeecertType != null">
        payeecert_type = #{record.payeecertType,jdbcType=VARCHAR},
      </if>
      <if test="record.payeecertNo != null">
        payeecert_no = #{record.payeecertNo,jdbcType=VARCHAR},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.bankInfoId != null">
        bank_info_id = #{record.bankInfoId,jdbcType=BIGINT},
      </if>
      <if test="record.claimStatus != null">
        claim_status = #{record.claimStatus,jdbcType=TINYINT},
      </if>
      <if test="record.prePaidAmount != null">
        pre_paid_amount = #{record.prePaidAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.prePaidReason != null">
        pre_paid_reason = #{record.prePaidReason,jdbcType=VARCHAR},
      </if>
      <if test="record.auditNoPassReason != null">
        audit_no_pass_reason = #{record.auditNoPassReason,jdbcType=VARCHAR},
      </if>
      <if test="record.isOfflineCompensation != null">
        is_offline_compensation = #{record.isOfflineCompensation,jdbcType=CHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.isAuthorizedEnterprise != null">
        is_authorized_enterprise = #{record.isAuthorizedEnterprise,jdbcType=VARCHAR},
      </if>
      <if test="record.claimNo != null">
        claim_no = #{record.claimNo,jdbcType=VARCHAR},
      </if>
      <if test="record.uniqId != null">
        uniq_id = #{record.uniqId,jdbcType=VARCHAR},
      </if>
      <if test="record.authorizedUseId != null">
        authorized_use_id = #{record.authorizedUseId,jdbcType=VARCHAR},
      </if>
      <if test="record.authorizedPhone != null">
        authorized_phone = #{record.authorizedPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.authorizedCertiNo != null">
        authorized_certi_no = #{record.authorizedCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalId != null">
        hospital_id = #{record.hospitalId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalName != null">
        hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="record.payNo != null">
        pay_no = #{record.payNo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_claim_pre_paid_info
    set id = #{record.id,jdbcType=BIGINT},
      dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      indemnity_type = #{record.indemnityType,jdbcType=TINYINT},
      liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      liability_name = #{record.liabilityName,jdbcType=VARCHAR},
      indemnity_amount = #{record.indemnityAmount,jdbcType=VARCHAR},
      account_type = #{record.accountType,jdbcType=TINYINT},
      payee_bank = #{record.payeeBank,jdbcType=VARCHAR},
      payee_bank_name = #{record.payeeBankName,jdbcType=VARCHAR},
      payee_account = #{record.payeeAccount,jdbcType=VARCHAR},
      payee_name = #{record.payeeName,jdbcType=VARCHAR},
      payee_bank_area = #{record.payeeBankArea,jdbcType=VARCHAR},
      payee_bank_code = #{record.payeeBankCode,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      product_policy_id = #{record.productPolicyId,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=BIGINT},
      product_code = #{record.productCode,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      liability_id = #{record.liabilityId,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      policy_id = #{record.policyId,jdbcType=BIGINT},
      indemnity_amount_curr = #{record.indemnityAmountCurr,jdbcType=VARCHAR},
      payment_way = #{record.paymentWay,jdbcType=INTEGER},
      scope = #{record.scope,jdbcType=VARCHAR},
      application_desc = #{record.applicationDesc,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_paid = #{record.isPaid,jdbcType=CHAR},
      settlement_id = #{record.settlementId,jdbcType=VARCHAR},
      payeecert_type = #{record.payeecertType,jdbcType=VARCHAR},
      payeecert_no = #{record.payeecertNo,jdbcType=VARCHAR},
      audit_status = #{record.auditStatus,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      bank_info_id = #{record.bankInfoId,jdbcType=BIGINT},
      claim_status = #{record.claimStatus,jdbcType=TINYINT},
      pre_paid_amount = #{record.prePaidAmount,jdbcType=VARCHAR},
      pre_paid_reason = #{record.prePaidReason,jdbcType=VARCHAR},
      audit_no_pass_reason = #{record.auditNoPassReason,jdbcType=VARCHAR},
      is_offline_compensation = #{record.isOfflineCompensation,jdbcType=CHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      is_authorized_enterprise = #{record.isAuthorizedEnterprise,jdbcType=VARCHAR},
      claim_no = #{record.claimNo,jdbcType=VARCHAR},
      uniq_id = #{record.uniqId,jdbcType=VARCHAR},
      authorized_use_id = #{record.authorizedUseId,jdbcType=VARCHAR},
      authorized_phone = #{record.authorizedPhone,jdbcType=VARCHAR},
      authorized_certi_no = #{record.authorizedCertiNo,jdbcType=VARCHAR},
      hospital_id = #{record.hospitalId,jdbcType=VARCHAR},
      hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      pay_no = #{record.payNo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPrePaidDO">
    update cargo_claim_pre_paid_info
    <set>
      <if test="dispatcherNo != null">
        dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="indemnityType != null">
        indemnity_type = #{indemnityType,jdbcType=TINYINT},
      </if>
      <if test="liabilityCode != null">
        liability_code = #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="liabilityName != null">
        liability_name = #{liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="indemnityAmount != null">
        indemnity_amount = #{indemnityAmount,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=TINYINT},
      </if>
      <if test="payeeBank != null">
        payee_bank = #{payeeBank,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankName != null">
        payee_bank_name = #{payeeBankName,jdbcType=VARCHAR},
      </if>
      <if test="payeeAccount != null">
        payee_account = #{payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        payee_name = #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankArea != null">
        payee_bank_area = #{payeeBankArea,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankCode != null">
        payee_bank_code = #{payeeBankCode,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="productPolicyId != null">
        product_policy_id = #{productPolicyId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="productCode != null">
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityId != null">
        liability_id = #{liabilityId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        policy_id = #{policyId,jdbcType=BIGINT},
      </if>
      <if test="indemnityAmountCurr != null">
        indemnity_amount_curr = #{indemnityAmountCurr,jdbcType=VARCHAR},
      </if>
      <if test="paymentWay != null">
        payment_way = #{paymentWay,jdbcType=INTEGER},
      </if>
      <if test="scope != null">
        scope = #{scope,jdbcType=VARCHAR},
      </if>
      <if test="applicationDesc != null">
        application_desc = #{applicationDesc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isPaid != null">
        is_paid = #{isPaid,jdbcType=CHAR},
      </if>
      <if test="settlementId != null">
        settlement_id = #{settlementId,jdbcType=VARCHAR},
      </if>
      <if test="payeecertType != null">
        payeecert_type = #{payeecertType,jdbcType=VARCHAR},
      </if>
      <if test="payeecertNo != null">
        payeecert_no = #{payeecertNo,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="bankInfoId != null">
        bank_info_id = #{bankInfoId,jdbcType=BIGINT},
      </if>
      <if test="claimStatus != null">
        claim_status = #{claimStatus,jdbcType=TINYINT},
      </if>
      <if test="prePaidAmount != null">
        pre_paid_amount = #{prePaidAmount,jdbcType=VARCHAR},
      </if>
      <if test="prePaidReason != null">
        pre_paid_reason = #{prePaidReason,jdbcType=VARCHAR},
      </if>
      <if test="auditNoPassReason != null">
        audit_no_pass_reason = #{auditNoPassReason,jdbcType=VARCHAR},
      </if>
      <if test="isOfflineCompensation != null">
        is_offline_compensation = #{isOfflineCompensation,jdbcType=CHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="isAuthorizedEnterprise != null">
        is_authorized_enterprise = #{isAuthorizedEnterprise,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        claim_no = #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="uniqId != null">
        uniq_id = #{uniqId,jdbcType=VARCHAR},
      </if>
      <if test="authorizedUseId != null">
        authorized_use_id = #{authorizedUseId,jdbcType=VARCHAR},
      </if>
      <if test="authorizedPhone != null">
        authorized_phone = #{authorizedPhone,jdbcType=VARCHAR},
      </if>
      <if test="authorizedCertiNo != null">
        authorized_certi_no = #{authorizedCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        hospital_name = #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="payNo != null">
        pay_no = #{payNo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimPrePaidDO">
    update cargo_claim_pre_paid_info
    set dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      indemnity_type = #{indemnityType,jdbcType=TINYINT},
      liability_code = #{liabilityCode,jdbcType=VARCHAR},
      liability_name = #{liabilityName,jdbcType=VARCHAR},
      indemnity_amount = #{indemnityAmount,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=TINYINT},
      payee_bank = #{payeeBank,jdbcType=VARCHAR},
      payee_bank_name = #{payeeBankName,jdbcType=VARCHAR},
      payee_account = #{payeeAccount,jdbcType=VARCHAR},
      payee_name = #{payeeName,jdbcType=VARCHAR},
      payee_bank_area = #{payeeBankArea,jdbcType=VARCHAR},
      payee_bank_code = #{payeeBankCode,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      product_policy_id = #{productPolicyId,jdbcType=BIGINT},
      product_id = #{productId,jdbcType=BIGINT},
      product_code = #{productCode,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      liability_id = #{liabilityId,jdbcType=BIGINT},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      policy_id = #{policyId,jdbcType=BIGINT},
      indemnity_amount_curr = #{indemnityAmountCurr,jdbcType=VARCHAR},
      payment_way = #{paymentWay,jdbcType=INTEGER},
      scope = #{scope,jdbcType=VARCHAR},
      application_desc = #{applicationDesc,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_paid = #{isPaid,jdbcType=CHAR},
      settlement_id = #{settlementId,jdbcType=VARCHAR},
      payeecert_type = #{payeecertType,jdbcType=VARCHAR},
      payeecert_no = #{payeecertNo,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      bank_info_id = #{bankInfoId,jdbcType=BIGINT},
      claim_status = #{claimStatus,jdbcType=TINYINT},
      pre_paid_amount = #{prePaidAmount,jdbcType=VARCHAR},
      pre_paid_reason = #{prePaidReason,jdbcType=VARCHAR},
      audit_no_pass_reason = #{auditNoPassReason,jdbcType=VARCHAR},
      is_offline_compensation = #{isOfflineCompensation,jdbcType=CHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      is_authorized_enterprise = #{isAuthorizedEnterprise,jdbcType=VARCHAR},
      claim_no = #{claimNo,jdbcType=VARCHAR},
      uniq_id = #{uniqId,jdbcType=VARCHAR},
      authorized_use_id = #{authorizedUseId,jdbcType=VARCHAR},
      authorized_phone = #{authorizedPhone,jdbcType=VARCHAR},
      authorized_certi_no = #{authorizedCertiNo,jdbcType=VARCHAR},
      hospital_id = #{hospitalId,jdbcType=VARCHAR},
      hospital_name = #{hospitalName,jdbcType=VARCHAR},
      pay_no = #{payNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into cargo_claim_pre_paid_info
    (dispatcher_no, indemnity_type, liability_code, liability_name, indemnity_amount, 
      account_type, payee_bank, payee_bank_name, payee_account, payee_name, payee_bank_area, 
      payee_bank_code, report_no, batch_claim_bill_no, product_policy_id, product_id, 
      product_code, product_name, liability_id, policy_no, policy_id, indemnity_amount_curr, 
      payment_way, scope, application_desc, remark, is_paid, settlement_id, payeecert_type, 
      payeecert_no, audit_status, is_deleted, gmt_created, gmt_modified, creator, modifier, 
      bank_info_id, claim_status, pre_paid_amount, pre_paid_reason, audit_no_pass_reason, 
      is_offline_compensation, extra_info, is_authorized_enterprise, claim_no, uniq_id, 
      authorized_use_id, authorized_phone, authorized_certi_no, hospital_id, hospital_name, 
      pay_no)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.dispatcherNo,jdbcType=VARCHAR}, #{item.indemnityType,jdbcType=TINYINT}, #{item.liabilityCode,jdbcType=VARCHAR}, 
        #{item.liabilityName,jdbcType=VARCHAR}, #{item.indemnityAmount,jdbcType=VARCHAR}, 
        #{item.accountType,jdbcType=TINYINT}, #{item.payeeBank,jdbcType=VARCHAR}, #{item.payeeBankName,jdbcType=VARCHAR}, 
        #{item.payeeAccount,jdbcType=VARCHAR}, #{item.payeeName,jdbcType=VARCHAR}, #{item.payeeBankArea,jdbcType=VARCHAR}, 
        #{item.payeeBankCode,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.batchClaimBillNo,jdbcType=VARCHAR}, 
        #{item.productPolicyId,jdbcType=BIGINT}, #{item.productId,jdbcType=BIGINT}, #{item.productCode,jdbcType=VARCHAR}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.liabilityId,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.policyId,jdbcType=BIGINT}, #{item.indemnityAmountCurr,jdbcType=VARCHAR}, 
        #{item.paymentWay,jdbcType=INTEGER}, #{item.scope,jdbcType=VARCHAR}, #{item.applicationDesc,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.isPaid,jdbcType=CHAR}, #{item.settlementId,jdbcType=VARCHAR}, 
        #{item.payeecertType,jdbcType=VARCHAR}, #{item.payeecertNo,jdbcType=VARCHAR}, #{item.auditStatus,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.bankInfoId,jdbcType=BIGINT}, 
        #{item.claimStatus,jdbcType=TINYINT}, #{item.prePaidAmount,jdbcType=VARCHAR}, #{item.prePaidReason,jdbcType=VARCHAR}, 
        #{item.auditNoPassReason,jdbcType=VARCHAR}, #{item.isOfflineCompensation,jdbcType=CHAR}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.isAuthorizedEnterprise,jdbcType=VARCHAR}, 
        #{item.claimNo,jdbcType=VARCHAR}, #{item.uniqId,jdbcType=VARCHAR}, #{item.authorizedUseId,jdbcType=VARCHAR}, 
        #{item.authorizedPhone,jdbcType=VARCHAR}, #{item.authorizedCertiNo,jdbcType=VARCHAR}, 
        #{item.hospitalId,jdbcType=VARCHAR}, #{item.hospitalName,jdbcType=VARCHAR}, #{item.payNo,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into cargo_claim_pre_paid_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'dispatcher_no'.toString() == column.value">
          #{item.dispatcherNo,jdbcType=VARCHAR}
        </if>
        <if test="'indemnity_type'.toString() == column.value">
          #{item.indemnityType,jdbcType=TINYINT}
        </if>
        <if test="'liability_code'.toString() == column.value">
          #{item.liabilityCode,jdbcType=VARCHAR}
        </if>
        <if test="'liability_name'.toString() == column.value">
          #{item.liabilityName,jdbcType=VARCHAR}
        </if>
        <if test="'indemnity_amount'.toString() == column.value">
          #{item.indemnityAmount,jdbcType=VARCHAR}
        </if>
        <if test="'account_type'.toString() == column.value">
          #{item.accountType,jdbcType=TINYINT}
        </if>
        <if test="'payee_bank'.toString() == column.value">
          #{item.payeeBank,jdbcType=VARCHAR}
        </if>
        <if test="'payee_bank_name'.toString() == column.value">
          #{item.payeeBankName,jdbcType=VARCHAR}
        </if>
        <if test="'payee_account'.toString() == column.value">
          #{item.payeeAccount,jdbcType=VARCHAR}
        </if>
        <if test="'payee_name'.toString() == column.value">
          #{item.payeeName,jdbcType=VARCHAR}
        </if>
        <if test="'payee_bank_area'.toString() == column.value">
          #{item.payeeBankArea,jdbcType=VARCHAR}
        </if>
        <if test="'payee_bank_code'.toString() == column.value">
          #{item.payeeBankCode,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'batch_claim_bill_no'.toString() == column.value">
          #{item.batchClaimBillNo,jdbcType=VARCHAR}
        </if>
        <if test="'product_policy_id'.toString() == column.value">
          #{item.productPolicyId,jdbcType=BIGINT}
        </if>
        <if test="'product_id'.toString() == column.value">
          #{item.productId,jdbcType=BIGINT}
        </if>
        <if test="'product_code'.toString() == column.value">
          #{item.productCode,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'liability_id'.toString() == column.value">
          #{item.liabilityId,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_id'.toString() == column.value">
          #{item.policyId,jdbcType=BIGINT}
        </if>
        <if test="'indemnity_amount_curr'.toString() == column.value">
          #{item.indemnityAmountCurr,jdbcType=VARCHAR}
        </if>
        <if test="'payment_way'.toString() == column.value">
          #{item.paymentWay,jdbcType=INTEGER}
        </if>
        <if test="'scope'.toString() == column.value">
          #{item.scope,jdbcType=VARCHAR}
        </if>
        <if test="'application_desc'.toString() == column.value">
          #{item.applicationDesc,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_paid'.toString() == column.value">
          #{item.isPaid,jdbcType=CHAR}
        </if>
        <if test="'settlement_id'.toString() == column.value">
          #{item.settlementId,jdbcType=VARCHAR}
        </if>
        <if test="'payeecert_type'.toString() == column.value">
          #{item.payeecertType,jdbcType=VARCHAR}
        </if>
        <if test="'payeecert_no'.toString() == column.value">
          #{item.payeecertNo,jdbcType=VARCHAR}
        </if>
        <if test="'audit_status'.toString() == column.value">
          #{item.auditStatus,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'bank_info_id'.toString() == column.value">
          #{item.bankInfoId,jdbcType=BIGINT}
        </if>
        <if test="'claim_status'.toString() == column.value">
          #{item.claimStatus,jdbcType=TINYINT}
        </if>
        <if test="'pre_paid_amount'.toString() == column.value">
          #{item.prePaidAmount,jdbcType=VARCHAR}
        </if>
        <if test="'pre_paid_reason'.toString() == column.value">
          #{item.prePaidReason,jdbcType=VARCHAR}
        </if>
        <if test="'audit_no_pass_reason'.toString() == column.value">
          #{item.auditNoPassReason,jdbcType=VARCHAR}
        </if>
        <if test="'is_offline_compensation'.toString() == column.value">
          #{item.isOfflineCompensation,jdbcType=CHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'is_authorized_enterprise'.toString() == column.value">
          #{item.isAuthorizedEnterprise,jdbcType=VARCHAR}
        </if>
        <if test="'claim_no'.toString() == column.value">
          #{item.claimNo,jdbcType=VARCHAR}
        </if>
        <if test="'uniq_id'.toString() == column.value">
          #{item.uniqId,jdbcType=VARCHAR}
        </if>
        <if test="'authorized_use_id'.toString() == column.value">
          #{item.authorizedUseId,jdbcType=VARCHAR}
        </if>
        <if test="'authorized_phone'.toString() == column.value">
          #{item.authorizedPhone,jdbcType=VARCHAR}
        </if>
        <if test="'authorized_certi_no'.toString() == column.value">
          #{item.authorizedCertiNo,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_id'.toString() == column.value">
          #{item.hospitalId,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_name'.toString() == column.value">
          #{item.hospitalName,jdbcType=VARCHAR}
        </if>
        <if test="'pay_no'.toString() == column.value">
          #{item.payNo,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>