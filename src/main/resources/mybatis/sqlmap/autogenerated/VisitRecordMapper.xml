<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.VisitRecordMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.VisitRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mobile_phone" jdbcType="VARCHAR" property="mobilePhone" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="is_attachment" jdbcType="CHAR" property="isAttachment" />
    <result column="is_inquisition" jdbcType="CHAR" property="isInquisition" />
    <result column="is_negotiation" jdbcType="CHAR" property="isNegotiation" />
    <result column="out_biz_no" jdbcType="VARCHAR" property="outBizNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, mobile_phone, report_no, is_attachment, is_inquisition, is_negotiation, out_biz_no, 
    batch_no, is_deleted, gmt_modified, gmt_created, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.VisitRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_visit_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_visit_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.VisitRecordDO">
    insert into claim_visit_record (id, mobile_phone, report_no, 
      is_attachment, is_inquisition, is_negotiation, 
      out_biz_no, batch_no, is_deleted, 
      gmt_modified, gmt_created, creator, 
      modifier)
    values (#{id,jdbcType=BIGINT}, #{mobilePhone,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{isAttachment,jdbcType=CHAR}, #{isInquisition,jdbcType=CHAR}, #{isNegotiation,jdbcType=CHAR}, 
      #{outBizNo,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.VisitRecordDO">
    insert into claim_visit_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="mobilePhone != null">
        mobile_phone,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="isAttachment != null">
        is_attachment,
      </if>
      <if test="isInquisition != null">
        is_inquisition,
      </if>
      <if test="isNegotiation != null">
        is_negotiation,
      </if>
      <if test="outBizNo != null">
        out_biz_no,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mobilePhone != null">
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="isAttachment != null">
        #{isAttachment,jdbcType=CHAR},
      </if>
      <if test="isInquisition != null">
        #{isInquisition,jdbcType=CHAR},
      </if>
      <if test="isNegotiation != null">
        #{isNegotiation,jdbcType=CHAR},
      </if>
      <if test="outBizNo != null">
        #{outBizNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.VisitRecordExample" resultType="java.lang.Long">
    select count(*) from claim_visit_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_visit_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mobilePhone != null">
        mobile_phone = #{record.mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.isAttachment != null">
        is_attachment = #{record.isAttachment,jdbcType=CHAR},
      </if>
      <if test="record.isInquisition != null">
        is_inquisition = #{record.isInquisition,jdbcType=CHAR},
      </if>
      <if test="record.isNegotiation != null">
        is_negotiation = #{record.isNegotiation,jdbcType=CHAR},
      </if>
      <if test="record.outBizNo != null">
        out_biz_no = #{record.outBizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_visit_record
    set id = #{record.id,jdbcType=BIGINT},
      mobile_phone = #{record.mobilePhone,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      is_attachment = #{record.isAttachment,jdbcType=CHAR},
      is_inquisition = #{record.isInquisition,jdbcType=CHAR},
      is_negotiation = #{record.isNegotiation,jdbcType=CHAR},
      out_biz_no = #{record.outBizNo,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      gmt_modified = sysdate(),
    
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.VisitRecordDO">
    update claim_visit_record
    <set>
      <if test="mobilePhone != null">
        mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="isAttachment != null">
        is_attachment = #{isAttachment,jdbcType=CHAR},
      </if>
      <if test="isInquisition != null">
        is_inquisition = #{isInquisition,jdbcType=CHAR},
      </if>
      <if test="isNegotiation != null">
        is_negotiation = #{isNegotiation,jdbcType=CHAR},
      </if>
      <if test="outBizNo != null">
        out_biz_no = #{outBizNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.VisitRecordDO">
    update claim_visit_record
    set mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      is_attachment = #{isAttachment,jdbcType=CHAR},
      is_inquisition = #{isInquisition,jdbcType=CHAR},
      is_negotiation = #{isNegotiation,jdbcType=CHAR},
      out_biz_no = #{outBizNo,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      gmt_modified = sysdate(),
    
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_visit_record
    (id, mobile_phone, report_no, is_attachment, is_inquisition, is_negotiation, out_biz_no, 
      batch_no, is_deleted, gmt_modified, gmt_created, creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.mobilePhone,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.isAttachment,jdbcType=CHAR}, #{item.isInquisition,jdbcType=CHAR}, #{item.isNegotiation,jdbcType=CHAR}, 
        #{item.outBizNo,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_visit_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'mobile_phone'.toString() == column.value">
          #{item.mobilePhone,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'is_attachment'.toString() == column.value">
          #{item.isAttachment,jdbcType=CHAR}
        </if>
        <if test="'is_inquisition'.toString() == column.value">
          #{item.isInquisition,jdbcType=CHAR}
        </if>
        <if test="'is_negotiation'.toString() == column.value">
          #{item.isNegotiation,jdbcType=CHAR}
        </if>
        <if test="'out_biz_no'.toString() == column.value">
          #{item.outBizNo,jdbcType=VARCHAR}
        </if>
        <if test="'batch_no'.toString() == column.value">
          #{item.batchNo,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>