<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AirLinesCompanyMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AirLinesCompanyDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="two_bit_air_line_code" jdbcType="VARCHAR" property="twoBitAirLineCode" />
    <result column="three_bit_air_line_code" jdbcType="VARCHAR" property="threeBitAirLineCode" />
    <result column="air_line_name_ch" jdbcType="VARCHAR" property="airLineNameCh" />
    <result column="air_line_name_en" jdbcType="VARCHAR" property="airLineNameEn" />
    <result column="is_international" jdbcType="CHAR" property="isInternational" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, two_bit_air_line_code, three_bit_air_line_code, air_line_name_ch, air_line_name_en, 
    is_international, remark, is_deleted, gmt_created, gmt_modified, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AirLinesCompanyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from air_lines_company_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from air_lines_company_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AirLinesCompanyDO">
    insert into air_lines_company_info (id, two_bit_air_line_code, three_bit_air_line_code, 
      air_line_name_ch, air_line_name_en, is_international, 
      remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier
      )
    values (#{id,jdbcType=BIGINT}, #{twoBitAirLineCode,jdbcType=VARCHAR}, #{threeBitAirLineCode,jdbcType=VARCHAR}, 
      #{airLineNameCh,jdbcType=VARCHAR}, #{airLineNameEn,jdbcType=VARCHAR}, #{isInternational,jdbcType=CHAR}, 
      #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AirLinesCompanyDO">
    insert into air_lines_company_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="twoBitAirLineCode != null">
        two_bit_air_line_code,
      </if>
      <if test="threeBitAirLineCode != null">
        three_bit_air_line_code,
      </if>
      <if test="airLineNameCh != null">
        air_line_name_ch,
      </if>
      <if test="airLineNameEn != null">
        air_line_name_en,
      </if>
      <if test="isInternational != null">
        is_international,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="twoBitAirLineCode != null">
        #{twoBitAirLineCode,jdbcType=VARCHAR},
      </if>
      <if test="threeBitAirLineCode != null">
        #{threeBitAirLineCode,jdbcType=VARCHAR},
      </if>
      <if test="airLineNameCh != null">
        #{airLineNameCh,jdbcType=VARCHAR},
      </if>
      <if test="airLineNameEn != null">
        #{airLineNameEn,jdbcType=VARCHAR},
      </if>
      <if test="isInternational != null">
        #{isInternational,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AirLinesCompanyExample" resultType="java.lang.Long">
    select count(*) from air_lines_company_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update air_lines_company_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.twoBitAirLineCode != null">
        two_bit_air_line_code = #{record.twoBitAirLineCode,jdbcType=VARCHAR},
      </if>
      <if test="record.threeBitAirLineCode != null">
        three_bit_air_line_code = #{record.threeBitAirLineCode,jdbcType=VARCHAR},
      </if>
      <if test="record.airLineNameCh != null">
        air_line_name_ch = #{record.airLineNameCh,jdbcType=VARCHAR},
      </if>
      <if test="record.airLineNameEn != null">
        air_line_name_en = #{record.airLineNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.isInternational != null">
        is_international = #{record.isInternational,jdbcType=CHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update air_lines_company_info
    set id = #{record.id,jdbcType=BIGINT},
      two_bit_air_line_code = #{record.twoBitAirLineCode,jdbcType=VARCHAR},
      three_bit_air_line_code = #{record.threeBitAirLineCode,jdbcType=VARCHAR},
      air_line_name_ch = #{record.airLineNameCh,jdbcType=VARCHAR},
      air_line_name_en = #{record.airLineNameEn,jdbcType=VARCHAR},
      is_international = #{record.isInternational,jdbcType=CHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AirLinesCompanyDO">
    update air_lines_company_info
    <set>
      <if test="twoBitAirLineCode != null">
        two_bit_air_line_code = #{twoBitAirLineCode,jdbcType=VARCHAR},
      </if>
      <if test="threeBitAirLineCode != null">
        three_bit_air_line_code = #{threeBitAirLineCode,jdbcType=VARCHAR},
      </if>
      <if test="airLineNameCh != null">
        air_line_name_ch = #{airLineNameCh,jdbcType=VARCHAR},
      </if>
      <if test="airLineNameEn != null">
        air_line_name_en = #{airLineNameEn,jdbcType=VARCHAR},
      </if>
      <if test="isInternational != null">
        is_international = #{isInternational,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AirLinesCompanyDO">
    update air_lines_company_info
    set two_bit_air_line_code = #{twoBitAirLineCode,jdbcType=VARCHAR},
      three_bit_air_line_code = #{threeBitAirLineCode,jdbcType=VARCHAR},
      air_line_name_ch = #{airLineNameCh,jdbcType=VARCHAR},
      air_line_name_en = #{airLineNameEn,jdbcType=VARCHAR},
      is_international = #{isInternational,jdbcType=CHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into air_lines_company_info
    (id, two_bit_air_line_code, three_bit_air_line_code, air_line_name_ch, air_line_name_en, 
      is_international, remark, is_deleted, gmt_created, gmt_modified, creator, modifier
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.twoBitAirLineCode,jdbcType=VARCHAR}, #{item.threeBitAirLineCode,jdbcType=VARCHAR}, 
        #{item.airLineNameCh,jdbcType=VARCHAR}, #{item.airLineNameEn,jdbcType=VARCHAR}, 
        #{item.isInternational,jdbcType=CHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into air_lines_company_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'two_bit_air_line_code'.toString() == column.value">
          #{item.twoBitAirLineCode,jdbcType=VARCHAR}
        </if>
        <if test="'three_bit_air_line_code'.toString() == column.value">
          #{item.threeBitAirLineCode,jdbcType=VARCHAR}
        </if>
        <if test="'air_line_name_ch'.toString() == column.value">
          #{item.airLineNameCh,jdbcType=VARCHAR}
        </if>
        <if test="'air_line_name_en'.toString() == column.value">
          #{item.airLineNameEn,jdbcType=VARCHAR}
        </if>
        <if test="'is_international'.toString() == column.value">
          #{item.isInternational,jdbcType=CHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>