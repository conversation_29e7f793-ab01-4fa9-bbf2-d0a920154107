<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.GenericAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.GenericAttachmentDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="sub_no" jdbcType="VARCHAR" property="subNo" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="document_type" jdbcType="VARCHAR" property="documentType" />
    <result column="oss_bucket" jdbcType="VARCHAR" property="ossBucket" />
    <result column="oss_key" jdbcType="VARCHAR" property="ossKey" />
    <result column="original_url" jdbcType="VARCHAR" property="originalUrl" />
    <result column="sort" jdbcType="TINYINT" property="sort" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, type, biz_no, sub_no, file_type, document_type, oss_bucket, oss_key, original_url, 
    sort, extra_info, creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.GenericAttachmentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from generic_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from generic_attachment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.GenericAttachmentDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into generic_attachment (name, type, biz_no, 
      sub_no, file_type, document_type, 
      oss_bucket, oss_key, original_url, 
      sort, extra_info, creator, 
      modifier, gmt_created, gmt_modified, 
      is_deleted)
    values (#{name,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{bizNo,jdbcType=VARCHAR}, 
      #{subNo,jdbcType=VARCHAR}, #{fileType,jdbcType=VARCHAR}, #{documentType,jdbcType=VARCHAR}, 
      #{ossBucket,jdbcType=VARCHAR}, #{ossKey,jdbcType=VARCHAR}, #{originalUrl,jdbcType=VARCHAR}, 
      #{sort,jdbcType=TINYINT}, #{extraInfo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate(), 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.GenericAttachmentDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into generic_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="subNo != null">
        sub_no,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="documentType != null">
        document_type,
      </if>
      <if test="ossBucket != null">
        oss_bucket,
      </if>
      <if test="ossKey != null">
        oss_key,
      </if>
      <if test="originalUrl != null">
        original_url,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="subNo != null">
        #{subNo,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="documentType != null">
        #{documentType,jdbcType=VARCHAR},
      </if>
      <if test="ossBucket != null">
        #{ossBucket,jdbcType=VARCHAR},
      </if>
      <if test="ossKey != null">
        #{ossKey,jdbcType=VARCHAR},
      </if>
      <if test="originalUrl != null">
        #{originalUrl,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=TINYINT},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.GenericAttachmentExample" resultType="java.lang.Long">
    select count(*) from generic_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update generic_attachment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.bizNo != null">
        biz_no = #{record.bizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.subNo != null">
        sub_no = #{record.subNo,jdbcType=VARCHAR},
      </if>
      <if test="record.fileType != null">
        file_type = #{record.fileType,jdbcType=VARCHAR},
      </if>
      <if test="record.documentType != null">
        document_type = #{record.documentType,jdbcType=VARCHAR},
      </if>
      <if test="record.ossBucket != null">
        oss_bucket = #{record.ossBucket,jdbcType=VARCHAR},
      </if>
      <if test="record.ossKey != null">
        oss_key = #{record.ossKey,jdbcType=VARCHAR},
      </if>
      <if test="record.originalUrl != null">
        original_url = #{record.originalUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=TINYINT},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update generic_attachment
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      biz_no = #{record.bizNo,jdbcType=VARCHAR},
      sub_no = #{record.subNo,jdbcType=VARCHAR},
      file_type = #{record.fileType,jdbcType=VARCHAR},
      document_type = #{record.documentType,jdbcType=VARCHAR},
      oss_bucket = #{record.ossBucket,jdbcType=VARCHAR},
      oss_key = #{record.ossKey,jdbcType=VARCHAR},
      original_url = #{record.originalUrl,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=TINYINT},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.GenericAttachmentDO">
    update generic_attachment
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="subNo != null">
        sub_no = #{subNo,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="documentType != null">
        document_type = #{documentType,jdbcType=VARCHAR},
      </if>
      <if test="ossBucket != null">
        oss_bucket = #{ossBucket,jdbcType=VARCHAR},
      </if>
      <if test="ossKey != null">
        oss_key = #{ossKey,jdbcType=VARCHAR},
      </if>
      <if test="originalUrl != null">
        original_url = #{originalUrl,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=TINYINT},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.GenericAttachmentDO">
    update generic_attachment
    set name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      biz_no = #{bizNo,jdbcType=VARCHAR},
      sub_no = #{subNo,jdbcType=VARCHAR},
      file_type = #{fileType,jdbcType=VARCHAR},
      document_type = #{documentType,jdbcType=VARCHAR},
      oss_bucket = #{ossBucket,jdbcType=VARCHAR},
      oss_key = #{ossKey,jdbcType=VARCHAR},
      original_url = #{originalUrl,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=TINYINT},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into generic_attachment
    (name, type, biz_no, sub_no, file_type, document_type, oss_bucket, oss_key, original_url, 
      sort, extra_info, creator, modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, #{item.bizNo,jdbcType=VARCHAR}, 
        #{item.subNo,jdbcType=VARCHAR}, #{item.fileType,jdbcType=VARCHAR}, #{item.documentType,jdbcType=VARCHAR}, 
        #{item.ossBucket,jdbcType=VARCHAR}, #{item.ossKey,jdbcType=VARCHAR}, #{item.originalUrl,jdbcType=VARCHAR}, 
        #{item.sort,jdbcType=TINYINT}, #{item.extraInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into generic_attachment (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'biz_no'.toString() == column.value">
          #{item.bizNo,jdbcType=VARCHAR}
        </if>
        <if test="'sub_no'.toString() == column.value">
          #{item.subNo,jdbcType=VARCHAR}
        </if>
        <if test="'file_type'.toString() == column.value">
          #{item.fileType,jdbcType=VARCHAR}
        </if>
        <if test="'document_type'.toString() == column.value">
          #{item.documentType,jdbcType=VARCHAR}
        </if>
        <if test="'oss_bucket'.toString() == column.value">
          #{item.ossBucket,jdbcType=VARCHAR}
        </if>
        <if test="'oss_key'.toString() == column.value">
          #{item.ossKey,jdbcType=VARCHAR}
        </if>
        <if test="'original_url'.toString() == column.value">
          #{item.originalUrl,jdbcType=VARCHAR}
        </if>
        <if test="'sort'.toString() == column.value">
          #{item.sort,jdbcType=TINYINT}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>