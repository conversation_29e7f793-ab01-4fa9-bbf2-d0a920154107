<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.BClaimLogInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.BClaimLogInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="claim_batch_relation_id" jdbcType="BIGINT" property="claimBatchRelationId" />
    <result column="batch_claim_bill_id" jdbcType="BIGINT" property="batchClaimBillId" />
    <result column="batch_claim_bill_no" jdbcType="VARCHAR" property="batchClaimBillNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="claim_status" jdbcType="INTEGER" property="claimStatus" />
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="msg_content" jdbcType="VARCHAR" property="msgContent" />
    <result column="deal_result" jdbcType="INTEGER" property="dealResult" />
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_type, task_id, claim_batch_relation_id, batch_claim_bill_id, batch_claim_bill_no, 
    policy_no, report_no, claim_status, msg_id, msg_content, deal_result, error_msg, 
    is_deleted, creator, modifier, gmt_created, gmt_modified
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.BClaimLogInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_batch_log_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_batch_log_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.BClaimLogInfoDO">
    insert into claim_batch_log_info (id, task_type, task_id, 
      claim_batch_relation_id, batch_claim_bill_id, batch_claim_bill_no, 
      policy_no, report_no, claim_status, 
      msg_id, msg_content, deal_result, 
      error_msg, is_deleted, creator, 
      modifier, gmt_created, gmt_modified
      )
    values (#{id,jdbcType=BIGINT}, #{taskType,jdbcType=VARCHAR}, #{taskId,jdbcType=BIGINT}, 
      #{claimBatchRelationId,jdbcType=BIGINT}, #{batchClaimBillId,jdbcType=BIGINT}, #{batchClaimBillNo,jdbcType=VARCHAR}, 
      #{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{claimStatus,jdbcType=INTEGER}, 
      #{msgId,jdbcType=VARCHAR}, #{msgContent,jdbcType=VARCHAR}, #{dealResult,jdbcType=INTEGER}, 
      #{errorMsg,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate()
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.BClaimLogInfoDO">
    insert into claim_batch_log_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="claimBatchRelationId != null">
        claim_batch_relation_id,
      </if>
      <if test="batchClaimBillId != null">
        batch_claim_bill_id,
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="claimStatus != null">
        claim_status,
      </if>
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="msgContent != null">
        msg_content,
      </if>
      <if test="dealResult != null">
        deal_result,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="claimBatchRelationId != null">
        #{claimBatchRelationId,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillId != null">
        #{batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillNo != null">
        #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        #{claimStatus,jdbcType=INTEGER},
      </if>
      <if test="msgId != null">
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="msgContent != null">
        #{msgContent,jdbcType=VARCHAR},
      </if>
      <if test="dealResult != null">
        #{dealResult,jdbcType=INTEGER},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.BClaimLogInfoExample" resultType="java.lang.Long">
    select count(*) from claim_batch_log_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_batch_log_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=VARCHAR},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.claimBatchRelationId != null">
        claim_batch_relation_id = #{record.claimBatchRelationId,jdbcType=BIGINT},
      </if>
      <if test="record.batchClaimBillId != null">
        batch_claim_bill_id = #{record.batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="record.batchClaimBillNo != null">
        batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimStatus != null">
        claim_status = #{record.claimStatus,jdbcType=INTEGER},
      </if>
      <if test="record.msgId != null">
        msg_id = #{record.msgId,jdbcType=VARCHAR},
      </if>
      <if test="record.msgContent != null">
        msg_content = #{record.msgContent,jdbcType=VARCHAR},
      </if>
      <if test="record.dealResult != null">
        deal_result = #{record.dealResult,jdbcType=INTEGER},
      </if>
      <if test="record.errorMsg != null">
        error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_batch_log_info
    set id = #{record.id,jdbcType=BIGINT},
      task_type = #{record.taskType,jdbcType=VARCHAR},
      task_id = #{record.taskId,jdbcType=BIGINT},
      claim_batch_relation_id = #{record.claimBatchRelationId,jdbcType=BIGINT},
      batch_claim_bill_id = #{record.batchClaimBillId,jdbcType=BIGINT},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      claim_status = #{record.claimStatus,jdbcType=INTEGER},
      msg_id = #{record.msgId,jdbcType=VARCHAR},
      msg_content = #{record.msgContent,jdbcType=VARCHAR},
      deal_result = #{record.dealResult,jdbcType=INTEGER},
      error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate()
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.BClaimLogInfoDO">
    update claim_batch_log_info
    <set>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="claimBatchRelationId != null">
        claim_batch_relation_id = #{claimBatchRelationId,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillId != null">
        batch_claim_bill_id = #{batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        claim_status = #{claimStatus,jdbcType=INTEGER},
      </if>
      <if test="msgId != null">
        msg_id = #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="msgContent != null">
        msg_content = #{msgContent,jdbcType=VARCHAR},
      </if>
      <if test="dealResult != null">
        deal_result = #{dealResult,jdbcType=INTEGER},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.BClaimLogInfoDO">
    update claim_batch_log_info
    set task_type = #{taskType,jdbcType=VARCHAR},
      task_id = #{taskId,jdbcType=BIGINT},
      claim_batch_relation_id = #{claimBatchRelationId,jdbcType=BIGINT},
      batch_claim_bill_id = #{batchClaimBillId,jdbcType=BIGINT},
      batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      claim_status = #{claimStatus,jdbcType=INTEGER},
      msg_id = #{msgId,jdbcType=VARCHAR},
      msg_content = #{msgContent,jdbcType=VARCHAR},
      deal_result = #{dealResult,jdbcType=INTEGER},
      error_msg = #{errorMsg,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate()
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_batch_log_info
    (id, task_type, task_id, claim_batch_relation_id, batch_claim_bill_id, batch_claim_bill_no, 
      policy_no, report_no, claim_status, msg_id, msg_content, deal_result, error_msg, 
      is_deleted, creator, modifier, gmt_created, gmt_modified)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.taskType,jdbcType=VARCHAR}, #{item.taskId,jdbcType=BIGINT}, 
        #{item.claimBatchRelationId,jdbcType=BIGINT}, #{item.batchClaimBillId,jdbcType=BIGINT}, 
        #{item.batchClaimBillNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.claimStatus,jdbcType=INTEGER}, #{item.msgId,jdbcType=VARCHAR}, #{item.msgContent,jdbcType=VARCHAR}, 
        #{item.dealResult,jdbcType=INTEGER}, #{item.errorMsg,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_batch_log_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'task_type'.toString() == column.value">
          #{item.taskType,jdbcType=VARCHAR}
        </if>
        <if test="'task_id'.toString() == column.value">
          #{item.taskId,jdbcType=BIGINT}
        </if>
        <if test="'claim_batch_relation_id'.toString() == column.value">
          #{item.claimBatchRelationId,jdbcType=BIGINT}
        </if>
        <if test="'batch_claim_bill_id'.toString() == column.value">
          #{item.batchClaimBillId,jdbcType=BIGINT}
        </if>
        <if test="'batch_claim_bill_no'.toString() == column.value">
          #{item.batchClaimBillNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'claim_status'.toString() == column.value">
          #{item.claimStatus,jdbcType=INTEGER}
        </if>
        <if test="'msg_id'.toString() == column.value">
          #{item.msgId,jdbcType=VARCHAR}
        </if>
        <if test="'msg_content'.toString() == column.value">
          #{item.msgContent,jdbcType=VARCHAR}
        </if>
        <if test="'deal_result'.toString() == column.value">
          #{item.dealResult,jdbcType=INTEGER}
        </if>
        <if test="'error_msg'.toString() == column.value">
          #{item.errorMsg,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>