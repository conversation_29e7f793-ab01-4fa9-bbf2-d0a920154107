<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.WoTypeDictMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.WoTypeDictDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dic_category" jdbcType="VARCHAR" property="dicCategory" />
    <result column="dic_name" jdbcType="VARCHAR" property="dicName" />
    <result column="dic_value" jdbcType="VARCHAR" property="dicValue" />
    <result column="parent_dic_value" jdbcType="VARCHAR" property="parentDicValue" />
    <result column="biz_line" jdbcType="VARCHAR" property="bizLine" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dic_category, dic_name, dic_value, parent_dic_value, biz_line, creator, gmt_created, 
    modifier, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.WoTypeDictExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wo_type_dict
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wo_type_dict
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.WoTypeDictDO">
    insert into wo_type_dict (id, dic_category, dic_name, 
      dic_value, parent_dic_value, biz_line, 
      creator, gmt_created, modifier, 
      gmt_modified, is_deleted)
    values (#{id,jdbcType=BIGINT}, #{dicCategory,jdbcType=VARCHAR}, #{dicName,jdbcType=VARCHAR}, 
      #{dicValue,jdbcType=VARCHAR}, #{parentDicValue,jdbcType=VARCHAR}, #{bizLine,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, sysdate(), #{modifier,jdbcType=VARCHAR}, 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.WoTypeDictDO">
    insert into wo_type_dict
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dicCategory != null">
        dic_category,
      </if>
      <if test="dicName != null">
        dic_name,
      </if>
      <if test="dicValue != null">
        dic_value,
      </if>
      <if test="parentDicValue != null">
        parent_dic_value,
      </if>
      <if test="bizLine != null">
        biz_line,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dicCategory != null">
        #{dicCategory,jdbcType=VARCHAR},
      </if>
      <if test="dicName != null">
        #{dicName,jdbcType=VARCHAR},
      </if>
      <if test="dicValue != null">
        #{dicValue,jdbcType=VARCHAR},
      </if>
      <if test="parentDicValue != null">
        #{parentDicValue,jdbcType=VARCHAR},
      </if>
      <if test="bizLine != null">
        #{bizLine,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.WoTypeDictExample" resultType="java.lang.Long">
    select count(*) from wo_type_dict
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wo_type_dict
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dicCategory != null">
        dic_category = #{record.dicCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.dicName != null">
        dic_name = #{record.dicName,jdbcType=VARCHAR},
      </if>
      <if test="record.dicValue != null">
        dic_value = #{record.dicValue,jdbcType=VARCHAR},
      </if>
      <if test="record.parentDicValue != null">
        parent_dic_value = #{record.parentDicValue,jdbcType=VARCHAR},
      </if>
      <if test="record.bizLine != null">
        biz_line = #{record.bizLine,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wo_type_dict
    set id = #{record.id,jdbcType=BIGINT},
      dic_category = #{record.dicCategory,jdbcType=VARCHAR},
      dic_name = #{record.dicName,jdbcType=VARCHAR},
      dic_value = #{record.dicValue,jdbcType=VARCHAR},
      parent_dic_value = #{record.parentDicValue,jdbcType=VARCHAR},
      biz_line = #{record.bizLine,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.WoTypeDictDO">
    update wo_type_dict
    <set>
      <if test="dicCategory != null">
        dic_category = #{dicCategory,jdbcType=VARCHAR},
      </if>
      <if test="dicName != null">
        dic_name = #{dicName,jdbcType=VARCHAR},
      </if>
      <if test="dicValue != null">
        dic_value = #{dicValue,jdbcType=VARCHAR},
      </if>
      <if test="parentDicValue != null">
        parent_dic_value = #{parentDicValue,jdbcType=VARCHAR},
      </if>
      <if test="bizLine != null">
        biz_line = #{bizLine,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.WoTypeDictDO">
    update wo_type_dict
    set dic_category = #{dicCategory,jdbcType=VARCHAR},
      dic_name = #{dicName,jdbcType=VARCHAR},
      dic_value = #{dicValue,jdbcType=VARCHAR},
      parent_dic_value = #{parentDicValue,jdbcType=VARCHAR},
      biz_line = #{bizLine,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into wo_type_dict
    (id, dic_category, dic_name, dic_value, parent_dic_value, biz_line, creator, gmt_created, 
      modifier, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.dicCategory,jdbcType=VARCHAR}, #{item.dicName,jdbcType=VARCHAR}, 
        #{item.dicValue,jdbcType=VARCHAR}, #{item.parentDicValue,jdbcType=VARCHAR}, #{item.bizLine,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into wo_type_dict (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'dic_category'.toString() == column.value">
          #{item.dicCategory,jdbcType=VARCHAR}
        </if>
        <if test="'dic_name'.toString() == column.value">
          #{item.dicName,jdbcType=VARCHAR}
        </if>
        <if test="'dic_value'.toString() == column.value">
          #{item.dicValue,jdbcType=VARCHAR}
        </if>
        <if test="'parent_dic_value'.toString() == column.value">
          #{item.parentDicValue,jdbcType=VARCHAR}
        </if>
        <if test="'biz_line'.toString() == column.value">
          #{item.bizLine,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>