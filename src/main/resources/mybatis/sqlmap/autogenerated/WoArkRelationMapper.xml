<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.WoArkRelationMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.WoArkRelationDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="assign_no" jdbcType="VARCHAR" property="assignNo" />
    <result column="wo_info_id" jdbcType="BIGINT" property="woInfoId" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="assign_urgency" jdbcType="TINYINT" property="assignUrgency" />
    <result column="is_urge" jdbcType="BIT" property="isUrge" />
    <result column="urge_count" jdbcType="INTEGER" property="urgeCount" />
    <result column="is_timeout" jdbcType="BIT" property="isTimeout" />
    <result column="time_interval" jdbcType="INTEGER" property="timeInterval" />
    <result column="last_hand_time_interval" jdbcType="INTEGER" property="lastHandTimeInterval" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, assign_no, wo_info_id, product_id, assign_urgency, is_urge, urge_count, is_timeout, 
    time_interval, last_hand_time_interval, extra_info, remark, is_deleted, gmt_created, 
    gmt_modified, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.WoArkRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wo_ark_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wo_ark_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.WoArkRelationDO">
    insert into wo_ark_relation (id, assign_no, wo_info_id, 
      product_id, assign_urgency, is_urge, 
      urge_count, is_timeout, time_interval, 
      last_hand_time_interval, extra_info, remark, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier)
    values (#{id,jdbcType=BIGINT}, #{assignNo,jdbcType=VARCHAR}, #{woInfoId,jdbcType=BIGINT}, 
      #{productId,jdbcType=INTEGER}, #{assignUrgency,jdbcType=TINYINT}, #{isUrge,jdbcType=BIT}, 
      #{urgeCount,jdbcType=INTEGER}, #{isTimeout,jdbcType=BIT}, #{timeInterval,jdbcType=INTEGER}, 
      #{lastHandTimeInterval,jdbcType=INTEGER}, #{extraInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.WoArkRelationDO">
    insert into wo_ark_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="assignNo != null">
        assign_no,
      </if>
      <if test="woInfoId != null">
        wo_info_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="assignUrgency != null">
        assign_urgency,
      </if>
      <if test="isUrge != null">
        is_urge,
      </if>
      <if test="urgeCount != null">
        urge_count,
      </if>
      <if test="isTimeout != null">
        is_timeout,
      </if>
      <if test="timeInterval != null">
        time_interval,
      </if>
      <if test="lastHandTimeInterval != null">
        last_hand_time_interval,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="assignNo != null">
        #{assignNo,jdbcType=VARCHAR},
      </if>
      <if test="woInfoId != null">
        #{woInfoId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="assignUrgency != null">
        #{assignUrgency,jdbcType=TINYINT},
      </if>
      <if test="isUrge != null">
        #{isUrge,jdbcType=BIT},
      </if>
      <if test="urgeCount != null">
        #{urgeCount,jdbcType=INTEGER},
      </if>
      <if test="isTimeout != null">
        #{isTimeout,jdbcType=BIT},
      </if>
      <if test="timeInterval != null">
        #{timeInterval,jdbcType=INTEGER},
      </if>
      <if test="lastHandTimeInterval != null">
        #{lastHandTimeInterval,jdbcType=INTEGER},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.WoArkRelationExample" resultType="java.lang.Long">
    select count(*) from wo_ark_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wo_ark_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.assignNo != null">
        assign_no = #{record.assignNo,jdbcType=VARCHAR},
      </if>
      <if test="record.woInfoId != null">
        wo_info_id = #{record.woInfoId,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=INTEGER},
      </if>
      <if test="record.assignUrgency != null">
        assign_urgency = #{record.assignUrgency,jdbcType=TINYINT},
      </if>
      <if test="record.isUrge != null">
        is_urge = #{record.isUrge,jdbcType=BIT},
      </if>
      <if test="record.urgeCount != null">
        urge_count = #{record.urgeCount,jdbcType=INTEGER},
      </if>
      <if test="record.isTimeout != null">
        is_timeout = #{record.isTimeout,jdbcType=BIT},
      </if>
      <if test="record.timeInterval != null">
        time_interval = #{record.timeInterval,jdbcType=INTEGER},
      </if>
      <if test="record.lastHandTimeInterval != null">
        last_hand_time_interval = #{record.lastHandTimeInterval,jdbcType=INTEGER},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wo_ark_relation
    set id = #{record.id,jdbcType=BIGINT},
      assign_no = #{record.assignNo,jdbcType=VARCHAR},
      wo_info_id = #{record.woInfoId,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=INTEGER},
      assign_urgency = #{record.assignUrgency,jdbcType=TINYINT},
      is_urge = #{record.isUrge,jdbcType=BIT},
      urge_count = #{record.urgeCount,jdbcType=INTEGER},
      is_timeout = #{record.isTimeout,jdbcType=BIT},
      time_interval = #{record.timeInterval,jdbcType=INTEGER},
      last_hand_time_interval = #{record.lastHandTimeInterval,jdbcType=INTEGER},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.WoArkRelationDO">
    update wo_ark_relation
    <set>
      <if test="assignNo != null">
        assign_no = #{assignNo,jdbcType=VARCHAR},
      </if>
      <if test="woInfoId != null">
        wo_info_id = #{woInfoId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="assignUrgency != null">
        assign_urgency = #{assignUrgency,jdbcType=TINYINT},
      </if>
      <if test="isUrge != null">
        is_urge = #{isUrge,jdbcType=BIT},
      </if>
      <if test="urgeCount != null">
        urge_count = #{urgeCount,jdbcType=INTEGER},
      </if>
      <if test="isTimeout != null">
        is_timeout = #{isTimeout,jdbcType=BIT},
      </if>
      <if test="timeInterval != null">
        time_interval = #{timeInterval,jdbcType=INTEGER},
      </if>
      <if test="lastHandTimeInterval != null">
        last_hand_time_interval = #{lastHandTimeInterval,jdbcType=INTEGER},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.WoArkRelationDO">
    update wo_ark_relation
    set assign_no = #{assignNo,jdbcType=VARCHAR},
      wo_info_id = #{woInfoId,jdbcType=BIGINT},
      product_id = #{productId,jdbcType=INTEGER},
      assign_urgency = #{assignUrgency,jdbcType=TINYINT},
      is_urge = #{isUrge,jdbcType=BIT},
      urge_count = #{urgeCount,jdbcType=INTEGER},
      is_timeout = #{isTimeout,jdbcType=BIT},
      time_interval = #{timeInterval,jdbcType=INTEGER},
      last_hand_time_interval = #{lastHandTimeInterval,jdbcType=INTEGER},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into wo_ark_relation
    (id, assign_no, wo_info_id, product_id, assign_urgency, is_urge, urge_count, is_timeout, 
      time_interval, last_hand_time_interval, extra_info, remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.assignNo,jdbcType=VARCHAR}, #{item.woInfoId,jdbcType=BIGINT}, 
        #{item.productId,jdbcType=INTEGER}, #{item.assignUrgency,jdbcType=TINYINT}, #{item.isUrge,jdbcType=BIT}, 
        #{item.urgeCount,jdbcType=INTEGER}, #{item.isTimeout,jdbcType=BIT}, #{item.timeInterval,jdbcType=INTEGER}, 
        #{item.lastHandTimeInterval,jdbcType=INTEGER}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into wo_ark_relation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'assign_no'.toString() == column.value">
          #{item.assignNo,jdbcType=VARCHAR}
        </if>
        <if test="'wo_info_id'.toString() == column.value">
          #{item.woInfoId,jdbcType=BIGINT}
        </if>
        <if test="'product_id'.toString() == column.value">
          #{item.productId,jdbcType=INTEGER}
        </if>
        <if test="'assign_urgency'.toString() == column.value">
          #{item.assignUrgency,jdbcType=TINYINT}
        </if>
        <if test="'is_urge'.toString() == column.value">
          #{item.isUrge,jdbcType=BIT}
        </if>
        <if test="'urge_count'.toString() == column.value">
          #{item.urgeCount,jdbcType=INTEGER}
        </if>
        <if test="'is_timeout'.toString() == column.value">
          #{item.isTimeout,jdbcType=BIT}
        </if>
        <if test="'time_interval'.toString() == column.value">
          #{item.timeInterval,jdbcType=INTEGER}
        </if>
        <if test="'last_hand_time_interval'.toString() == column.value">
          #{item.lastHandTimeInterval,jdbcType=INTEGER}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>