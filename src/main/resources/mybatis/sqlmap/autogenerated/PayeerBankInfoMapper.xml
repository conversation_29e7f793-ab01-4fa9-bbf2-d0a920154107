<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.PayeerBankInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.PayeerBankInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="indemnity_type" jdbcType="TINYINT" property="indemnityType" />
    <result column="account_type" jdbcType="TINYINT" property="accountType" />
    <result column="indemnity_amount" jdbcType="VARCHAR" property="indemnityAmount" />
    <result column="payment_way" jdbcType="INTEGER" property="paymentWay" />
    <result column="payee_bank_location_code" jdbcType="VARCHAR" property="payeeBankLocationCode" />
    <result column="payee_bank_name" jdbcType="VARCHAR" property="payeeBankName" />
    <result column="payee_bank" jdbcType="VARCHAR" property="payeeBank" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="payee_bank_area" jdbcType="VARCHAR" property="payeeBankArea" />
    <result column="bank_area_name" jdbcType="VARCHAR" property="bankAreaName" />
    <result column="payee_account" jdbcType="VARCHAR" property="payeeAccount" />
    <result column="payee_name" jdbcType="VARCHAR" property="payeeName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, indemnity_type, account_type, indemnity_amount, payment_way, payee_bank_location_code, 
    payee_bank_name, payee_bank, bank_name, payee_bank_area, bank_area_name, payee_account, 
    payee_name, remark, extra_info, creator, gmt_created, modifier, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.PayeerBankInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_payeer_bank_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_payeer_bank_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.PayeerBankInfoDO">
    insert into claim_payeer_bank_info (id, report_no, indemnity_type, 
      account_type, indemnity_amount, payment_way, 
      payee_bank_location_code, payee_bank_name, payee_bank, 
      bank_name, payee_bank_area, bank_area_name, 
      payee_account, payee_name, remark, 
      extra_info, creator, gmt_created, 
      modifier, gmt_modified, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{indemnityType,jdbcType=TINYINT}, 
      #{accountType,jdbcType=TINYINT}, #{indemnityAmount,jdbcType=VARCHAR}, #{paymentWay,jdbcType=INTEGER}, 
      #{payeeBankLocationCode,jdbcType=VARCHAR}, #{payeeBankName,jdbcType=VARCHAR}, #{payeeBank,jdbcType=VARCHAR}, 
      #{bankName,jdbcType=VARCHAR}, #{payeeBankArea,jdbcType=VARCHAR}, #{bankAreaName,jdbcType=VARCHAR}, 
      #{payeeAccount,jdbcType=VARCHAR}, #{payeeName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{extraInfo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, sysdate(), 
      #{modifier,jdbcType=VARCHAR}, sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.PayeerBankInfoDO">
    insert into claim_payeer_bank_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="indemnityType != null">
        indemnity_type,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="indemnityAmount != null">
        indemnity_amount,
      </if>
      <if test="paymentWay != null">
        payment_way,
      </if>
      <if test="payeeBankLocationCode != null">
        payee_bank_location_code,
      </if>
      <if test="payeeBankName != null">
        payee_bank_name,
      </if>
      <if test="payeeBank != null">
        payee_bank,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="payeeBankArea != null">
        payee_bank_area,
      </if>
      <if test="bankAreaName != null">
        bank_area_name,
      </if>
      <if test="payeeAccount != null">
        payee_account,
      </if>
      <if test="payeeName != null">
        payee_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="indemnityType != null">
        #{indemnityType,jdbcType=TINYINT},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=TINYINT},
      </if>
      <if test="indemnityAmount != null">
        #{indemnityAmount,jdbcType=VARCHAR},
      </if>
      <if test="paymentWay != null">
        #{paymentWay,jdbcType=INTEGER},
      </if>
      <if test="payeeBankLocationCode != null">
        #{payeeBankLocationCode,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankName != null">
        #{payeeBankName,jdbcType=VARCHAR},
      </if>
      <if test="payeeBank != null">
        #{payeeBank,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankArea != null">
        #{payeeBankArea,jdbcType=VARCHAR},
      </if>
      <if test="bankAreaName != null">
        #{bankAreaName,jdbcType=VARCHAR},
      </if>
      <if test="payeeAccount != null">
        #{payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.PayeerBankInfoExample" resultType="java.lang.Long">
    select count(*) from claim_payeer_bank_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_payeer_bank_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.indemnityType != null">
        indemnity_type = #{record.indemnityType,jdbcType=TINYINT},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=TINYINT},
      </if>
      <if test="record.indemnityAmount != null">
        indemnity_amount = #{record.indemnityAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentWay != null">
        payment_way = #{record.paymentWay,jdbcType=INTEGER},
      </if>
      <if test="record.payeeBankLocationCode != null">
        payee_bank_location_code = #{record.payeeBankLocationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeBankName != null">
        payee_bank_name = #{record.payeeBankName,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeBank != null">
        payee_bank = #{record.payeeBank,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeBankArea != null">
        payee_bank_area = #{record.payeeBankArea,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAreaName != null">
        bank_area_name = #{record.bankAreaName,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeAccount != null">
        payee_account = #{record.payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeName != null">
        payee_name = #{record.payeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_payeer_bank_info
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      indemnity_type = #{record.indemnityType,jdbcType=TINYINT},
      account_type = #{record.accountType,jdbcType=TINYINT},
      indemnity_amount = #{record.indemnityAmount,jdbcType=VARCHAR},
      payment_way = #{record.paymentWay,jdbcType=INTEGER},
      payee_bank_location_code = #{record.payeeBankLocationCode,jdbcType=VARCHAR},
      payee_bank_name = #{record.payeeBankName,jdbcType=VARCHAR},
      payee_bank = #{record.payeeBank,jdbcType=VARCHAR},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      payee_bank_area = #{record.payeeBankArea,jdbcType=VARCHAR},
      bank_area_name = #{record.bankAreaName,jdbcType=VARCHAR},
      payee_account = #{record.payeeAccount,jdbcType=VARCHAR},
      payee_name = #{record.payeeName,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.PayeerBankInfoDO">
    update claim_payeer_bank_info
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="indemnityType != null">
        indemnity_type = #{indemnityType,jdbcType=TINYINT},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=TINYINT},
      </if>
      <if test="indemnityAmount != null">
        indemnity_amount = #{indemnityAmount,jdbcType=VARCHAR},
      </if>
      <if test="paymentWay != null">
        payment_way = #{paymentWay,jdbcType=INTEGER},
      </if>
      <if test="payeeBankLocationCode != null">
        payee_bank_location_code = #{payeeBankLocationCode,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankName != null">
        payee_bank_name = #{payeeBankName,jdbcType=VARCHAR},
      </if>
      <if test="payeeBank != null">
        payee_bank = #{payeeBank,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankArea != null">
        payee_bank_area = #{payeeBankArea,jdbcType=VARCHAR},
      </if>
      <if test="bankAreaName != null">
        bank_area_name = #{bankAreaName,jdbcType=VARCHAR},
      </if>
      <if test="payeeAccount != null">
        payee_account = #{payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        payee_name = #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.PayeerBankInfoDO">
    update claim_payeer_bank_info
    set report_no = #{reportNo,jdbcType=VARCHAR},
      indemnity_type = #{indemnityType,jdbcType=TINYINT},
      account_type = #{accountType,jdbcType=TINYINT},
      indemnity_amount = #{indemnityAmount,jdbcType=VARCHAR},
      payment_way = #{paymentWay,jdbcType=INTEGER},
      payee_bank_location_code = #{payeeBankLocationCode,jdbcType=VARCHAR},
      payee_bank_name = #{payeeBankName,jdbcType=VARCHAR},
      payee_bank = #{payeeBank,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      payee_bank_area = #{payeeBankArea,jdbcType=VARCHAR},
      bank_area_name = #{bankAreaName,jdbcType=VARCHAR},
      payee_account = #{payeeAccount,jdbcType=VARCHAR},
      payee_name = #{payeeName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_payeer_bank_info
    (id, report_no, indemnity_type, account_type, indemnity_amount, payment_way, payee_bank_location_code, 
      payee_bank_name, payee_bank, bank_name, payee_bank_area, bank_area_name, payee_account, 
      payee_name, remark, extra_info, creator, gmt_created, modifier, gmt_modified, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.indemnityType,jdbcType=TINYINT}, 
        #{item.accountType,jdbcType=TINYINT}, #{item.indemnityAmount,jdbcType=VARCHAR}, 
        #{item.paymentWay,jdbcType=INTEGER}, #{item.payeeBankLocationCode,jdbcType=VARCHAR}, 
        #{item.payeeBankName,jdbcType=VARCHAR}, #{item.payeeBank,jdbcType=VARCHAR}, #{item.bankName,jdbcType=VARCHAR}, 
        #{item.payeeBankArea,jdbcType=VARCHAR}, #{item.bankAreaName,jdbcType=VARCHAR}, 
        #{item.payeeAccount,jdbcType=VARCHAR}, #{item.payeeName,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_payeer_bank_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'indemnity_type'.toString() == column.value">
          #{item.indemnityType,jdbcType=TINYINT}
        </if>
        <if test="'account_type'.toString() == column.value">
          #{item.accountType,jdbcType=TINYINT}
        </if>
        <if test="'indemnity_amount'.toString() == column.value">
          #{item.indemnityAmount,jdbcType=VARCHAR}
        </if>
        <if test="'payment_way'.toString() == column.value">
          #{item.paymentWay,jdbcType=INTEGER}
        </if>
        <if test="'payee_bank_location_code'.toString() == column.value">
          #{item.payeeBankLocationCode,jdbcType=VARCHAR}
        </if>
        <if test="'payee_bank_name'.toString() == column.value">
          #{item.payeeBankName,jdbcType=VARCHAR}
        </if>
        <if test="'payee_bank'.toString() == column.value">
          #{item.payeeBank,jdbcType=VARCHAR}
        </if>
        <if test="'bank_name'.toString() == column.value">
          #{item.bankName,jdbcType=VARCHAR}
        </if>
        <if test="'payee_bank_area'.toString() == column.value">
          #{item.payeeBankArea,jdbcType=VARCHAR}
        </if>
        <if test="'bank_area_name'.toString() == column.value">
          #{item.bankAreaName,jdbcType=VARCHAR}
        </if>
        <if test="'payee_account'.toString() == column.value">
          #{item.payeeAccount,jdbcType=VARCHAR}
        </if>
        <if test="'payee_name'.toString() == column.value">
          #{item.payeeName,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>