<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimHealthRelationMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimHealthRelationDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="relation_type" jdbcType="VARCHAR" property="relationType" />
    <result column="health_report_no" jdbcType="VARCHAR" property="healthReportNo" />
    <result column="health_policy_no" jdbcType="VARCHAR" property="healthPolicyNo" />
    <result column="health_policy_id" jdbcType="BIGINT" property="healthPolicyId" />
    <result column="campaign_def_id" jdbcType="BIGINT" property="campaignDefId" />
    <result column="campaign_full_name" jdbcType="VARCHAR" property="campaignFullName" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="policy_insurant" jdbcType="VARCHAR" property="policyInsurant" />
    <result column="insurant_cert_no" jdbcType="VARCHAR" property="insurantCertNo" />
    <result column="health_mobile" jdbcType="VARCHAR" property="healthMobile" />
    <result column="health_claim_status" jdbcType="VARCHAR" property="healthClaimStatus" />
    <result column="rollback_reason" jdbcType="VARCHAR" property="rollbackReason" />
    <result column="dispatcher_no" jdbcType="VARCHAR" property="dispatcherNo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, relation_type, health_report_no, health_policy_no, health_policy_id, 
    campaign_def_id, campaign_full_name, package_def_id, product_name, policy_insurant, 
    insurant_cert_no, health_mobile, health_claim_status, rollback_reason, dispatcher_no, 
    creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimHealthRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_health_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_health_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimHealthRelationDO">
    insert into claim_health_relation (id, report_no, relation_type, 
      health_report_no, health_policy_no, health_policy_id, 
      campaign_def_id, campaign_full_name, package_def_id, 
      product_name, policy_insurant, insurant_cert_no, 
      health_mobile, health_claim_status, rollback_reason, 
      dispatcher_no, creator, modifier, 
      gmt_created, gmt_modified, is_deleted
      )
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{relationType,jdbcType=VARCHAR}, 
      #{healthReportNo,jdbcType=VARCHAR}, #{healthPolicyNo,jdbcType=VARCHAR}, #{healthPolicyId,jdbcType=BIGINT}, 
      #{campaignDefId,jdbcType=BIGINT}, #{campaignFullName,jdbcType=VARCHAR}, #{packageDefId,jdbcType=BIGINT}, 
      #{productName,jdbcType=VARCHAR}, #{policyInsurant,jdbcType=VARCHAR}, #{insurantCertNo,jdbcType=VARCHAR}, 
      #{healthMobile,jdbcType=VARCHAR}, #{healthClaimStatus,jdbcType=VARCHAR}, #{rollbackReason,jdbcType=VARCHAR}, 
      #{dispatcherNo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{isDeleted,jdbcType=CHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimHealthRelationDO">
    insert into claim_health_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="relationType != null">
        relation_type,
      </if>
      <if test="healthReportNo != null">
        health_report_no,
      </if>
      <if test="healthPolicyNo != null">
        health_policy_no,
      </if>
      <if test="healthPolicyId != null">
        health_policy_id,
      </if>
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="campaignFullName != null">
        campaign_full_name,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="policyInsurant != null">
        policy_insurant,
      </if>
      <if test="insurantCertNo != null">
        insurant_cert_no,
      </if>
      <if test="healthMobile != null">
        health_mobile,
      </if>
      <if test="healthClaimStatus != null">
        health_claim_status,
      </if>
      <if test="rollbackReason != null">
        rollback_reason,
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=VARCHAR},
      </if>
      <if test="healthReportNo != null">
        #{healthReportNo,jdbcType=VARCHAR},
      </if>
      <if test="healthPolicyNo != null">
        #{healthPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="healthPolicyId != null">
        #{healthPolicyId,jdbcType=BIGINT},
      </if>
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="campaignFullName != null">
        #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="policyInsurant != null">
        #{policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertNo != null">
        #{insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="healthMobile != null">
        #{healthMobile,jdbcType=VARCHAR},
      </if>
      <if test="healthClaimStatus != null">
        #{healthClaimStatus,jdbcType=VARCHAR},
      </if>
      <if test="rollbackReason != null">
        #{rollbackReason,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherNo != null">
        #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimHealthRelationExample" resultType="java.lang.Long">
    select count(*) from claim_health_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_health_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.relationType != null">
        relation_type = #{record.relationType,jdbcType=VARCHAR},
      </if>
      <if test="record.healthReportNo != null">
        health_report_no = #{record.healthReportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.healthPolicyNo != null">
        health_policy_no = #{record.healthPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.healthPolicyId != null">
        health_policy_id = #{record.healthPolicyId,jdbcType=BIGINT},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="record.campaignFullName != null">
        campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.policyInsurant != null">
        policy_insurant = #{record.policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCertNo != null">
        insurant_cert_no = #{record.insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="record.healthMobile != null">
        health_mobile = #{record.healthMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.healthClaimStatus != null">
        health_claim_status = #{record.healthClaimStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.rollbackReason != null">
        rollback_reason = #{record.rollbackReason,jdbcType=VARCHAR},
      </if>
      <if test="record.dispatcherNo != null">
        dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_health_relation
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      relation_type = #{record.relationType,jdbcType=VARCHAR},
      health_report_no = #{record.healthReportNo,jdbcType=VARCHAR},
      health_policy_no = #{record.healthPolicyNo,jdbcType=VARCHAR},
      health_policy_id = #{record.healthPolicyId,jdbcType=BIGINT},
      campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      product_name = #{record.productName,jdbcType=VARCHAR},
      policy_insurant = #{record.policyInsurant,jdbcType=VARCHAR},
      insurant_cert_no = #{record.insurantCertNo,jdbcType=VARCHAR},
      health_mobile = #{record.healthMobile,jdbcType=VARCHAR},
      health_claim_status = #{record.healthClaimStatus,jdbcType=VARCHAR},
      rollback_reason = #{record.rollbackReason,jdbcType=VARCHAR},
      dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimHealthRelationDO">
    update claim_health_relation
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        relation_type = #{relationType,jdbcType=VARCHAR},
      </if>
      <if test="healthReportNo != null">
        health_report_no = #{healthReportNo,jdbcType=VARCHAR},
      </if>
      <if test="healthPolicyNo != null">
        health_policy_no = #{healthPolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="healthPolicyId != null">
        health_policy_id = #{healthPolicyId,jdbcType=BIGINT},
      </if>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="campaignFullName != null">
        campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="policyInsurant != null">
        policy_insurant = #{policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertNo != null">
        insurant_cert_no = #{insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="healthMobile != null">
        health_mobile = #{healthMobile,jdbcType=VARCHAR},
      </if>
      <if test="healthClaimStatus != null">
        health_claim_status = #{healthClaimStatus,jdbcType=VARCHAR},
      </if>
      <if test="rollbackReason != null">
        rollback_reason = #{rollbackReason,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimHealthRelationDO">
    update claim_health_relation
    set report_no = #{reportNo,jdbcType=VARCHAR},
      relation_type = #{relationType,jdbcType=VARCHAR},
      health_report_no = #{healthReportNo,jdbcType=VARCHAR},
      health_policy_no = #{healthPolicyNo,jdbcType=VARCHAR},
      health_policy_id = #{healthPolicyId,jdbcType=BIGINT},
      campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      package_def_id = #{packageDefId,jdbcType=BIGINT},
      product_name = #{productName,jdbcType=VARCHAR},
      policy_insurant = #{policyInsurant,jdbcType=VARCHAR},
      insurant_cert_no = #{insurantCertNo,jdbcType=VARCHAR},
      health_mobile = #{healthMobile,jdbcType=VARCHAR},
      health_claim_status = #{healthClaimStatus,jdbcType=VARCHAR},
      rollback_reason = #{rollbackReason,jdbcType=VARCHAR},
      dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_health_relation
    (id, report_no, relation_type, health_report_no, health_policy_no, health_policy_id, 
      campaign_def_id, campaign_full_name, package_def_id, product_name, policy_insurant, 
      insurant_cert_no, health_mobile, health_claim_status, rollback_reason, dispatcher_no, 
      creator, modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.relationType,jdbcType=VARCHAR}, 
        #{item.healthReportNo,jdbcType=VARCHAR}, #{item.healthPolicyNo,jdbcType=VARCHAR}, 
        #{item.healthPolicyId,jdbcType=BIGINT}, #{item.campaignDefId,jdbcType=BIGINT}, 
        #{item.campaignFullName,jdbcType=VARCHAR}, #{item.packageDefId,jdbcType=BIGINT}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.policyInsurant,jdbcType=VARCHAR}, 
        #{item.insurantCertNo,jdbcType=VARCHAR}, #{item.healthMobile,jdbcType=VARCHAR}, 
        #{item.healthClaimStatus,jdbcType=VARCHAR}, #{item.rollbackReason,jdbcType=VARCHAR}, 
        #{item.dispatcherNo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_health_relation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'relation_type'.toString() == column.value">
          #{item.relationType,jdbcType=VARCHAR}
        </if>
        <if test="'health_report_no'.toString() == column.value">
          #{item.healthReportNo,jdbcType=VARCHAR}
        </if>
        <if test="'health_policy_no'.toString() == column.value">
          #{item.healthPolicyNo,jdbcType=VARCHAR}
        </if>
        <if test="'health_policy_id'.toString() == column.value">
          #{item.healthPolicyId,jdbcType=BIGINT}
        </if>
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=BIGINT}
        </if>
        <if test="'campaign_full_name'.toString() == column.value">
          #{item.campaignFullName,jdbcType=VARCHAR}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'policy_insurant'.toString() == column.value">
          #{item.policyInsurant,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_cert_no'.toString() == column.value">
          #{item.insurantCertNo,jdbcType=VARCHAR}
        </if>
        <if test="'health_mobile'.toString() == column.value">
          #{item.healthMobile,jdbcType=VARCHAR}
        </if>
        <if test="'health_claim_status'.toString() == column.value">
          #{item.healthClaimStatus,jdbcType=VARCHAR}
        </if>
        <if test="'rollback_reason'.toString() == column.value">
          #{item.rollbackReason,jdbcType=VARCHAR}
        </if>
        <if test="'dispatcher_no'.toString() == column.value">
          #{item.dispatcherNo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>