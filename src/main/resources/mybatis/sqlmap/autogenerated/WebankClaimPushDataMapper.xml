<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.WebankClaimPushDataMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.WebankClaimPushDataDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="endorse_no" jdbcType="VARCHAR" property="endorseNo" />
    <result column="policy_insurant" jdbcType="VARCHAR" property="policyInsurant" />
    <result column="customer_address" jdbcType="VARCHAR" property="customerAddress" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="apply_date" jdbcType="VARCHAR" property="applyDate" />
    <result column="effective_date" jdbcType="VARCHAR" property="effectiveDate" />
    <result column="employee_start_date" jdbcType="VARCHAR" property="employeeStartDate" />
    <result column="accident_date" jdbcType="VARCHAR" property="accidentDate" />
    <result column="report_date" jdbcType="VARCHAR" property="reportDate" />
    <result column="register_date" jdbcType="VARCHAR" property="registerDate" />
    <result column="close_date" jdbcType="VARCHAR" property="closeDate" />
    <result column="employee_certi_no" jdbcType="VARCHAR" property="employeeCertiNo" />
    <result column="job_category" jdbcType="VARCHAR" property="jobCategory" />
    <result column="occupation" jdbcType="VARCHAR" property="occupation" />
    <result column="insurance_plan" jdbcType="VARCHAR" property="insurancePlan" />
    <result column="loss_cause_name" jdbcType="VARCHAR" property="lossCauseName" />
    <result column="report_amount" jdbcType="VARCHAR" property="reportAmount" />
    <result column="loss_assessment_amount" jdbcType="VARCHAR" property="lossAssessmentAmount" />
    <result column="paid_amount" jdbcType="VARCHAR" property="paidAmount" />
    <result column="claim_status" jdbcType="VARCHAR" property="claimStatus" />
    <result column="accident_desc" jdbcType="VARCHAR" property="accidentDesc" />
    <result column="claim_remark" jdbcType="VARCHAR" property="claimRemark" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, policy_no, endorse_no, policy_insurant, customer_address, product_name, 
    apply_date, effective_date, employee_start_date, accident_date, report_date, register_date, 
    close_date, employee_certi_no, job_category, occupation, insurance_plan, loss_cause_name, 
    report_amount, loss_assessment_amount, paid_amount, claim_status, accident_desc, 
    claim_remark, gmt_created, gmt_modified, creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.WebankClaimPushDataExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from webank_claim_push_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from webank_claim_push_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.WebankClaimPushDataDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into webank_claim_push_data (report_no, policy_no, endorse_no, 
      policy_insurant, customer_address, product_name, 
      apply_date, effective_date, employee_start_date, 
      accident_date, report_date, register_date, 
      close_date, employee_certi_no, job_category, 
      occupation, insurance_plan, loss_cause_name, 
      report_amount, loss_assessment_amount, paid_amount, 
      claim_status, accident_desc, claim_remark, 
      gmt_created, gmt_modified, creator, 
      modifier, is_deleted)
    values (#{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, #{endorseNo,jdbcType=VARCHAR}, 
      #{policyInsurant,jdbcType=VARCHAR}, #{customerAddress,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, 
      #{applyDate,jdbcType=VARCHAR}, #{effectiveDate,jdbcType=VARCHAR}, #{employeeStartDate,jdbcType=VARCHAR}, 
      #{accidentDate,jdbcType=VARCHAR}, #{reportDate,jdbcType=VARCHAR}, #{registerDate,jdbcType=VARCHAR}, 
      #{closeDate,jdbcType=VARCHAR}, #{employeeCertiNo,jdbcType=VARCHAR}, #{jobCategory,jdbcType=VARCHAR}, 
      #{occupation,jdbcType=VARCHAR}, #{insurancePlan,jdbcType=VARCHAR}, #{lossCauseName,jdbcType=VARCHAR}, 
      #{reportAmount,jdbcType=VARCHAR}, #{lossAssessmentAmount,jdbcType=VARCHAR}, #{paidAmount,jdbcType=VARCHAR}, 
      #{claimStatus,jdbcType=VARCHAR}, #{accidentDesc,jdbcType=VARCHAR}, #{claimRemark,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.WebankClaimPushDataDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into webank_claim_push_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="endorseNo != null">
        endorse_no,
      </if>
      <if test="policyInsurant != null">
        policy_insurant,
      </if>
      <if test="customerAddress != null">
        customer_address,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="applyDate != null">
        apply_date,
      </if>
      <if test="effectiveDate != null">
        effective_date,
      </if>
      <if test="employeeStartDate != null">
        employee_start_date,
      </if>
      <if test="accidentDate != null">
        accident_date,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="registerDate != null">
        register_date,
      </if>
      <if test="closeDate != null">
        close_date,
      </if>
      <if test="employeeCertiNo != null">
        employee_certi_no,
      </if>
      <if test="jobCategory != null">
        job_category,
      </if>
      <if test="occupation != null">
        occupation,
      </if>
      <if test="insurancePlan != null">
        insurance_plan,
      </if>
      <if test="lossCauseName != null">
        loss_cause_name,
      </if>
      <if test="reportAmount != null">
        report_amount,
      </if>
      <if test="lossAssessmentAmount != null">
        loss_assessment_amount,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="claimStatus != null">
        claim_status,
      </if>
      <if test="accidentDesc != null">
        accident_desc,
      </if>
      <if test="claimRemark != null">
        claim_remark,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseNo != null">
        #{endorseNo,jdbcType=VARCHAR},
      </if>
      <if test="policyInsurant != null">
        #{policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="customerAddress != null">
        #{customerAddress,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        #{applyDate,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDate != null">
        #{effectiveDate,jdbcType=VARCHAR},
      </if>
      <if test="employeeStartDate != null">
        #{employeeStartDate,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        #{accidentDate,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        #{registerDate,jdbcType=VARCHAR},
      </if>
      <if test="closeDate != null">
        #{closeDate,jdbcType=VARCHAR},
      </if>
      <if test="employeeCertiNo != null">
        #{employeeCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="jobCategory != null">
        #{jobCategory,jdbcType=VARCHAR},
      </if>
      <if test="occupation != null">
        #{occupation,jdbcType=VARCHAR},
      </if>
      <if test="insurancePlan != null">
        #{insurancePlan,jdbcType=VARCHAR},
      </if>
      <if test="lossCauseName != null">
        #{lossCauseName,jdbcType=VARCHAR},
      </if>
      <if test="reportAmount != null">
        #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="lossAssessmentAmount != null">
        #{lossAssessmentAmount,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        #{claimStatus,jdbcType=VARCHAR},
      </if>
      <if test="accidentDesc != null">
        #{accidentDesc,jdbcType=VARCHAR},
      </if>
      <if test="claimRemark != null">
        #{claimRemark,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.WebankClaimPushDataExample" resultType="java.lang.Long">
    select count(*) from webank_claim_push_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update webank_claim_push_data
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.endorseNo != null">
        endorse_no = #{record.endorseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyInsurant != null">
        policy_insurant = #{record.policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="record.customerAddress != null">
        customer_address = #{record.customerAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.applyDate != null">
        apply_date = #{record.applyDate,jdbcType=VARCHAR},
      </if>
      <if test="record.effectiveDate != null">
        effective_date = #{record.effectiveDate,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeStartDate != null">
        employee_start_date = #{record.employeeStartDate,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentDate != null">
        accident_date = #{record.accidentDate,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDate != null">
        report_date = #{record.reportDate,jdbcType=VARCHAR},
      </if>
      <if test="record.registerDate != null">
        register_date = #{record.registerDate,jdbcType=VARCHAR},
      </if>
      <if test="record.closeDate != null">
        close_date = #{record.closeDate,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeCertiNo != null">
        employee_certi_no = #{record.employeeCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="record.jobCategory != null">
        job_category = #{record.jobCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.occupation != null">
        occupation = #{record.occupation,jdbcType=VARCHAR},
      </if>
      <if test="record.insurancePlan != null">
        insurance_plan = #{record.insurancePlan,jdbcType=VARCHAR},
      </if>
      <if test="record.lossCauseName != null">
        loss_cause_name = #{record.lossCauseName,jdbcType=VARCHAR},
      </if>
      <if test="record.reportAmount != null">
        report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.lossAssessmentAmount != null">
        loss_assessment_amount = #{record.lossAssessmentAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.claimStatus != null">
        claim_status = #{record.claimStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentDesc != null">
        accident_desc = #{record.accidentDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.claimRemark != null">
        claim_remark = #{record.claimRemark,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update webank_claim_push_data
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      endorse_no = #{record.endorseNo,jdbcType=VARCHAR},
      policy_insurant = #{record.policyInsurant,jdbcType=VARCHAR},
      customer_address = #{record.customerAddress,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      apply_date = #{record.applyDate,jdbcType=VARCHAR},
      effective_date = #{record.effectiveDate,jdbcType=VARCHAR},
      employee_start_date = #{record.employeeStartDate,jdbcType=VARCHAR},
      accident_date = #{record.accidentDate,jdbcType=VARCHAR},
      report_date = #{record.reportDate,jdbcType=VARCHAR},
      register_date = #{record.registerDate,jdbcType=VARCHAR},
      close_date = #{record.closeDate,jdbcType=VARCHAR},
      employee_certi_no = #{record.employeeCertiNo,jdbcType=VARCHAR},
      job_category = #{record.jobCategory,jdbcType=VARCHAR},
      occupation = #{record.occupation,jdbcType=VARCHAR},
      insurance_plan = #{record.insurancePlan,jdbcType=VARCHAR},
      loss_cause_name = #{record.lossCauseName,jdbcType=VARCHAR},
      report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      loss_assessment_amount = #{record.lossAssessmentAmount,jdbcType=VARCHAR},
      paid_amount = #{record.paidAmount,jdbcType=VARCHAR},
      claim_status = #{record.claimStatus,jdbcType=VARCHAR},
      accident_desc = #{record.accidentDesc,jdbcType=VARCHAR},
      claim_remark = #{record.claimRemark,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.WebankClaimPushDataDO">
    update webank_claim_push_data
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseNo != null">
        endorse_no = #{endorseNo,jdbcType=VARCHAR},
      </if>
      <if test="policyInsurant != null">
        policy_insurant = #{policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="customerAddress != null">
        customer_address = #{customerAddress,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        apply_date = #{applyDate,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDate != null">
        effective_date = #{effectiveDate,jdbcType=VARCHAR},
      </if>
      <if test="employeeStartDate != null">
        employee_start_date = #{employeeStartDate,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        accident_date = #{accidentDate,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        register_date = #{registerDate,jdbcType=VARCHAR},
      </if>
      <if test="closeDate != null">
        close_date = #{closeDate,jdbcType=VARCHAR},
      </if>
      <if test="employeeCertiNo != null">
        employee_certi_no = #{employeeCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="jobCategory != null">
        job_category = #{jobCategory,jdbcType=VARCHAR},
      </if>
      <if test="occupation != null">
        occupation = #{occupation,jdbcType=VARCHAR},
      </if>
      <if test="insurancePlan != null">
        insurance_plan = #{insurancePlan,jdbcType=VARCHAR},
      </if>
      <if test="lossCauseName != null">
        loss_cause_name = #{lossCauseName,jdbcType=VARCHAR},
      </if>
      <if test="reportAmount != null">
        report_amount = #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="lossAssessmentAmount != null">
        loss_assessment_amount = #{lossAssessmentAmount,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=VARCHAR},
      </if>
      <if test="claimStatus != null">
        claim_status = #{claimStatus,jdbcType=VARCHAR},
      </if>
      <if test="accidentDesc != null">
        accident_desc = #{accidentDesc,jdbcType=VARCHAR},
      </if>
      <if test="claimRemark != null">
        claim_remark = #{claimRemark,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.WebankClaimPushDataDO">
    update webank_claim_push_data
    set report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      endorse_no = #{endorseNo,jdbcType=VARCHAR},
      policy_insurant = #{policyInsurant,jdbcType=VARCHAR},
      customer_address = #{customerAddress,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      apply_date = #{applyDate,jdbcType=VARCHAR},
      effective_date = #{effectiveDate,jdbcType=VARCHAR},
      employee_start_date = #{employeeStartDate,jdbcType=VARCHAR},
      accident_date = #{accidentDate,jdbcType=VARCHAR},
      report_date = #{reportDate,jdbcType=VARCHAR},
      register_date = #{registerDate,jdbcType=VARCHAR},
      close_date = #{closeDate,jdbcType=VARCHAR},
      employee_certi_no = #{employeeCertiNo,jdbcType=VARCHAR},
      job_category = #{jobCategory,jdbcType=VARCHAR},
      occupation = #{occupation,jdbcType=VARCHAR},
      insurance_plan = #{insurancePlan,jdbcType=VARCHAR},
      loss_cause_name = #{lossCauseName,jdbcType=VARCHAR},
      report_amount = #{reportAmount,jdbcType=VARCHAR},
      loss_assessment_amount = #{lossAssessmentAmount,jdbcType=VARCHAR},
      paid_amount = #{paidAmount,jdbcType=VARCHAR},
      claim_status = #{claimStatus,jdbcType=VARCHAR},
      accident_desc = #{accidentDesc,jdbcType=VARCHAR},
      claim_remark = #{claimRemark,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into webank_claim_push_data
    (report_no, policy_no, endorse_no, policy_insurant, customer_address, product_name, 
      apply_date, effective_date, employee_start_date, accident_date, report_date, register_date, 
      close_date, employee_certi_no, job_category, occupation, insurance_plan, loss_cause_name, 
      report_amount, loss_assessment_amount, paid_amount, claim_status, accident_desc, 
      claim_remark, gmt_created, gmt_modified, creator, modifier, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.endorseNo,jdbcType=VARCHAR}, 
        #{item.policyInsurant,jdbcType=VARCHAR}, #{item.customerAddress,jdbcType=VARCHAR}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.applyDate,jdbcType=VARCHAR}, #{item.effectiveDate,jdbcType=VARCHAR}, 
        #{item.employeeStartDate,jdbcType=VARCHAR}, #{item.accidentDate,jdbcType=VARCHAR}, 
        #{item.reportDate,jdbcType=VARCHAR}, #{item.registerDate,jdbcType=VARCHAR}, #{item.closeDate,jdbcType=VARCHAR}, 
        #{item.employeeCertiNo,jdbcType=VARCHAR}, #{item.jobCategory,jdbcType=VARCHAR}, 
        #{item.occupation,jdbcType=VARCHAR}, #{item.insurancePlan,jdbcType=VARCHAR}, #{item.lossCauseName,jdbcType=VARCHAR}, 
        #{item.reportAmount,jdbcType=VARCHAR}, #{item.lossAssessmentAmount,jdbcType=VARCHAR}, 
        #{item.paidAmount,jdbcType=VARCHAR}, #{item.claimStatus,jdbcType=VARCHAR}, #{item.accidentDesc,jdbcType=VARCHAR}, 
        #{item.claimRemark,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into webank_claim_push_data (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'endorse_no'.toString() == column.value">
          #{item.endorseNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_insurant'.toString() == column.value">
          #{item.policyInsurant,jdbcType=VARCHAR}
        </if>
        <if test="'customer_address'.toString() == column.value">
          #{item.customerAddress,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'apply_date'.toString() == column.value">
          #{item.applyDate,jdbcType=VARCHAR}
        </if>
        <if test="'effective_date'.toString() == column.value">
          #{item.effectiveDate,jdbcType=VARCHAR}
        </if>
        <if test="'employee_start_date'.toString() == column.value">
          #{item.employeeStartDate,jdbcType=VARCHAR}
        </if>
        <if test="'accident_date'.toString() == column.value">
          #{item.accidentDate,jdbcType=VARCHAR}
        </if>
        <if test="'report_date'.toString() == column.value">
          #{item.reportDate,jdbcType=VARCHAR}
        </if>
        <if test="'register_date'.toString() == column.value">
          #{item.registerDate,jdbcType=VARCHAR}
        </if>
        <if test="'close_date'.toString() == column.value">
          #{item.closeDate,jdbcType=VARCHAR}
        </if>
        <if test="'employee_certi_no'.toString() == column.value">
          #{item.employeeCertiNo,jdbcType=VARCHAR}
        </if>
        <if test="'job_category'.toString() == column.value">
          #{item.jobCategory,jdbcType=VARCHAR}
        </if>
        <if test="'occupation'.toString() == column.value">
          #{item.occupation,jdbcType=VARCHAR}
        </if>
        <if test="'insurance_plan'.toString() == column.value">
          #{item.insurancePlan,jdbcType=VARCHAR}
        </if>
        <if test="'loss_cause_name'.toString() == column.value">
          #{item.lossCauseName,jdbcType=VARCHAR}
        </if>
        <if test="'report_amount'.toString() == column.value">
          #{item.reportAmount,jdbcType=VARCHAR}
        </if>
        <if test="'loss_assessment_amount'.toString() == column.value">
          #{item.lossAssessmentAmount,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount'.toString() == column.value">
          #{item.paidAmount,jdbcType=VARCHAR}
        </if>
        <if test="'claim_status'.toString() == column.value">
          #{item.claimStatus,jdbcType=VARCHAR}
        </if>
        <if test="'accident_desc'.toString() == column.value">
          #{item.accidentDesc,jdbcType=VARCHAR}
        </if>
        <if test="'claim_remark'.toString() == column.value">
          #{item.claimRemark,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>