<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.InvoiceDetailInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="bill_number" jdbcType="VARCHAR" property="billNumber" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_amount" jdbcType="VARCHAR" property="projectAmount" />
    <result column="self_expense" jdbcType="VARCHAR" property="selfExpense" />
    <result column="self_expense_category" jdbcType="VARCHAR" property="selfExpenseCategory" />
    <result column="non_responsible_cost" jdbcType="VARCHAR" property="nonResponsibleCost" />
    <result column="medical_insurance_pay" jdbcType="VARCHAR" property="medicalInsurancePay" />
    <result column="other_pay" jdbcType="VARCHAR" property="otherPay" />
    <result column="reasonable_pay" jdbcType="VARCHAR" property="reasonablePay" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="attachment_id" jdbcType="VARCHAR" property="attachmentId" />
    <result column="charge_level_desc" jdbcType="VARCHAR" property="chargeLevelDesc" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="self_expense_category_rate" jdbcType="INTEGER" property="selfExpenseCategoryRate" />
    <result column="project_name_match" jdbcType="VARCHAR" property="projectNameMatch" />
    <result column="credibility_level" jdbcType="VARCHAR" property="credibilityLevel" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, bill_number, project_name, project_amount, self_expense, self_expense_category, 
    non_responsible_cost, medical_insurance_pay, other_pay, reasonable_pay, extra_info, 
    remark, is_deleted, gmt_created, gmt_modified, creator, modifier, attachment_id, 
    charge_level_desc, data_source, self_expense_category_rate, project_name_match, credibility_level, 
    province, city
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from invoice_detail_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from invoice_detail_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into invoice_detail_info (report_no, bill_number, project_name, 
      project_amount, self_expense, self_expense_category, 
      non_responsible_cost, medical_insurance_pay, 
      other_pay, reasonable_pay, extra_info, 
      remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, 
      attachment_id, charge_level_desc, data_source, 
      self_expense_category_rate, project_name_match, 
      credibility_level, province, city
      )
    values (#{reportNo,jdbcType=VARCHAR}, #{billNumber,jdbcType=VARCHAR}, #{projectName,jdbcType=VARCHAR}, 
      #{projectAmount,jdbcType=VARCHAR}, #{selfExpense,jdbcType=VARCHAR}, #{selfExpenseCategory,jdbcType=VARCHAR}, 
      #{nonResponsibleCost,jdbcType=VARCHAR}, #{medicalInsurancePay,jdbcType=VARCHAR}, 
      #{otherPay,jdbcType=VARCHAR}, #{reasonablePay,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{attachmentId,jdbcType=VARCHAR}, #{chargeLevelDesc,jdbcType=VARCHAR}, #{dataSource,jdbcType=VARCHAR}, 
      #{selfExpenseCategoryRate,jdbcType=INTEGER}, #{projectNameMatch,jdbcType=VARCHAR}, 
      #{credibilityLevel,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into invoice_detail_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="billNumber != null">
        bill_number,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="projectAmount != null">
        project_amount,
      </if>
      <if test="selfExpense != null">
        self_expense,
      </if>
      <if test="selfExpenseCategory != null">
        self_expense_category,
      </if>
      <if test="nonResponsibleCost != null">
        non_responsible_cost,
      </if>
      <if test="medicalInsurancePay != null">
        medical_insurance_pay,
      </if>
      <if test="otherPay != null">
        other_pay,
      </if>
      <if test="reasonablePay != null">
        reasonable_pay,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="attachmentId != null">
        attachment_id,
      </if>
      <if test="chargeLevelDesc != null">
        charge_level_desc,
      </if>
      <if test="dataSource != null">
        data_source,
      </if>
      <if test="selfExpenseCategoryRate != null">
        self_expense_category_rate,
      </if>
      <if test="projectNameMatch != null">
        project_name_match,
      </if>
      <if test="credibilityLevel != null">
        credibility_level,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="billNumber != null">
        #{billNumber,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectAmount != null">
        #{projectAmount,jdbcType=VARCHAR},
      </if>
      <if test="selfExpense != null">
        #{selfExpense,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseCategory != null">
        #{selfExpenseCategory,jdbcType=VARCHAR},
      </if>
      <if test="nonResponsibleCost != null">
        #{nonResponsibleCost,jdbcType=VARCHAR},
      </if>
      <if test="medicalInsurancePay != null">
        #{medicalInsurancePay,jdbcType=VARCHAR},
      </if>
      <if test="otherPay != null">
        #{otherPay,jdbcType=VARCHAR},
      </if>
      <if test="reasonablePay != null">
        #{reasonablePay,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="attachmentId != null">
        #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="chargeLevelDesc != null">
        #{chargeLevelDesc,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseCategoryRate != null">
        #{selfExpenseCategoryRate,jdbcType=INTEGER},
      </if>
      <if test="projectNameMatch != null">
        #{projectNameMatch,jdbcType=VARCHAR},
      </if>
      <if test="credibilityLevel != null">
        #{credibilityLevel,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoExample" resultType="java.lang.Long">
    select count(*) from invoice_detail_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update invoice_detail_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.billNumber != null">
        bill_number = #{record.billNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.projectName != null">
        project_name = #{record.projectName,jdbcType=VARCHAR},
      </if>
      <if test="record.projectAmount != null">
        project_amount = #{record.projectAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.selfExpense != null">
        self_expense = #{record.selfExpense,jdbcType=VARCHAR},
      </if>
      <if test="record.selfExpenseCategory != null">
        self_expense_category = #{record.selfExpenseCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.nonResponsibleCost != null">
        non_responsible_cost = #{record.nonResponsibleCost,jdbcType=VARCHAR},
      </if>
      <if test="record.medicalInsurancePay != null">
        medical_insurance_pay = #{record.medicalInsurancePay,jdbcType=VARCHAR},
      </if>
      <if test="record.otherPay != null">
        other_pay = #{record.otherPay,jdbcType=VARCHAR},
      </if>
      <if test="record.reasonablePay != null">
        reasonable_pay = #{record.reasonablePay,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentId != null">
        attachment_id = #{record.attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeLevelDesc != null">
        charge_level_desc = #{record.chargeLevelDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.dataSource != null">
        data_source = #{record.dataSource,jdbcType=VARCHAR},
      </if>
      <if test="record.selfExpenseCategoryRate != null">
        self_expense_category_rate = #{record.selfExpenseCategoryRate,jdbcType=INTEGER},
      </if>
      <if test="record.projectNameMatch != null">
        project_name_match = #{record.projectNameMatch,jdbcType=VARCHAR},
      </if>
      <if test="record.credibilityLevel != null">
        credibility_level = #{record.credibilityLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update invoice_detail_info
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      bill_number = #{record.billNumber,jdbcType=VARCHAR},
      project_name = #{record.projectName,jdbcType=VARCHAR},
      project_amount = #{record.projectAmount,jdbcType=VARCHAR},
      self_expense = #{record.selfExpense,jdbcType=VARCHAR},
      self_expense_category = #{record.selfExpenseCategory,jdbcType=VARCHAR},
      non_responsible_cost = #{record.nonResponsibleCost,jdbcType=VARCHAR},
      medical_insurance_pay = #{record.medicalInsurancePay,jdbcType=VARCHAR},
      other_pay = #{record.otherPay,jdbcType=VARCHAR},
      reasonable_pay = #{record.reasonablePay,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      attachment_id = #{record.attachmentId,jdbcType=VARCHAR},
      charge_level_desc = #{record.chargeLevelDesc,jdbcType=VARCHAR},
      data_source = #{record.dataSource,jdbcType=VARCHAR},
      self_expense_category_rate = #{record.selfExpenseCategoryRate,jdbcType=INTEGER},
      project_name_match = #{record.projectNameMatch,jdbcType=VARCHAR},
      credibility_level = #{record.credibilityLevel,jdbcType=VARCHAR},
      province = #{record.province,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoDO">
    update invoice_detail_info
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="billNumber != null">
        bill_number = #{billNumber,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectAmount != null">
        project_amount = #{projectAmount,jdbcType=VARCHAR},
      </if>
      <if test="selfExpense != null">
        self_expense = #{selfExpense,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseCategory != null">
        self_expense_category = #{selfExpenseCategory,jdbcType=VARCHAR},
      </if>
      <if test="nonResponsibleCost != null">
        non_responsible_cost = #{nonResponsibleCost,jdbcType=VARCHAR},
      </if>
      <if test="medicalInsurancePay != null">
        medical_insurance_pay = #{medicalInsurancePay,jdbcType=VARCHAR},
      </if>
      <if test="otherPay != null">
        other_pay = #{otherPay,jdbcType=VARCHAR},
      </if>
      <if test="reasonablePay != null">
        reasonable_pay = #{reasonablePay,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="attachmentId != null">
        attachment_id = #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="chargeLevelDesc != null">
        charge_level_desc = #{chargeLevelDesc,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        data_source = #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="selfExpenseCategoryRate != null">
        self_expense_category_rate = #{selfExpenseCategoryRate,jdbcType=INTEGER},
      </if>
      <if test="projectNameMatch != null">
        project_name_match = #{projectNameMatch,jdbcType=VARCHAR},
      </if>
      <if test="credibilityLevel != null">
        credibility_level = #{credibilityLevel,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.InvoiceDetailInfoDO">
    update invoice_detail_info
    set report_no = #{reportNo,jdbcType=VARCHAR},
      bill_number = #{billNumber,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      project_amount = #{projectAmount,jdbcType=VARCHAR},
      self_expense = #{selfExpense,jdbcType=VARCHAR},
      self_expense_category = #{selfExpenseCategory,jdbcType=VARCHAR},
      non_responsible_cost = #{nonResponsibleCost,jdbcType=VARCHAR},
      medical_insurance_pay = #{medicalInsurancePay,jdbcType=VARCHAR},
      other_pay = #{otherPay,jdbcType=VARCHAR},
      reasonable_pay = #{reasonablePay,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      attachment_id = #{attachmentId,jdbcType=VARCHAR},
      charge_level_desc = #{chargeLevelDesc,jdbcType=VARCHAR},
      data_source = #{dataSource,jdbcType=VARCHAR},
      self_expense_category_rate = #{selfExpenseCategoryRate,jdbcType=INTEGER},
      project_name_match = #{projectNameMatch,jdbcType=VARCHAR},
      credibility_level = #{credibilityLevel,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into invoice_detail_info
    (report_no, bill_number, project_name, project_amount, self_expense, self_expense_category, 
      non_responsible_cost, medical_insurance_pay, other_pay, reasonable_pay, extra_info, 
      remark, is_deleted, gmt_created, gmt_modified, creator, modifier, attachment_id, 
      charge_level_desc, data_source, self_expense_category_rate, project_name_match, 
      credibility_level, province, city)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reportNo,jdbcType=VARCHAR}, #{item.billNumber,jdbcType=VARCHAR}, #{item.projectName,jdbcType=VARCHAR}, 
        #{item.projectAmount,jdbcType=VARCHAR}, #{item.selfExpense,jdbcType=VARCHAR}, #{item.selfExpenseCategory,jdbcType=VARCHAR}, 
        #{item.nonResponsibleCost,jdbcType=VARCHAR}, #{item.medicalInsurancePay,jdbcType=VARCHAR}, 
        #{item.otherPay,jdbcType=VARCHAR}, #{item.reasonablePay,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.attachmentId,jdbcType=VARCHAR}, #{item.chargeLevelDesc,jdbcType=VARCHAR}, 
        #{item.dataSource,jdbcType=VARCHAR}, #{item.selfExpenseCategoryRate,jdbcType=INTEGER}, 
        #{item.projectNameMatch,jdbcType=VARCHAR}, #{item.credibilityLevel,jdbcType=VARCHAR}, 
        #{item.province,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into invoice_detail_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'bill_number'.toString() == column.value">
          #{item.billNumber,jdbcType=VARCHAR}
        </if>
        <if test="'project_name'.toString() == column.value">
          #{item.projectName,jdbcType=VARCHAR}
        </if>
        <if test="'project_amount'.toString() == column.value">
          #{item.projectAmount,jdbcType=VARCHAR}
        </if>
        <if test="'self_expense'.toString() == column.value">
          #{item.selfExpense,jdbcType=VARCHAR}
        </if>
        <if test="'self_expense_category'.toString() == column.value">
          #{item.selfExpenseCategory,jdbcType=VARCHAR}
        </if>
        <if test="'non_responsible_cost'.toString() == column.value">
          #{item.nonResponsibleCost,jdbcType=VARCHAR}
        </if>
        <if test="'medical_insurance_pay'.toString() == column.value">
          #{item.medicalInsurancePay,jdbcType=VARCHAR}
        </if>
        <if test="'other_pay'.toString() == column.value">
          #{item.otherPay,jdbcType=VARCHAR}
        </if>
        <if test="'reasonable_pay'.toString() == column.value">
          #{item.reasonablePay,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_id'.toString() == column.value">
          #{item.attachmentId,jdbcType=VARCHAR}
        </if>
        <if test="'charge_level_desc'.toString() == column.value">
          #{item.chargeLevelDesc,jdbcType=VARCHAR}
        </if>
        <if test="'data_source'.toString() == column.value">
          #{item.dataSource,jdbcType=VARCHAR}
        </if>
        <if test="'self_expense_category_rate'.toString() == column.value">
          #{item.selfExpenseCategoryRate,jdbcType=INTEGER}
        </if>
        <if test="'project_name_match'.toString() == column.value">
          #{item.projectNameMatch,jdbcType=VARCHAR}
        </if>
        <if test="'credibility_level'.toString() == column.value">
          #{item.credibilityLevel,jdbcType=VARCHAR}
        </if>
        <if test="'province'.toString() == column.value">
          #{item.province,jdbcType=VARCHAR}
        </if>
        <if test="'city'.toString() == column.value">
          #{item.city,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>