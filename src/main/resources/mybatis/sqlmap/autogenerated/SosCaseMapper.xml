<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.SosCaseMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.SosCaseDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_no" jdbcType="VARCHAR" property="caseNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="service_type" jdbcType="VARCHAR" property="serviceType" />
    <result column="accident_place" jdbcType="VARCHAR" property="accidentPlace" />
    <result column="accident_date" jdbcType="TIMESTAMP" property="accidentDate" />
    <result column="case_brief" jdbcType="VARCHAR" property="caseBrief" />
    <result column="insurant_name" jdbcType="VARCHAR" property="insurantName" />
    <result column="insurant_cert_no" jdbcType="VARCHAR" property="insurantCertNo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, case_no, policy_no, service_type, accident_place, accident_date, case_brief, 
    insurant_name, insurant_cert_no, creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.SosCaseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sos_case
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sos_case
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.SosCaseDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sos_case (case_no, policy_no, service_type, 
      accident_place, accident_date, case_brief, 
      insurant_name, insurant_cert_no, creator, 
      modifier, gmt_created, gmt_modified, 
      is_deleted)
    values (#{caseNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, #{serviceType,jdbcType=VARCHAR}, 
      #{accidentPlace,jdbcType=VARCHAR}, #{accidentDate,jdbcType=TIMESTAMP}, #{caseBrief,jdbcType=VARCHAR}, 
      #{insurantName,jdbcType=VARCHAR}, #{insurantCertNo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate(), 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.SosCaseDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sos_case
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="caseNo != null">
        case_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="serviceType != null">
        service_type,
      </if>
      <if test="accidentPlace != null">
        accident_place,
      </if>
      <if test="accidentDate != null">
        accident_date,
      </if>
      <if test="caseBrief != null">
        case_brief,
      </if>
      <if test="insurantName != null">
        insurant_name,
      </if>
      <if test="insurantCertNo != null">
        insurant_cert_no,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="caseNo != null">
        #{caseNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        #{serviceType,jdbcType=VARCHAR},
      </if>
      <if test="accidentPlace != null">
        #{accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        #{accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="caseBrief != null">
        #{caseBrief,jdbcType=VARCHAR},
      </if>
      <if test="insurantName != null">
        #{insurantName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertNo != null">
        #{insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.SosCaseExample" resultType="java.lang.Long">
    select count(*) from sos_case
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sos_case
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.caseNo != null">
        case_no = #{record.caseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceType != null">
        service_type = #{record.serviceType,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentPlace != null">
        accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentDate != null">
        accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.caseBrief != null">
        case_brief = #{record.caseBrief,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantName != null">
        insurant_name = #{record.insurantName,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCertNo != null">
        insurant_cert_no = #{record.insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sos_case
    set id = #{record.id,jdbcType=BIGINT},
      case_no = #{record.caseNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      service_type = #{record.serviceType,jdbcType=VARCHAR},
      accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      case_brief = #{record.caseBrief,jdbcType=VARCHAR},
      insurant_name = #{record.insurantName,jdbcType=VARCHAR},
      insurant_cert_no = #{record.insurantCertNo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.SosCaseDO">
    update sos_case
    <set>
      <if test="caseNo != null">
        case_no = #{caseNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        service_type = #{serviceType,jdbcType=VARCHAR},
      </if>
      <if test="accidentPlace != null">
        accident_place = #{accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="caseBrief != null">
        case_brief = #{caseBrief,jdbcType=VARCHAR},
      </if>
      <if test="insurantName != null">
        insurant_name = #{insurantName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertNo != null">
        insurant_cert_no = #{insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.SosCaseDO">
    update sos_case
    set case_no = #{caseNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      service_type = #{serviceType,jdbcType=VARCHAR},
      accident_place = #{accidentPlace,jdbcType=VARCHAR},
      accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      case_brief = #{caseBrief,jdbcType=VARCHAR},
      insurant_name = #{insurantName,jdbcType=VARCHAR},
      insurant_cert_no = #{insurantCertNo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into sos_case
    (case_no, policy_no, service_type, accident_place, accident_date, case_brief, insurant_name, 
      insurant_cert_no, creator, modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.caseNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.serviceType,jdbcType=VARCHAR}, 
        #{item.accidentPlace,jdbcType=VARCHAR}, #{item.accidentDate,jdbcType=TIMESTAMP}, 
        #{item.caseBrief,jdbcType=VARCHAR}, #{item.insurantName,jdbcType=VARCHAR}, #{item.insurantCertNo,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into sos_case (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'case_no'.toString() == column.value">
          #{item.caseNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'service_type'.toString() == column.value">
          #{item.serviceType,jdbcType=VARCHAR}
        </if>
        <if test="'accident_place'.toString() == column.value">
          #{item.accidentPlace,jdbcType=VARCHAR}
        </if>
        <if test="'accident_date'.toString() == column.value">
          #{item.accidentDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'case_brief'.toString() == column.value">
          #{item.caseBrief,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_name'.toString() == column.value">
          #{item.insurantName,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_cert_no'.toString() == column.value">
          #{item.insurantCertNo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>