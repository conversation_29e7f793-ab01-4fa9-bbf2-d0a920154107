<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.OnKneadingBillMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.OnKneadingBillDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="claim_no" jdbcType="VARCHAR" property="claimNo" />
    <result column="campaign_def_id" jdbcType="BIGINT" property="campaignDefId" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="cash_id" jdbcType="BIGINT" property="cashId" />
    <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
    <result column="paid_amount" jdbcType="DECIMAL" property="paidAmount" />
    <result column="knead_config_id" jdbcType="BIGINT" property="kneadConfigId" />
    <result column="payment_date" jdbcType="TIMESTAMP" property="paymentDate" />
    <result column="use_des" jdbcType="VARCHAR" property="useDes" />
    <result column="is_kneading" jdbcType="CHAR" property="isKneading" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_no, report_no, claim_no, campaign_def_id, package_def_id, cash_id, batch_id, 
    paid_amount, knead_config_id, payment_date, use_des, is_kneading, extra_info, creator, 
    modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.OnKneadingBillExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from on_kneading_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from on_kneading_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.OnKneadingBillDO">
    insert into on_kneading_bill (id, policy_no, report_no, 
      claim_no, campaign_def_id, package_def_id, 
      cash_id, batch_id, paid_amount, 
      knead_config_id, payment_date, use_des, 
      is_kneading, extra_info, creator, 
      modifier, gmt_created, gmt_modified, 
      is_deleted)
    values (#{id,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{claimNo,jdbcType=VARCHAR}, #{campaignDefId,jdbcType=BIGINT}, #{packageDefId,jdbcType=BIGINT}, 
      #{cashId,jdbcType=BIGINT}, #{batchId,jdbcType=VARCHAR}, #{paidAmount,jdbcType=DECIMAL}, 
      #{kneadConfigId,jdbcType=BIGINT}, #{paymentDate,jdbcType=TIMESTAMP}, #{useDes,jdbcType=VARCHAR}, 
      #{isKneading,jdbcType=CHAR}, #{extraInfo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate(), 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.OnKneadingBillDO">
    insert into on_kneading_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="claimNo != null">
        claim_no,
      </if>
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="cashId != null">
        cash_id,
      </if>
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="kneadConfigId != null">
        knead_config_id,
      </if>
      <if test="paymentDate != null">
        payment_date,
      </if>
      <if test="useDes != null">
        use_des,
      </if>
      <if test="isKneading != null">
        is_kneading,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="cashId != null">
        #{cashId,jdbcType=BIGINT},
      </if>
      <if test="batchId != null">
        #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="kneadConfigId != null">
        #{kneadConfigId,jdbcType=BIGINT},
      </if>
      <if test="paymentDate != null">
        #{paymentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="useDes != null">
        #{useDes,jdbcType=VARCHAR},
      </if>
      <if test="isKneading != null">
        #{isKneading,jdbcType=CHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.OnKneadingBillExample" resultType="java.lang.Long">
    select count(*) from on_kneading_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update on_kneading_bill
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimNo != null">
        claim_no = #{record.claimNo,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.cashId != null">
        cash_id = #{record.cashId,jdbcType=BIGINT},
      </if>
      <if test="record.batchId != null">
        batch_id = #{record.batchId,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.kneadConfigId != null">
        knead_config_id = #{record.kneadConfigId,jdbcType=BIGINT},
      </if>
      <if test="record.paymentDate != null">
        payment_date = #{record.paymentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.useDes != null">
        use_des = #{record.useDes,jdbcType=VARCHAR},
      </if>
      <if test="record.isKneading != null">
        is_kneading = #{record.isKneading,jdbcType=CHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update on_kneading_bill
    set id = #{record.id,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      claim_no = #{record.claimNo,jdbcType=VARCHAR},
      campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      cash_id = #{record.cashId,jdbcType=BIGINT},
      batch_id = #{record.batchId,jdbcType=VARCHAR},
      paid_amount = #{record.paidAmount,jdbcType=DECIMAL},
      knead_config_id = #{record.kneadConfigId,jdbcType=BIGINT},
      payment_date = #{record.paymentDate,jdbcType=TIMESTAMP},
      use_des = #{record.useDes,jdbcType=VARCHAR},
      is_kneading = #{record.isKneading,jdbcType=CHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.OnKneadingBillDO">
    update on_kneading_bill
    <set>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        claim_no = #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="cashId != null">
        cash_id = #{cashId,jdbcType=BIGINT},
      </if>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="kneadConfigId != null">
        knead_config_id = #{kneadConfigId,jdbcType=BIGINT},
      </if>
      <if test="paymentDate != null">
        payment_date = #{paymentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="useDes != null">
        use_des = #{useDes,jdbcType=VARCHAR},
      </if>
      <if test="isKneading != null">
        is_kneading = #{isKneading,jdbcType=CHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.OnKneadingBillDO">
    update on_kneading_bill
    set policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      claim_no = #{claimNo,jdbcType=VARCHAR},
      campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      package_def_id = #{packageDefId,jdbcType=BIGINT},
      cash_id = #{cashId,jdbcType=BIGINT},
      batch_id = #{batchId,jdbcType=VARCHAR},
      paid_amount = #{paidAmount,jdbcType=DECIMAL},
      knead_config_id = #{kneadConfigId,jdbcType=BIGINT},
      payment_date = #{paymentDate,jdbcType=TIMESTAMP},
      use_des = #{useDes,jdbcType=VARCHAR},
      is_kneading = #{isKneading,jdbcType=CHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into on_kneading_bill
    (id, policy_no, report_no, claim_no, campaign_def_id, package_def_id, cash_id, batch_id, 
      paid_amount, knead_config_id, payment_date, use_des, is_kneading, extra_info, creator, 
      modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.claimNo,jdbcType=VARCHAR}, #{item.campaignDefId,jdbcType=BIGINT}, #{item.packageDefId,jdbcType=BIGINT}, 
        #{item.cashId,jdbcType=BIGINT}, #{item.batchId,jdbcType=VARCHAR}, #{item.paidAmount,jdbcType=DECIMAL}, 
        #{item.kneadConfigId,jdbcType=BIGINT}, #{item.paymentDate,jdbcType=TIMESTAMP}, 
        #{item.useDes,jdbcType=VARCHAR}, #{item.isKneading,jdbcType=CHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into on_kneading_bill (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'claim_no'.toString() == column.value">
          #{item.claimNo,jdbcType=VARCHAR}
        </if>
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=BIGINT}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'cash_id'.toString() == column.value">
          #{item.cashId,jdbcType=BIGINT}
        </if>
        <if test="'batch_id'.toString() == column.value">
          #{item.batchId,jdbcType=VARCHAR}
        </if>
        <if test="'paid_amount'.toString() == column.value">
          #{item.paidAmount,jdbcType=DECIMAL}
        </if>
        <if test="'knead_config_id'.toString() == column.value">
          #{item.kneadConfigId,jdbcType=BIGINT}
        </if>
        <if test="'payment_date'.toString() == column.value">
          #{item.paymentDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'use_des'.toString() == column.value">
          #{item.useDes,jdbcType=VARCHAR}
        </if>
        <if test="'is_kneading'.toString() == column.value">
          #{item.isKneading,jdbcType=CHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>