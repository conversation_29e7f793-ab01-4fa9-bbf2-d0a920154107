<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimBatchHeadInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimBatchHeadInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_claim_bill_no" jdbcType="VARCHAR" property="batchClaimBillNo" />
    <result column="degree_of_disability" jdbcType="VARCHAR" property="degreeOfDisability" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="campaign_def_id" jdbcType="BIGINT" property="campaignDefId" />
    <result column="campaign_full_name" jdbcType="VARCHAR" property="campaignFullName" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="out_product_code" jdbcType="VARCHAR" property="outProductCode" />
    <result column="accident_date" jdbcType="TIMESTAMP" property="accidentDate" />
    <result column="accident_place" jdbcType="VARCHAR" property="accidentPlace" />
    <result column="accident_desc" jdbcType="VARCHAR" property="accidentDesc" />
    <result column="report_date" jdbcType="TIMESTAMP" property="reportDate" />
    <result column="claim_currency" jdbcType="VARCHAR" property="claimCurrency" />
    <result column="report_amount" jdbcType="VARCHAR" property="reportAmount" />
    <result column="loss_amount" jdbcType="VARCHAR" property="lossAmount" />
    <result column="loss_cause" jdbcType="VARCHAR" property="lossCause" />
    <result column="loss_cause_name" jdbcType="VARCHAR" property="lossCauseName" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="insurant_name" jdbcType="VARCHAR" property="insurantName" />
    <result column="insurant_cert_type" jdbcType="VARCHAR" property="insurantCertType" />
    <result column="insurant_cert_no" jdbcType="VARCHAR" property="insurantCertNo" />
    <result column="insurant_phone" jdbcType="VARCHAR" property="insurantPhone" />
    <result column="insurant_is_died" jdbcType="CHAR" property="insurantIsDied" />
    <result column="report_user_name" jdbcType="VARCHAR" property="reportUserName" />
    <result column="reportor_relinsured" jdbcType="VARCHAR" property="reportorRelinsured" />
    <result column="reportor_phone" jdbcType="VARCHAR" property="reportorPhone" />
    <result column="reportor_email" jdbcType="VARCHAR" property="reportorEmail" />
    <result column="claimant_name" jdbcType="VARCHAR" property="claimantName" />
    <result column="claimant_phone" jdbcType="VARCHAR" property="claimantPhone" />
    <result column="claimant_email" jdbcType="VARCHAR" property="claimantEmail" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
    <result column="is_auto_register" jdbcType="CHAR" property="isAutoRegister" />
    <result column="is_hang_up" jdbcType="CHAR" property="isHangUp" />
    <result column="insure_place" jdbcType="VARCHAR" property="insurePlace" />
    <result column="agency_user_id" jdbcType="BIGINT" property="agencyUserId" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="claim_type" jdbcType="TINYINT" property="claimType" />
    <result column="register_user" jdbcType="VARCHAR" property="registerUser" />
    <result column="register_date" jdbcType="TIMESTAMP" property="registerDate" />
    <result column="settlement_date" jdbcType="TIMESTAMP" property="settlementDate" />
    <result column="close_date" jdbcType="TIMESTAMP" property="closeDate" />
    <result column="is_confirm" jdbcType="CHAR" property="isConfirm" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="pre_paid_audit_status" jdbcType="VARCHAR" property="prePaidAuditStatus" />
    <result column="is_handle" jdbcType="CHAR" property="isHandle" />
    <result column="claim_timeliness" jdbcType="INTEGER" property="claimTimeliness" />
    <result column="pay_state" jdbcType="TINYINT" property="payState" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, batch_claim_bill_no, degree_of_disability, channel_id, channel_name, campaign_def_id, 
    campaign_full_name, package_def_id, product_id, product_name, out_product_code, accident_date, 
    accident_place, accident_desc, report_date, claim_currency, report_amount, loss_amount, 
    loss_cause, loss_cause_name, extra_info, insurant_name, insurant_cert_type, insurant_cert_no, 
    insurant_phone, insurant_is_died, report_user_name, reportor_relinsured, reportor_phone, 
    reportor_email, claimant_name, claimant_phone, claimant_email, hospital_name, is_auto_register, 
    is_hang_up, insure_place, agency_user_id, auditor, status, claim_type, register_user, 
    register_date, settlement_date, close_date, is_confirm, remark, policy_no, pre_paid_audit_status, 
    is_handle, claim_timeliness, pay_state, is_deleted, gmt_created, gmt_modified, creator, 
    modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchHeadInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_batch_head_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_batch_head_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchHeadInfoDO">
    insert into claim_batch_head_info (id, batch_claim_bill_no, degree_of_disability, 
      channel_id, channel_name, campaign_def_id, 
      campaign_full_name, package_def_id, product_id, 
      product_name, out_product_code, accident_date, 
      accident_place, accident_desc, report_date, 
      claim_currency, report_amount, loss_amount, 
      loss_cause, loss_cause_name, extra_info, 
      insurant_name, insurant_cert_type, insurant_cert_no, 
      insurant_phone, insurant_is_died, report_user_name, 
      reportor_relinsured, reportor_phone, reportor_email, 
      claimant_name, claimant_phone, claimant_email, 
      hospital_name, is_auto_register, is_hang_up, 
      insure_place, agency_user_id, auditor, 
      status, claim_type, register_user, 
      register_date, settlement_date, close_date, 
      is_confirm, remark, policy_no, 
      pre_paid_audit_status, is_handle, claim_timeliness, 
      pay_state, is_deleted, gmt_created, 
      gmt_modified, creator, modifier
      )
    values (#{id,jdbcType=BIGINT}, #{batchClaimBillNo,jdbcType=VARCHAR}, #{degreeOfDisability,jdbcType=VARCHAR}, 
      #{channelId,jdbcType=BIGINT}, #{channelName,jdbcType=VARCHAR}, #{campaignDefId,jdbcType=BIGINT}, 
      #{campaignFullName,jdbcType=VARCHAR}, #{packageDefId,jdbcType=BIGINT}, #{productId,jdbcType=BIGINT}, 
      #{productName,jdbcType=VARCHAR}, #{outProductCode,jdbcType=VARCHAR}, #{accidentDate,jdbcType=TIMESTAMP}, 
      #{accidentPlace,jdbcType=VARCHAR}, #{accidentDesc,jdbcType=VARCHAR}, #{reportDate,jdbcType=TIMESTAMP}, 
      #{claimCurrency,jdbcType=VARCHAR}, #{reportAmount,jdbcType=VARCHAR}, #{lossAmount,jdbcType=VARCHAR}, 
      #{lossCause,jdbcType=VARCHAR}, #{lossCauseName,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      #{insurantName,jdbcType=VARCHAR}, #{insurantCertType,jdbcType=VARCHAR}, #{insurantCertNo,jdbcType=VARCHAR}, 
      #{insurantPhone,jdbcType=VARCHAR}, #{insurantIsDied,jdbcType=CHAR}, #{reportUserName,jdbcType=VARCHAR}, 
      #{reportorRelinsured,jdbcType=VARCHAR}, #{reportorPhone,jdbcType=VARCHAR}, #{reportorEmail,jdbcType=VARCHAR}, 
      #{claimantName,jdbcType=VARCHAR}, #{claimantPhone,jdbcType=VARCHAR}, #{claimantEmail,jdbcType=VARCHAR}, 
      #{hospitalName,jdbcType=VARCHAR}, #{isAutoRegister,jdbcType=CHAR}, #{isHangUp,jdbcType=CHAR}, 
      #{insurePlace,jdbcType=VARCHAR}, #{agencyUserId,jdbcType=BIGINT}, #{auditor,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{claimType,jdbcType=TINYINT}, #{registerUser,jdbcType=VARCHAR}, 
      #{registerDate,jdbcType=TIMESTAMP}, #{settlementDate,jdbcType=TIMESTAMP}, #{closeDate,jdbcType=TIMESTAMP}, 
      #{isConfirm,jdbcType=CHAR}, #{remark,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, 
      #{prePaidAuditStatus,jdbcType=VARCHAR}, #{isHandle,jdbcType=CHAR}, #{claimTimeliness,jdbcType=INTEGER}, 
      #{payState,jdbcType=TINYINT}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchHeadInfoDO">
    insert into claim_batch_head_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no,
      </if>
      <if test="degreeOfDisability != null">
        degree_of_disability,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="channelName != null">
        channel_name,
      </if>
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="campaignFullName != null">
        campaign_full_name,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="outProductCode != null">
        out_product_code,
      </if>
      <if test="accidentDate != null">
        accident_date,
      </if>
      <if test="accidentPlace != null">
        accident_place,
      </if>
      <if test="accidentDesc != null">
        accident_desc,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="claimCurrency != null">
        claim_currency,
      </if>
      <if test="reportAmount != null">
        report_amount,
      </if>
      <if test="lossAmount != null">
        loss_amount,
      </if>
      <if test="lossCause != null">
        loss_cause,
      </if>
      <if test="lossCauseName != null">
        loss_cause_name,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="insurantName != null">
        insurant_name,
      </if>
      <if test="insurantCertType != null">
        insurant_cert_type,
      </if>
      <if test="insurantCertNo != null">
        insurant_cert_no,
      </if>
      <if test="insurantPhone != null">
        insurant_phone,
      </if>
      <if test="insurantIsDied != null">
        insurant_is_died,
      </if>
      <if test="reportUserName != null">
        report_user_name,
      </if>
      <if test="reportorRelinsured != null">
        reportor_relinsured,
      </if>
      <if test="reportorPhone != null">
        reportor_phone,
      </if>
      <if test="reportorEmail != null">
        reportor_email,
      </if>
      <if test="claimantName != null">
        claimant_name,
      </if>
      <if test="claimantPhone != null">
        claimant_phone,
      </if>
      <if test="claimantEmail != null">
        claimant_email,
      </if>
      <if test="hospitalName != null">
        hospital_name,
      </if>
      <if test="isAutoRegister != null">
        is_auto_register,
      </if>
      <if test="isHangUp != null">
        is_hang_up,
      </if>
      <if test="insurePlace != null">
        insure_place,
      </if>
      <if test="agencyUserId != null">
        agency_user_id,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="claimType != null">
        claim_type,
      </if>
      <if test="registerUser != null">
        register_user,
      </if>
      <if test="registerDate != null">
        register_date,
      </if>
      <if test="settlementDate != null">
        settlement_date,
      </if>
      <if test="closeDate != null">
        close_date,
      </if>
      <if test="isConfirm != null">
        is_confirm,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="prePaidAuditStatus != null">
        pre_paid_audit_status,
      </if>
      <if test="isHandle != null">
        is_handle,
      </if>
      <if test="claimTimeliness != null">
        claim_timeliness,
      </if>
      <if test="payState != null">
        pay_state,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillNo != null">
        #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="degreeOfDisability != null">
        #{degreeOfDisability,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="channelName != null">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="campaignFullName != null">
        #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="outProductCode != null">
        #{outProductCode,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        #{accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="accidentPlace != null">
        #{accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="accidentDesc != null">
        #{accidentDesc,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="claimCurrency != null">
        #{claimCurrency,jdbcType=VARCHAR},
      </if>
      <if test="reportAmount != null">
        #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="lossAmount != null">
        #{lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="lossCause != null">
        #{lossCause,jdbcType=VARCHAR},
      </if>
      <if test="lossCauseName != null">
        #{lossCauseName,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="insurantName != null">
        #{insurantName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertType != null">
        #{insurantCertType,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertNo != null">
        #{insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantPhone != null">
        #{insurantPhone,jdbcType=VARCHAR},
      </if>
      <if test="insurantIsDied != null">
        #{insurantIsDied,jdbcType=CHAR},
      </if>
      <if test="reportUserName != null">
        #{reportUserName,jdbcType=VARCHAR},
      </if>
      <if test="reportorRelinsured != null">
        #{reportorRelinsured,jdbcType=VARCHAR},
      </if>
      <if test="reportorPhone != null">
        #{reportorPhone,jdbcType=VARCHAR},
      </if>
      <if test="reportorEmail != null">
        #{reportorEmail,jdbcType=VARCHAR},
      </if>
      <if test="claimantName != null">
        #{claimantName,jdbcType=VARCHAR},
      </if>
      <if test="claimantPhone != null">
        #{claimantPhone,jdbcType=VARCHAR},
      </if>
      <if test="claimantEmail != null">
        #{claimantEmail,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="isAutoRegister != null">
        #{isAutoRegister,jdbcType=CHAR},
      </if>
      <if test="isHangUp != null">
        #{isHangUp,jdbcType=CHAR},
      </if>
      <if test="insurePlace != null">
        #{insurePlace,jdbcType=VARCHAR},
      </if>
      <if test="agencyUserId != null">
        #{agencyUserId,jdbcType=BIGINT},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="claimType != null">
        #{claimType,jdbcType=TINYINT},
      </if>
      <if test="registerUser != null">
        #{registerUser,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementDate != null">
        #{settlementDate,jdbcType=TIMESTAMP},
      </if>
      <if test="closeDate != null">
        #{closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isConfirm != null">
        #{isConfirm,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="prePaidAuditStatus != null">
        #{prePaidAuditStatus,jdbcType=VARCHAR},
      </if>
      <if test="isHandle != null">
        #{isHandle,jdbcType=CHAR},
      </if>
      <if test="claimTimeliness != null">
        #{claimTimeliness,jdbcType=INTEGER},
      </if>
      <if test="payState != null">
        #{payState,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchHeadInfoExample" resultType="java.lang.Long">
    select count(*) from claim_batch_head_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_batch_head_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.batchClaimBillNo != null">
        batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.degreeOfDisability != null">
        degree_of_disability = #{record.degreeOfDisability,jdbcType=VARCHAR},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=BIGINT},
      </if>
      <if test="record.channelName != null">
        channel_name = #{record.channelName,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="record.campaignFullName != null">
        campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.outProductCode != null">
        out_product_code = #{record.outProductCode,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentDate != null">
        accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accidentPlace != null">
        accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentDesc != null">
        accident_desc = #{record.accidentDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDate != null">
        report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.claimCurrency != null">
        claim_currency = #{record.claimCurrency,jdbcType=VARCHAR},
      </if>
      <if test="record.reportAmount != null">
        report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.lossAmount != null">
        loss_amount = #{record.lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.lossCause != null">
        loss_cause = #{record.lossCause,jdbcType=VARCHAR},
      </if>
      <if test="record.lossCauseName != null">
        loss_cause_name = #{record.lossCauseName,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantName != null">
        insurant_name = #{record.insurantName,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCertType != null">
        insurant_cert_type = #{record.insurantCertType,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCertNo != null">
        insurant_cert_no = #{record.insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantPhone != null">
        insurant_phone = #{record.insurantPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantIsDied != null">
        insurant_is_died = #{record.insurantIsDied,jdbcType=CHAR},
      </if>
      <if test="record.reportUserName != null">
        report_user_name = #{record.reportUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.reportorRelinsured != null">
        reportor_relinsured = #{record.reportorRelinsured,jdbcType=VARCHAR},
      </if>
      <if test="record.reportorPhone != null">
        reportor_phone = #{record.reportorPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.reportorEmail != null">
        reportor_email = #{record.reportorEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.claimantName != null">
        claimant_name = #{record.claimantName,jdbcType=VARCHAR},
      </if>
      <if test="record.claimantPhone != null">
        claimant_phone = #{record.claimantPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.claimantEmail != null">
        claimant_email = #{record.claimantEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalName != null">
        hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="record.isAutoRegister != null">
        is_auto_register = #{record.isAutoRegister,jdbcType=CHAR},
      </if>
      <if test="record.isHangUp != null">
        is_hang_up = #{record.isHangUp,jdbcType=CHAR},
      </if>
      <if test="record.insurePlace != null">
        insure_place = #{record.insurePlace,jdbcType=VARCHAR},
      </if>
      <if test="record.agencyUserId != null">
        agency_user_id = #{record.agencyUserId,jdbcType=BIGINT},
      </if>
      <if test="record.auditor != null">
        auditor = #{record.auditor,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.claimType != null">
        claim_type = #{record.claimType,jdbcType=TINYINT},
      </if>
      <if test="record.registerUser != null">
        register_user = #{record.registerUser,jdbcType=VARCHAR},
      </if>
      <if test="record.registerDate != null">
        register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.settlementDate != null">
        settlement_date = #{record.settlementDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.closeDate != null">
        close_date = #{record.closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isConfirm != null">
        is_confirm = #{record.isConfirm,jdbcType=CHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.prePaidAuditStatus != null">
        pre_paid_audit_status = #{record.prePaidAuditStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.isHandle != null">
        is_handle = #{record.isHandle,jdbcType=CHAR},
      </if>
      <if test="record.claimTimeliness != null">
        claim_timeliness = #{record.claimTimeliness,jdbcType=INTEGER},
      </if>
      <if test="record.payState != null">
        pay_state = #{record.payState,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_batch_head_info
    set id = #{record.id,jdbcType=BIGINT},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      degree_of_disability = #{record.degreeOfDisability,jdbcType=VARCHAR},
      channel_id = #{record.channelId,jdbcType=BIGINT},
      channel_name = #{record.channelName,jdbcType=VARCHAR},
      campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      product_id = #{record.productId,jdbcType=BIGINT},
      product_name = #{record.productName,jdbcType=VARCHAR},
      out_product_code = #{record.outProductCode,jdbcType=VARCHAR},
      accident_date = #{record.accidentDate,jdbcType=TIMESTAMP},
      accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      accident_desc = #{record.accidentDesc,jdbcType=VARCHAR},
      report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      claim_currency = #{record.claimCurrency,jdbcType=VARCHAR},
      report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      loss_amount = #{record.lossAmount,jdbcType=VARCHAR},
      loss_cause = #{record.lossCause,jdbcType=VARCHAR},
      loss_cause_name = #{record.lossCauseName,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      insurant_name = #{record.insurantName,jdbcType=VARCHAR},
      insurant_cert_type = #{record.insurantCertType,jdbcType=VARCHAR},
      insurant_cert_no = #{record.insurantCertNo,jdbcType=VARCHAR},
      insurant_phone = #{record.insurantPhone,jdbcType=VARCHAR},
      insurant_is_died = #{record.insurantIsDied,jdbcType=CHAR},
      report_user_name = #{record.reportUserName,jdbcType=VARCHAR},
      reportor_relinsured = #{record.reportorRelinsured,jdbcType=VARCHAR},
      reportor_phone = #{record.reportorPhone,jdbcType=VARCHAR},
      reportor_email = #{record.reportorEmail,jdbcType=VARCHAR},
      claimant_name = #{record.claimantName,jdbcType=VARCHAR},
      claimant_phone = #{record.claimantPhone,jdbcType=VARCHAR},
      claimant_email = #{record.claimantEmail,jdbcType=VARCHAR},
      hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      is_auto_register = #{record.isAutoRegister,jdbcType=CHAR},
      is_hang_up = #{record.isHangUp,jdbcType=CHAR},
      insure_place = #{record.insurePlace,jdbcType=VARCHAR},
      agency_user_id = #{record.agencyUserId,jdbcType=BIGINT},
      auditor = #{record.auditor,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      claim_type = #{record.claimType,jdbcType=TINYINT},
      register_user = #{record.registerUser,jdbcType=VARCHAR},
      register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      settlement_date = #{record.settlementDate,jdbcType=TIMESTAMP},
      close_date = #{record.closeDate,jdbcType=TIMESTAMP},
      is_confirm = #{record.isConfirm,jdbcType=CHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      pre_paid_audit_status = #{record.prePaidAuditStatus,jdbcType=VARCHAR},
      is_handle = #{record.isHandle,jdbcType=CHAR},
      claim_timeliness = #{record.claimTimeliness,jdbcType=INTEGER},
      pay_state = #{record.payState,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchHeadInfoDO">
    update claim_batch_head_info
    <set>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="degreeOfDisability != null">
        degree_of_disability = #{degreeOfDisability,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="channelName != null">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="campaignFullName != null">
        campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="outProductCode != null">
        out_product_code = #{outProductCode,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="accidentPlace != null">
        accident_place = #{accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="accidentDesc != null">
        accident_desc = #{accidentDesc,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="claimCurrency != null">
        claim_currency = #{claimCurrency,jdbcType=VARCHAR},
      </if>
      <if test="reportAmount != null">
        report_amount = #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="lossAmount != null">
        loss_amount = #{lossAmount,jdbcType=VARCHAR},
      </if>
      <if test="lossCause != null">
        loss_cause = #{lossCause,jdbcType=VARCHAR},
      </if>
      <if test="lossCauseName != null">
        loss_cause_name = #{lossCauseName,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="insurantName != null">
        insurant_name = #{insurantName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertType != null">
        insurant_cert_type = #{insurantCertType,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertNo != null">
        insurant_cert_no = #{insurantCertNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantPhone != null">
        insurant_phone = #{insurantPhone,jdbcType=VARCHAR},
      </if>
      <if test="insurantIsDied != null">
        insurant_is_died = #{insurantIsDied,jdbcType=CHAR},
      </if>
      <if test="reportUserName != null">
        report_user_name = #{reportUserName,jdbcType=VARCHAR},
      </if>
      <if test="reportorRelinsured != null">
        reportor_relinsured = #{reportorRelinsured,jdbcType=VARCHAR},
      </if>
      <if test="reportorPhone != null">
        reportor_phone = #{reportorPhone,jdbcType=VARCHAR},
      </if>
      <if test="reportorEmail != null">
        reportor_email = #{reportorEmail,jdbcType=VARCHAR},
      </if>
      <if test="claimantName != null">
        claimant_name = #{claimantName,jdbcType=VARCHAR},
      </if>
      <if test="claimantPhone != null">
        claimant_phone = #{claimantPhone,jdbcType=VARCHAR},
      </if>
      <if test="claimantEmail != null">
        claimant_email = #{claimantEmail,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        hospital_name = #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="isAutoRegister != null">
        is_auto_register = #{isAutoRegister,jdbcType=CHAR},
      </if>
      <if test="isHangUp != null">
        is_hang_up = #{isHangUp,jdbcType=CHAR},
      </if>
      <if test="insurePlace != null">
        insure_place = #{insurePlace,jdbcType=VARCHAR},
      </if>
      <if test="agencyUserId != null">
        agency_user_id = #{agencyUserId,jdbcType=BIGINT},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="claimType != null">
        claim_type = #{claimType,jdbcType=TINYINT},
      </if>
      <if test="registerUser != null">
        register_user = #{registerUser,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        register_date = #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementDate != null">
        settlement_date = #{settlementDate,jdbcType=TIMESTAMP},
      </if>
      <if test="closeDate != null">
        close_date = #{closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isConfirm != null">
        is_confirm = #{isConfirm,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="prePaidAuditStatus != null">
        pre_paid_audit_status = #{prePaidAuditStatus,jdbcType=VARCHAR},
      </if>
      <if test="isHandle != null">
        is_handle = #{isHandle,jdbcType=CHAR},
      </if>
      <if test="claimTimeliness != null">
        claim_timeliness = #{claimTimeliness,jdbcType=INTEGER},
      </if>
      <if test="payState != null">
        pay_state = #{payState,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimBatchHeadInfoDO">
    update claim_batch_head_info
    set batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      degree_of_disability = #{degreeOfDisability,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=BIGINT},
      channel_name = #{channelName,jdbcType=VARCHAR},
      campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      package_def_id = #{packageDefId,jdbcType=BIGINT},
      product_id = #{productId,jdbcType=BIGINT},
      product_name = #{productName,jdbcType=VARCHAR},
      out_product_code = #{outProductCode,jdbcType=VARCHAR},
      accident_date = #{accidentDate,jdbcType=TIMESTAMP},
      accident_place = #{accidentPlace,jdbcType=VARCHAR},
      accident_desc = #{accidentDesc,jdbcType=VARCHAR},
      report_date = #{reportDate,jdbcType=TIMESTAMP},
      claim_currency = #{claimCurrency,jdbcType=VARCHAR},
      report_amount = #{reportAmount,jdbcType=VARCHAR},
      loss_amount = #{lossAmount,jdbcType=VARCHAR},
      loss_cause = #{lossCause,jdbcType=VARCHAR},
      loss_cause_name = #{lossCauseName,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      insurant_name = #{insurantName,jdbcType=VARCHAR},
      insurant_cert_type = #{insurantCertType,jdbcType=VARCHAR},
      insurant_cert_no = #{insurantCertNo,jdbcType=VARCHAR},
      insurant_phone = #{insurantPhone,jdbcType=VARCHAR},
      insurant_is_died = #{insurantIsDied,jdbcType=CHAR},
      report_user_name = #{reportUserName,jdbcType=VARCHAR},
      reportor_relinsured = #{reportorRelinsured,jdbcType=VARCHAR},
      reportor_phone = #{reportorPhone,jdbcType=VARCHAR},
      reportor_email = #{reportorEmail,jdbcType=VARCHAR},
      claimant_name = #{claimantName,jdbcType=VARCHAR},
      claimant_phone = #{claimantPhone,jdbcType=VARCHAR},
      claimant_email = #{claimantEmail,jdbcType=VARCHAR},
      hospital_name = #{hospitalName,jdbcType=VARCHAR},
      is_auto_register = #{isAutoRegister,jdbcType=CHAR},
      is_hang_up = #{isHangUp,jdbcType=CHAR},
      insure_place = #{insurePlace,jdbcType=VARCHAR},
      agency_user_id = #{agencyUserId,jdbcType=BIGINT},
      auditor = #{auditor,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      claim_type = #{claimType,jdbcType=TINYINT},
      register_user = #{registerUser,jdbcType=VARCHAR},
      register_date = #{registerDate,jdbcType=TIMESTAMP},
      settlement_date = #{settlementDate,jdbcType=TIMESTAMP},
      close_date = #{closeDate,jdbcType=TIMESTAMP},
      is_confirm = #{isConfirm,jdbcType=CHAR},
      remark = #{remark,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      pre_paid_audit_status = #{prePaidAuditStatus,jdbcType=VARCHAR},
      is_handle = #{isHandle,jdbcType=CHAR},
      claim_timeliness = #{claimTimeliness,jdbcType=INTEGER},
      pay_state = #{payState,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_batch_head_info
    (id, batch_claim_bill_no, degree_of_disability, channel_id, channel_name, campaign_def_id, 
      campaign_full_name, package_def_id, product_id, product_name, out_product_code, 
      accident_date, accident_place, accident_desc, report_date, claim_currency, report_amount, 
      loss_amount, loss_cause, loss_cause_name, extra_info, insurant_name, insurant_cert_type, 
      insurant_cert_no, insurant_phone, insurant_is_died, report_user_name, reportor_relinsured, 
      reportor_phone, reportor_email, claimant_name, claimant_phone, claimant_email, 
      hospital_name, is_auto_register, is_hang_up, insure_place, agency_user_id, auditor, 
      status, claim_type, register_user, register_date, settlement_date, close_date, 
      is_confirm, remark, policy_no, pre_paid_audit_status, is_handle, claim_timeliness, 
      pay_state, is_deleted, gmt_created, gmt_modified, creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.batchClaimBillNo,jdbcType=VARCHAR}, #{item.degreeOfDisability,jdbcType=VARCHAR}, 
        #{item.channelId,jdbcType=BIGINT}, #{item.channelName,jdbcType=VARCHAR}, #{item.campaignDefId,jdbcType=BIGINT}, 
        #{item.campaignFullName,jdbcType=VARCHAR}, #{item.packageDefId,jdbcType=BIGINT}, 
        #{item.productId,jdbcType=BIGINT}, #{item.productName,jdbcType=VARCHAR}, #{item.outProductCode,jdbcType=VARCHAR}, 
        #{item.accidentDate,jdbcType=TIMESTAMP}, #{item.accidentPlace,jdbcType=VARCHAR}, 
        #{item.accidentDesc,jdbcType=VARCHAR}, #{item.reportDate,jdbcType=TIMESTAMP}, #{item.claimCurrency,jdbcType=VARCHAR}, 
        #{item.reportAmount,jdbcType=VARCHAR}, #{item.lossAmount,jdbcType=VARCHAR}, #{item.lossCause,jdbcType=VARCHAR}, 
        #{item.lossCauseName,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, #{item.insurantName,jdbcType=VARCHAR}, 
        #{item.insurantCertType,jdbcType=VARCHAR}, #{item.insurantCertNo,jdbcType=VARCHAR}, 
        #{item.insurantPhone,jdbcType=VARCHAR}, #{item.insurantIsDied,jdbcType=CHAR}, #{item.reportUserName,jdbcType=VARCHAR}, 
        #{item.reportorRelinsured,jdbcType=VARCHAR}, #{item.reportorPhone,jdbcType=VARCHAR}, 
        #{item.reportorEmail,jdbcType=VARCHAR}, #{item.claimantName,jdbcType=VARCHAR}, 
        #{item.claimantPhone,jdbcType=VARCHAR}, #{item.claimantEmail,jdbcType=VARCHAR}, 
        #{item.hospitalName,jdbcType=VARCHAR}, #{item.isAutoRegister,jdbcType=CHAR}, #{item.isHangUp,jdbcType=CHAR}, 
        #{item.insurePlace,jdbcType=VARCHAR}, #{item.agencyUserId,jdbcType=BIGINT}, #{item.auditor,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.claimType,jdbcType=TINYINT}, #{item.registerUser,jdbcType=VARCHAR}, 
        #{item.registerDate,jdbcType=TIMESTAMP}, #{item.settlementDate,jdbcType=TIMESTAMP}, 
        #{item.closeDate,jdbcType=TIMESTAMP}, #{item.isConfirm,jdbcType=CHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.policyNo,jdbcType=VARCHAR}, #{item.prePaidAuditStatus,jdbcType=VARCHAR}, 
        #{item.isHandle,jdbcType=CHAR}, #{item.claimTimeliness,jdbcType=INTEGER}, #{item.payState,jdbcType=TINYINT}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_batch_head_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'batch_claim_bill_no'.toString() == column.value">
          #{item.batchClaimBillNo,jdbcType=VARCHAR}
        </if>
        <if test="'degree_of_disability'.toString() == column.value">
          #{item.degreeOfDisability,jdbcType=VARCHAR}
        </if>
        <if test="'channel_id'.toString() == column.value">
          #{item.channelId,jdbcType=BIGINT}
        </if>
        <if test="'channel_name'.toString() == column.value">
          #{item.channelName,jdbcType=VARCHAR}
        </if>
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=BIGINT}
        </if>
        <if test="'campaign_full_name'.toString() == column.value">
          #{item.campaignFullName,jdbcType=VARCHAR}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'product_id'.toString() == column.value">
          #{item.productId,jdbcType=BIGINT}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'out_product_code'.toString() == column.value">
          #{item.outProductCode,jdbcType=VARCHAR}
        </if>
        <if test="'accident_date'.toString() == column.value">
          #{item.accidentDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'accident_place'.toString() == column.value">
          #{item.accidentPlace,jdbcType=VARCHAR}
        </if>
        <if test="'accident_desc'.toString() == column.value">
          #{item.accidentDesc,jdbcType=VARCHAR}
        </if>
        <if test="'report_date'.toString() == column.value">
          #{item.reportDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'claim_currency'.toString() == column.value">
          #{item.claimCurrency,jdbcType=VARCHAR}
        </if>
        <if test="'report_amount'.toString() == column.value">
          #{item.reportAmount,jdbcType=VARCHAR}
        </if>
        <if test="'loss_amount'.toString() == column.value">
          #{item.lossAmount,jdbcType=VARCHAR}
        </if>
        <if test="'loss_cause'.toString() == column.value">
          #{item.lossCause,jdbcType=VARCHAR}
        </if>
        <if test="'loss_cause_name'.toString() == column.value">
          #{item.lossCauseName,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_name'.toString() == column.value">
          #{item.insurantName,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_cert_type'.toString() == column.value">
          #{item.insurantCertType,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_cert_no'.toString() == column.value">
          #{item.insurantCertNo,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_phone'.toString() == column.value">
          #{item.insurantPhone,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_is_died'.toString() == column.value">
          #{item.insurantIsDied,jdbcType=CHAR}
        </if>
        <if test="'report_user_name'.toString() == column.value">
          #{item.reportUserName,jdbcType=VARCHAR}
        </if>
        <if test="'reportor_relinsured'.toString() == column.value">
          #{item.reportorRelinsured,jdbcType=VARCHAR}
        </if>
        <if test="'reportor_phone'.toString() == column.value">
          #{item.reportorPhone,jdbcType=VARCHAR}
        </if>
        <if test="'reportor_email'.toString() == column.value">
          #{item.reportorEmail,jdbcType=VARCHAR}
        </if>
        <if test="'claimant_name'.toString() == column.value">
          #{item.claimantName,jdbcType=VARCHAR}
        </if>
        <if test="'claimant_phone'.toString() == column.value">
          #{item.claimantPhone,jdbcType=VARCHAR}
        </if>
        <if test="'claimant_email'.toString() == column.value">
          #{item.claimantEmail,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_name'.toString() == column.value">
          #{item.hospitalName,jdbcType=VARCHAR}
        </if>
        <if test="'is_auto_register'.toString() == column.value">
          #{item.isAutoRegister,jdbcType=CHAR}
        </if>
        <if test="'is_hang_up'.toString() == column.value">
          #{item.isHangUp,jdbcType=CHAR}
        </if>
        <if test="'insure_place'.toString() == column.value">
          #{item.insurePlace,jdbcType=VARCHAR}
        </if>
        <if test="'agency_user_id'.toString() == column.value">
          #{item.agencyUserId,jdbcType=BIGINT}
        </if>
        <if test="'auditor'.toString() == column.value">
          #{item.auditor,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'claim_type'.toString() == column.value">
          #{item.claimType,jdbcType=TINYINT}
        </if>
        <if test="'register_user'.toString() == column.value">
          #{item.registerUser,jdbcType=VARCHAR}
        </if>
        <if test="'register_date'.toString() == column.value">
          #{item.registerDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'settlement_date'.toString() == column.value">
          #{item.settlementDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'close_date'.toString() == column.value">
          #{item.closeDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_confirm'.toString() == column.value">
          #{item.isConfirm,jdbcType=CHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'pre_paid_audit_status'.toString() == column.value">
          #{item.prePaidAuditStatus,jdbcType=VARCHAR}
        </if>
        <if test="'is_handle'.toString() == column.value">
          #{item.isHandle,jdbcType=CHAR}
        </if>
        <if test="'claim_timeliness'.toString() == column.value">
          #{item.claimTimeliness,jdbcType=INTEGER}
        </if>
        <if test="'pay_state'.toString() == column.value">
          #{item.payState,jdbcType=TINYINT}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>