<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.MedicalSupplyEntityMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.MedicalSupplyEntityDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="no" jdbcType="VARCHAR" property="no" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="generic_name" jdbcType="VARCHAR" property="genericName" />
    <result column="simplified_name" jdbcType="VARCHAR" property="simplifiedName" />
    <result column="official_name" jdbcType="VARCHAR" property="officialName" />
    <result column="alias_name" jdbcType="VARCHAR" property="aliasName" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="drug_pharmacological" jdbcType="VARCHAR" property="drugPharmacological" />
    <result column="drug_purpose" jdbcType="VARCHAR" property="drugPurpose" />
    <result column="drug_ingredient" jdbcType="VARCHAR" property="drugIngredient" />
    <result column="drug_applicable_symptoms" jdbcType="VARCHAR" property="drugApplicableSymptoms" />
    <result column="drug_effect" jdbcType="VARCHAR" property="drugEffect" />
    <result column="drug_form" jdbcType="VARCHAR" property="drugForm" />
    <result column="drug_target" jdbcType="VARCHAR" property="drugTarget" />
    <result column="inspection_purpose" jdbcType="VARCHAR" property="inspectionPurpose" />
    <result column="inspection_category" jdbcType="VARCHAR" property="inspectionCategory" />
    <result column="excluded" jdbcType="BIT" property="excluded" />
    <result column="exclusion_judgment_reason" jdbcType="VARCHAR" property="exclusionJudgmentReason" />
    <result column="origin_excluded" jdbcType="BIT" property="originExcluded" />
    <result column="entity_extracted" jdbcType="BIT" property="entityExtracted" />
    <result column="exclusion_judged" jdbcType="BIT" property="exclusionJudged" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, source, no, name, generic_name, simplified_name, official_name, alias_name, brand, 
    manufacturer, category, drug_pharmacological, drug_purpose, drug_ingredient, drug_applicable_symptoms, 
    drug_effect, drug_form, drug_target, inspection_purpose, inspection_category, excluded, 
    exclusion_judgment_reason, origin_excluded, entity_extracted, exclusion_judged, extra_info, 
    creator, modifier, gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.MedicalSupplyEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pet_medical_supply_entity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pet_medical_supply_entity
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.MedicalSupplyEntityDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pet_medical_supply_entity (source, no, name, 
      generic_name, simplified_name, official_name, 
      alias_name, brand, manufacturer, 
      category, drug_pharmacological, drug_purpose, 
      drug_ingredient, drug_applicable_symptoms, 
      drug_effect, drug_form, drug_target, 
      inspection_purpose, inspection_category, 
      excluded, exclusion_judgment_reason, origin_excluded, 
      entity_extracted, exclusion_judged, extra_info, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted)
    values (#{source,jdbcType=VARCHAR}, #{no,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{genericName,jdbcType=VARCHAR}, #{simplifiedName,jdbcType=VARCHAR}, #{officialName,jdbcType=VARCHAR}, 
      #{aliasName,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{category,jdbcType=VARCHAR}, #{drugPharmacological,jdbcType=VARCHAR}, #{drugPurpose,jdbcType=VARCHAR}, 
      #{drugIngredient,jdbcType=VARCHAR}, #{drugApplicableSymptoms,jdbcType=VARCHAR}, 
      #{drugEffect,jdbcType=VARCHAR}, #{drugForm,jdbcType=VARCHAR}, #{drugTarget,jdbcType=VARCHAR}, 
      #{inspectionPurpose,jdbcType=VARCHAR}, #{inspectionCategory,jdbcType=VARCHAR}, 
      #{excluded,jdbcType=BIT}, #{exclusionJudgmentReason,jdbcType=VARCHAR}, #{originExcluded,jdbcType=BIT}, 
      #{entityExtracted,jdbcType=BIT}, #{exclusionJudged,jdbcType=BIT}, #{extraInfo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.MedicalSupplyEntityDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into pet_medical_supply_entity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="source != null">
        source,
      </if>
      <if test="no != null">
        no,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="genericName != null">
        generic_name,
      </if>
      <if test="simplifiedName != null">
        simplified_name,
      </if>
      <if test="officialName != null">
        official_name,
      </if>
      <if test="aliasName != null">
        alias_name,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="drugPharmacological != null">
        drug_pharmacological,
      </if>
      <if test="drugPurpose != null">
        drug_purpose,
      </if>
      <if test="drugIngredient != null">
        drug_ingredient,
      </if>
      <if test="drugApplicableSymptoms != null">
        drug_applicable_symptoms,
      </if>
      <if test="drugEffect != null">
        drug_effect,
      </if>
      <if test="drugForm != null">
        drug_form,
      </if>
      <if test="drugTarget != null">
        drug_target,
      </if>
      <if test="inspectionPurpose != null">
        inspection_purpose,
      </if>
      <if test="inspectionCategory != null">
        inspection_category,
      </if>
      <if test="excluded != null">
        excluded,
      </if>
      <if test="exclusionJudgmentReason != null">
        exclusion_judgment_reason,
      </if>
      <if test="originExcluded != null">
        origin_excluded,
      </if>
      <if test="entityExtracted != null">
        entity_extracted,
      </if>
      <if test="exclusionJudged != null">
        exclusion_judged,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="no != null">
        #{no,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="genericName != null">
        #{genericName,jdbcType=VARCHAR},
      </if>
      <if test="simplifiedName != null">
        #{simplifiedName,jdbcType=VARCHAR},
      </if>
      <if test="officialName != null">
        #{officialName,jdbcType=VARCHAR},
      </if>
      <if test="aliasName != null">
        #{aliasName,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="drugPharmacological != null">
        #{drugPharmacological,jdbcType=VARCHAR},
      </if>
      <if test="drugPurpose != null">
        #{drugPurpose,jdbcType=VARCHAR},
      </if>
      <if test="drugIngredient != null">
        #{drugIngredient,jdbcType=VARCHAR},
      </if>
      <if test="drugApplicableSymptoms != null">
        #{drugApplicableSymptoms,jdbcType=VARCHAR},
      </if>
      <if test="drugEffect != null">
        #{drugEffect,jdbcType=VARCHAR},
      </if>
      <if test="drugForm != null">
        #{drugForm,jdbcType=VARCHAR},
      </if>
      <if test="drugTarget != null">
        #{drugTarget,jdbcType=VARCHAR},
      </if>
      <if test="inspectionPurpose != null">
        #{inspectionPurpose,jdbcType=VARCHAR},
      </if>
      <if test="inspectionCategory != null">
        #{inspectionCategory,jdbcType=VARCHAR},
      </if>
      <if test="excluded != null">
        #{excluded,jdbcType=BIT},
      </if>
      <if test="exclusionJudgmentReason != null">
        #{exclusionJudgmentReason,jdbcType=VARCHAR},
      </if>
      <if test="originExcluded != null">
        #{originExcluded,jdbcType=BIT},
      </if>
      <if test="entityExtracted != null">
        #{entityExtracted,jdbcType=BIT},
      </if>
      <if test="exclusionJudged != null">
        #{exclusionJudged,jdbcType=BIT},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.MedicalSupplyEntityExample" resultType="java.lang.Long">
    select count(*) from pet_medical_supply_entity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update pet_medical_supply_entity
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.no != null">
        no = #{record.no,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.genericName != null">
        generic_name = #{record.genericName,jdbcType=VARCHAR},
      </if>
      <if test="record.simplifiedName != null">
        simplified_name = #{record.simplifiedName,jdbcType=VARCHAR},
      </if>
      <if test="record.officialName != null">
        official_name = #{record.officialName,jdbcType=VARCHAR},
      </if>
      <if test="record.aliasName != null">
        alias_name = #{record.aliasName,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.drugPharmacological != null">
        drug_pharmacological = #{record.drugPharmacological,jdbcType=VARCHAR},
      </if>
      <if test="record.drugPurpose != null">
        drug_purpose = #{record.drugPurpose,jdbcType=VARCHAR},
      </if>
      <if test="record.drugIngredient != null">
        drug_ingredient = #{record.drugIngredient,jdbcType=VARCHAR},
      </if>
      <if test="record.drugApplicableSymptoms != null">
        drug_applicable_symptoms = #{record.drugApplicableSymptoms,jdbcType=VARCHAR},
      </if>
      <if test="record.drugEffect != null">
        drug_effect = #{record.drugEffect,jdbcType=VARCHAR},
      </if>
      <if test="record.drugForm != null">
        drug_form = #{record.drugForm,jdbcType=VARCHAR},
      </if>
      <if test="record.drugTarget != null">
        drug_target = #{record.drugTarget,jdbcType=VARCHAR},
      </if>
      <if test="record.inspectionPurpose != null">
        inspection_purpose = #{record.inspectionPurpose,jdbcType=VARCHAR},
      </if>
      <if test="record.inspectionCategory != null">
        inspection_category = #{record.inspectionCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.excluded != null">
        excluded = #{record.excluded,jdbcType=BIT},
      </if>
      <if test="record.exclusionJudgmentReason != null">
        exclusion_judgment_reason = #{record.exclusionJudgmentReason,jdbcType=VARCHAR},
      </if>
      <if test="record.originExcluded != null">
        origin_excluded = #{record.originExcluded,jdbcType=BIT},
      </if>
      <if test="record.entityExtracted != null">
        entity_extracted = #{record.entityExtracted,jdbcType=BIT},
      </if>
      <if test="record.exclusionJudged != null">
        exclusion_judged = #{record.exclusionJudged,jdbcType=BIT},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update pet_medical_supply_entity
    set id = #{record.id,jdbcType=BIGINT},
      source = #{record.source,jdbcType=VARCHAR},
      no = #{record.no,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      generic_name = #{record.genericName,jdbcType=VARCHAR},
      simplified_name = #{record.simplifiedName,jdbcType=VARCHAR},
      official_name = #{record.officialName,jdbcType=VARCHAR},
      alias_name = #{record.aliasName,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=VARCHAR},
      drug_pharmacological = #{record.drugPharmacological,jdbcType=VARCHAR},
      drug_purpose = #{record.drugPurpose,jdbcType=VARCHAR},
      drug_ingredient = #{record.drugIngredient,jdbcType=VARCHAR},
      drug_applicable_symptoms = #{record.drugApplicableSymptoms,jdbcType=VARCHAR},
      drug_effect = #{record.drugEffect,jdbcType=VARCHAR},
      drug_form = #{record.drugForm,jdbcType=VARCHAR},
      drug_target = #{record.drugTarget,jdbcType=VARCHAR},
      inspection_purpose = #{record.inspectionPurpose,jdbcType=VARCHAR},
      inspection_category = #{record.inspectionCategory,jdbcType=VARCHAR},
      excluded = #{record.excluded,jdbcType=BIT},
      exclusion_judgment_reason = #{record.exclusionJudgmentReason,jdbcType=VARCHAR},
      origin_excluded = #{record.originExcluded,jdbcType=BIT},
      entity_extracted = #{record.entityExtracted,jdbcType=BIT},
      exclusion_judged = #{record.exclusionJudged,jdbcType=BIT},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.MedicalSupplyEntityDO">
    update pet_medical_supply_entity
    <set>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="no != null">
        no = #{no,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="genericName != null">
        generic_name = #{genericName,jdbcType=VARCHAR},
      </if>
      <if test="simplifiedName != null">
        simplified_name = #{simplifiedName,jdbcType=VARCHAR},
      </if>
      <if test="officialName != null">
        official_name = #{officialName,jdbcType=VARCHAR},
      </if>
      <if test="aliasName != null">
        alias_name = #{aliasName,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="drugPharmacological != null">
        drug_pharmacological = #{drugPharmacological,jdbcType=VARCHAR},
      </if>
      <if test="drugPurpose != null">
        drug_purpose = #{drugPurpose,jdbcType=VARCHAR},
      </if>
      <if test="drugIngredient != null">
        drug_ingredient = #{drugIngredient,jdbcType=VARCHAR},
      </if>
      <if test="drugApplicableSymptoms != null">
        drug_applicable_symptoms = #{drugApplicableSymptoms,jdbcType=VARCHAR},
      </if>
      <if test="drugEffect != null">
        drug_effect = #{drugEffect,jdbcType=VARCHAR},
      </if>
      <if test="drugForm != null">
        drug_form = #{drugForm,jdbcType=VARCHAR},
      </if>
      <if test="drugTarget != null">
        drug_target = #{drugTarget,jdbcType=VARCHAR},
      </if>
      <if test="inspectionPurpose != null">
        inspection_purpose = #{inspectionPurpose,jdbcType=VARCHAR},
      </if>
      <if test="inspectionCategory != null">
        inspection_category = #{inspectionCategory,jdbcType=VARCHAR},
      </if>
      <if test="excluded != null">
        excluded = #{excluded,jdbcType=BIT},
      </if>
      <if test="exclusionJudgmentReason != null">
        exclusion_judgment_reason = #{exclusionJudgmentReason,jdbcType=VARCHAR},
      </if>
      <if test="originExcluded != null">
        origin_excluded = #{originExcluded,jdbcType=BIT},
      </if>
      <if test="entityExtracted != null">
        entity_extracted = #{entityExtracted,jdbcType=BIT},
      </if>
      <if test="exclusionJudged != null">
        exclusion_judged = #{exclusionJudged,jdbcType=BIT},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.MedicalSupplyEntityDO">
    update pet_medical_supply_entity
    set source = #{source,jdbcType=VARCHAR},
      no = #{no,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      generic_name = #{genericName,jdbcType=VARCHAR},
      simplified_name = #{simplifiedName,jdbcType=VARCHAR},
      official_name = #{officialName,jdbcType=VARCHAR},
      alias_name = #{aliasName,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      drug_pharmacological = #{drugPharmacological,jdbcType=VARCHAR},
      drug_purpose = #{drugPurpose,jdbcType=VARCHAR},
      drug_ingredient = #{drugIngredient,jdbcType=VARCHAR},
      drug_applicable_symptoms = #{drugApplicableSymptoms,jdbcType=VARCHAR},
      drug_effect = #{drugEffect,jdbcType=VARCHAR},
      drug_form = #{drugForm,jdbcType=VARCHAR},
      drug_target = #{drugTarget,jdbcType=VARCHAR},
      inspection_purpose = #{inspectionPurpose,jdbcType=VARCHAR},
      inspection_category = #{inspectionCategory,jdbcType=VARCHAR},
      excluded = #{excluded,jdbcType=BIT},
      exclusion_judgment_reason = #{exclusionJudgmentReason,jdbcType=VARCHAR},
      origin_excluded = #{originExcluded,jdbcType=BIT},
      entity_extracted = #{entityExtracted,jdbcType=BIT},
      exclusion_judged = #{exclusionJudged,jdbcType=BIT},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into pet_medical_supply_entity
    (source, no, name, generic_name, simplified_name, official_name, alias_name, brand, 
      manufacturer, category, drug_pharmacological, drug_purpose, drug_ingredient, drug_applicable_symptoms, 
      drug_effect, drug_form, drug_target, inspection_purpose, inspection_category, excluded, 
      exclusion_judgment_reason, origin_excluded, entity_extracted, exclusion_judged, 
      extra_info, creator, modifier, gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.source,jdbcType=VARCHAR}, #{item.no,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.genericName,jdbcType=VARCHAR}, #{item.simplifiedName,jdbcType=VARCHAR}, 
        #{item.officialName,jdbcType=VARCHAR}, #{item.aliasName,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR}, 
        #{item.manufacturer,jdbcType=VARCHAR}, #{item.category,jdbcType=VARCHAR}, #{item.drugPharmacological,jdbcType=VARCHAR}, 
        #{item.drugPurpose,jdbcType=VARCHAR}, #{item.drugIngredient,jdbcType=VARCHAR}, 
        #{item.drugApplicableSymptoms,jdbcType=VARCHAR}, #{item.drugEffect,jdbcType=VARCHAR}, 
        #{item.drugForm,jdbcType=VARCHAR}, #{item.drugTarget,jdbcType=VARCHAR}, #{item.inspectionPurpose,jdbcType=VARCHAR}, 
        #{item.inspectionCategory,jdbcType=VARCHAR}, #{item.excluded,jdbcType=BIT}, #{item.exclusionJudgmentReason,jdbcType=VARCHAR}, 
        #{item.originExcluded,jdbcType=BIT}, #{item.entityExtracted,jdbcType=BIT}, #{item.exclusionJudged,jdbcType=BIT}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into pet_medical_supply_entity (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=VARCHAR}
        </if>
        <if test="'no'.toString() == column.value">
          #{item.no,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'generic_name'.toString() == column.value">
          #{item.genericName,jdbcType=VARCHAR}
        </if>
        <if test="'simplified_name'.toString() == column.value">
          #{item.simplifiedName,jdbcType=VARCHAR}
        </if>
        <if test="'official_name'.toString() == column.value">
          #{item.officialName,jdbcType=VARCHAR}
        </if>
        <if test="'alias_name'.toString() == column.value">
          #{item.aliasName,jdbcType=VARCHAR}
        </if>
        <if test="'brand'.toString() == column.value">
          #{item.brand,jdbcType=VARCHAR}
        </if>
        <if test="'manufacturer'.toString() == column.value">
          #{item.manufacturer,jdbcType=VARCHAR}
        </if>
        <if test="'category'.toString() == column.value">
          #{item.category,jdbcType=VARCHAR}
        </if>
        <if test="'drug_pharmacological'.toString() == column.value">
          #{item.drugPharmacological,jdbcType=VARCHAR}
        </if>
        <if test="'drug_purpose'.toString() == column.value">
          #{item.drugPurpose,jdbcType=VARCHAR}
        </if>
        <if test="'drug_ingredient'.toString() == column.value">
          #{item.drugIngredient,jdbcType=VARCHAR}
        </if>
        <if test="'drug_applicable_symptoms'.toString() == column.value">
          #{item.drugApplicableSymptoms,jdbcType=VARCHAR}
        </if>
        <if test="'drug_effect'.toString() == column.value">
          #{item.drugEffect,jdbcType=VARCHAR}
        </if>
        <if test="'drug_form'.toString() == column.value">
          #{item.drugForm,jdbcType=VARCHAR}
        </if>
        <if test="'drug_target'.toString() == column.value">
          #{item.drugTarget,jdbcType=VARCHAR}
        </if>
        <if test="'inspection_purpose'.toString() == column.value">
          #{item.inspectionPurpose,jdbcType=VARCHAR}
        </if>
        <if test="'inspection_category'.toString() == column.value">
          #{item.inspectionCategory,jdbcType=VARCHAR}
        </if>
        <if test="'excluded'.toString() == column.value">
          #{item.excluded,jdbcType=BIT}
        </if>
        <if test="'exclusion_judgment_reason'.toString() == column.value">
          #{item.exclusionJudgmentReason,jdbcType=VARCHAR}
        </if>
        <if test="'origin_excluded'.toString() == column.value">
          #{item.originExcluded,jdbcType=BIT}
        </if>
        <if test="'entity_extracted'.toString() == column.value">
          #{item.entityExtracted,jdbcType=BIT}
        </if>
        <if test="'exclusion_judged'.toString() == column.value">
          #{item.exclusionJudged,jdbcType=BIT}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>