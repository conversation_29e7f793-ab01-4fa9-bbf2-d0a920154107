<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.BlackListRecordMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.BlackListRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="account_type" jdbcType="TINYINT" property="accountType" />
    <result column="cert_type" jdbcType="VARCHAR" property="certType" />
    <result column="cert_no" jdbcType="VARCHAR" property="certNo" />
    <result column="involved_amount" jdbcType="VARCHAR" property="involvedAmount" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="biz_scenario" jdbcType="VARCHAR" property="bizScenario" />
    <result column="insu_participant" jdbcType="VARCHAR" property="insuParticipant" />
    <result column="insu_related_person" jdbcType="VARCHAR" property="insuRelatedPerson" />
    <result column="belong_department" jdbcType="VARCHAR" property="belongDepartment" />
    <result column="risk_classification" jdbcType="VARCHAR" property="riskClassification" />
    <result column="rule_label" jdbcType="VARCHAR" property="ruleLabel" />
    <result column="risk_level" jdbcType="VARCHAR" property="riskLevel" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type_id, policy_no, biz_no, user_name, account_type, cert_type, cert_no, involved_amount, 
    user_type, end_time, source, biz_scenario, insu_participant, insu_related_person, 
    belong_department, risk_classification, rule_label, risk_level, remark, is_deleted, 
    gmt_created, gmt_modified, modifier, creator, extra_info
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.BlackListRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from black_list_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from black_list_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.BlackListRecordDO">
    insert into black_list_record (id, type_id, policy_no, 
      biz_no, user_name, account_type, 
      cert_type, cert_no, involved_amount, 
      user_type, end_time, source, 
      biz_scenario, insu_participant, insu_related_person, 
      belong_department, risk_classification, rule_label, 
      risk_level, remark, is_deleted, 
      gmt_created, gmt_modified, modifier, 
      creator, extra_info)
    values (#{id,jdbcType=BIGINT}, #{typeId,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, 
      #{bizNo,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{accountType,jdbcType=TINYINT}, 
      #{certType,jdbcType=VARCHAR}, #{certNo,jdbcType=VARCHAR}, #{involvedAmount,jdbcType=VARCHAR}, 
      #{userType,jdbcType=VARCHAR}, #{endTime,jdbcType=TIMESTAMP}, #{source,jdbcType=VARCHAR}, 
      #{bizScenario,jdbcType=VARCHAR}, #{insuParticipant,jdbcType=VARCHAR}, #{insuRelatedPerson,jdbcType=VARCHAR}, 
      #{belongDepartment,jdbcType=VARCHAR}, #{riskClassification,jdbcType=VARCHAR}, #{ruleLabel,jdbcType=VARCHAR}, 
      #{riskLevel,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, 
      sysdate(), sysdate(), #{modifier,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.BlackListRecordDO">
    insert into black_list_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="certType != null">
        cert_type,
      </if>
      <if test="certNo != null">
        cert_no,
      </if>
      <if test="involvedAmount != null">
        involved_amount,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="bizScenario != null">
        biz_scenario,
      </if>
      <if test="insuParticipant != null">
        insu_participant,
      </if>
      <if test="insuRelatedPerson != null">
        insu_related_person,
      </if>
      <if test="belongDepartment != null">
        belong_department,
      </if>
      <if test="riskClassification != null">
        risk_classification,
      </if>
      <if test="ruleLabel != null">
        rule_label,
      </if>
      <if test="riskLevel != null">
        risk_level,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=TINYINT},
      </if>
      <if test="certType != null">
        #{certType,jdbcType=VARCHAR},
      </if>
      <if test="certNo != null">
        #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="involvedAmount != null">
        #{involvedAmount,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="bizScenario != null">
        #{bizScenario,jdbcType=VARCHAR},
      </if>
      <if test="insuParticipant != null">
        #{insuParticipant,jdbcType=VARCHAR},
      </if>
      <if test="insuRelatedPerson != null">
        #{insuRelatedPerson,jdbcType=VARCHAR},
      </if>
      <if test="belongDepartment != null">
        #{belongDepartment,jdbcType=VARCHAR},
      </if>
      <if test="riskClassification != null">
        #{riskClassification,jdbcType=VARCHAR},
      </if>
      <if test="ruleLabel != null">
        #{ruleLabel,jdbcType=VARCHAR},
      </if>
      <if test="riskLevel != null">
        #{riskLevel,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.BlackListRecordExample" resultType="java.lang.Long">
    select count(*) from black_list_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update black_list_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.typeId != null">
        type_id = #{record.typeId,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bizNo != null">
        biz_no = #{record.bizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=TINYINT},
      </if>
      <if test="record.certType != null">
        cert_type = #{record.certType,jdbcType=VARCHAR},
      </if>
      <if test="record.certNo != null">
        cert_no = #{record.certNo,jdbcType=VARCHAR},
      </if>
      <if test="record.involvedAmount != null">
        involved_amount = #{record.involvedAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=VARCHAR},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.bizScenario != null">
        biz_scenario = #{record.bizScenario,jdbcType=VARCHAR},
      </if>
      <if test="record.insuParticipant != null">
        insu_participant = #{record.insuParticipant,jdbcType=VARCHAR},
      </if>
      <if test="record.insuRelatedPerson != null">
        insu_related_person = #{record.insuRelatedPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.belongDepartment != null">
        belong_department = #{record.belongDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.riskClassification != null">
        risk_classification = #{record.riskClassification,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleLabel != null">
        rule_label = #{record.ruleLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.riskLevel != null">
        risk_level = #{record.riskLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update black_list_record
    set id = #{record.id,jdbcType=BIGINT},
      type_id = #{record.typeId,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      biz_no = #{record.bizNo,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      account_type = #{record.accountType,jdbcType=TINYINT},
      cert_type = #{record.certType,jdbcType=VARCHAR},
      cert_no = #{record.certNo,jdbcType=VARCHAR},
      involved_amount = #{record.involvedAmount,jdbcType=VARCHAR},
      user_type = #{record.userType,jdbcType=VARCHAR},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      source = #{record.source,jdbcType=VARCHAR},
      biz_scenario = #{record.bizScenario,jdbcType=VARCHAR},
      insu_participant = #{record.insuParticipant,jdbcType=VARCHAR},
      insu_related_person = #{record.insuRelatedPerson,jdbcType=VARCHAR},
      belong_department = #{record.belongDepartment,jdbcType=VARCHAR},
      risk_classification = #{record.riskClassification,jdbcType=VARCHAR},
      rule_label = #{record.ruleLabel,jdbcType=VARCHAR},
      risk_level = #{record.riskLevel,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      modifier = #{record.modifier,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.BlackListRecordDO">
    update black_list_record
    <set>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=TINYINT},
      </if>
      <if test="certType != null">
        cert_type = #{certType,jdbcType=VARCHAR},
      </if>
      <if test="certNo != null">
        cert_no = #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="involvedAmount != null">
        involved_amount = #{involvedAmount,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="bizScenario != null">
        biz_scenario = #{bizScenario,jdbcType=VARCHAR},
      </if>
      <if test="insuParticipant != null">
        insu_participant = #{insuParticipant,jdbcType=VARCHAR},
      </if>
      <if test="insuRelatedPerson != null">
        insu_related_person = #{insuRelatedPerson,jdbcType=VARCHAR},
      </if>
      <if test="belongDepartment != null">
        belong_department = #{belongDepartment,jdbcType=VARCHAR},
      </if>
      <if test="riskClassification != null">
        risk_classification = #{riskClassification,jdbcType=VARCHAR},
      </if>
      <if test="ruleLabel != null">
        rule_label = #{ruleLabel,jdbcType=VARCHAR},
      </if>
      <if test="riskLevel != null">
        risk_level = #{riskLevel,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.BlackListRecordDO">
    update black_list_record
    set type_id = #{typeId,jdbcType=BIGINT},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      biz_no = #{bizNo,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=TINYINT},
      cert_type = #{certType,jdbcType=VARCHAR},
      cert_no = #{certNo,jdbcType=VARCHAR},
      involved_amount = #{involvedAmount,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=VARCHAR},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      source = #{source,jdbcType=VARCHAR},
      biz_scenario = #{bizScenario,jdbcType=VARCHAR},
      insu_participant = #{insuParticipant,jdbcType=VARCHAR},
      insu_related_person = #{insuRelatedPerson,jdbcType=VARCHAR},
      belong_department = #{belongDepartment,jdbcType=VARCHAR},
      risk_classification = #{riskClassification,jdbcType=VARCHAR},
      rule_label = #{ruleLabel,jdbcType=VARCHAR},
      risk_level = #{riskLevel,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      modifier = #{modifier,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into black_list_record
    (id, type_id, policy_no, biz_no, user_name, account_type, cert_type, cert_no, involved_amount, 
      user_type, end_time, source, biz_scenario, insu_participant, insu_related_person, 
      belong_department, risk_classification, rule_label, risk_level, remark, is_deleted, 
      gmt_created, gmt_modified, modifier, creator, extra_info)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.typeId,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.bizNo,jdbcType=VARCHAR}, #{item.userName,jdbcType=VARCHAR}, #{item.accountType,jdbcType=TINYINT}, 
        #{item.certType,jdbcType=VARCHAR}, #{item.certNo,jdbcType=VARCHAR}, #{item.involvedAmount,jdbcType=VARCHAR}, 
        #{item.userType,jdbcType=VARCHAR}, #{item.endTime,jdbcType=TIMESTAMP}, #{item.source,jdbcType=VARCHAR}, 
        #{item.bizScenario,jdbcType=VARCHAR}, #{item.insuParticipant,jdbcType=VARCHAR}, 
        #{item.insuRelatedPerson,jdbcType=VARCHAR}, #{item.belongDepartment,jdbcType=VARCHAR}, 
        #{item.riskClassification,jdbcType=VARCHAR}, #{item.ruleLabel,jdbcType=VARCHAR}, 
        #{item.riskLevel,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into black_list_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'type_id'.toString() == column.value">
          #{item.typeId,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'biz_no'.toString() == column.value">
          #{item.bizNo,jdbcType=VARCHAR}
        </if>
        <if test="'user_name'.toString() == column.value">
          #{item.userName,jdbcType=VARCHAR}
        </if>
        <if test="'account_type'.toString() == column.value">
          #{item.accountType,jdbcType=TINYINT}
        </if>
        <if test="'cert_type'.toString() == column.value">
          #{item.certType,jdbcType=VARCHAR}
        </if>
        <if test="'cert_no'.toString() == column.value">
          #{item.certNo,jdbcType=VARCHAR}
        </if>
        <if test="'involved_amount'.toString() == column.value">
          #{item.involvedAmount,jdbcType=VARCHAR}
        </if>
        <if test="'user_type'.toString() == column.value">
          #{item.userType,jdbcType=VARCHAR}
        </if>
        <if test="'end_time'.toString() == column.value">
          #{item.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=VARCHAR}
        </if>
        <if test="'biz_scenario'.toString() == column.value">
          #{item.bizScenario,jdbcType=VARCHAR}
        </if>
        <if test="'insu_participant'.toString() == column.value">
          #{item.insuParticipant,jdbcType=VARCHAR}
        </if>
        <if test="'insu_related_person'.toString() == column.value">
          #{item.insuRelatedPerson,jdbcType=VARCHAR}
        </if>
        <if test="'belong_department'.toString() == column.value">
          #{item.belongDepartment,jdbcType=VARCHAR}
        </if>
        <if test="'risk_classification'.toString() == column.value">
          #{item.riskClassification,jdbcType=VARCHAR}
        </if>
        <if test="'rule_label'.toString() == column.value">
          #{item.ruleLabel,jdbcType=VARCHAR}
        </if>
        <if test="'risk_level'.toString() == column.value">
          #{item.riskLevel,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>