<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.TpaCompanyInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.TpaCompanyInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="connect_name" jdbcType="VARCHAR" property="connectName" />
    <result column="connect_phone" jdbcType="VARCHAR" property="connectPhone" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="half_flow_pay_method" jdbcType="TINYINT" property="halfFlowPayMethod" />
    <result column="half_flow_hos_amount" jdbcType="VARCHAR" property="halfFlowHosAmount" />
    <result column="half_flow_amount" jdbcType="VARCHAR" property="halfFlowAmount" />
    <result column="all_flow_pay_method" jdbcType="TINYINT" property="allFlowPayMethod" />
    <result column="all_flow_hos_amount" jdbcType="VARCHAR" property="allFlowHosAmount" />
    <result column="all_flow_amount" jdbcType="VARCHAR" property="allFlowAmount" />
    <result column="charge_mode" jdbcType="TINYINT" property="chargeMode" />
    <result column="company_type" jdbcType="CHAR" property="companyType" />
    <result column="claim_price" jdbcType="DECIMAL" property="claimPrice" />
    <result column="outbound_price" jdbcType="DECIMAL" property="outboundPrice" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="parent_company_id" jdbcType="BIGINT" property="parentCompanyId" />
    <result column="level" jdbcType="TINYINT" property="level" />
    <result column="allot_rule" jdbcType="VARCHAR" property="allotRule" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_name, connect_name, connect_phone, email, half_flow_pay_method, half_flow_hos_amount, 
    half_flow_amount, all_flow_pay_method, all_flow_hos_amount, all_flow_amount, charge_mode, 
    company_type, claim_price, outbound_price, auditor, status, parent_company_id, level, 
    allot_rule, extra_info, remark, is_deleted, gmt_created, gmt_modified, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.TpaCompanyInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpa_company_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tpa_company_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.TpaCompanyInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpa_company_info (company_name, connect_name, connect_phone, 
      email, half_flow_pay_method, half_flow_hos_amount, 
      half_flow_amount, all_flow_pay_method, all_flow_hos_amount, 
      all_flow_amount, charge_mode, company_type, 
      claim_price, outbound_price, auditor, 
      status, parent_company_id, level, 
      allot_rule, extra_info, remark, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier)
    values (#{companyName,jdbcType=VARCHAR}, #{connectName,jdbcType=VARCHAR}, #{connectPhone,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{halfFlowPayMethod,jdbcType=TINYINT}, #{halfFlowHosAmount,jdbcType=VARCHAR}, 
      #{halfFlowAmount,jdbcType=VARCHAR}, #{allFlowPayMethod,jdbcType=TINYINT}, #{allFlowHosAmount,jdbcType=VARCHAR}, 
      #{allFlowAmount,jdbcType=VARCHAR}, #{chargeMode,jdbcType=TINYINT}, #{companyType,jdbcType=CHAR}, 
      #{claimPrice,jdbcType=DECIMAL}, #{outboundPrice,jdbcType=DECIMAL}, #{auditor,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{parentCompanyId,jdbcType=BIGINT}, #{level,jdbcType=TINYINT}, 
      #{allotRule,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.TpaCompanyInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpa_company_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyName != null">
        company_name,
      </if>
      <if test="connectName != null">
        connect_name,
      </if>
      <if test="connectPhone != null">
        connect_phone,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="halfFlowPayMethod != null">
        half_flow_pay_method,
      </if>
      <if test="halfFlowHosAmount != null">
        half_flow_hos_amount,
      </if>
      <if test="halfFlowAmount != null">
        half_flow_amount,
      </if>
      <if test="allFlowPayMethod != null">
        all_flow_pay_method,
      </if>
      <if test="allFlowHosAmount != null">
        all_flow_hos_amount,
      </if>
      <if test="allFlowAmount != null">
        all_flow_amount,
      </if>
      <if test="chargeMode != null">
        charge_mode,
      </if>
      <if test="companyType != null">
        company_type,
      </if>
      <if test="claimPrice != null">
        claim_price,
      </if>
      <if test="outboundPrice != null">
        outbound_price,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="parentCompanyId != null">
        parent_company_id,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="allotRule != null">
        allot_rule,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="connectName != null">
        #{connectName,jdbcType=VARCHAR},
      </if>
      <if test="connectPhone != null">
        #{connectPhone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="halfFlowPayMethod != null">
        #{halfFlowPayMethod,jdbcType=TINYINT},
      </if>
      <if test="halfFlowHosAmount != null">
        #{halfFlowHosAmount,jdbcType=VARCHAR},
      </if>
      <if test="halfFlowAmount != null">
        #{halfFlowAmount,jdbcType=VARCHAR},
      </if>
      <if test="allFlowPayMethod != null">
        #{allFlowPayMethod,jdbcType=TINYINT},
      </if>
      <if test="allFlowHosAmount != null">
        #{allFlowHosAmount,jdbcType=VARCHAR},
      </if>
      <if test="allFlowAmount != null">
        #{allFlowAmount,jdbcType=VARCHAR},
      </if>
      <if test="chargeMode != null">
        #{chargeMode,jdbcType=TINYINT},
      </if>
      <if test="companyType != null">
        #{companyType,jdbcType=CHAR},
      </if>
      <if test="claimPrice != null">
        #{claimPrice,jdbcType=DECIMAL},
      </if>
      <if test="outboundPrice != null">
        #{outboundPrice,jdbcType=DECIMAL},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="parentCompanyId != null">
        #{parentCompanyId,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        #{level,jdbcType=TINYINT},
      </if>
      <if test="allotRule != null">
        #{allotRule,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.TpaCompanyInfoExample" resultType="java.lang.Long">
    select count(*) from tpa_company_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tpa_company_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.connectName != null">
        connect_name = #{record.connectName,jdbcType=VARCHAR},
      </if>
      <if test="record.connectPhone != null">
        connect_phone = #{record.connectPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.halfFlowPayMethod != null">
        half_flow_pay_method = #{record.halfFlowPayMethod,jdbcType=TINYINT},
      </if>
      <if test="record.halfFlowHosAmount != null">
        half_flow_hos_amount = #{record.halfFlowHosAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.halfFlowAmount != null">
        half_flow_amount = #{record.halfFlowAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.allFlowPayMethod != null">
        all_flow_pay_method = #{record.allFlowPayMethod,jdbcType=TINYINT},
      </if>
      <if test="record.allFlowHosAmount != null">
        all_flow_hos_amount = #{record.allFlowHosAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.allFlowAmount != null">
        all_flow_amount = #{record.allFlowAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeMode != null">
        charge_mode = #{record.chargeMode,jdbcType=TINYINT},
      </if>
      <if test="record.companyType != null">
        company_type = #{record.companyType,jdbcType=CHAR},
      </if>
      <if test="record.claimPrice != null">
        claim_price = #{record.claimPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.outboundPrice != null">
        outbound_price = #{record.outboundPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.auditor != null">
        auditor = #{record.auditor,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.parentCompanyId != null">
        parent_company_id = #{record.parentCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.level != null">
        level = #{record.level,jdbcType=TINYINT},
      </if>
      <if test="record.allotRule != null">
        allot_rule = #{record.allotRule,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tpa_company_info
    set id = #{record.id,jdbcType=BIGINT},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      connect_name = #{record.connectName,jdbcType=VARCHAR},
      connect_phone = #{record.connectPhone,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      half_flow_pay_method = #{record.halfFlowPayMethod,jdbcType=TINYINT},
      half_flow_hos_amount = #{record.halfFlowHosAmount,jdbcType=VARCHAR},
      half_flow_amount = #{record.halfFlowAmount,jdbcType=VARCHAR},
      all_flow_pay_method = #{record.allFlowPayMethod,jdbcType=TINYINT},
      all_flow_hos_amount = #{record.allFlowHosAmount,jdbcType=VARCHAR},
      all_flow_amount = #{record.allFlowAmount,jdbcType=VARCHAR},
      charge_mode = #{record.chargeMode,jdbcType=TINYINT},
      company_type = #{record.companyType,jdbcType=CHAR},
      claim_price = #{record.claimPrice,jdbcType=DECIMAL},
      outbound_price = #{record.outboundPrice,jdbcType=DECIMAL},
      auditor = #{record.auditor,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      parent_company_id = #{record.parentCompanyId,jdbcType=BIGINT},
      level = #{record.level,jdbcType=TINYINT},
      allot_rule = #{record.allotRule,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.TpaCompanyInfoDO">
    update tpa_company_info
    <set>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="connectName != null">
        connect_name = #{connectName,jdbcType=VARCHAR},
      </if>
      <if test="connectPhone != null">
        connect_phone = #{connectPhone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="halfFlowPayMethod != null">
        half_flow_pay_method = #{halfFlowPayMethod,jdbcType=TINYINT},
      </if>
      <if test="halfFlowHosAmount != null">
        half_flow_hos_amount = #{halfFlowHosAmount,jdbcType=VARCHAR},
      </if>
      <if test="halfFlowAmount != null">
        half_flow_amount = #{halfFlowAmount,jdbcType=VARCHAR},
      </if>
      <if test="allFlowPayMethod != null">
        all_flow_pay_method = #{allFlowPayMethod,jdbcType=TINYINT},
      </if>
      <if test="allFlowHosAmount != null">
        all_flow_hos_amount = #{allFlowHosAmount,jdbcType=VARCHAR},
      </if>
      <if test="allFlowAmount != null">
        all_flow_amount = #{allFlowAmount,jdbcType=VARCHAR},
      </if>
      <if test="chargeMode != null">
        charge_mode = #{chargeMode,jdbcType=TINYINT},
      </if>
      <if test="companyType != null">
        company_type = #{companyType,jdbcType=CHAR},
      </if>
      <if test="claimPrice != null">
        claim_price = #{claimPrice,jdbcType=DECIMAL},
      </if>
      <if test="outboundPrice != null">
        outbound_price = #{outboundPrice,jdbcType=DECIMAL},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="parentCompanyId != null">
        parent_company_id = #{parentCompanyId,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=TINYINT},
      </if>
      <if test="allotRule != null">
        allot_rule = #{allotRule,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.TpaCompanyInfoDO">
    update tpa_company_info
    set company_name = #{companyName,jdbcType=VARCHAR},
      connect_name = #{connectName,jdbcType=VARCHAR},
      connect_phone = #{connectPhone,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      half_flow_pay_method = #{halfFlowPayMethod,jdbcType=TINYINT},
      half_flow_hos_amount = #{halfFlowHosAmount,jdbcType=VARCHAR},
      half_flow_amount = #{halfFlowAmount,jdbcType=VARCHAR},
      all_flow_pay_method = #{allFlowPayMethod,jdbcType=TINYINT},
      all_flow_hos_amount = #{allFlowHosAmount,jdbcType=VARCHAR},
      all_flow_amount = #{allFlowAmount,jdbcType=VARCHAR},
      charge_mode = #{chargeMode,jdbcType=TINYINT},
      company_type = #{companyType,jdbcType=CHAR},
      claim_price = #{claimPrice,jdbcType=DECIMAL},
      outbound_price = #{outboundPrice,jdbcType=DECIMAL},
      auditor = #{auditor,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      parent_company_id = #{parentCompanyId,jdbcType=BIGINT},
      level = #{level,jdbcType=TINYINT},
      allot_rule = #{allotRule,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into tpa_company_info
    (company_name, connect_name, connect_phone, email, half_flow_pay_method, half_flow_hos_amount, 
      half_flow_amount, all_flow_pay_method, all_flow_hos_amount, all_flow_amount, charge_mode, 
      company_type, claim_price, outbound_price, auditor, status, parent_company_id, 
      level, allot_rule, extra_info, remark, is_deleted, gmt_created, gmt_modified, creator, 
      modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.companyName,jdbcType=VARCHAR}, #{item.connectName,jdbcType=VARCHAR}, #{item.connectPhone,jdbcType=VARCHAR}, 
        #{item.email,jdbcType=VARCHAR}, #{item.halfFlowPayMethod,jdbcType=TINYINT}, #{item.halfFlowHosAmount,jdbcType=VARCHAR}, 
        #{item.halfFlowAmount,jdbcType=VARCHAR}, #{item.allFlowPayMethod,jdbcType=TINYINT}, 
        #{item.allFlowHosAmount,jdbcType=VARCHAR}, #{item.allFlowAmount,jdbcType=VARCHAR}, 
        #{item.chargeMode,jdbcType=TINYINT}, #{item.companyType,jdbcType=CHAR}, #{item.claimPrice,jdbcType=DECIMAL}, 
        #{item.outboundPrice,jdbcType=DECIMAL}, #{item.auditor,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, 
        #{item.parentCompanyId,jdbcType=BIGINT}, #{item.level,jdbcType=TINYINT}, #{item.allotRule,jdbcType=VARCHAR}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into tpa_company_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'company_name'.toString() == column.value">
          #{item.companyName,jdbcType=VARCHAR}
        </if>
        <if test="'connect_name'.toString() == column.value">
          #{item.connectName,jdbcType=VARCHAR}
        </if>
        <if test="'connect_phone'.toString() == column.value">
          #{item.connectPhone,jdbcType=VARCHAR}
        </if>
        <if test="'email'.toString() == column.value">
          #{item.email,jdbcType=VARCHAR}
        </if>
        <if test="'half_flow_pay_method'.toString() == column.value">
          #{item.halfFlowPayMethod,jdbcType=TINYINT}
        </if>
        <if test="'half_flow_hos_amount'.toString() == column.value">
          #{item.halfFlowHosAmount,jdbcType=VARCHAR}
        </if>
        <if test="'half_flow_amount'.toString() == column.value">
          #{item.halfFlowAmount,jdbcType=VARCHAR}
        </if>
        <if test="'all_flow_pay_method'.toString() == column.value">
          #{item.allFlowPayMethod,jdbcType=TINYINT}
        </if>
        <if test="'all_flow_hos_amount'.toString() == column.value">
          #{item.allFlowHosAmount,jdbcType=VARCHAR}
        </if>
        <if test="'all_flow_amount'.toString() == column.value">
          #{item.allFlowAmount,jdbcType=VARCHAR}
        </if>
        <if test="'charge_mode'.toString() == column.value">
          #{item.chargeMode,jdbcType=TINYINT}
        </if>
        <if test="'company_type'.toString() == column.value">
          #{item.companyType,jdbcType=CHAR}
        </if>
        <if test="'claim_price'.toString() == column.value">
          #{item.claimPrice,jdbcType=DECIMAL}
        </if>
        <if test="'outbound_price'.toString() == column.value">
          #{item.outboundPrice,jdbcType=DECIMAL}
        </if>
        <if test="'auditor'.toString() == column.value">
          #{item.auditor,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'parent_company_id'.toString() == column.value">
          #{item.parentCompanyId,jdbcType=BIGINT}
        </if>
        <if test="'level'.toString() == column.value">
          #{item.level,jdbcType=TINYINT}
        </if>
        <if test="'allot_rule'.toString() == column.value">
          #{item.allotRule,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>