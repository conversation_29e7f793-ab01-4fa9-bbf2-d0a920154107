<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ZbxRequestRecordMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ZbxRequestRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bill_number" jdbcType="VARCHAR" property="billNumber" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="request_params" jdbcType="VARCHAR" property="requestParams" />
    <result column="response" jdbcType="VARCHAR" property="response" />
    <result column="repeat_bill" jdbcType="VARCHAR" property="repeatBill" />
    <result column="is_success" jdbcType="CHAR" property="isSuccess" />
    <result column="is_repeat" jdbcType="CHAR" property="isRepeat" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bill_number, report_no, request_params, response, repeat_bill, is_success, is_repeat, 
    is_deleted, gmt_created, gmt_modified, creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ZbxRequestRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from zbx_request_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from zbx_request_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ZbxRequestRecordDO">
    insert into zbx_request_record (id, bill_number, report_no, 
      request_params, response, repeat_bill, 
      is_success, is_repeat, is_deleted, 
      gmt_created, gmt_modified, creator, 
      modifier)
    values (#{id,jdbcType=BIGINT}, #{billNumber,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{requestParams,jdbcType=VARCHAR}, #{response,jdbcType=VARCHAR}, #{repeatBill,jdbcType=VARCHAR}, 
      #{isSuccess,jdbcType=CHAR}, #{isRepeat,jdbcType=CHAR}, #{isDeleted,jdbcType=CHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ZbxRequestRecordDO">
    insert into zbx_request_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="billNumber != null">
        bill_number,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="requestParams != null">
        request_params,
      </if>
      <if test="response != null">
        response,
      </if>
      <if test="repeatBill != null">
        repeat_bill,
      </if>
      <if test="isSuccess != null">
        is_success,
      </if>
      <if test="isRepeat != null">
        is_repeat,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="billNumber != null">
        #{billNumber,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="requestParams != null">
        #{requestParams,jdbcType=VARCHAR},
      </if>
      <if test="response != null">
        #{response,jdbcType=VARCHAR},
      </if>
      <if test="repeatBill != null">
        #{repeatBill,jdbcType=VARCHAR},
      </if>
      <if test="isSuccess != null">
        #{isSuccess,jdbcType=CHAR},
      </if>
      <if test="isRepeat != null">
        #{isRepeat,jdbcType=CHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ZbxRequestRecordExample" resultType="java.lang.Long">
    select count(*) from zbx_request_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update zbx_request_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.billNumber != null">
        bill_number = #{record.billNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.requestParams != null">
        request_params = #{record.requestParams,jdbcType=VARCHAR},
      </if>
      <if test="record.response != null">
        response = #{record.response,jdbcType=VARCHAR},
      </if>
      <if test="record.repeatBill != null">
        repeat_bill = #{record.repeatBill,jdbcType=VARCHAR},
      </if>
      <if test="record.isSuccess != null">
        is_success = #{record.isSuccess,jdbcType=CHAR},
      </if>
      <if test="record.isRepeat != null">
        is_repeat = #{record.isRepeat,jdbcType=CHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update zbx_request_record
    set id = #{record.id,jdbcType=BIGINT},
      bill_number = #{record.billNumber,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      request_params = #{record.requestParams,jdbcType=VARCHAR},
      response = #{record.response,jdbcType=VARCHAR},
      repeat_bill = #{record.repeatBill,jdbcType=VARCHAR},
      is_success = #{record.isSuccess,jdbcType=CHAR},
      is_repeat = #{record.isRepeat,jdbcType=CHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ZbxRequestRecordDO">
    update zbx_request_record
    <set>
      <if test="billNumber != null">
        bill_number = #{billNumber,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="requestParams != null">
        request_params = #{requestParams,jdbcType=VARCHAR},
      </if>
      <if test="response != null">
        response = #{response,jdbcType=VARCHAR},
      </if>
      <if test="repeatBill != null">
        repeat_bill = #{repeatBill,jdbcType=VARCHAR},
      </if>
      <if test="isSuccess != null">
        is_success = #{isSuccess,jdbcType=CHAR},
      </if>
      <if test="isRepeat != null">
        is_repeat = #{isRepeat,jdbcType=CHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ZbxRequestRecordDO">
    update zbx_request_record
    set bill_number = #{billNumber,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      request_params = #{requestParams,jdbcType=VARCHAR},
      response = #{response,jdbcType=VARCHAR},
      repeat_bill = #{repeatBill,jdbcType=VARCHAR},
      is_success = #{isSuccess,jdbcType=CHAR},
      is_repeat = #{isRepeat,jdbcType=CHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into zbx_request_record
    (id, bill_number, report_no, request_params, response, repeat_bill, is_success, is_repeat, 
      is_deleted, gmt_created, gmt_modified, creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.billNumber,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.requestParams,jdbcType=VARCHAR}, #{item.response,jdbcType=VARCHAR}, #{item.repeatBill,jdbcType=VARCHAR}, 
        #{item.isSuccess,jdbcType=CHAR}, #{item.isRepeat,jdbcType=CHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into zbx_request_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'bill_number'.toString() == column.value">
          #{item.billNumber,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'request_params'.toString() == column.value">
          #{item.requestParams,jdbcType=VARCHAR}
        </if>
        <if test="'response'.toString() == column.value">
          #{item.response,jdbcType=VARCHAR}
        </if>
        <if test="'repeat_bill'.toString() == column.value">
          #{item.repeatBill,jdbcType=VARCHAR}
        </if>
        <if test="'is_success'.toString() == column.value">
          #{item.isSuccess,jdbcType=CHAR}
        </if>
        <if test="'is_repeat'.toString() == column.value">
          #{item.isRepeat,jdbcType=CHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>