<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.BBankInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.BBankInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_claim_bill_id" jdbcType="BIGINT" property="batchClaimBillId" />
    <result column="batch_claim_bill_no" jdbcType="VARCHAR" property="batchClaimBillNo" />
    <result column="indemnity_type" jdbcType="TINYINT" property="indemnityType" />
    <result column="indemnity_amount" jdbcType="VARCHAR" property="indemnityAmount" />
    <result column="indemnity_amount_curr" jdbcType="VARCHAR" property="indemnityAmountCurr" />
    <result column="account_type" jdbcType="TINYINT" property="accountType" />
    <result column="payment_way" jdbcType="INTEGER" property="paymentWay" />
    <result column="payee_bank" jdbcType="VARCHAR" property="payeeBank" />
    <result column="payee_bank_name" jdbcType="VARCHAR" property="payeeBankName" />
    <result column="payee_account" jdbcType="VARCHAR" property="payeeAccount" />
    <result column="payee_name" jdbcType="VARCHAR" property="payeeName" />
    <result column="payee_cert_type" jdbcType="VARCHAR" property="payeeCertType" />
    <result column="payee_cert_no" jdbcType="VARCHAR" property="payeeCertNo" />
    <result column="payee_bank_area" jdbcType="VARCHAR" property="payeeBankArea" />
    <result column="payee_bank_code" jdbcType="VARCHAR" property="payeeBankCode" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="liability_id" jdbcType="BIGINT" property="liabilityId" />
    <result column="liability_code" jdbcType="VARCHAR" property="liabilityCode" />
    <result column="liability_name" jdbcType="VARCHAR" property="liabilityName" />
    <result column="application_desc" jdbcType="VARCHAR" property="applicationDesc" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="pay_state" jdbcType="TINYINT" property="payState" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="settlement_no" jdbcType="VARCHAR" property="settlementNo" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="cert_validity_start" jdbcType="VARCHAR" property="certValidityStart" />
    <result column="cert_validity_end" jdbcType="VARCHAR" property="certValidityEnd" />
    <result column="is_paid" jdbcType="CHAR" property="isPaid" />
    <result column="pay_type" jdbcType="VARCHAR" property="payType" />
    <result column="pre_paid_amount" jdbcType="VARCHAR" property="prePaidAmount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, batch_claim_bill_id, batch_claim_bill_no, indemnity_type, indemnity_amount, indemnity_amount_curr, 
    account_type, payment_way, payee_bank, payee_bank_name, payee_account, payee_name, 
    payee_cert_type, payee_cert_no, payee_bank_area, payee_bank_code, product_id, product_code, 
    product_name, liability_id, liability_code, liability_name, application_desc, remark, 
    pay_state, pay_time, settlement_no, is_deleted, creator, modifier, gmt_created, gmt_modified, 
    cert_validity_start, cert_validity_end, is_paid, pay_type, pre_paid_amount
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.BBankInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_batch_bank_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_batch_bank_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.BBankInfoDO">
    insert into claim_batch_bank_info (id, batch_claim_bill_id, batch_claim_bill_no, 
      indemnity_type, indemnity_amount, indemnity_amount_curr, 
      account_type, payment_way, payee_bank, 
      payee_bank_name, payee_account, payee_name, 
      payee_cert_type, payee_cert_no, payee_bank_area, 
      payee_bank_code, product_id, product_code, 
      product_name, liability_id, liability_code, 
      liability_name, application_desc, remark, 
      pay_state, pay_time, settlement_no, 
      is_deleted, creator, modifier, 
      gmt_created, gmt_modified, cert_validity_start, 
      cert_validity_end, is_paid, pay_type, 
      pre_paid_amount)
    values (#{id,jdbcType=BIGINT}, #{batchClaimBillId,jdbcType=BIGINT}, #{batchClaimBillNo,jdbcType=VARCHAR}, 
      #{indemnityType,jdbcType=TINYINT}, #{indemnityAmount,jdbcType=VARCHAR}, #{indemnityAmountCurr,jdbcType=VARCHAR}, 
      #{accountType,jdbcType=TINYINT}, #{paymentWay,jdbcType=INTEGER}, #{payeeBank,jdbcType=VARCHAR}, 
      #{payeeBankName,jdbcType=VARCHAR}, #{payeeAccount,jdbcType=VARCHAR}, #{payeeName,jdbcType=VARCHAR}, 
      #{payeeCertType,jdbcType=VARCHAR}, #{payeeCertNo,jdbcType=VARCHAR}, #{payeeBankArea,jdbcType=VARCHAR}, 
      #{payeeBankCode,jdbcType=VARCHAR}, #{productId,jdbcType=BIGINT}, #{productCode,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{liabilityId,jdbcType=BIGINT}, #{liabilityCode,jdbcType=VARCHAR}, 
      #{liabilityName,jdbcType=VARCHAR}, #{applicationDesc,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{payState,jdbcType=TINYINT}, #{payTime,jdbcType=TIMESTAMP}, #{settlementNo,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{certValidityStart,jdbcType=VARCHAR}, 
      #{certValidityEnd,jdbcType=VARCHAR}, #{isPaid,jdbcType=CHAR}, #{payType,jdbcType=VARCHAR}, 
      #{prePaidAmount,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.BBankInfoDO">
    insert into claim_batch_bank_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="batchClaimBillId != null">
        batch_claim_bill_id,
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no,
      </if>
      <if test="indemnityType != null">
        indemnity_type,
      </if>
      <if test="indemnityAmount != null">
        indemnity_amount,
      </if>
      <if test="indemnityAmountCurr != null">
        indemnity_amount_curr,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="paymentWay != null">
        payment_way,
      </if>
      <if test="payeeBank != null">
        payee_bank,
      </if>
      <if test="payeeBankName != null">
        payee_bank_name,
      </if>
      <if test="payeeAccount != null">
        payee_account,
      </if>
      <if test="payeeName != null">
        payee_name,
      </if>
      <if test="payeeCertType != null">
        payee_cert_type,
      </if>
      <if test="payeeCertNo != null">
        payee_cert_no,
      </if>
      <if test="payeeBankArea != null">
        payee_bank_area,
      </if>
      <if test="payeeBankCode != null">
        payee_bank_code,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="productCode != null">
        product_code,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="liabilityId != null">
        liability_id,
      </if>
      <if test="liabilityCode != null">
        liability_code,
      </if>
      <if test="liabilityName != null">
        liability_name,
      </if>
      <if test="applicationDesc != null">
        application_desc,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="payState != null">
        pay_state,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="settlementNo != null">
        settlement_no,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="certValidityStart != null">
        cert_validity_start,
      </if>
      <if test="certValidityEnd != null">
        cert_validity_end,
      </if>
      <if test="isPaid != null">
        is_paid,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="prePaidAmount != null">
        pre_paid_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillId != null">
        #{batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillNo != null">
        #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="indemnityType != null">
        #{indemnityType,jdbcType=TINYINT},
      </if>
      <if test="indemnityAmount != null">
        #{indemnityAmount,jdbcType=VARCHAR},
      </if>
      <if test="indemnityAmountCurr != null">
        #{indemnityAmountCurr,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=TINYINT},
      </if>
      <if test="paymentWay != null">
        #{paymentWay,jdbcType=INTEGER},
      </if>
      <if test="payeeBank != null">
        #{payeeBank,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankName != null">
        #{payeeBankName,jdbcType=VARCHAR},
      </if>
      <if test="payeeAccount != null">
        #{payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="payeeCertType != null">
        #{payeeCertType,jdbcType=VARCHAR},
      </if>
      <if test="payeeCertNo != null">
        #{payeeCertNo,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankArea != null">
        #{payeeBankArea,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankCode != null">
        #{payeeBankCode,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityId != null">
        #{liabilityId,jdbcType=BIGINT},
      </if>
      <if test="liabilityCode != null">
        #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="liabilityName != null">
        #{liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="applicationDesc != null">
        #{applicationDesc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        #{payState,jdbcType=TINYINT},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementNo != null">
        #{settlementNo,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="certValidityStart != null">
        #{certValidityStart,jdbcType=VARCHAR},
      </if>
      <if test="certValidityEnd != null">
        #{certValidityEnd,jdbcType=VARCHAR},
      </if>
      <if test="isPaid != null">
        #{isPaid,jdbcType=CHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=VARCHAR},
      </if>
      <if test="prePaidAmount != null">
        #{prePaidAmount,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.BBankInfoExample" resultType="java.lang.Long">
    select count(*) from claim_batch_bank_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_batch_bank_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.batchClaimBillId != null">
        batch_claim_bill_id = #{record.batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="record.batchClaimBillNo != null">
        batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.indemnityType != null">
        indemnity_type = #{record.indemnityType,jdbcType=TINYINT},
      </if>
      <if test="record.indemnityAmount != null">
        indemnity_amount = #{record.indemnityAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.indemnityAmountCurr != null">
        indemnity_amount_curr = #{record.indemnityAmountCurr,jdbcType=VARCHAR},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=TINYINT},
      </if>
      <if test="record.paymentWay != null">
        payment_way = #{record.paymentWay,jdbcType=INTEGER},
      </if>
      <if test="record.payeeBank != null">
        payee_bank = #{record.payeeBank,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeBankName != null">
        payee_bank_name = #{record.payeeBankName,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeAccount != null">
        payee_account = #{record.payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeName != null">
        payee_name = #{record.payeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeCertType != null">
        payee_cert_type = #{record.payeeCertType,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeCertNo != null">
        payee_cert_no = #{record.payeeCertNo,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeBankArea != null">
        payee_bank_area = #{record.payeeBankArea,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeBankCode != null">
        payee_bank_code = #{record.payeeBankCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.productCode != null">
        product_code = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityId != null">
        liability_id = #{record.liabilityId,jdbcType=BIGINT},
      </if>
      <if test="record.liabilityCode != null">
        liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.liabilityName != null">
        liability_name = #{record.liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="record.applicationDesc != null">
        application_desc = #{record.applicationDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.payState != null">
        pay_state = #{record.payState,jdbcType=TINYINT},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.settlementNo != null">
        settlement_no = #{record.settlementNo,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.certValidityStart != null">
        cert_validity_start = #{record.certValidityStart,jdbcType=VARCHAR},
      </if>
      <if test="record.certValidityEnd != null">
        cert_validity_end = #{record.certValidityEnd,jdbcType=VARCHAR},
      </if>
      <if test="record.isPaid != null">
        is_paid = #{record.isPaid,jdbcType=CHAR},
      </if>
      <if test="record.payType != null">
        pay_type = #{record.payType,jdbcType=VARCHAR},
      </if>
      <if test="record.prePaidAmount != null">
        pre_paid_amount = #{record.prePaidAmount,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_batch_bank_info
    set id = #{record.id,jdbcType=BIGINT},
      batch_claim_bill_id = #{record.batchClaimBillId,jdbcType=BIGINT},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      indemnity_type = #{record.indemnityType,jdbcType=TINYINT},
      indemnity_amount = #{record.indemnityAmount,jdbcType=VARCHAR},
      indemnity_amount_curr = #{record.indemnityAmountCurr,jdbcType=VARCHAR},
      account_type = #{record.accountType,jdbcType=TINYINT},
      payment_way = #{record.paymentWay,jdbcType=INTEGER},
      payee_bank = #{record.payeeBank,jdbcType=VARCHAR},
      payee_bank_name = #{record.payeeBankName,jdbcType=VARCHAR},
      payee_account = #{record.payeeAccount,jdbcType=VARCHAR},
      payee_name = #{record.payeeName,jdbcType=VARCHAR},
      payee_cert_type = #{record.payeeCertType,jdbcType=VARCHAR},
      payee_cert_no = #{record.payeeCertNo,jdbcType=VARCHAR},
      payee_bank_area = #{record.payeeBankArea,jdbcType=VARCHAR},
      payee_bank_code = #{record.payeeBankCode,jdbcType=VARCHAR},
      product_id = #{record.productId,jdbcType=BIGINT},
      product_code = #{record.productCode,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      liability_id = #{record.liabilityId,jdbcType=BIGINT},
      liability_code = #{record.liabilityCode,jdbcType=VARCHAR},
      liability_name = #{record.liabilityName,jdbcType=VARCHAR},
      application_desc = #{record.applicationDesc,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      pay_state = #{record.payState,jdbcType=TINYINT},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      settlement_no = #{record.settlementNo,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      cert_validity_start = #{record.certValidityStart,jdbcType=VARCHAR},
      cert_validity_end = #{record.certValidityEnd,jdbcType=VARCHAR},
      is_paid = #{record.isPaid,jdbcType=CHAR},
      pay_type = #{record.payType,jdbcType=VARCHAR},
      pre_paid_amount = #{record.prePaidAmount,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.BBankInfoDO">
    update claim_batch_bank_info
    <set>
      <if test="batchClaimBillId != null">
        batch_claim_bill_id = #{batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="indemnityType != null">
        indemnity_type = #{indemnityType,jdbcType=TINYINT},
      </if>
      <if test="indemnityAmount != null">
        indemnity_amount = #{indemnityAmount,jdbcType=VARCHAR},
      </if>
      <if test="indemnityAmountCurr != null">
        indemnity_amount_curr = #{indemnityAmountCurr,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=TINYINT},
      </if>
      <if test="paymentWay != null">
        payment_way = #{paymentWay,jdbcType=INTEGER},
      </if>
      <if test="payeeBank != null">
        payee_bank = #{payeeBank,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankName != null">
        payee_bank_name = #{payeeBankName,jdbcType=VARCHAR},
      </if>
      <if test="payeeAccount != null">
        payee_account = #{payeeAccount,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        payee_name = #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="payeeCertType != null">
        payee_cert_type = #{payeeCertType,jdbcType=VARCHAR},
      </if>
      <if test="payeeCertNo != null">
        payee_cert_no = #{payeeCertNo,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankArea != null">
        payee_bank_area = #{payeeBankArea,jdbcType=VARCHAR},
      </if>
      <if test="payeeBankCode != null">
        payee_bank_code = #{payeeBankCode,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="productCode != null">
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="liabilityId != null">
        liability_id = #{liabilityId,jdbcType=BIGINT},
      </if>
      <if test="liabilityCode != null">
        liability_code = #{liabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="liabilityName != null">
        liability_name = #{liabilityName,jdbcType=VARCHAR},
      </if>
      <if test="applicationDesc != null">
        application_desc = #{applicationDesc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        pay_state = #{payState,jdbcType=TINYINT},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementNo != null">
        settlement_no = #{settlementNo,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="certValidityStart != null">
        cert_validity_start = #{certValidityStart,jdbcType=VARCHAR},
      </if>
      <if test="certValidityEnd != null">
        cert_validity_end = #{certValidityEnd,jdbcType=VARCHAR},
      </if>
      <if test="isPaid != null">
        is_paid = #{isPaid,jdbcType=CHAR},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="prePaidAmount != null">
        pre_paid_amount = #{prePaidAmount,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.BBankInfoDO">
    update claim_batch_bank_info
    set batch_claim_bill_id = #{batchClaimBillId,jdbcType=BIGINT},
      batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      indemnity_type = #{indemnityType,jdbcType=TINYINT},
      indemnity_amount = #{indemnityAmount,jdbcType=VARCHAR},
      indemnity_amount_curr = #{indemnityAmountCurr,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=TINYINT},
      payment_way = #{paymentWay,jdbcType=INTEGER},
      payee_bank = #{payeeBank,jdbcType=VARCHAR},
      payee_bank_name = #{payeeBankName,jdbcType=VARCHAR},
      payee_account = #{payeeAccount,jdbcType=VARCHAR},
      payee_name = #{payeeName,jdbcType=VARCHAR},
      payee_cert_type = #{payeeCertType,jdbcType=VARCHAR},
      payee_cert_no = #{payeeCertNo,jdbcType=VARCHAR},
      payee_bank_area = #{payeeBankArea,jdbcType=VARCHAR},
      payee_bank_code = #{payeeBankCode,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=BIGINT},
      product_code = #{productCode,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      liability_id = #{liabilityId,jdbcType=BIGINT},
      liability_code = #{liabilityCode,jdbcType=VARCHAR},
      liability_name = #{liabilityName,jdbcType=VARCHAR},
      application_desc = #{applicationDesc,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      pay_state = #{payState,jdbcType=TINYINT},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      settlement_no = #{settlementNo,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      cert_validity_start = #{certValidityStart,jdbcType=VARCHAR},
      cert_validity_end = #{certValidityEnd,jdbcType=VARCHAR},
      is_paid = #{isPaid,jdbcType=CHAR},
      pay_type = #{payType,jdbcType=VARCHAR},
      pre_paid_amount = #{prePaidAmount,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_batch_bank_info
    (id, batch_claim_bill_id, batch_claim_bill_no, indemnity_type, indemnity_amount, 
      indemnity_amount_curr, account_type, payment_way, payee_bank, payee_bank_name, 
      payee_account, payee_name, payee_cert_type, payee_cert_no, payee_bank_area, payee_bank_code, 
      product_id, product_code, product_name, liability_id, liability_code, liability_name, 
      application_desc, remark, pay_state, pay_time, settlement_no, is_deleted, creator, 
      modifier, gmt_created, gmt_modified, cert_validity_start, cert_validity_end, is_paid, 
      pay_type, pre_paid_amount)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.batchClaimBillId,jdbcType=BIGINT}, #{item.batchClaimBillNo,jdbcType=VARCHAR}, 
        #{item.indemnityType,jdbcType=TINYINT}, #{item.indemnityAmount,jdbcType=VARCHAR}, 
        #{item.indemnityAmountCurr,jdbcType=VARCHAR}, #{item.accountType,jdbcType=TINYINT}, 
        #{item.paymentWay,jdbcType=INTEGER}, #{item.payeeBank,jdbcType=VARCHAR}, #{item.payeeBankName,jdbcType=VARCHAR}, 
        #{item.payeeAccount,jdbcType=VARCHAR}, #{item.payeeName,jdbcType=VARCHAR}, #{item.payeeCertType,jdbcType=VARCHAR}, 
        #{item.payeeCertNo,jdbcType=VARCHAR}, #{item.payeeBankArea,jdbcType=VARCHAR}, #{item.payeeBankCode,jdbcType=VARCHAR}, 
        #{item.productId,jdbcType=BIGINT}, #{item.productCode,jdbcType=VARCHAR}, #{item.productName,jdbcType=VARCHAR}, 
        #{item.liabilityId,jdbcType=BIGINT}, #{item.liabilityCode,jdbcType=VARCHAR}, #{item.liabilityName,jdbcType=VARCHAR}, 
        #{item.applicationDesc,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.payState,jdbcType=TINYINT}, 
        #{item.payTime,jdbcType=TIMESTAMP}, #{item.settlementNo,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.certValidityStart,jdbcType=VARCHAR}, 
        #{item.certValidityEnd,jdbcType=VARCHAR}, #{item.isPaid,jdbcType=CHAR}, #{item.payType,jdbcType=VARCHAR}, 
        #{item.prePaidAmount,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_batch_bank_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'batch_claim_bill_id'.toString() == column.value">
          #{item.batchClaimBillId,jdbcType=BIGINT}
        </if>
        <if test="'batch_claim_bill_no'.toString() == column.value">
          #{item.batchClaimBillNo,jdbcType=VARCHAR}
        </if>
        <if test="'indemnity_type'.toString() == column.value">
          #{item.indemnityType,jdbcType=TINYINT}
        </if>
        <if test="'indemnity_amount'.toString() == column.value">
          #{item.indemnityAmount,jdbcType=VARCHAR}
        </if>
        <if test="'indemnity_amount_curr'.toString() == column.value">
          #{item.indemnityAmountCurr,jdbcType=VARCHAR}
        </if>
        <if test="'account_type'.toString() == column.value">
          #{item.accountType,jdbcType=TINYINT}
        </if>
        <if test="'payment_way'.toString() == column.value">
          #{item.paymentWay,jdbcType=INTEGER}
        </if>
        <if test="'payee_bank'.toString() == column.value">
          #{item.payeeBank,jdbcType=VARCHAR}
        </if>
        <if test="'payee_bank_name'.toString() == column.value">
          #{item.payeeBankName,jdbcType=VARCHAR}
        </if>
        <if test="'payee_account'.toString() == column.value">
          #{item.payeeAccount,jdbcType=VARCHAR}
        </if>
        <if test="'payee_name'.toString() == column.value">
          #{item.payeeName,jdbcType=VARCHAR}
        </if>
        <if test="'payee_cert_type'.toString() == column.value">
          #{item.payeeCertType,jdbcType=VARCHAR}
        </if>
        <if test="'payee_cert_no'.toString() == column.value">
          #{item.payeeCertNo,jdbcType=VARCHAR}
        </if>
        <if test="'payee_bank_area'.toString() == column.value">
          #{item.payeeBankArea,jdbcType=VARCHAR}
        </if>
        <if test="'payee_bank_code'.toString() == column.value">
          #{item.payeeBankCode,jdbcType=VARCHAR}
        </if>
        <if test="'product_id'.toString() == column.value">
          #{item.productId,jdbcType=BIGINT}
        </if>
        <if test="'product_code'.toString() == column.value">
          #{item.productCode,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'liability_id'.toString() == column.value">
          #{item.liabilityId,jdbcType=BIGINT}
        </if>
        <if test="'liability_code'.toString() == column.value">
          #{item.liabilityCode,jdbcType=VARCHAR}
        </if>
        <if test="'liability_name'.toString() == column.value">
          #{item.liabilityName,jdbcType=VARCHAR}
        </if>
        <if test="'application_desc'.toString() == column.value">
          #{item.applicationDesc,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'pay_state'.toString() == column.value">
          #{item.payState,jdbcType=TINYINT}
        </if>
        <if test="'pay_time'.toString() == column.value">
          #{item.payTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'settlement_no'.toString() == column.value">
          #{item.settlementNo,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'cert_validity_start'.toString() == column.value">
          #{item.certValidityStart,jdbcType=VARCHAR}
        </if>
        <if test="'cert_validity_end'.toString() == column.value">
          #{item.certValidityEnd,jdbcType=VARCHAR}
        </if>
        <if test="'is_paid'.toString() == column.value">
          #{item.isPaid,jdbcType=CHAR}
        </if>
        <if test="'pay_type'.toString() == column.value">
          #{item.payType,jdbcType=VARCHAR}
        </if>
        <if test="'pre_paid_amount'.toString() == column.value">
          #{item.prePaidAmount,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>