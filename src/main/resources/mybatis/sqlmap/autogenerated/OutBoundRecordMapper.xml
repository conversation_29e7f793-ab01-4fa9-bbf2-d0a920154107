<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.OutBoundRecordMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.OutBoundRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="relation_table_id" jdbcType="BIGINT" property="relationTableId" />
    <result column="relation_table_name" jdbcType="VARCHAR" property="relationTableName" />
    <result column="cno" jdbcType="VARCHAR" property="cno" />
    <result column="request_unique_id" jdbcType="VARCHAR" property="requestUniqueId" />
    <result column="unique_id" jdbcType="VARCHAR" property="uniqueId" />
    <result column="call_time" jdbcType="TIMESTAMP" property="callTime" />
    <result column="customer_phone" jdbcType="VARCHAR" property="customerPhone" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="call_operator" jdbcType="VARCHAR" property="callOperator" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, relation_table_id, relation_table_name, cno, request_unique_id, unique_id, 
    call_time, customer_phone, extra_info, remark, is_deleted, gmt_created, gmt_modified, 
    creator, modifier, call_operator, company_id
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.OutBoundRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from out_bound_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from out_bound_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.OutBoundRecordDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into out_bound_record (report_no, relation_table_id, relation_table_name, 
      cno, request_unique_id, unique_id, 
      call_time, customer_phone, extra_info, 
      remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, 
      call_operator, company_id)
    values (#{reportNo,jdbcType=VARCHAR}, #{relationTableId,jdbcType=BIGINT}, #{relationTableName,jdbcType=VARCHAR}, 
      #{cno,jdbcType=VARCHAR}, #{requestUniqueId,jdbcType=VARCHAR}, #{uniqueId,jdbcType=VARCHAR}, 
      #{callTime,jdbcType=TIMESTAMP}, #{customerPhone,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{callOperator,jdbcType=VARCHAR}, #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.OutBoundRecordDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into out_bound_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="relationTableId != null">
        relation_table_id,
      </if>
      <if test="relationTableName != null">
        relation_table_name,
      </if>
      <if test="cno != null">
        cno,
      </if>
      <if test="requestUniqueId != null">
        request_unique_id,
      </if>
      <if test="uniqueId != null">
        unique_id,
      </if>
      <if test="callTime != null">
        call_time,
      </if>
      <if test="customerPhone != null">
        customer_phone,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="callOperator != null">
        call_operator,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="relationTableId != null">
        #{relationTableId,jdbcType=BIGINT},
      </if>
      <if test="relationTableName != null">
        #{relationTableName,jdbcType=VARCHAR},
      </if>
      <if test="cno != null">
        #{cno,jdbcType=VARCHAR},
      </if>
      <if test="requestUniqueId != null">
        #{requestUniqueId,jdbcType=VARCHAR},
      </if>
      <if test="uniqueId != null">
        #{uniqueId,jdbcType=VARCHAR},
      </if>
      <if test="callTime != null">
        #{callTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerPhone != null">
        #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="callOperator != null">
        #{callOperator,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.OutBoundRecordExample" resultType="java.lang.Long">
    select count(*) from out_bound_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update out_bound_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.relationTableId != null">
        relation_table_id = #{record.relationTableId,jdbcType=BIGINT},
      </if>
      <if test="record.relationTableName != null">
        relation_table_name = #{record.relationTableName,jdbcType=VARCHAR},
      </if>
      <if test="record.cno != null">
        cno = #{record.cno,jdbcType=VARCHAR},
      </if>
      <if test="record.requestUniqueId != null">
        request_unique_id = #{record.requestUniqueId,jdbcType=VARCHAR},
      </if>
      <if test="record.uniqueId != null">
        unique_id = #{record.uniqueId,jdbcType=VARCHAR},
      </if>
      <if test="record.callTime != null">
        call_time = #{record.callTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.customerPhone != null">
        customer_phone = #{record.customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.callOperator != null">
        call_operator = #{record.callOperator,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update out_bound_record
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      relation_table_id = #{record.relationTableId,jdbcType=BIGINT},
      relation_table_name = #{record.relationTableName,jdbcType=VARCHAR},
      cno = #{record.cno,jdbcType=VARCHAR},
      request_unique_id = #{record.requestUniqueId,jdbcType=VARCHAR},
      unique_id = #{record.uniqueId,jdbcType=VARCHAR},
      call_time = #{record.callTime,jdbcType=TIMESTAMP},
      customer_phone = #{record.customerPhone,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      call_operator = #{record.callOperator,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.OutBoundRecordDO">
    update out_bound_record
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="relationTableId != null">
        relation_table_id = #{relationTableId,jdbcType=BIGINT},
      </if>
      <if test="relationTableName != null">
        relation_table_name = #{relationTableName,jdbcType=VARCHAR},
      </if>
      <if test="cno != null">
        cno = #{cno,jdbcType=VARCHAR},
      </if>
      <if test="requestUniqueId != null">
        request_unique_id = #{requestUniqueId,jdbcType=VARCHAR},
      </if>
      <if test="uniqueId != null">
        unique_id = #{uniqueId,jdbcType=VARCHAR},
      </if>
      <if test="callTime != null">
        call_time = #{callTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerPhone != null">
        customer_phone = #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="callOperator != null">
        call_operator = #{callOperator,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.OutBoundRecordDO">
    update out_bound_record
    set report_no = #{reportNo,jdbcType=VARCHAR},
      relation_table_id = #{relationTableId,jdbcType=BIGINT},
      relation_table_name = #{relationTableName,jdbcType=VARCHAR},
      cno = #{cno,jdbcType=VARCHAR},
      request_unique_id = #{requestUniqueId,jdbcType=VARCHAR},
      unique_id = #{uniqueId,jdbcType=VARCHAR},
      call_time = #{callTime,jdbcType=TIMESTAMP},
      customer_phone = #{customerPhone,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      call_operator = #{callOperator,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into out_bound_record
    (report_no, relation_table_id, relation_table_name, cno, request_unique_id, unique_id, 
      call_time, customer_phone, extra_info, remark, is_deleted, gmt_created, gmt_modified, 
      creator, modifier, call_operator, company_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reportNo,jdbcType=VARCHAR}, #{item.relationTableId,jdbcType=BIGINT}, #{item.relationTableName,jdbcType=VARCHAR}, 
        #{item.cno,jdbcType=VARCHAR}, #{item.requestUniqueId,jdbcType=VARCHAR}, #{item.uniqueId,jdbcType=VARCHAR}, 
        #{item.callTime,jdbcType=TIMESTAMP}, #{item.customerPhone,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.callOperator,jdbcType=VARCHAR}, #{item.companyId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into out_bound_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'relation_table_id'.toString() == column.value">
          #{item.relationTableId,jdbcType=BIGINT}
        </if>
        <if test="'relation_table_name'.toString() == column.value">
          #{item.relationTableName,jdbcType=VARCHAR}
        </if>
        <if test="'cno'.toString() == column.value">
          #{item.cno,jdbcType=VARCHAR}
        </if>
        <if test="'request_unique_id'.toString() == column.value">
          #{item.requestUniqueId,jdbcType=VARCHAR}
        </if>
        <if test="'unique_id'.toString() == column.value">
          #{item.uniqueId,jdbcType=VARCHAR}
        </if>
        <if test="'call_time'.toString() == column.value">
          #{item.callTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'customer_phone'.toString() == column.value">
          #{item.customerPhone,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'call_operator'.toString() == column.value">
          #{item.callOperator,jdbcType=VARCHAR}
        </if>
        <if test="'company_id'.toString() == column.value">
          #{item.companyId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>