<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AttachmentMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AttachmentDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName" />
    <result column="attachment_type" jdbcType="TINYINT" property="attachmentType" />
    <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="is_core_uploaded" jdbcType="CHAR" property="isCoreUploaded" />
    <result column="core_attachment_id" jdbcType="VARCHAR" property="coreAttachmentId" />
    <result column="uploaded_attachment_url" jdbcType="VARCHAR" property="uploadedAttachmentUrl" />
    <result column="dispatcher_no" jdbcType="VARCHAR" property="dispatcherNo" />
    <result column="is_visible" jdbcType="CHAR" property="isVisible" />
    <result column="is_sync_coreps" jdbcType="VARCHAR" property="isSyncCoreps" />
    <result column="is_complete" jdbcType="CHAR" property="isComplete" />
    <result column="is_entered_invoice" jdbcType="CHAR" property="isEnteredInvoice" />
    <result column="ps_content" jdbcType="VARCHAR" property="psContent" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sub_attachment_type" jdbcType="INTEGER" property="subAttachmentType" />
    <result column="file_format" jdbcType="TINYINT" property="fileFormat" />
    <result column="origin_upload_time" jdbcType="TIMESTAMP" property="originUploadTime" />
    <result column="pic_tag" jdbcType="VARCHAR" property="picTag" />
    <result column="is_changed_ossKey" jdbcType="CHAR" property="isChangedOsskey" />
    <result column="pre_closed_send_mark" jdbcType="CHAR" property="preClosedSendMark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, attachment_name, attachment_type, attachment_url, is_deleted, gmt_created, gmt_modified, 
    creator, modifier, report_no, description, version, is_core_uploaded, core_attachment_id, 
    uploaded_attachment_url, dispatcher_no, is_visible, is_sync_coreps, is_complete, 
    is_entered_invoice, ps_content, remark, sub_attachment_type, file_format, origin_upload_time, 
    pic_tag, is_changed_ossKey, pre_closed_send_mark
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AttachmentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_assess_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_assess_attachment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AttachmentDO">
    insert into cargo_assess_attachment (id, attachment_name, attachment_type, 
      attachment_url, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, 
      report_no, description, version, 
      is_core_uploaded, core_attachment_id, uploaded_attachment_url, 
      dispatcher_no, is_visible, is_sync_coreps, 
      is_complete, is_entered_invoice, ps_content, 
      remark, sub_attachment_type, file_format, 
      origin_upload_time, pic_tag, is_changed_ossKey, 
      pre_closed_send_mark)
    values (#{id,jdbcType=BIGINT}, #{attachmentName,jdbcType=VARCHAR}, #{attachmentType,jdbcType=TINYINT}, 
      #{attachmentUrl,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{isCoreUploaded,jdbcType=CHAR}, #{coreAttachmentId,jdbcType=VARCHAR}, #{uploadedAttachmentUrl,jdbcType=VARCHAR}, 
      #{dispatcherNo,jdbcType=VARCHAR}, #{isVisible,jdbcType=CHAR}, #{isSyncCoreps,jdbcType=VARCHAR}, 
      #{isComplete,jdbcType=CHAR}, #{isEnteredInvoice,jdbcType=CHAR}, #{psContent,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{subAttachmentType,jdbcType=INTEGER}, #{fileFormat,jdbcType=TINYINT}, 
      #{originUploadTime,jdbcType=TIMESTAMP}, #{picTag,jdbcType=VARCHAR}, #{isChangedOsskey,jdbcType=CHAR}, 
      #{preClosedSendMark,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AttachmentDO">
    insert into cargo_assess_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="attachmentName != null">
        attachment_name,
      </if>
      <if test="attachmentType != null">
        attachment_type,
      </if>
      <if test="attachmentUrl != null">
        attachment_url,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="isCoreUploaded != null">
        is_core_uploaded,
      </if>
      <if test="coreAttachmentId != null">
        core_attachment_id,
      </if>
      <if test="uploadedAttachmentUrl != null">
        uploaded_attachment_url,
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no,
      </if>
      <if test="isVisible != null">
        is_visible,
      </if>
      <if test="isSyncCoreps != null">
        is_sync_coreps,
      </if>
      <if test="isComplete != null">
        is_complete,
      </if>
      <if test="isEnteredInvoice != null">
        is_entered_invoice,
      </if>
      <if test="psContent != null">
        ps_content,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="subAttachmentType != null">
        sub_attachment_type,
      </if>
      <if test="fileFormat != null">
        file_format,
      </if>
      <if test="originUploadTime != null">
        origin_upload_time,
      </if>
      <if test="picTag != null">
        pic_tag,
      </if>
      <if test="isChangedOsskey != null">
        is_changed_ossKey,
      </if>
      <if test="preClosedSendMark != null">
        pre_closed_send_mark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="attachmentName != null">
        #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="attachmentType != null">
        #{attachmentType,jdbcType=TINYINT},
      </if>
      <if test="attachmentUrl != null">
        #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="isCoreUploaded != null">
        #{isCoreUploaded,jdbcType=CHAR},
      </if>
      <if test="coreAttachmentId != null">
        #{coreAttachmentId,jdbcType=VARCHAR},
      </if>
      <if test="uploadedAttachmentUrl != null">
        #{uploadedAttachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherNo != null">
        #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="isVisible != null">
        #{isVisible,jdbcType=CHAR},
      </if>
      <if test="isSyncCoreps != null">
        #{isSyncCoreps,jdbcType=VARCHAR},
      </if>
      <if test="isComplete != null">
        #{isComplete,jdbcType=CHAR},
      </if>
      <if test="isEnteredInvoice != null">
        #{isEnteredInvoice,jdbcType=CHAR},
      </if>
      <if test="psContent != null">
        #{psContent,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="subAttachmentType != null">
        #{subAttachmentType,jdbcType=INTEGER},
      </if>
      <if test="fileFormat != null">
        #{fileFormat,jdbcType=TINYINT},
      </if>
      <if test="originUploadTime != null">
        #{originUploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="picTag != null">
        #{picTag,jdbcType=VARCHAR},
      </if>
      <if test="isChangedOsskey != null">
        #{isChangedOsskey,jdbcType=CHAR},
      </if>
      <if test="preClosedSendMark != null">
        #{preClosedSendMark,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AttachmentExample" resultType="java.lang.Long">
    select count(*) from cargo_assess_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_assess_attachment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.attachmentName != null">
        attachment_name = #{record.attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentType != null">
        attachment_type = #{record.attachmentType,jdbcType=TINYINT},
      </if>
      <if test="record.attachmentUrl != null">
        attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.isCoreUploaded != null">
        is_core_uploaded = #{record.isCoreUploaded,jdbcType=CHAR},
      </if>
      <if test="record.coreAttachmentId != null">
        core_attachment_id = #{record.coreAttachmentId,jdbcType=VARCHAR},
      </if>
      <if test="record.uploadedAttachmentUrl != null">
        uploaded_attachment_url = #{record.uploadedAttachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.dispatcherNo != null">
        dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="record.isVisible != null">
        is_visible = #{record.isVisible,jdbcType=CHAR},
      </if>
      <if test="record.isSyncCoreps != null">
        is_sync_coreps = #{record.isSyncCoreps,jdbcType=VARCHAR},
      </if>
      <if test="record.isComplete != null">
        is_complete = #{record.isComplete,jdbcType=CHAR},
      </if>
      <if test="record.isEnteredInvoice != null">
        is_entered_invoice = #{record.isEnteredInvoice,jdbcType=CHAR},
      </if>
      <if test="record.psContent != null">
        ps_content = #{record.psContent,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.subAttachmentType != null">
        sub_attachment_type = #{record.subAttachmentType,jdbcType=INTEGER},
      </if>
      <if test="record.fileFormat != null">
        file_format = #{record.fileFormat,jdbcType=TINYINT},
      </if>
      <if test="record.originUploadTime != null">
        origin_upload_time = #{record.originUploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.picTag != null">
        pic_tag = #{record.picTag,jdbcType=VARCHAR},
      </if>
      <if test="record.isChangedOsskey != null">
        is_changed_ossKey = #{record.isChangedOsskey,jdbcType=CHAR},
      </if>
      <if test="record.preClosedSendMark != null">
        pre_closed_send_mark = #{record.preClosedSendMark,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_assess_attachment
    set id = #{record.id,jdbcType=BIGINT},
      attachment_name = #{record.attachmentName,jdbcType=VARCHAR},
      attachment_type = #{record.attachmentType,jdbcType=TINYINT},
      attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      is_core_uploaded = #{record.isCoreUploaded,jdbcType=CHAR},
      core_attachment_id = #{record.coreAttachmentId,jdbcType=VARCHAR},
      uploaded_attachment_url = #{record.uploadedAttachmentUrl,jdbcType=VARCHAR},
      dispatcher_no = #{record.dispatcherNo,jdbcType=VARCHAR},
      is_visible = #{record.isVisible,jdbcType=CHAR},
      is_sync_coreps = #{record.isSyncCoreps,jdbcType=VARCHAR},
      is_complete = #{record.isComplete,jdbcType=CHAR},
      is_entered_invoice = #{record.isEnteredInvoice,jdbcType=CHAR},
      ps_content = #{record.psContent,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      sub_attachment_type = #{record.subAttachmentType,jdbcType=INTEGER},
      file_format = #{record.fileFormat,jdbcType=TINYINT},
      origin_upload_time = #{record.originUploadTime,jdbcType=TIMESTAMP},
      pic_tag = #{record.picTag,jdbcType=VARCHAR},
      is_changed_ossKey = #{record.isChangedOsskey,jdbcType=CHAR},
      pre_closed_send_mark = #{record.preClosedSendMark,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AttachmentDO">
    update cargo_assess_attachment
    <set>
      <if test="attachmentName != null">
        attachment_name = #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="attachmentType != null">
        attachment_type = #{attachmentType,jdbcType=TINYINT},
      </if>
      <if test="attachmentUrl != null">
        attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="isCoreUploaded != null">
        is_core_uploaded = #{isCoreUploaded,jdbcType=CHAR},
      </if>
      <if test="coreAttachmentId != null">
        core_attachment_id = #{coreAttachmentId,jdbcType=VARCHAR},
      </if>
      <if test="uploadedAttachmentUrl != null">
        uploaded_attachment_url = #{uploadedAttachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="dispatcherNo != null">
        dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      </if>
      <if test="isVisible != null">
        is_visible = #{isVisible,jdbcType=CHAR},
      </if>
      <if test="isSyncCoreps != null">
        is_sync_coreps = #{isSyncCoreps,jdbcType=VARCHAR},
      </if>
      <if test="isComplete != null">
        is_complete = #{isComplete,jdbcType=CHAR},
      </if>
      <if test="isEnteredInvoice != null">
        is_entered_invoice = #{isEnteredInvoice,jdbcType=CHAR},
      </if>
      <if test="psContent != null">
        ps_content = #{psContent,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="subAttachmentType != null">
        sub_attachment_type = #{subAttachmentType,jdbcType=INTEGER},
      </if>
      <if test="fileFormat != null">
        file_format = #{fileFormat,jdbcType=TINYINT},
      </if>
      <if test="originUploadTime != null">
        origin_upload_time = #{originUploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="picTag != null">
        pic_tag = #{picTag,jdbcType=VARCHAR},
      </if>
      <if test="isChangedOsskey != null">
        is_changed_ossKey = #{isChangedOsskey,jdbcType=CHAR},
      </if>
      <if test="preClosedSendMark != null">
        pre_closed_send_mark = #{preClosedSendMark,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AttachmentDO">
    update cargo_assess_attachment
    set attachment_name = #{attachmentName,jdbcType=VARCHAR},
      attachment_type = #{attachmentType,jdbcType=TINYINT},
      attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      is_core_uploaded = #{isCoreUploaded,jdbcType=CHAR},
      core_attachment_id = #{coreAttachmentId,jdbcType=VARCHAR},
      uploaded_attachment_url = #{uploadedAttachmentUrl,jdbcType=VARCHAR},
      dispatcher_no = #{dispatcherNo,jdbcType=VARCHAR},
      is_visible = #{isVisible,jdbcType=CHAR},
      is_sync_coreps = #{isSyncCoreps,jdbcType=VARCHAR},
      is_complete = #{isComplete,jdbcType=CHAR},
      is_entered_invoice = #{isEnteredInvoice,jdbcType=CHAR},
      ps_content = #{psContent,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      sub_attachment_type = #{subAttachmentType,jdbcType=INTEGER},
      file_format = #{fileFormat,jdbcType=TINYINT},
      origin_upload_time = #{originUploadTime,jdbcType=TIMESTAMP},
      pic_tag = #{picTag,jdbcType=VARCHAR},
      is_changed_ossKey = #{isChangedOsskey,jdbcType=CHAR},
      pre_closed_send_mark = #{preClosedSendMark,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_assess_attachment
    (id, attachment_name, attachment_type, attachment_url, is_deleted, gmt_created, gmt_modified, 
      creator, modifier, report_no, description, version, is_core_uploaded, core_attachment_id, 
      uploaded_attachment_url, dispatcher_no, is_visible, is_sync_coreps, is_complete, 
      is_entered_invoice, ps_content, remark, sub_attachment_type, file_format, origin_upload_time, 
      pic_tag, is_changed_ossKey, pre_closed_send_mark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.attachmentName,jdbcType=VARCHAR}, #{item.attachmentType,jdbcType=TINYINT}, 
        #{item.attachmentUrl,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.reportNo,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER}, 
        #{item.isCoreUploaded,jdbcType=CHAR}, #{item.coreAttachmentId,jdbcType=VARCHAR}, 
        #{item.uploadedAttachmentUrl,jdbcType=VARCHAR}, #{item.dispatcherNo,jdbcType=VARCHAR}, 
        #{item.isVisible,jdbcType=CHAR}, #{item.isSyncCoreps,jdbcType=VARCHAR}, #{item.isComplete,jdbcType=CHAR}, 
        #{item.isEnteredInvoice,jdbcType=CHAR}, #{item.psContent,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.subAttachmentType,jdbcType=INTEGER}, #{item.fileFormat,jdbcType=TINYINT}, 
        #{item.originUploadTime,jdbcType=TIMESTAMP}, #{item.picTag,jdbcType=VARCHAR}, #{item.isChangedOsskey,jdbcType=CHAR}, 
        #{item.preClosedSendMark,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_assess_attachment (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'attachment_name'.toString() == column.value">
          #{item.attachmentName,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_type'.toString() == column.value">
          #{item.attachmentType,jdbcType=TINYINT}
        </if>
        <if test="'attachment_url'.toString() == column.value">
          #{item.attachmentUrl,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'version'.toString() == column.value">
          #{item.version,jdbcType=INTEGER}
        </if>
        <if test="'is_core_uploaded'.toString() == column.value">
          #{item.isCoreUploaded,jdbcType=CHAR}
        </if>
        <if test="'core_attachment_id'.toString() == column.value">
          #{item.coreAttachmentId,jdbcType=VARCHAR}
        </if>
        <if test="'uploaded_attachment_url'.toString() == column.value">
          #{item.uploadedAttachmentUrl,jdbcType=VARCHAR}
        </if>
        <if test="'dispatcher_no'.toString() == column.value">
          #{item.dispatcherNo,jdbcType=VARCHAR}
        </if>
        <if test="'is_visible'.toString() == column.value">
          #{item.isVisible,jdbcType=CHAR}
        </if>
        <if test="'is_sync_coreps'.toString() == column.value">
          #{item.isSyncCoreps,jdbcType=VARCHAR}
        </if>
        <if test="'is_complete'.toString() == column.value">
          #{item.isComplete,jdbcType=CHAR}
        </if>
        <if test="'is_entered_invoice'.toString() == column.value">
          #{item.isEnteredInvoice,jdbcType=CHAR}
        </if>
        <if test="'ps_content'.toString() == column.value">
          #{item.psContent,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'sub_attachment_type'.toString() == column.value">
          #{item.subAttachmentType,jdbcType=INTEGER}
        </if>
        <if test="'file_format'.toString() == column.value">
          #{item.fileFormat,jdbcType=TINYINT}
        </if>
        <if test="'origin_upload_time'.toString() == column.value">
          #{item.originUploadTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'pic_tag'.toString() == column.value">
          #{item.picTag,jdbcType=VARCHAR}
        </if>
        <if test="'is_changed_ossKey'.toString() == column.value">
          #{item.isChangedOsskey,jdbcType=CHAR}
        </if>
        <if test="'pre_closed_send_mark'.toString() == column.value">
          #{item.preClosedSendMark,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>