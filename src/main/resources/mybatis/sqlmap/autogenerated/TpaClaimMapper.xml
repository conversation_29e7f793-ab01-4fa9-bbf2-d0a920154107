<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.TpaClaimMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.TpaClaimDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="claim_no" jdbcType="VARCHAR" property="claimNo" />
    <result column="report_amount" jdbcType="VARCHAR" property="reportAmount" />
    <result column="report_date" jdbcType="TIMESTAMP" property="reportDate" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="sub_company_id" jdbcType="BIGINT" property="subCompanyId" />
    <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
    <result column="is_error" jdbcType="TINYINT" property="isError" />
    <result column="error_type" jdbcType="VARCHAR" property="errorType" />
    <result column="error_remark" jdbcType="VARCHAR" property="errorRemark" />
    <result column="allot_date" jdbcType="TIMESTAMP" property="allotDate" />
    <result column="cooperate_method" jdbcType="TINYINT" property="cooperateMethod" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="submit_num" jdbcType="TINYINT" property="submitNum" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, policy_no, claim_no, report_amount, report_date, company_id, sub_company_id, 
    employee_id, is_error, error_type, error_remark, allot_date, cooperate_method, status, 
    submit_time, submit_num, extra_info, remark, is_deleted, gmt_created, gmt_modified, 
    creator, modifier
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpa_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tpa_claim
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpa_claim (report_no, policy_no, claim_no, 
      report_amount, report_date, company_id, 
      sub_company_id, employee_id, is_error, 
      error_type, error_remark, allot_date, 
      cooperate_method, status, submit_time, 
      submit_num, extra_info, remark, 
      is_deleted, gmt_created, gmt_modified, 
      creator, modifier)
    values (#{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, #{claimNo,jdbcType=VARCHAR}, 
      #{reportAmount,jdbcType=VARCHAR}, #{reportDate,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT}, 
      #{subCompanyId,jdbcType=BIGINT}, #{employeeId,jdbcType=BIGINT}, #{isError,jdbcType=TINYINT}, 
      #{errorType,jdbcType=VARCHAR}, #{errorRemark,jdbcType=VARCHAR}, #{allotDate,jdbcType=TIMESTAMP}, 
      #{cooperateMethod,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{submitTime,jdbcType=TIMESTAMP}, 
      #{submitNum,jdbcType=TINYINT}, #{extraInfo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR}, sysdate(), sysdate(), 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpa_claim
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="claimNo != null">
        claim_no,
      </if>
      <if test="reportAmount != null">
        report_amount,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="subCompanyId != null">
        sub_company_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="isError != null">
        is_error,
      </if>
      <if test="errorType != null">
        error_type,
      </if>
      <if test="errorRemark != null">
        error_remark,
      </if>
      <if test="allotDate != null">
        allot_date,
      </if>
      <if test="cooperateMethod != null">
        cooperate_method,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="submitNum != null">
        submit_num,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="reportAmount != null">
        #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="subCompanyId != null">
        #{subCompanyId,jdbcType=BIGINT},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=BIGINT},
      </if>
      <if test="isError != null">
        #{isError,jdbcType=TINYINT},
      </if>
      <if test="errorType != null">
        #{errorType,jdbcType=VARCHAR},
      </if>
      <if test="errorRemark != null">
        #{errorRemark,jdbcType=VARCHAR},
      </if>
      <if test="allotDate != null">
        #{allotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cooperateMethod != null">
        #{cooperateMethod,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="submitNum != null">
        #{submitNum,jdbcType=TINYINT},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimExample" resultType="java.lang.Long">
    select count(*) from tpa_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tpa_claim
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimNo != null">
        claim_no = #{record.claimNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportAmount != null">
        report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDate != null">
        report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.subCompanyId != null">
        sub_company_id = #{record.subCompanyId,jdbcType=BIGINT},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=BIGINT},
      </if>
      <if test="record.isError != null">
        is_error = #{record.isError,jdbcType=TINYINT},
      </if>
      <if test="record.errorType != null">
        error_type = #{record.errorType,jdbcType=VARCHAR},
      </if>
      <if test="record.errorRemark != null">
        error_remark = #{record.errorRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.allotDate != null">
        allot_date = #{record.allotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cooperateMethod != null">
        cooperate_method = #{record.cooperateMethod,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.submitTime != null">
        submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.submitNum != null">
        submit_num = #{record.submitNum,jdbcType=TINYINT},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tpa_claim
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      claim_no = #{record.claimNo,jdbcType=VARCHAR},
      report_amount = #{record.reportAmount,jdbcType=VARCHAR},
      report_date = #{record.reportDate,jdbcType=TIMESTAMP},
      company_id = #{record.companyId,jdbcType=BIGINT},
      sub_company_id = #{record.subCompanyId,jdbcType=BIGINT},
      employee_id = #{record.employeeId,jdbcType=BIGINT},
      is_error = #{record.isError,jdbcType=TINYINT},
      error_type = #{record.errorType,jdbcType=VARCHAR},
      error_remark = #{record.errorRemark,jdbcType=VARCHAR},
      allot_date = #{record.allotDate,jdbcType=TIMESTAMP},
      cooperate_method = #{record.cooperateMethod,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      submit_num = #{record.submitNum,jdbcType=TINYINT},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimDO">
    update tpa_claim
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        claim_no = #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="reportAmount != null">
        report_amount = #{reportAmount,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="subCompanyId != null">
        sub_company_id = #{subCompanyId,jdbcType=BIGINT},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=BIGINT},
      </if>
      <if test="isError != null">
        is_error = #{isError,jdbcType=TINYINT},
      </if>
      <if test="errorType != null">
        error_type = #{errorType,jdbcType=VARCHAR},
      </if>
      <if test="errorRemark != null">
        error_remark = #{errorRemark,jdbcType=VARCHAR},
      </if>
      <if test="allotDate != null">
        allot_date = #{allotDate,jdbcType=TIMESTAMP},
      </if>
      <if test="cooperateMethod != null">
        cooperate_method = #{cooperateMethod,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="submitNum != null">
        submit_num = #{submitNum,jdbcType=TINYINT},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.TpaClaimDO">
    update tpa_claim
    set report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      claim_no = #{claimNo,jdbcType=VARCHAR},
      report_amount = #{reportAmount,jdbcType=VARCHAR},
      report_date = #{reportDate,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT},
      sub_company_id = #{subCompanyId,jdbcType=BIGINT},
      employee_id = #{employeeId,jdbcType=BIGINT},
      is_error = #{isError,jdbcType=TINYINT},
      error_type = #{errorType,jdbcType=VARCHAR},
      error_remark = #{errorRemark,jdbcType=VARCHAR},
      allot_date = #{allotDate,jdbcType=TIMESTAMP},
      cooperate_method = #{cooperateMethod,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      submit_time = #{submitTime,jdbcType=TIMESTAMP},
      submit_num = #{submitNum,jdbcType=TINYINT},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into tpa_claim
    (report_no, policy_no, claim_no, report_amount, report_date, company_id, sub_company_id, 
      employee_id, is_error, error_type, error_remark, allot_date, cooperate_method, 
      status, submit_time, submit_num, extra_info, remark, is_deleted, gmt_created, gmt_modified, 
      creator, modifier)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.claimNo,jdbcType=VARCHAR}, 
        #{item.reportAmount,jdbcType=VARCHAR}, #{item.reportDate,jdbcType=TIMESTAMP}, #{item.companyId,jdbcType=BIGINT}, 
        #{item.subCompanyId,jdbcType=BIGINT}, #{item.employeeId,jdbcType=BIGINT}, #{item.isError,jdbcType=TINYINT}, 
        #{item.errorType,jdbcType=VARCHAR}, #{item.errorRemark,jdbcType=VARCHAR}, #{item.allotDate,jdbcType=TIMESTAMP}, 
        #{item.cooperateMethod,jdbcType=TINYINT}, #{item.status,jdbcType=TINYINT}, #{item.submitTime,jdbcType=TIMESTAMP}, 
        #{item.submitNum,jdbcType=TINYINT}, #{item.extraInfo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into tpa_claim (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'claim_no'.toString() == column.value">
          #{item.claimNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_amount'.toString() == column.value">
          #{item.reportAmount,jdbcType=VARCHAR}
        </if>
        <if test="'report_date'.toString() == column.value">
          #{item.reportDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'company_id'.toString() == column.value">
          #{item.companyId,jdbcType=BIGINT}
        </if>
        <if test="'sub_company_id'.toString() == column.value">
          #{item.subCompanyId,jdbcType=BIGINT}
        </if>
        <if test="'employee_id'.toString() == column.value">
          #{item.employeeId,jdbcType=BIGINT}
        </if>
        <if test="'is_error'.toString() == column.value">
          #{item.isError,jdbcType=TINYINT}
        </if>
        <if test="'error_type'.toString() == column.value">
          #{item.errorType,jdbcType=VARCHAR}
        </if>
        <if test="'error_remark'.toString() == column.value">
          #{item.errorRemark,jdbcType=VARCHAR}
        </if>
        <if test="'allot_date'.toString() == column.value">
          #{item.allotDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'cooperate_method'.toString() == column.value">
          #{item.cooperateMethod,jdbcType=TINYINT}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'submit_time'.toString() == column.value">
          #{item.submitTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'submit_num'.toString() == column.value">
          #{item.submitNum,jdbcType=TINYINT}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>