<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.BAssessAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.BAssessAttachmentDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_claim_bill_id" jdbcType="BIGINT" property="batchClaimBillId" />
    <result column="batch_claim_bill_no" jdbcType="VARCHAR" property="batchClaimBillNo" />
    <result column="attachment_type" jdbcType="TINYINT" property="attachmentType" />
    <result column="attachment_name" jdbcType="VARCHAR" property="attachmentName" />
    <result column="attachment_url" jdbcType="VARCHAR" property="attachmentUrl" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="is_core_uploaded" jdbcType="CHAR" property="isCoreUploaded" />
    <result column="is_complete" jdbcType="CHAR" property="isComplete" />
    <result column="is_entered_invoice" jdbcType="CHAR" property="isEnteredInvoice" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="sub_attachment_type" jdbcType="INTEGER" property="subAttachmentType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, batch_claim_bill_id, batch_claim_bill_no, attachment_type, attachment_name, attachment_url, 
    description, version, is_core_uploaded, is_complete, is_entered_invoice, is_deleted, 
    creator, modifier, gmt_created, gmt_modified, sub_attachment_type
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.BAssessAttachmentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_batch_assess_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_batch_assess_attachment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.BAssessAttachmentDO">
    insert into claim_batch_assess_attachment (id, batch_claim_bill_id, batch_claim_bill_no, 
      attachment_type, attachment_name, attachment_url, 
      description, version, is_core_uploaded, 
      is_complete, is_entered_invoice, is_deleted, 
      creator, modifier, gmt_created, 
      gmt_modified, sub_attachment_type)
    values (#{id,jdbcType=BIGINT}, #{batchClaimBillId,jdbcType=BIGINT}, #{batchClaimBillNo,jdbcType=VARCHAR}, 
      #{attachmentType,jdbcType=TINYINT}, #{attachmentName,jdbcType=VARCHAR}, #{attachmentUrl,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{isCoreUploaded,jdbcType=CHAR}, 
      #{isComplete,jdbcType=CHAR}, #{isEnteredInvoice,jdbcType=CHAR}, #{isDeleted,jdbcType=CHAR}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{subAttachmentType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.BAssessAttachmentDO">
    insert into claim_batch_assess_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="batchClaimBillId != null">
        batch_claim_bill_id,
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no,
      </if>
      <if test="attachmentType != null">
        attachment_type,
      </if>
      <if test="attachmentName != null">
        attachment_name,
      </if>
      <if test="attachmentUrl != null">
        attachment_url,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="isCoreUploaded != null">
        is_core_uploaded,
      </if>
      <if test="isComplete != null">
        is_complete,
      </if>
      <if test="isEnteredInvoice != null">
        is_entered_invoice,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="subAttachmentType != null">
        sub_attachment_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillId != null">
        #{batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillNo != null">
        #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="attachmentType != null">
        #{attachmentType,jdbcType=TINYINT},
      </if>
      <if test="attachmentName != null">
        #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null">
        #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="isCoreUploaded != null">
        #{isCoreUploaded,jdbcType=CHAR},
      </if>
      <if test="isComplete != null">
        #{isComplete,jdbcType=CHAR},
      </if>
      <if test="isEnteredInvoice != null">
        #{isEnteredInvoice,jdbcType=CHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="subAttachmentType != null">
        #{subAttachmentType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.BAssessAttachmentExample" resultType="java.lang.Long">
    select count(*) from claim_batch_assess_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_batch_assess_attachment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.batchClaimBillId != null">
        batch_claim_bill_id = #{record.batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="record.batchClaimBillNo != null">
        batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentType != null">
        attachment_type = #{record.attachmentType,jdbcType=TINYINT},
      </if>
      <if test="record.attachmentName != null">
        attachment_name = #{record.attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentUrl != null">
        attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.isCoreUploaded != null">
        is_core_uploaded = #{record.isCoreUploaded,jdbcType=CHAR},
      </if>
      <if test="record.isComplete != null">
        is_complete = #{record.isComplete,jdbcType=CHAR},
      </if>
      <if test="record.isEnteredInvoice != null">
        is_entered_invoice = #{record.isEnteredInvoice,jdbcType=CHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.subAttachmentType != null">
        sub_attachment_type = #{record.subAttachmentType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_batch_assess_attachment
    set id = #{record.id,jdbcType=BIGINT},
      batch_claim_bill_id = #{record.batchClaimBillId,jdbcType=BIGINT},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      attachment_type = #{record.attachmentType,jdbcType=TINYINT},
      attachment_name = #{record.attachmentName,jdbcType=VARCHAR},
      attachment_url = #{record.attachmentUrl,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      is_core_uploaded = #{record.isCoreUploaded,jdbcType=CHAR},
      is_complete = #{record.isComplete,jdbcType=CHAR},
      is_entered_invoice = #{record.isEnteredInvoice,jdbcType=CHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      sub_attachment_type = #{record.subAttachmentType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.BAssessAttachmentDO">
    update claim_batch_assess_attachment
    <set>
      <if test="batchClaimBillId != null">
        batch_claim_bill_id = #{batchClaimBillId,jdbcType=BIGINT},
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="attachmentType != null">
        attachment_type = #{attachmentType,jdbcType=TINYINT},
      </if>
      <if test="attachmentName != null">
        attachment_name = #{attachmentName,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUrl != null">
        attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="isCoreUploaded != null">
        is_core_uploaded = #{isCoreUploaded,jdbcType=CHAR},
      </if>
      <if test="isComplete != null">
        is_complete = #{isComplete,jdbcType=CHAR},
      </if>
      <if test="isEnteredInvoice != null">
        is_entered_invoice = #{isEnteredInvoice,jdbcType=CHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="subAttachmentType != null">
        sub_attachment_type = #{subAttachmentType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.BAssessAttachmentDO">
    update claim_batch_assess_attachment
    set batch_claim_bill_id = #{batchClaimBillId,jdbcType=BIGINT},
      batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      attachment_type = #{attachmentType,jdbcType=TINYINT},
      attachment_name = #{attachmentName,jdbcType=VARCHAR},
      attachment_url = #{attachmentUrl,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      is_core_uploaded = #{isCoreUploaded,jdbcType=CHAR},
      is_complete = #{isComplete,jdbcType=CHAR},
      is_entered_invoice = #{isEnteredInvoice,jdbcType=CHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      sub_attachment_type = #{subAttachmentType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_batch_assess_attachment
    (id, batch_claim_bill_id, batch_claim_bill_no, attachment_type, attachment_name, 
      attachment_url, description, version, is_core_uploaded, is_complete, is_entered_invoice, 
      is_deleted, creator, modifier, gmt_created, gmt_modified, sub_attachment_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.batchClaimBillId,jdbcType=BIGINT}, #{item.batchClaimBillNo,jdbcType=VARCHAR}, 
        #{item.attachmentType,jdbcType=TINYINT}, #{item.attachmentName,jdbcType=VARCHAR}, 
        #{item.attachmentUrl,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER}, 
        #{item.isCoreUploaded,jdbcType=CHAR}, #{item.isComplete,jdbcType=CHAR}, #{item.isEnteredInvoice,jdbcType=CHAR}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.subAttachmentType,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_batch_assess_attachment (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'batch_claim_bill_id'.toString() == column.value">
          #{item.batchClaimBillId,jdbcType=BIGINT}
        </if>
        <if test="'batch_claim_bill_no'.toString() == column.value">
          #{item.batchClaimBillNo,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_type'.toString() == column.value">
          #{item.attachmentType,jdbcType=TINYINT}
        </if>
        <if test="'attachment_name'.toString() == column.value">
          #{item.attachmentName,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_url'.toString() == column.value">
          #{item.attachmentUrl,jdbcType=VARCHAR}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'version'.toString() == column.value">
          #{item.version,jdbcType=INTEGER}
        </if>
        <if test="'is_core_uploaded'.toString() == column.value">
          #{item.isCoreUploaded,jdbcType=CHAR}
        </if>
        <if test="'is_complete'.toString() == column.value">
          #{item.isComplete,jdbcType=CHAR}
        </if>
        <if test="'is_entered_invoice'.toString() == column.value">
          #{item.isEnteredInvoice,jdbcType=CHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'sub_attachment_type'.toString() == column.value">
          #{item.subAttachmentType,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>