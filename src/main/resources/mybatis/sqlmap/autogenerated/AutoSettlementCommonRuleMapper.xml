<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.AutoSettlementCommonRuleMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.AutoSettlementCommonRuleDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="rule_code" jdbcType="VARCHAR" property="ruleCode" />
    <result column="clause_name" jdbcType="VARCHAR" property="clauseName" />
    <result column="clause_code" jdbcType="VARCHAR" property="clauseCode" />
    <result column="rule_logic" jdbcType="VARCHAR" property="ruleLogic" />
    <result column="rule_detail" jdbcType="VARCHAR" property="ruleDetail" />
    <result column="rule_description" jdbcType="VARCHAR" property="ruleDescription" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="out_level" jdbcType="INTEGER" property="outLevel" />
    <result column="total_hits" jdbcType="BIGINT" property="totalHits" />
    <result column="total_misses" jdbcType="BIGINT" property="totalMisses" />
    <result column="total_unknowns" jdbcType="BIGINT" property="totalUnknowns" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, rule_type, rule_name, rule_code, clause_name, clause_code, rule_logic, rule_detail, 
    rule_description, extra_info, remark, is_deleted, gmt_created, gmt_modified, creator, 
    modifier, out_level, total_hits, total_misses, total_unknowns, priority
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementCommonRuleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from auto_settlement_common_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    select
    <if test="example != null and example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from auto_settlement_common_rule
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example != null and example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auto_settlement_common_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.aliasedEscapedColumnName}
        </foreach>
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from auto_settlement_common_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementCommonRuleDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into auto_settlement_common_rule (rule_type, rule_name, rule_code, 
      clause_name, clause_code, rule_logic, 
      rule_detail, rule_description, extra_info, 
      remark, is_deleted, gmt_created, 
      gmt_modified, creator, modifier, 
      out_level, total_hits, total_misses, 
      total_unknowns, priority)
    values (#{ruleType,jdbcType=INTEGER}, #{ruleName,jdbcType=VARCHAR}, #{ruleCode,jdbcType=VARCHAR}, 
      #{clauseName,jdbcType=VARCHAR}, #{clauseCode,jdbcType=VARCHAR}, #{ruleLogic,jdbcType=VARCHAR}, 
      #{ruleDetail,jdbcType=VARCHAR}, #{ruleDescription,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{outLevel,jdbcType=INTEGER}, #{totalHits,jdbcType=BIGINT}, #{totalMisses,jdbcType=BIGINT}, 
      #{totalUnknowns,jdbcType=BIGINT}, #{priority,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementCommonRuleDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into auto_settlement_common_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="ruleName != null">
        rule_name,
      </if>
      <if test="ruleCode != null">
        rule_code,
      </if>
      <if test="clauseName != null">
        clause_name,
      </if>
      <if test="clauseCode != null">
        clause_code,
      </if>
      <if test="ruleLogic != null">
        rule_logic,
      </if>
      <if test="ruleDetail != null">
        rule_detail,
      </if>
      <if test="ruleDescription != null">
        rule_description,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="outLevel != null">
        out_level,
      </if>
      <if test="totalHits != null">
        total_hits,
      </if>
      <if test="totalMisses != null">
        total_misses,
      </if>
      <if test="totalUnknowns != null">
        total_unknowns,
      </if>
      <if test="priority != null">
        priority,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ruleType != null">
        #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="ruleName != null">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleCode != null">
        #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="clauseName != null">
        #{clauseName,jdbcType=VARCHAR},
      </if>
      <if test="clauseCode != null">
        #{clauseCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleLogic != null">
        #{ruleLogic,jdbcType=VARCHAR},
      </if>
      <if test="ruleDetail != null">
        #{ruleDetail,jdbcType=VARCHAR},
      </if>
      <if test="ruleDescription != null">
        #{ruleDescription,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="outLevel != null">
        #{outLevel,jdbcType=INTEGER},
      </if>
      <if test="totalHits != null">
        #{totalHits,jdbcType=BIGINT},
      </if>
      <if test="totalMisses != null">
        #{totalMisses,jdbcType=BIGINT},
      </if>
      <if test="totalUnknowns != null">
        #{totalUnknowns,jdbcType=BIGINT},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementCommonRuleExample" resultType="java.lang.Long">
    select count(*) from auto_settlement_common_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update auto_settlement_common_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ruleType != null">
        rule_type = #{record.ruleType,jdbcType=INTEGER},
      </if>
      <if test="record.ruleName != null">
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleCode != null">
        rule_code = #{record.ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.clauseName != null">
        clause_name = #{record.clauseName,jdbcType=VARCHAR},
      </if>
      <if test="record.clauseCode != null">
        clause_code = #{record.clauseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleLogic != null">
        rule_logic = #{record.ruleLogic,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleDetail != null">
        rule_detail = #{record.ruleDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleDescription != null">
        rule_description = #{record.ruleDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.outLevel != null">
        out_level = #{record.outLevel,jdbcType=INTEGER},
      </if>
      <if test="record.totalHits != null">
        total_hits = #{record.totalHits,jdbcType=BIGINT},
      </if>
      <if test="record.totalMisses != null">
        total_misses = #{record.totalMisses,jdbcType=BIGINT},
      </if>
      <if test="record.totalUnknowns != null">
        total_unknowns = #{record.totalUnknowns,jdbcType=BIGINT},
      </if>
      <if test="record.priority != null">
        priority = #{record.priority,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update auto_settlement_common_rule
    set id = #{record.id,jdbcType=BIGINT},
      rule_type = #{record.ruleType,jdbcType=INTEGER},
      rule_name = #{record.ruleName,jdbcType=VARCHAR},
      rule_code = #{record.ruleCode,jdbcType=VARCHAR},
      clause_name = #{record.clauseName,jdbcType=VARCHAR},
      clause_code = #{record.clauseCode,jdbcType=VARCHAR},
      rule_logic = #{record.ruleLogic,jdbcType=VARCHAR},
      rule_detail = #{record.ruleDetail,jdbcType=VARCHAR},
      rule_description = #{record.ruleDescription,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      out_level = #{record.outLevel,jdbcType=INTEGER},
      total_hits = #{record.totalHits,jdbcType=BIGINT},
      total_misses = #{record.totalMisses,jdbcType=BIGINT},
      total_unknowns = #{record.totalUnknowns,jdbcType=BIGINT},
      priority = #{record.priority,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementCommonRuleDO">
    update auto_settlement_common_rule
    <set>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="ruleName != null">
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleCode != null">
        rule_code = #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="clauseName != null">
        clause_name = #{clauseName,jdbcType=VARCHAR},
      </if>
      <if test="clauseCode != null">
        clause_code = #{clauseCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleLogic != null">
        rule_logic = #{ruleLogic,jdbcType=VARCHAR},
      </if>
      <if test="ruleDetail != null">
        rule_detail = #{ruleDetail,jdbcType=VARCHAR},
      </if>
      <if test="ruleDescription != null">
        rule_description = #{ruleDescription,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="outLevel != null">
        out_level = #{outLevel,jdbcType=INTEGER},
      </if>
      <if test="totalHits != null">
        total_hits = #{totalHits,jdbcType=BIGINT},
      </if>
      <if test="totalMisses != null">
        total_misses = #{totalMisses,jdbcType=BIGINT},
      </if>
      <if test="totalUnknowns != null">
        total_unknowns = #{totalUnknowns,jdbcType=BIGINT},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.AutoSettlementCommonRuleDO">
    update auto_settlement_common_rule
    set rule_type = #{ruleType,jdbcType=INTEGER},
      rule_name = #{ruleName,jdbcType=VARCHAR},
      rule_code = #{ruleCode,jdbcType=VARCHAR},
      clause_name = #{clauseName,jdbcType=VARCHAR},
      clause_code = #{clauseCode,jdbcType=VARCHAR},
      rule_logic = #{ruleLogic,jdbcType=VARCHAR},
      rule_detail = #{ruleDetail,jdbcType=VARCHAR},
      rule_description = #{ruleDescription,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      out_level = #{outLevel,jdbcType=INTEGER},
      total_hits = #{totalHits,jdbcType=BIGINT},
      total_misses = #{totalMisses,jdbcType=BIGINT},
      total_unknowns = #{totalUnknowns,jdbcType=BIGINT},
      priority = #{priority,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into auto_settlement_common_rule
    (rule_type, rule_name, rule_code, clause_name, clause_code, rule_logic, rule_detail, 
      rule_description, extra_info, remark, is_deleted, gmt_created, gmt_modified, creator, 
      modifier, out_level, total_hits, total_misses, total_unknowns, priority)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.ruleType,jdbcType=INTEGER}, #{item.ruleName,jdbcType=VARCHAR}, #{item.ruleCode,jdbcType=VARCHAR}, 
        #{item.clauseName,jdbcType=VARCHAR}, #{item.clauseCode,jdbcType=VARCHAR}, #{item.ruleLogic,jdbcType=VARCHAR}, 
        #{item.ruleDetail,jdbcType=VARCHAR}, #{item.ruleDescription,jdbcType=VARCHAR}, 
        #{item.extraInfo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.outLevel,jdbcType=INTEGER}, 
        #{item.totalHits,jdbcType=BIGINT}, #{item.totalMisses,jdbcType=BIGINT}, #{item.totalUnknowns,jdbcType=BIGINT}, 
        #{item.priority,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into auto_settlement_common_rule (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'rule_type'.toString() == column.value">
          #{item.ruleType,jdbcType=INTEGER}
        </if>
        <if test="'rule_name'.toString() == column.value">
          #{item.ruleName,jdbcType=VARCHAR}
        </if>
        <if test="'rule_code'.toString() == column.value">
          #{item.ruleCode,jdbcType=VARCHAR}
        </if>
        <if test="'clause_name'.toString() == column.value">
          #{item.clauseName,jdbcType=VARCHAR}
        </if>
        <if test="'clause_code'.toString() == column.value">
          #{item.clauseCode,jdbcType=VARCHAR}
        </if>
        <if test="'rule_logic'.toString() == column.value">
          #{item.ruleLogic,jdbcType=VARCHAR}
        </if>
        <if test="'rule_detail'.toString() == column.value">
          #{item.ruleDetail,jdbcType=VARCHAR}
        </if>
        <if test="'rule_description'.toString() == column.value">
          #{item.ruleDescription,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'out_level'.toString() == column.value">
          #{item.outLevel,jdbcType=INTEGER}
        </if>
        <if test="'total_hits'.toString() == column.value">
          #{item.totalHits,jdbcType=BIGINT}
        </if>
        <if test="'total_misses'.toString() == column.value">
          #{item.totalMisses,jdbcType=BIGINT}
        </if>
        <if test="'total_unknowns'.toString() == column.value">
          #{item.totalUnknowns,jdbcType=BIGINT}
        </if>
        <if test="'priority'.toString() == column.value">
          #{item.priority,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>