<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.MdpClaimMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.MdpClaimDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="claim_type" jdbcType="TINYINT" property="claimType" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
    <result column="hospital_mdp_type" jdbcType="VARCHAR" property="hospitalMdpType" />
    <result column="hospital_mdp_tag" jdbcType="VARCHAR" property="hospitalMdpTag" />
    <result column="no_image_hospital" jdbcType="TINYINT" property="noImageHospital" />
    <result column="hospital_addr" jdbcType="VARCHAR" property="hospitalAddr" />
    <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
    <result column="visit_date" jdbcType="DATE" property="visitDate" />
    <result column="hospitalized_date" jdbcType="DATE" property="hospitalizedDate" />
    <result column="leave_hospital_date" jdbcType="DATE" property="leaveHospitalDate" />
    <result column="invoice_list" jdbcType="VARCHAR" property="invoiceList" />
    <result column="dispatched_time" jdbcType="TIMESTAMP" property="dispatchedTime" />
    <result column="ack_time" jdbcType="TIMESTAMP" property="ackTime" />
    <result column="adjustment_time" jdbcType="TIMESTAMP" property="adjustmentTime" />
    <result column="ack_fail_reason" jdbcType="VARCHAR" property="ackFailReason" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_no, report_no, order_no, status, claim_type, hospital_code, hospital_name, 
    hospital_mdp_type, hospital_mdp_tag, no_image_hospital, hospital_addr, visit_type, 
    visit_date, hospitalized_date, leave_hospital_date, invoice_list, dispatched_time, 
    ack_time, adjustment_time, ack_fail_reason, remark, extra_info, creator, modifier, 
    gmt_created, gmt_modified, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.MdpClaimExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mdp_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mdp_claim
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.MdpClaimDO">
    insert into mdp_claim (id, policy_no, report_no, 
      order_no, status, claim_type, 
      hospital_code, hospital_name, hospital_mdp_type, 
      hospital_mdp_tag, no_image_hospital, hospital_addr, 
      visit_type, visit_date, hospitalized_date, 
      leave_hospital_date, invoice_list, dispatched_time, 
      ack_time, adjustment_time, ack_fail_reason, 
      remark, extra_info, creator, 
      modifier, gmt_created, gmt_modified, 
      is_deleted)
    values (#{id,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{claimType,jdbcType=TINYINT}, 
      #{hospitalCode,jdbcType=VARCHAR}, #{hospitalName,jdbcType=VARCHAR}, #{hospitalMdpType,jdbcType=VARCHAR}, 
      #{hospitalMdpTag,jdbcType=VARCHAR}, #{noImageHospital,jdbcType=TINYINT}, #{hospitalAddr,jdbcType=VARCHAR}, 
      #{visitType,jdbcType=VARCHAR}, #{visitDate,jdbcType=DATE}, #{hospitalizedDate,jdbcType=DATE}, 
      #{leaveHospitalDate,jdbcType=DATE}, #{invoiceList,jdbcType=VARCHAR}, #{dispatchedTime,jdbcType=TIMESTAMP}, 
      #{ackTime,jdbcType=TIMESTAMP}, #{adjustmentTime,jdbcType=TIMESTAMP}, #{ackFailReason,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{extraInfo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate(), 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.MdpClaimDO">
    insert into mdp_claim
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="claimType != null">
        claim_type,
      </if>
      <if test="hospitalCode != null">
        hospital_code,
      </if>
      <if test="hospitalName != null">
        hospital_name,
      </if>
      <if test="hospitalMdpType != null">
        hospital_mdp_type,
      </if>
      <if test="hospitalMdpTag != null">
        hospital_mdp_tag,
      </if>
      <if test="noImageHospital != null">
        no_image_hospital,
      </if>
      <if test="hospitalAddr != null">
        hospital_addr,
      </if>
      <if test="visitType != null">
        visit_type,
      </if>
      <if test="visitDate != null">
        visit_date,
      </if>
      <if test="hospitalizedDate != null">
        hospitalized_date,
      </if>
      <if test="leaveHospitalDate != null">
        leave_hospital_date,
      </if>
      <if test="invoiceList != null">
        invoice_list,
      </if>
      <if test="dispatchedTime != null">
        dispatched_time,
      </if>
      <if test="ackTime != null">
        ack_time,
      </if>
      <if test="adjustmentTime != null">
        adjustment_time,
      </if>
      <if test="ackFailReason != null">
        ack_fail_reason,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="claimType != null">
        #{claimType,jdbcType=TINYINT},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalMdpType != null">
        #{hospitalMdpType,jdbcType=VARCHAR},
      </if>
      <if test="hospitalMdpTag != null">
        #{hospitalMdpTag,jdbcType=VARCHAR},
      </if>
      <if test="noImageHospital != null">
        #{noImageHospital,jdbcType=TINYINT},
      </if>
      <if test="hospitalAddr != null">
        #{hospitalAddr,jdbcType=VARCHAR},
      </if>
      <if test="visitType != null">
        #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="visitDate != null">
        #{visitDate,jdbcType=DATE},
      </if>
      <if test="hospitalizedDate != null">
        #{hospitalizedDate,jdbcType=DATE},
      </if>
      <if test="leaveHospitalDate != null">
        #{leaveHospitalDate,jdbcType=DATE},
      </if>
      <if test="invoiceList != null">
        #{invoiceList,jdbcType=VARCHAR},
      </if>
      <if test="dispatchedTime != null">
        #{dispatchedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ackTime != null">
        #{ackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustmentTime != null">
        #{adjustmentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ackFailReason != null">
        #{ackFailReason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.MdpClaimExample" resultType="java.lang.Long">
    select count(*) from mdp_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mdp_claim
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.claimType != null">
        claim_type = #{record.claimType,jdbcType=TINYINT},
      </if>
      <if test="record.hospitalCode != null">
        hospital_code = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalName != null">
        hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalMdpType != null">
        hospital_mdp_type = #{record.hospitalMdpType,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalMdpTag != null">
        hospital_mdp_tag = #{record.hospitalMdpTag,jdbcType=VARCHAR},
      </if>
      <if test="record.noImageHospital != null">
        no_image_hospital = #{record.noImageHospital,jdbcType=TINYINT},
      </if>
      <if test="record.hospitalAddr != null">
        hospital_addr = #{record.hospitalAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.visitType != null">
        visit_type = #{record.visitType,jdbcType=VARCHAR},
      </if>
      <if test="record.visitDate != null">
        visit_date = #{record.visitDate,jdbcType=DATE},
      </if>
      <if test="record.hospitalizedDate != null">
        hospitalized_date = #{record.hospitalizedDate,jdbcType=DATE},
      </if>
      <if test="record.leaveHospitalDate != null">
        leave_hospital_date = #{record.leaveHospitalDate,jdbcType=DATE},
      </if>
      <if test="record.invoiceList != null">
        invoice_list = #{record.invoiceList,jdbcType=VARCHAR},
      </if>
      <if test="record.dispatchedTime != null">
        dispatched_time = #{record.dispatchedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ackTime != null">
        ack_time = #{record.ackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.adjustmentTime != null">
        adjustment_time = #{record.adjustmentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ackFailReason != null">
        ack_fail_reason = #{record.ackFailReason,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.extraInfo != null">
        extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mdp_claim
    set id = #{record.id,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      claim_type = #{record.claimType,jdbcType=TINYINT},
      hospital_code = #{record.hospitalCode,jdbcType=VARCHAR},
      hospital_name = #{record.hospitalName,jdbcType=VARCHAR},
      hospital_mdp_type = #{record.hospitalMdpType,jdbcType=VARCHAR},
      hospital_mdp_tag = #{record.hospitalMdpTag,jdbcType=VARCHAR},
      no_image_hospital = #{record.noImageHospital,jdbcType=TINYINT},
      hospital_addr = #{record.hospitalAddr,jdbcType=VARCHAR},
      visit_type = #{record.visitType,jdbcType=VARCHAR},
      visit_date = #{record.visitDate,jdbcType=DATE},
      hospitalized_date = #{record.hospitalizedDate,jdbcType=DATE},
      leave_hospital_date = #{record.leaveHospitalDate,jdbcType=DATE},
      invoice_list = #{record.invoiceList,jdbcType=VARCHAR},
      dispatched_time = #{record.dispatchedTime,jdbcType=TIMESTAMP},
      ack_time = #{record.ackTime,jdbcType=TIMESTAMP},
      adjustment_time = #{record.adjustmentTime,jdbcType=TIMESTAMP},
      ack_fail_reason = #{record.ackFailReason,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      extra_info = #{record.extraInfo,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.MdpClaimDO">
    update mdp_claim
    <set>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="claimType != null">
        claim_type = #{claimType,jdbcType=TINYINT},
      </if>
      <if test="hospitalCode != null">
        hospital_code = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="hospitalName != null">
        hospital_name = #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="hospitalMdpType != null">
        hospital_mdp_type = #{hospitalMdpType,jdbcType=VARCHAR},
      </if>
      <if test="hospitalMdpTag != null">
        hospital_mdp_tag = #{hospitalMdpTag,jdbcType=VARCHAR},
      </if>
      <if test="noImageHospital != null">
        no_image_hospital = #{noImageHospital,jdbcType=TINYINT},
      </if>
      <if test="hospitalAddr != null">
        hospital_addr = #{hospitalAddr,jdbcType=VARCHAR},
      </if>
      <if test="visitType != null">
        visit_type = #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="visitDate != null">
        visit_date = #{visitDate,jdbcType=DATE},
      </if>
      <if test="hospitalizedDate != null">
        hospitalized_date = #{hospitalizedDate,jdbcType=DATE},
      </if>
      <if test="leaveHospitalDate != null">
        leave_hospital_date = #{leaveHospitalDate,jdbcType=DATE},
      </if>
      <if test="invoiceList != null">
        invoice_list = #{invoiceList,jdbcType=VARCHAR},
      </if>
      <if test="dispatchedTime != null">
        dispatched_time = #{dispatchedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ackTime != null">
        ack_time = #{ackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustmentTime != null">
        adjustment_time = #{adjustmentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ackFailReason != null">
        ack_fail_reason = #{ackFailReason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.MdpClaimDO">
    update mdp_claim
    set policy_no = #{policyNo,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      claim_type = #{claimType,jdbcType=TINYINT},
      hospital_code = #{hospitalCode,jdbcType=VARCHAR},
      hospital_name = #{hospitalName,jdbcType=VARCHAR},
      hospital_mdp_type = #{hospitalMdpType,jdbcType=VARCHAR},
      hospital_mdp_tag = #{hospitalMdpTag,jdbcType=VARCHAR},
      no_image_hospital = #{noImageHospital,jdbcType=TINYINT},
      hospital_addr = #{hospitalAddr,jdbcType=VARCHAR},
      visit_type = #{visitType,jdbcType=VARCHAR},
      visit_date = #{visitDate,jdbcType=DATE},
      hospitalized_date = #{hospitalizedDate,jdbcType=DATE},
      leave_hospital_date = #{leaveHospitalDate,jdbcType=DATE},
      invoice_list = #{invoiceList,jdbcType=VARCHAR},
      dispatched_time = #{dispatchedTime,jdbcType=TIMESTAMP},
      ack_time = #{ackTime,jdbcType=TIMESTAMP},
      adjustment_time = #{adjustmentTime,jdbcType=TIMESTAMP},
      ack_fail_reason = #{ackFailReason,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      extra_info = #{extraInfo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into mdp_claim
    (id, policy_no, report_no, order_no, status, claim_type, hospital_code, hospital_name, 
      hospital_mdp_type, hospital_mdp_tag, no_image_hospital, hospital_addr, visit_type, 
      visit_date, hospitalized_date, leave_hospital_date, invoice_list, dispatched_time, 
      ack_time, adjustment_time, ack_fail_reason, remark, extra_info, creator, modifier, 
      gmt_created, gmt_modified, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, 
        #{item.orderNo,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, #{item.claimType,jdbcType=TINYINT}, 
        #{item.hospitalCode,jdbcType=VARCHAR}, #{item.hospitalName,jdbcType=VARCHAR}, #{item.hospitalMdpType,jdbcType=VARCHAR}, 
        #{item.hospitalMdpTag,jdbcType=VARCHAR}, #{item.noImageHospital,jdbcType=TINYINT}, 
        #{item.hospitalAddr,jdbcType=VARCHAR}, #{item.visitType,jdbcType=VARCHAR}, #{item.visitDate,jdbcType=DATE}, 
        #{item.hospitalizedDate,jdbcType=DATE}, #{item.leaveHospitalDate,jdbcType=DATE}, 
        #{item.invoiceList,jdbcType=VARCHAR}, #{item.dispatchedTime,jdbcType=TIMESTAMP}, 
        #{item.ackTime,jdbcType=TIMESTAMP}, #{item.adjustmentTime,jdbcType=TIMESTAMP}, 
        #{item.ackFailReason,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into mdp_claim (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'order_no'.toString() == column.value">
          #{item.orderNo,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=VARCHAR}
        </if>
        <if test="'claim_type'.toString() == column.value">
          #{item.claimType,jdbcType=TINYINT}
        </if>
        <if test="'hospital_code'.toString() == column.value">
          #{item.hospitalCode,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_name'.toString() == column.value">
          #{item.hospitalName,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_mdp_type'.toString() == column.value">
          #{item.hospitalMdpType,jdbcType=VARCHAR}
        </if>
        <if test="'hospital_mdp_tag'.toString() == column.value">
          #{item.hospitalMdpTag,jdbcType=VARCHAR}
        </if>
        <if test="'no_image_hospital'.toString() == column.value">
          #{item.noImageHospital,jdbcType=TINYINT}
        </if>
        <if test="'hospital_addr'.toString() == column.value">
          #{item.hospitalAddr,jdbcType=VARCHAR}
        </if>
        <if test="'visit_type'.toString() == column.value">
          #{item.visitType,jdbcType=VARCHAR}
        </if>
        <if test="'visit_date'.toString() == column.value">
          #{item.visitDate,jdbcType=DATE}
        </if>
        <if test="'hospitalized_date'.toString() == column.value">
          #{item.hospitalizedDate,jdbcType=DATE}
        </if>
        <if test="'leave_hospital_date'.toString() == column.value">
          #{item.leaveHospitalDate,jdbcType=DATE}
        </if>
        <if test="'invoice_list'.toString() == column.value">
          #{item.invoiceList,jdbcType=VARCHAR}
        </if>
        <if test="'dispatched_time'.toString() == column.value">
          #{item.dispatchedTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'ack_time'.toString() == column.value">
          #{item.ackTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'adjustment_time'.toString() == column.value">
          #{item.adjustmentTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'ack_fail_reason'.toString() == column.value">
          #{item.ackFailReason,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'extra_info'.toString() == column.value">
          #{item.extraInfo,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>