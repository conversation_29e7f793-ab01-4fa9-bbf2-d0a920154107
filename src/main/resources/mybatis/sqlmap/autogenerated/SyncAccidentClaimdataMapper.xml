<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.SyncAccidentClaimdataMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.SyncAccidentClaimdataDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="product_name_type" jdbcType="VARCHAR" property="productNameType" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="claimant_phone" jdbcType="VARCHAR" property="claimantPhone" />
    <result column="policy_holder_user_name" jdbcType="VARCHAR" property="policyHolderUserName" />
    <result column="policy_holder_certi_no" jdbcType="VARCHAR" property="policyHolderCertiNo" />
    <result column="insurant_user_name" jdbcType="VARCHAR" property="insurantUserName" />
    <result column="insurant_certi_no" jdbcType="VARCHAR" property="insurantCertiNo" />
    <result column="insurant_phone_no" jdbcType="VARCHAR" property="insurantPhoneNo" />
    <result column="policy_holder_phone_no" jdbcType="VARCHAR" property="policyHolderPhoneNo" />
    <result column="campaign_full_name" jdbcType="VARCHAR" property="campaignFullName" />
    <result column="package_full_name" jdbcType="VARCHAR" property="packageFullName" />
    <result column="accident_date" jdbcType="VARCHAR" property="accidentDate" />
    <result column="accident_place" jdbcType="VARCHAR" property="accidentPlace" />
    <result column="report_date" jdbcType="VARCHAR" property="reportDate" />
    <result column="settled_claim_amt_set" jdbcType="VARCHAR" property="settledClaimAmtSet" />
    <result column="loss_cause_type" jdbcType="VARCHAR" property="lossCauseType" />
    <result column="accidentprocess" jdbcType="VARCHAR" property="accidentprocess" />
    <result column="visit_hospital" jdbcType="VARCHAR" property="visitHospital" />
    <result column="bill_number" jdbcType="VARCHAR" property="billNumber" />
    <result column="insurant_city" jdbcType="VARCHAR" property="insurantCity" />
    <result column="payee_account_no" jdbcType="VARCHAR" property="payeeAccountNo" />
    <result column="payee_name" jdbcType="VARCHAR" property="payeeName" />
    <result column="black_type" jdbcType="VARCHAR" property="blackType" />
    <result column="community_id" jdbcType="VARCHAR" property="communityId" />
    <result column="count_certno" jdbcType="VARCHAR" property="countCertno" />
    <result column="count_phoneno" jdbcType="VARCHAR" property="countPhoneno" />
    <result column="count_reportno" jdbcType="VARCHAR" property="countReportno" />
    <result column="id_cnt" jdbcType="VARCHAR" property="idCnt" />
    <result column="claim_phone_cnt" jdbcType="BIGINT" property="claimPhoneCnt" />
    <result column="ph_phone_cnt" jdbcType="BIGINT" property="phPhoneCnt" />
    <result column="ph_cert_cnt" jdbcType="BIGINT" property="phCertCnt" />
    <result column="pi_cert_cnt" jdbcType="BIGINT" property="piCertCnt" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_no, product_name_type, report_no, claimant_phone, policy_holder_user_name, 
    policy_holder_certi_no, insurant_user_name, insurant_certi_no, insurant_phone_no, 
    policy_holder_phone_no, campaign_full_name, package_full_name, accident_date, accident_place, 
    report_date, settled_claim_amt_set, loss_cause_type, accidentprocess, visit_hospital, 
    bill_number, insurant_city, payee_account_no, payee_name, black_type, community_id, 
    count_certno, count_phoneno, count_reportno, id_cnt, claim_phone_cnt, ph_phone_cnt, 
    ph_cert_cnt, pi_cert_cnt, gmt_created, gmt_modified, creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.SyncAccidentClaimdataExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sync_accident_claimdata
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sync_accident_claimdata
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.SyncAccidentClaimdataDO">
    insert into sync_accident_claimdata (id, policy_no, product_name_type, 
      report_no, claimant_phone, policy_holder_user_name, 
      policy_holder_certi_no, insurant_user_name, 
      insurant_certi_no, insurant_phone_no, policy_holder_phone_no, 
      campaign_full_name, package_full_name, accident_date, 
      accident_place, report_date, settled_claim_amt_set, 
      loss_cause_type, accidentprocess, visit_hospital, 
      bill_number, insurant_city, payee_account_no, 
      payee_name, black_type, community_id, 
      count_certno, count_phoneno, count_reportno, 
      id_cnt, claim_phone_cnt, ph_phone_cnt, 
      ph_cert_cnt, pi_cert_cnt, gmt_created, 
      gmt_modified, creator, modifier, 
      is_deleted)
    values (#{id,jdbcType=BIGINT}, #{policyNo,jdbcType=VARCHAR}, #{productNameType,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{claimantPhone,jdbcType=VARCHAR}, #{policyHolderUserName,jdbcType=VARCHAR}, 
      #{policyHolderCertiNo,jdbcType=VARCHAR}, #{insurantUserName,jdbcType=VARCHAR}, 
      #{insurantCertiNo,jdbcType=VARCHAR}, #{insurantPhoneNo,jdbcType=VARCHAR}, #{policyHolderPhoneNo,jdbcType=VARCHAR}, 
      #{campaignFullName,jdbcType=VARCHAR}, #{packageFullName,jdbcType=VARCHAR}, #{accidentDate,jdbcType=VARCHAR}, 
      #{accidentPlace,jdbcType=VARCHAR}, #{reportDate,jdbcType=VARCHAR}, #{settledClaimAmtSet,jdbcType=VARCHAR}, 
      #{lossCauseType,jdbcType=VARCHAR}, #{accidentprocess,jdbcType=VARCHAR}, #{visitHospital,jdbcType=VARCHAR}, 
      #{billNumber,jdbcType=VARCHAR}, #{insurantCity,jdbcType=VARCHAR}, #{payeeAccountNo,jdbcType=VARCHAR}, 
      #{payeeName,jdbcType=VARCHAR}, #{blackType,jdbcType=VARCHAR}, #{communityId,jdbcType=VARCHAR}, 
      #{countCertno,jdbcType=VARCHAR}, #{countPhoneno,jdbcType=VARCHAR}, #{countReportno,jdbcType=VARCHAR}, 
      #{idCnt,jdbcType=VARCHAR}, #{claimPhoneCnt,jdbcType=BIGINT}, #{phPhoneCnt,jdbcType=BIGINT}, 
      #{phCertCnt,jdbcType=BIGINT}, #{piCertCnt,jdbcType=BIGINT}, sysdate(), 
      sysdate(), #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.SyncAccidentClaimdataDO">
    insert into sync_accident_claimdata
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="productNameType != null">
        product_name_type,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="claimantPhone != null">
        claimant_phone,
      </if>
      <if test="policyHolderUserName != null">
        policy_holder_user_name,
      </if>
      <if test="policyHolderCertiNo != null">
        policy_holder_certi_no,
      </if>
      <if test="insurantUserName != null">
        insurant_user_name,
      </if>
      <if test="insurantCertiNo != null">
        insurant_certi_no,
      </if>
      <if test="insurantPhoneNo != null">
        insurant_phone_no,
      </if>
      <if test="policyHolderPhoneNo != null">
        policy_holder_phone_no,
      </if>
      <if test="campaignFullName != null">
        campaign_full_name,
      </if>
      <if test="packageFullName != null">
        package_full_name,
      </if>
      <if test="accidentDate != null">
        accident_date,
      </if>
      <if test="accidentPlace != null">
        accident_place,
      </if>
      <if test="reportDate != null">
        report_date,
      </if>
      <if test="settledClaimAmtSet != null">
        settled_claim_amt_set,
      </if>
      <if test="lossCauseType != null">
        loss_cause_type,
      </if>
      <if test="accidentprocess != null">
        accidentprocess,
      </if>
      <if test="visitHospital != null">
        visit_hospital,
      </if>
      <if test="billNumber != null">
        bill_number,
      </if>
      <if test="insurantCity != null">
        insurant_city,
      </if>
      <if test="payeeAccountNo != null">
        payee_account_no,
      </if>
      <if test="payeeName != null">
        payee_name,
      </if>
      <if test="blackType != null">
        black_type,
      </if>
      <if test="communityId != null">
        community_id,
      </if>
      <if test="countCertno != null">
        count_certno,
      </if>
      <if test="countPhoneno != null">
        count_phoneno,
      </if>
      <if test="countReportno != null">
        count_reportno,
      </if>
      <if test="idCnt != null">
        id_cnt,
      </if>
      <if test="claimPhoneCnt != null">
        claim_phone_cnt,
      </if>
      <if test="phPhoneCnt != null">
        ph_phone_cnt,
      </if>
      <if test="phCertCnt != null">
        ph_cert_cnt,
      </if>
      <if test="piCertCnt != null">
        pi_cert_cnt,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="productNameType != null">
        #{productNameType,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimantPhone != null">
        #{claimantPhone,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderUserName != null">
        #{policyHolderUserName,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderCertiNo != null">
        #{policyHolderCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantUserName != null">
        #{insurantUserName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertiNo != null">
        #{insurantCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantPhoneNo != null">
        #{insurantPhoneNo,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderPhoneNo != null">
        #{policyHolderPhoneNo,jdbcType=VARCHAR},
      </if>
      <if test="campaignFullName != null">
        #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="packageFullName != null">
        #{packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        #{accidentDate,jdbcType=VARCHAR},
      </if>
      <if test="accidentPlace != null">
        #{accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        #{reportDate,jdbcType=VARCHAR},
      </if>
      <if test="settledClaimAmtSet != null">
        #{settledClaimAmtSet,jdbcType=VARCHAR},
      </if>
      <if test="lossCauseType != null">
        #{lossCauseType,jdbcType=VARCHAR},
      </if>
      <if test="accidentprocess != null">
        #{accidentprocess,jdbcType=VARCHAR},
      </if>
      <if test="visitHospital != null">
        #{visitHospital,jdbcType=VARCHAR},
      </if>
      <if test="billNumber != null">
        #{billNumber,jdbcType=VARCHAR},
      </if>
      <if test="insurantCity != null">
        #{insurantCity,jdbcType=VARCHAR},
      </if>
      <if test="payeeAccountNo != null">
        #{payeeAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="blackType != null">
        #{blackType,jdbcType=VARCHAR},
      </if>
      <if test="communityId != null">
        #{communityId,jdbcType=VARCHAR},
      </if>
      <if test="countCertno != null">
        #{countCertno,jdbcType=VARCHAR},
      </if>
      <if test="countPhoneno != null">
        #{countPhoneno,jdbcType=VARCHAR},
      </if>
      <if test="countReportno != null">
        #{countReportno,jdbcType=VARCHAR},
      </if>
      <if test="idCnt != null">
        #{idCnt,jdbcType=VARCHAR},
      </if>
      <if test="claimPhoneCnt != null">
        #{claimPhoneCnt,jdbcType=BIGINT},
      </if>
      <if test="phPhoneCnt != null">
        #{phPhoneCnt,jdbcType=BIGINT},
      </if>
      <if test="phCertCnt != null">
        #{phCertCnt,jdbcType=BIGINT},
      </if>
      <if test="piCertCnt != null">
        #{piCertCnt,jdbcType=BIGINT},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.SyncAccidentClaimdataExample" resultType="java.lang.Long">
    select count(*) from sync_accident_claimdata
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sync_accident_claimdata
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.productNameType != null">
        product_name_type = #{record.productNameType,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimantPhone != null">
        claimant_phone = #{record.claimantPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.policyHolderUserName != null">
        policy_holder_user_name = #{record.policyHolderUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.policyHolderCertiNo != null">
        policy_holder_certi_no = #{record.policyHolderCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantUserName != null">
        insurant_user_name = #{record.insurantUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCertiNo != null">
        insurant_certi_no = #{record.insurantCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantPhoneNo != null">
        insurant_phone_no = #{record.insurantPhoneNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyHolderPhoneNo != null">
        policy_holder_phone_no = #{record.policyHolderPhoneNo,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignFullName != null">
        campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageFullName != null">
        package_full_name = #{record.packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentDate != null">
        accident_date = #{record.accidentDate,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentPlace != null">
        accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="record.reportDate != null">
        report_date = #{record.reportDate,jdbcType=VARCHAR},
      </if>
      <if test="record.settledClaimAmtSet != null">
        settled_claim_amt_set = #{record.settledClaimAmtSet,jdbcType=VARCHAR},
      </if>
      <if test="record.lossCauseType != null">
        loss_cause_type = #{record.lossCauseType,jdbcType=VARCHAR},
      </if>
      <if test="record.accidentprocess != null">
        accidentprocess = #{record.accidentprocess,jdbcType=VARCHAR},
      </if>
      <if test="record.visitHospital != null">
        visit_hospital = #{record.visitHospital,jdbcType=VARCHAR},
      </if>
      <if test="record.billNumber != null">
        bill_number = #{record.billNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.insurantCity != null">
        insurant_city = #{record.insurantCity,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeAccountNo != null">
        payee_account_no = #{record.payeeAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.payeeName != null">
        payee_name = #{record.payeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.blackType != null">
        black_type = #{record.blackType,jdbcType=VARCHAR},
      </if>
      <if test="record.communityId != null">
        community_id = #{record.communityId,jdbcType=VARCHAR},
      </if>
      <if test="record.countCertno != null">
        count_certno = #{record.countCertno,jdbcType=VARCHAR},
      </if>
      <if test="record.countPhoneno != null">
        count_phoneno = #{record.countPhoneno,jdbcType=VARCHAR},
      </if>
      <if test="record.countReportno != null">
        count_reportno = #{record.countReportno,jdbcType=VARCHAR},
      </if>
      <if test="record.idCnt != null">
        id_cnt = #{record.idCnt,jdbcType=VARCHAR},
      </if>
      <if test="record.claimPhoneCnt != null">
        claim_phone_cnt = #{record.claimPhoneCnt,jdbcType=BIGINT},
      </if>
      <if test="record.phPhoneCnt != null">
        ph_phone_cnt = #{record.phPhoneCnt,jdbcType=BIGINT},
      </if>
      <if test="record.phCertCnt != null">
        ph_cert_cnt = #{record.phCertCnt,jdbcType=BIGINT},
      </if>
      <if test="record.piCertCnt != null">
        pi_cert_cnt = #{record.piCertCnt,jdbcType=BIGINT},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sync_accident_claimdata
    set id = #{record.id,jdbcType=BIGINT},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      product_name_type = #{record.productNameType,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      claimant_phone = #{record.claimantPhone,jdbcType=VARCHAR},
      policy_holder_user_name = #{record.policyHolderUserName,jdbcType=VARCHAR},
      policy_holder_certi_no = #{record.policyHolderCertiNo,jdbcType=VARCHAR},
      insurant_user_name = #{record.insurantUserName,jdbcType=VARCHAR},
      insurant_certi_no = #{record.insurantCertiNo,jdbcType=VARCHAR},
      insurant_phone_no = #{record.insurantPhoneNo,jdbcType=VARCHAR},
      policy_holder_phone_no = #{record.policyHolderPhoneNo,jdbcType=VARCHAR},
      campaign_full_name = #{record.campaignFullName,jdbcType=VARCHAR},
      package_full_name = #{record.packageFullName,jdbcType=VARCHAR},
      accident_date = #{record.accidentDate,jdbcType=VARCHAR},
      accident_place = #{record.accidentPlace,jdbcType=VARCHAR},
      report_date = #{record.reportDate,jdbcType=VARCHAR},
      settled_claim_amt_set = #{record.settledClaimAmtSet,jdbcType=VARCHAR},
      loss_cause_type = #{record.lossCauseType,jdbcType=VARCHAR},
      accidentprocess = #{record.accidentprocess,jdbcType=VARCHAR},
      visit_hospital = #{record.visitHospital,jdbcType=VARCHAR},
      bill_number = #{record.billNumber,jdbcType=VARCHAR},
      insurant_city = #{record.insurantCity,jdbcType=VARCHAR},
      payee_account_no = #{record.payeeAccountNo,jdbcType=VARCHAR},
      payee_name = #{record.payeeName,jdbcType=VARCHAR},
      black_type = #{record.blackType,jdbcType=VARCHAR},
      community_id = #{record.communityId,jdbcType=VARCHAR},
      count_certno = #{record.countCertno,jdbcType=VARCHAR},
      count_phoneno = #{record.countPhoneno,jdbcType=VARCHAR},
      count_reportno = #{record.countReportno,jdbcType=VARCHAR},
      id_cnt = #{record.idCnt,jdbcType=VARCHAR},
      claim_phone_cnt = #{record.claimPhoneCnt,jdbcType=BIGINT},
      ph_phone_cnt = #{record.phPhoneCnt,jdbcType=BIGINT},
      ph_cert_cnt = #{record.phCertCnt,jdbcType=BIGINT},
      pi_cert_cnt = #{record.piCertCnt,jdbcType=BIGINT},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.SyncAccidentClaimdataDO">
    update sync_accident_claimdata
    <set>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="productNameType != null">
        product_name_type = #{productNameType,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimantPhone != null">
        claimant_phone = #{claimantPhone,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderUserName != null">
        policy_holder_user_name = #{policyHolderUserName,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderCertiNo != null">
        policy_holder_certi_no = #{policyHolderCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantUserName != null">
        insurant_user_name = #{insurantUserName,jdbcType=VARCHAR},
      </if>
      <if test="insurantCertiNo != null">
        insurant_certi_no = #{insurantCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="insurantPhoneNo != null">
        insurant_phone_no = #{insurantPhoneNo,jdbcType=VARCHAR},
      </if>
      <if test="policyHolderPhoneNo != null">
        policy_holder_phone_no = #{policyHolderPhoneNo,jdbcType=VARCHAR},
      </if>
      <if test="campaignFullName != null">
        campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      </if>
      <if test="packageFullName != null">
        package_full_name = #{packageFullName,jdbcType=VARCHAR},
      </if>
      <if test="accidentDate != null">
        accident_date = #{accidentDate,jdbcType=VARCHAR},
      </if>
      <if test="accidentPlace != null">
        accident_place = #{accidentPlace,jdbcType=VARCHAR},
      </if>
      <if test="reportDate != null">
        report_date = #{reportDate,jdbcType=VARCHAR},
      </if>
      <if test="settledClaimAmtSet != null">
        settled_claim_amt_set = #{settledClaimAmtSet,jdbcType=VARCHAR},
      </if>
      <if test="lossCauseType != null">
        loss_cause_type = #{lossCauseType,jdbcType=VARCHAR},
      </if>
      <if test="accidentprocess != null">
        accidentprocess = #{accidentprocess,jdbcType=VARCHAR},
      </if>
      <if test="visitHospital != null">
        visit_hospital = #{visitHospital,jdbcType=VARCHAR},
      </if>
      <if test="billNumber != null">
        bill_number = #{billNumber,jdbcType=VARCHAR},
      </if>
      <if test="insurantCity != null">
        insurant_city = #{insurantCity,jdbcType=VARCHAR},
      </if>
      <if test="payeeAccountNo != null">
        payee_account_no = #{payeeAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="payeeName != null">
        payee_name = #{payeeName,jdbcType=VARCHAR},
      </if>
      <if test="blackType != null">
        black_type = #{blackType,jdbcType=VARCHAR},
      </if>
      <if test="communityId != null">
        community_id = #{communityId,jdbcType=VARCHAR},
      </if>
      <if test="countCertno != null">
        count_certno = #{countCertno,jdbcType=VARCHAR},
      </if>
      <if test="countPhoneno != null">
        count_phoneno = #{countPhoneno,jdbcType=VARCHAR},
      </if>
      <if test="countReportno != null">
        count_reportno = #{countReportno,jdbcType=VARCHAR},
      </if>
      <if test="idCnt != null">
        id_cnt = #{idCnt,jdbcType=VARCHAR},
      </if>
      <if test="claimPhoneCnt != null">
        claim_phone_cnt = #{claimPhoneCnt,jdbcType=BIGINT},
      </if>
      <if test="phPhoneCnt != null">
        ph_phone_cnt = #{phPhoneCnt,jdbcType=BIGINT},
      </if>
      <if test="phCertCnt != null">
        ph_cert_cnt = #{phCertCnt,jdbcType=BIGINT},
      </if>
      <if test="piCertCnt != null">
        pi_cert_cnt = #{piCertCnt,jdbcType=BIGINT},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.SyncAccidentClaimdataDO">
    update sync_accident_claimdata
    set policy_no = #{policyNo,jdbcType=VARCHAR},
      product_name_type = #{productNameType,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      claimant_phone = #{claimantPhone,jdbcType=VARCHAR},
      policy_holder_user_name = #{policyHolderUserName,jdbcType=VARCHAR},
      policy_holder_certi_no = #{policyHolderCertiNo,jdbcType=VARCHAR},
      insurant_user_name = #{insurantUserName,jdbcType=VARCHAR},
      insurant_certi_no = #{insurantCertiNo,jdbcType=VARCHAR},
      insurant_phone_no = #{insurantPhoneNo,jdbcType=VARCHAR},
      policy_holder_phone_no = #{policyHolderPhoneNo,jdbcType=VARCHAR},
      campaign_full_name = #{campaignFullName,jdbcType=VARCHAR},
      package_full_name = #{packageFullName,jdbcType=VARCHAR},
      accident_date = #{accidentDate,jdbcType=VARCHAR},
      accident_place = #{accidentPlace,jdbcType=VARCHAR},
      report_date = #{reportDate,jdbcType=VARCHAR},
      settled_claim_amt_set = #{settledClaimAmtSet,jdbcType=VARCHAR},
      loss_cause_type = #{lossCauseType,jdbcType=VARCHAR},
      accidentprocess = #{accidentprocess,jdbcType=VARCHAR},
      visit_hospital = #{visitHospital,jdbcType=VARCHAR},
      bill_number = #{billNumber,jdbcType=VARCHAR},
      insurant_city = #{insurantCity,jdbcType=VARCHAR},
      payee_account_no = #{payeeAccountNo,jdbcType=VARCHAR},
      payee_name = #{payeeName,jdbcType=VARCHAR},
      black_type = #{blackType,jdbcType=VARCHAR},
      community_id = #{communityId,jdbcType=VARCHAR},
      count_certno = #{countCertno,jdbcType=VARCHAR},
      count_phoneno = #{countPhoneno,jdbcType=VARCHAR},
      count_reportno = #{countReportno,jdbcType=VARCHAR},
      id_cnt = #{idCnt,jdbcType=VARCHAR},
      claim_phone_cnt = #{claimPhoneCnt,jdbcType=BIGINT},
      ph_phone_cnt = #{phPhoneCnt,jdbcType=BIGINT},
      ph_cert_cnt = #{phCertCnt,jdbcType=BIGINT},
      pi_cert_cnt = #{piCertCnt,jdbcType=BIGINT},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into sync_accident_claimdata
    (id, policy_no, product_name_type, report_no, claimant_phone, policy_holder_user_name, 
      policy_holder_certi_no, insurant_user_name, insurant_certi_no, insurant_phone_no, 
      policy_holder_phone_no, campaign_full_name, package_full_name, accident_date, accident_place, 
      report_date, settled_claim_amt_set, loss_cause_type, accidentprocess, visit_hospital, 
      bill_number, insurant_city, payee_account_no, payee_name, black_type, community_id, 
      count_certno, count_phoneno, count_reportno, id_cnt, claim_phone_cnt, ph_phone_cnt, 
      ph_cert_cnt, pi_cert_cnt, gmt_created, gmt_modified, creator, modifier, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.policyNo,jdbcType=VARCHAR}, #{item.productNameType,jdbcType=VARCHAR}, 
        #{item.reportNo,jdbcType=VARCHAR}, #{item.claimantPhone,jdbcType=VARCHAR}, #{item.policyHolderUserName,jdbcType=VARCHAR}, 
        #{item.policyHolderCertiNo,jdbcType=VARCHAR}, #{item.insurantUserName,jdbcType=VARCHAR}, 
        #{item.insurantCertiNo,jdbcType=VARCHAR}, #{item.insurantPhoneNo,jdbcType=VARCHAR}, 
        #{item.policyHolderPhoneNo,jdbcType=VARCHAR}, #{item.campaignFullName,jdbcType=VARCHAR}, 
        #{item.packageFullName,jdbcType=VARCHAR}, #{item.accidentDate,jdbcType=VARCHAR}, 
        #{item.accidentPlace,jdbcType=VARCHAR}, #{item.reportDate,jdbcType=VARCHAR}, #{item.settledClaimAmtSet,jdbcType=VARCHAR}, 
        #{item.lossCauseType,jdbcType=VARCHAR}, #{item.accidentprocess,jdbcType=VARCHAR}, 
        #{item.visitHospital,jdbcType=VARCHAR}, #{item.billNumber,jdbcType=VARCHAR}, #{item.insurantCity,jdbcType=VARCHAR}, 
        #{item.payeeAccountNo,jdbcType=VARCHAR}, #{item.payeeName,jdbcType=VARCHAR}, #{item.blackType,jdbcType=VARCHAR}, 
        #{item.communityId,jdbcType=VARCHAR}, #{item.countCertno,jdbcType=VARCHAR}, #{item.countPhoneno,jdbcType=VARCHAR}, 
        #{item.countReportno,jdbcType=VARCHAR}, #{item.idCnt,jdbcType=VARCHAR}, #{item.claimPhoneCnt,jdbcType=BIGINT}, 
        #{item.phPhoneCnt,jdbcType=BIGINT}, #{item.phCertCnt,jdbcType=BIGINT}, #{item.piCertCnt,jdbcType=BIGINT}, 
        #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into sync_accident_claimdata (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'product_name_type'.toString() == column.value">
          #{item.productNameType,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'claimant_phone'.toString() == column.value">
          #{item.claimantPhone,jdbcType=VARCHAR}
        </if>
        <if test="'policy_holder_user_name'.toString() == column.value">
          #{item.policyHolderUserName,jdbcType=VARCHAR}
        </if>
        <if test="'policy_holder_certi_no'.toString() == column.value">
          #{item.policyHolderCertiNo,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_user_name'.toString() == column.value">
          #{item.insurantUserName,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_certi_no'.toString() == column.value">
          #{item.insurantCertiNo,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_phone_no'.toString() == column.value">
          #{item.insurantPhoneNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_holder_phone_no'.toString() == column.value">
          #{item.policyHolderPhoneNo,jdbcType=VARCHAR}
        </if>
        <if test="'campaign_full_name'.toString() == column.value">
          #{item.campaignFullName,jdbcType=VARCHAR}
        </if>
        <if test="'package_full_name'.toString() == column.value">
          #{item.packageFullName,jdbcType=VARCHAR}
        </if>
        <if test="'accident_date'.toString() == column.value">
          #{item.accidentDate,jdbcType=VARCHAR}
        </if>
        <if test="'accident_place'.toString() == column.value">
          #{item.accidentPlace,jdbcType=VARCHAR}
        </if>
        <if test="'report_date'.toString() == column.value">
          #{item.reportDate,jdbcType=VARCHAR}
        </if>
        <if test="'settled_claim_amt_set'.toString() == column.value">
          #{item.settledClaimAmtSet,jdbcType=VARCHAR}
        </if>
        <if test="'loss_cause_type'.toString() == column.value">
          #{item.lossCauseType,jdbcType=VARCHAR}
        </if>
        <if test="'accidentprocess'.toString() == column.value">
          #{item.accidentprocess,jdbcType=VARCHAR}
        </if>
        <if test="'visit_hospital'.toString() == column.value">
          #{item.visitHospital,jdbcType=VARCHAR}
        </if>
        <if test="'bill_number'.toString() == column.value">
          #{item.billNumber,jdbcType=VARCHAR}
        </if>
        <if test="'insurant_city'.toString() == column.value">
          #{item.insurantCity,jdbcType=VARCHAR}
        </if>
        <if test="'payee_account_no'.toString() == column.value">
          #{item.payeeAccountNo,jdbcType=VARCHAR}
        </if>
        <if test="'payee_name'.toString() == column.value">
          #{item.payeeName,jdbcType=VARCHAR}
        </if>
        <if test="'black_type'.toString() == column.value">
          #{item.blackType,jdbcType=VARCHAR}
        </if>
        <if test="'community_id'.toString() == column.value">
          #{item.communityId,jdbcType=VARCHAR}
        </if>
        <if test="'count_certno'.toString() == column.value">
          #{item.countCertno,jdbcType=VARCHAR}
        </if>
        <if test="'count_phoneno'.toString() == column.value">
          #{item.countPhoneno,jdbcType=VARCHAR}
        </if>
        <if test="'count_reportno'.toString() == column.value">
          #{item.countReportno,jdbcType=VARCHAR}
        </if>
        <if test="'id_cnt'.toString() == column.value">
          #{item.idCnt,jdbcType=VARCHAR}
        </if>
        <if test="'claim_phone_cnt'.toString() == column.value">
          #{item.claimPhoneCnt,jdbcType=BIGINT}
        </if>
        <if test="'ph_phone_cnt'.toString() == column.value">
          #{item.phPhoneCnt,jdbcType=BIGINT}
        </if>
        <if test="'ph_cert_cnt'.toString() == column.value">
          #{item.phCertCnt,jdbcType=BIGINT}
        </if>
        <if test="'pi_cert_cnt'.toString() == column.value">
          #{item.piCertCnt,jdbcType=BIGINT}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>