<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.FollowTaskMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.FollowTaskDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="task_type" jdbcType="TINYINT" property="taskType" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="policy_insurant" jdbcType="VARCHAR" property="policyInsurant" />
    <result column="next_approver" jdbcType="VARCHAR" property="nextApprover" />
    <result column="move_out_reason" jdbcType="VARCHAR" property="moveOutReason" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, policy_no, task_type, auditor, task_status, remark, policy_insurant, 
    next_approver, move_out_reason, gmt_created, gmt_modified, creator, modifier, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.FollowTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from follow_task_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from follow_task_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.FollowTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into follow_task_record (report_no, policy_no, task_type, 
      auditor, task_status, remark, 
      policy_insurant, next_approver, move_out_reason, 
      gmt_created, gmt_modified, creator, 
      modifier, is_deleted)
    values (#{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, #{taskType,jdbcType=TINYINT}, 
      #{auditor,jdbcType=VARCHAR}, #{taskStatus,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, 
      #{policyInsurant,jdbcType=VARCHAR}, #{nextApprover,jdbcType=VARCHAR}, #{moveOutReason,jdbcType=VARCHAR}, 
      sysdate(), sysdate(), #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.FollowTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into follow_task_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="auditor != null">
        auditor,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="policyInsurant != null">
        policy_insurant,
      </if>
      <if test="nextApprover != null">
        next_approver,
      </if>
      <if test="moveOutReason != null">
        move_out_reason,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=TINYINT},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="policyInsurant != null">
        #{policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="nextApprover != null">
        #{nextApprover,jdbcType=VARCHAR},
      </if>
      <if test="moveOutReason != null">
        #{moveOutReason,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.FollowTaskExample" resultType="java.lang.Long">
    select count(*) from follow_task_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update follow_task_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=TINYINT},
      </if>
      <if test="record.auditor != null">
        auditor = #{record.auditor,jdbcType=VARCHAR},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.policyInsurant != null">
        policy_insurant = #{record.policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="record.nextApprover != null">
        next_approver = #{record.nextApprover,jdbcType=VARCHAR},
      </if>
      <if test="record.moveOutReason != null">
        move_out_reason = #{record.moveOutReason,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update follow_task_record
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      task_type = #{record.taskType,jdbcType=TINYINT},
      auditor = #{record.auditor,jdbcType=VARCHAR},
      task_status = #{record.taskStatus,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      policy_insurant = #{record.policyInsurant,jdbcType=VARCHAR},
      next_approver = #{record.nextApprover,jdbcType=VARCHAR},
      move_out_reason = #{record.moveOutReason,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.FollowTaskDO">
    update follow_task_record
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=TINYINT},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="policyInsurant != null">
        policy_insurant = #{policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="nextApprover != null">
        next_approver = #{nextApprover,jdbcType=VARCHAR},
      </if>
      <if test="moveOutReason != null">
        move_out_reason = #{moveOutReason,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.FollowTaskDO">
    update follow_task_record
    set report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=TINYINT},
      auditor = #{auditor,jdbcType=VARCHAR},
      task_status = #{taskStatus,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      policy_insurant = #{policyInsurant,jdbcType=VARCHAR},
      next_approver = #{nextApprover,jdbcType=VARCHAR},
      move_out_reason = #{moveOutReason,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into follow_task_record
    (report_no, policy_no, task_type, auditor, task_status, remark, policy_insurant, 
      next_approver, move_out_reason, gmt_created, gmt_modified, creator, modifier, is_deleted
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.taskType,jdbcType=TINYINT}, 
        #{item.auditor,jdbcType=VARCHAR}, #{item.taskStatus,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.policyInsurant,jdbcType=VARCHAR}, #{item.nextApprover,jdbcType=VARCHAR}, 
        #{item.moveOutReason,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.isDeleted,jdbcType=CHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into follow_task_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'task_type'.toString() == column.value">
          #{item.taskType,jdbcType=TINYINT}
        </if>
        <if test="'auditor'.toString() == column.value">
          #{item.auditor,jdbcType=VARCHAR}
        </if>
        <if test="'task_status'.toString() == column.value">
          #{item.taskStatus,jdbcType=TINYINT}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'policy_insurant'.toString() == column.value">
          #{item.policyInsurant,jdbcType=VARCHAR}
        </if>
        <if test="'next_approver'.toString() == column.value">
          #{item.nextApprover,jdbcType=VARCHAR}
        </if>
        <if test="'move_out_reason'.toString() == column.value">
          #{item.moveOutReason,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>