<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ExpressMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ExpressDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="express_no" jdbcType="VARCHAR" property="expressNo" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="express_info" jdbcType="VARCHAR" property="expressInfo" />
    <result column="is_check" jdbcType="CHAR" property="isCheck" />
    <result column="express_check_date" jdbcType="TIMESTAMP" property="expressCheckDate" />
    <result column="express_type" jdbcType="CHAR" property="expressType" />
    <result column="include_supplementary_materials" jdbcType="CHAR" property="includeSupplementaryMaterials" />
    <result column="attachment_uploader" jdbcType="VARCHAR" property="attachmentUploader" />
    <result column="attachment_upload_date" jdbcType="TIMESTAMP" property="attachmentUploadDate" />
    <result column="related_attach_ids" jdbcType="VARCHAR" property="relatedAttachIds" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, express_no, phone, company_code, company_name, express_info, is_check, 
    express_check_date, express_type, include_supplementary_materials, attachment_uploader, 
    attachment_upload_date, related_attach_ids, remark, is_deleted, creator, modifier, 
    gmt_created, gmt_modified
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ExpressExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cargo_express
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cargo_express
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ExpressDO">
    insert into cargo_express (id, report_no, express_no, 
      phone, company_code, company_name, 
      express_info, is_check, express_check_date, 
      express_type, include_supplementary_materials, attachment_uploader, 
      attachment_upload_date, related_attach_ids, 
      remark, is_deleted, creator, 
      modifier, gmt_created, gmt_modified
      )
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{expressNo,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, 
      #{expressInfo,jdbcType=VARCHAR}, #{isCheck,jdbcType=CHAR}, #{expressCheckDate,jdbcType=TIMESTAMP}, 
      #{expressType,jdbcType=CHAR}, #{includeSupplementaryMaterials,jdbcType=CHAR}, #{attachmentUploader,jdbcType=VARCHAR}, 
      #{attachmentUploadDate,jdbcType=TIMESTAMP}, #{relatedAttachIds,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{isDeleted,jdbcType=CHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, sysdate(), sysdate()
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ExpressDO">
    insert into cargo_express
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="expressNo != null">
        express_no,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="expressInfo != null">
        express_info,
      </if>
      <if test="isCheck != null">
        is_check,
      </if>
      <if test="expressCheckDate != null">
        express_check_date,
      </if>
      <if test="expressType != null">
        express_type,
      </if>
      <if test="includeSupplementaryMaterials != null">
        include_supplementary_materials,
      </if>
      <if test="attachmentUploader != null">
        attachment_uploader,
      </if>
      <if test="attachmentUploadDate != null">
        attachment_upload_date,
      </if>
      <if test="relatedAttachIds != null">
        related_attach_ids,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="expressNo != null">
        #{expressNo,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="expressInfo != null">
        #{expressInfo,jdbcType=VARCHAR},
      </if>
      <if test="isCheck != null">
        #{isCheck,jdbcType=CHAR},
      </if>
      <if test="expressCheckDate != null">
        #{expressCheckDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expressType != null">
        #{expressType,jdbcType=CHAR},
      </if>
      <if test="includeSupplementaryMaterials != null">
        #{includeSupplementaryMaterials,jdbcType=CHAR},
      </if>
      <if test="attachmentUploader != null">
        #{attachmentUploader,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUploadDate != null">
        #{attachmentUploadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="relatedAttachIds != null">
        #{relatedAttachIds,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ExpressExample" resultType="java.lang.Long">
    select count(*) from cargo_express
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cargo_express
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.expressNo != null">
        express_no = #{record.expressNo,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.expressInfo != null">
        express_info = #{record.expressInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.isCheck != null">
        is_check = #{record.isCheck,jdbcType=CHAR},
      </if>
      <if test="record.expressCheckDate != null">
        express_check_date = #{record.expressCheckDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expressType != null">
        express_type = #{record.expressType,jdbcType=CHAR},
      </if>
      <if test="record.includeSupplementaryMaterials != null">
        include_supplementary_materials = #{record.includeSupplementaryMaterials,jdbcType=CHAR},
      </if>
      <if test="record.attachmentUploader != null">
        attachment_uploader = #{record.attachmentUploader,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentUploadDate != null">
        attachment_upload_date = #{record.attachmentUploadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.relatedAttachIds != null">
        related_attach_ids = #{record.relatedAttachIds,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cargo_express
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      express_no = #{record.expressNo,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      express_info = #{record.expressInfo,jdbcType=VARCHAR},
      is_check = #{record.isCheck,jdbcType=CHAR},
      express_check_date = #{record.expressCheckDate,jdbcType=TIMESTAMP},
      express_type = #{record.expressType,jdbcType=CHAR},
      include_supplementary_materials = #{record.includeSupplementaryMaterials,jdbcType=CHAR},
      attachment_uploader = #{record.attachmentUploader,jdbcType=VARCHAR},
      attachment_upload_date = #{record.attachmentUploadDate,jdbcType=TIMESTAMP},
      related_attach_ids = #{record.relatedAttachIds,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate()
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ExpressDO">
    update cargo_express
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="expressNo != null">
        express_no = #{expressNo,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="expressInfo != null">
        express_info = #{expressInfo,jdbcType=VARCHAR},
      </if>
      <if test="isCheck != null">
        is_check = #{isCheck,jdbcType=CHAR},
      </if>
      <if test="expressCheckDate != null">
        express_check_date = #{expressCheckDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expressType != null">
        express_type = #{expressType,jdbcType=CHAR},
      </if>
      <if test="includeSupplementaryMaterials != null">
        include_supplementary_materials = #{includeSupplementaryMaterials,jdbcType=CHAR},
      </if>
      <if test="attachmentUploader != null">
        attachment_uploader = #{attachmentUploader,jdbcType=VARCHAR},
      </if>
      <if test="attachmentUploadDate != null">
        attachment_upload_date = #{attachmentUploadDate,jdbcType=TIMESTAMP},
      </if>
      <if test="relatedAttachIds != null">
        related_attach_ids = #{relatedAttachIds,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ExpressDO">
    update cargo_express
    set report_no = #{reportNo,jdbcType=VARCHAR},
      express_no = #{expressNo,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      express_info = #{expressInfo,jdbcType=VARCHAR},
      is_check = #{isCheck,jdbcType=CHAR},
      express_check_date = #{expressCheckDate,jdbcType=TIMESTAMP},
      express_type = #{expressType,jdbcType=CHAR},
      include_supplementary_materials = #{includeSupplementaryMaterials,jdbcType=CHAR},
      attachment_uploader = #{attachmentUploader,jdbcType=VARCHAR},
      attachment_upload_date = #{attachmentUploadDate,jdbcType=TIMESTAMP},
      related_attach_ids = #{relatedAttachIds,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=CHAR},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate()
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into cargo_express
    (id, report_no, express_no, phone, company_code, company_name, express_info, is_check, 
      express_check_date, express_type, include_supplementary_materials, attachment_uploader, 
      attachment_upload_date, related_attach_ids, remark, is_deleted, creator, modifier, 
      gmt_created, gmt_modified)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.expressNo,jdbcType=VARCHAR}, 
        #{item.phone,jdbcType=VARCHAR}, #{item.companyCode,jdbcType=VARCHAR}, #{item.companyName,jdbcType=VARCHAR}, 
        #{item.expressInfo,jdbcType=VARCHAR}, #{item.isCheck,jdbcType=CHAR}, #{item.expressCheckDate,jdbcType=TIMESTAMP}, 
        #{item.expressType,jdbcType=CHAR}, #{item.includeSupplementaryMaterials,jdbcType=CHAR}, 
        #{item.attachmentUploader,jdbcType=VARCHAR}, #{item.attachmentUploadDate,jdbcType=TIMESTAMP}, 
        #{item.relatedAttachIds,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.gmtModified,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into cargo_express (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'express_no'.toString() == column.value">
          #{item.expressNo,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'company_code'.toString() == column.value">
          #{item.companyCode,jdbcType=VARCHAR}
        </if>
        <if test="'company_name'.toString() == column.value">
          #{item.companyName,jdbcType=VARCHAR}
        </if>
        <if test="'express_info'.toString() == column.value">
          #{item.expressInfo,jdbcType=VARCHAR}
        </if>
        <if test="'is_check'.toString() == column.value">
          #{item.isCheck,jdbcType=CHAR}
        </if>
        <if test="'express_check_date'.toString() == column.value">
          #{item.expressCheckDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'express_type'.toString() == column.value">
          #{item.expressType,jdbcType=CHAR}
        </if>
        <if test="'include_supplementary_materials'.toString() == column.value">
          #{item.includeSupplementaryMaterials,jdbcType=CHAR}
        </if>
        <if test="'attachment_uploader'.toString() == column.value">
          #{item.attachmentUploader,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_upload_date'.toString() == column.value">
          #{item.attachmentUploadDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'related_attach_ids'.toString() == column.value">
          #{item.relatedAttachIds,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>