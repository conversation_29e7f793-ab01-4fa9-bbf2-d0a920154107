<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.ClaimPayProblemMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.ClaimPayProblemDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="policy_insurant" jdbcType="VARCHAR" property="policyInsurant" />
    <result column="insurance_type_code" jdbcType="TINYINT" property="insuranceTypeCode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="settle_user_name" jdbcType="VARCHAR" property="settleUserName" />
    <result column="close_date" jdbcType="TIMESTAMP" property="closeDate" />
    <result column="indemnity_amount" jdbcType="VARCHAR" property="indemnityAmount" />
    <result column="pay_state" jdbcType="TINYINT" property="payState" />
    <result column="pay_info" jdbcType="VARCHAR" property="payInfo" />
    <result column="payment_date" jdbcType="TIMESTAMP" property="paymentDate" />
    <result column="is_pay_wait" jdbcType="CHAR" property="isPayWait" />
    <result column="notice_status" jdbcType="TINYINT" property="noticeStatus" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="visit_status" jdbcType="VARCHAR" property="visitStatus" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="campaign_def_id" jdbcType="BIGINT" property="campaignDefId" />
    <result column="package_def_id" jdbcType="BIGINT" property="packageDefId" />
    <result column="claim_bank_info_id" jdbcType="BIGINT" property="claimBankInfoId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, policy_no, product_name, policy_insurant, insurance_type_code, status, 
    settle_user_name, close_date, indemnity_amount, pay_state, pay_info, payment_date, 
    is_pay_wait, notice_status, audit_status, visit_status, creator, gmt_created, modifier, 
    gmt_modified, is_deleted, campaign_def_id, package_def_id, claim_bank_info_id
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayProblemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_pay_problem
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_pay_problem
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayProblemDO">
    insert into claim_pay_problem (id, report_no, policy_no, 
      product_name, policy_insurant, insurance_type_code, 
      status, settle_user_name, close_date, 
      indemnity_amount, pay_state, pay_info, 
      payment_date, is_pay_wait, notice_status, 
      audit_status, visit_status, creator, 
      gmt_created, modifier, gmt_modified, 
      is_deleted, campaign_def_id, package_def_id, 
      claim_bank_info_id)
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{policyNo,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{policyInsurant,jdbcType=VARCHAR}, #{insuranceTypeCode,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{settleUserName,jdbcType=VARCHAR}, #{closeDate,jdbcType=TIMESTAMP}, 
      #{indemnityAmount,jdbcType=VARCHAR}, #{payState,jdbcType=TINYINT}, #{payInfo,jdbcType=VARCHAR}, 
      #{paymentDate,jdbcType=TIMESTAMP}, #{isPayWait,jdbcType=CHAR}, #{noticeStatus,jdbcType=TINYINT}, 
      #{auditStatus,jdbcType=TINYINT}, #{visitStatus,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      sysdate(), #{modifier,jdbcType=VARCHAR}, sysdate(), 
      #{isDeleted,jdbcType=CHAR}, #{campaignDefId,jdbcType=BIGINT}, #{packageDefId,jdbcType=BIGINT}, 
      #{claimBankInfoId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayProblemDO">
    insert into claim_pay_problem
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="policyInsurant != null">
        policy_insurant,
      </if>
      <if test="insuranceTypeCode != null">
        insurance_type_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="settleUserName != null">
        settle_user_name,
      </if>
      <if test="closeDate != null">
        close_date,
      </if>
      <if test="indemnityAmount != null">
        indemnity_amount,
      </if>
      <if test="payState != null">
        pay_state,
      </if>
      <if test="payInfo != null">
        pay_info,
      </if>
      <if test="paymentDate != null">
        payment_date,
      </if>
      <if test="isPayWait != null">
        is_pay_wait,
      </if>
      <if test="noticeStatus != null">
        notice_status,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="visitStatus != null">
        visit_status,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="campaignDefId != null">
        campaign_def_id,
      </if>
      <if test="packageDefId != null">
        package_def_id,
      </if>
      <if test="claimBankInfoId != null">
        claim_bank_info_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="policyInsurant != null">
        #{policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="insuranceTypeCode != null">
        #{insuranceTypeCode,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="settleUserName != null">
        #{settleUserName,jdbcType=VARCHAR},
      </if>
      <if test="closeDate != null">
        #{closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="indemnityAmount != null">
        #{indemnityAmount,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        #{payState,jdbcType=TINYINT},
      </if>
      <if test="payInfo != null">
        #{payInfo,jdbcType=VARCHAR},
      </if>
      <if test="paymentDate != null">
        #{paymentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isPayWait != null">
        #{isPayWait,jdbcType=CHAR},
      </if>
      <if test="noticeStatus != null">
        #{noticeStatus,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="visitStatus != null">
        #{visitStatus,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="campaignDefId != null">
        #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="packageDefId != null">
        #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="claimBankInfoId != null">
        #{claimBankInfoId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayProblemExample" resultType="java.lang.Long">
    select count(*) from claim_pay_problem
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_pay_problem
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.policyInsurant != null">
        policy_insurant = #{record.policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="record.insuranceTypeCode != null">
        insurance_type_code = #{record.insuranceTypeCode,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.settleUserName != null">
        settle_user_name = #{record.settleUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.closeDate != null">
        close_date = #{record.closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.indemnityAmount != null">
        indemnity_amount = #{record.indemnityAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.payState != null">
        pay_state = #{record.payState,jdbcType=TINYINT},
      </if>
      <if test="record.payInfo != null">
        pay_info = #{record.payInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentDate != null">
        payment_date = #{record.paymentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isPayWait != null">
        is_pay_wait = #{record.isPayWait,jdbcType=CHAR},
      </if>
      <if test="record.noticeStatus != null">
        notice_status = #{record.noticeStatus,jdbcType=TINYINT},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.visitStatus != null">
        visit_status = #{record.visitStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.campaignDefId != null">
        campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="record.packageDefId != null">
        package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      </if>
      <if test="record.claimBankInfoId != null">
        claim_bank_info_id = #{record.claimBankInfoId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_pay_problem
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      policy_insurant = #{record.policyInsurant,jdbcType=VARCHAR},
      insurance_type_code = #{record.insuranceTypeCode,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      settle_user_name = #{record.settleUserName,jdbcType=VARCHAR},
      close_date = #{record.closeDate,jdbcType=TIMESTAMP},
      indemnity_amount = #{record.indemnityAmount,jdbcType=VARCHAR},
      pay_state = #{record.payState,jdbcType=TINYINT},
      pay_info = #{record.payInfo,jdbcType=VARCHAR},
      payment_date = #{record.paymentDate,jdbcType=TIMESTAMP},
      is_pay_wait = #{record.isPayWait,jdbcType=CHAR},
      notice_status = #{record.noticeStatus,jdbcType=TINYINT},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      visit_status = #{record.visitStatus,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      campaign_def_id = #{record.campaignDefId,jdbcType=BIGINT},
      package_def_id = #{record.packageDefId,jdbcType=BIGINT},
      claim_bank_info_id = #{record.claimBankInfoId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayProblemDO">
    update claim_pay_problem
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="policyInsurant != null">
        policy_insurant = #{policyInsurant,jdbcType=VARCHAR},
      </if>
      <if test="insuranceTypeCode != null">
        insurance_type_code = #{insuranceTypeCode,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="settleUserName != null">
        settle_user_name = #{settleUserName,jdbcType=VARCHAR},
      </if>
      <if test="closeDate != null">
        close_date = #{closeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="indemnityAmount != null">
        indemnity_amount = #{indemnityAmount,jdbcType=VARCHAR},
      </if>
      <if test="payState != null">
        pay_state = #{payState,jdbcType=TINYINT},
      </if>
      <if test="payInfo != null">
        pay_info = #{payInfo,jdbcType=VARCHAR},
      </if>
      <if test="paymentDate != null">
        payment_date = #{paymentDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isPayWait != null">
        is_pay_wait = #{isPayWait,jdbcType=CHAR},
      </if>
      <if test="noticeStatus != null">
        notice_status = #{noticeStatus,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="visitStatus != null">
        visit_status = #{visitStatus,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="campaignDefId != null">
        campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      </if>
      <if test="packageDefId != null">
        package_def_id = #{packageDefId,jdbcType=BIGINT},
      </if>
      <if test="claimBankInfoId != null">
        claim_bank_info_id = #{claimBankInfoId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.ClaimPayProblemDO">
    update claim_pay_problem
    set report_no = #{reportNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      policy_insurant = #{policyInsurant,jdbcType=VARCHAR},
      insurance_type_code = #{insuranceTypeCode,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      settle_user_name = #{settleUserName,jdbcType=VARCHAR},
      close_date = #{closeDate,jdbcType=TIMESTAMP},
      indemnity_amount = #{indemnityAmount,jdbcType=VARCHAR},
      pay_state = #{payState,jdbcType=TINYINT},
      pay_info = #{payInfo,jdbcType=VARCHAR},
      payment_date = #{paymentDate,jdbcType=TIMESTAMP},
      is_pay_wait = #{isPayWait,jdbcType=CHAR},
      notice_status = #{noticeStatus,jdbcType=TINYINT},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      visit_status = #{visitStatus,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR},
      campaign_def_id = #{campaignDefId,jdbcType=BIGINT},
      package_def_id = #{packageDefId,jdbcType=BIGINT},
      claim_bank_info_id = #{claimBankInfoId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_pay_problem
    (id, report_no, policy_no, product_name, policy_insurant, insurance_type_code, status, 
      settle_user_name, close_date, indemnity_amount, pay_state, pay_info, payment_date, 
      is_pay_wait, notice_status, audit_status, visit_status, creator, gmt_created, modifier, 
      gmt_modified, is_deleted, campaign_def_id, package_def_id, claim_bank_info_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.policyInsurant,jdbcType=VARCHAR}, 
        #{item.insuranceTypeCode,jdbcType=TINYINT}, #{item.status,jdbcType=TINYINT}, #{item.settleUserName,jdbcType=VARCHAR}, 
        #{item.closeDate,jdbcType=TIMESTAMP}, #{item.indemnityAmount,jdbcType=VARCHAR}, 
        #{item.payState,jdbcType=TINYINT}, #{item.payInfo,jdbcType=VARCHAR}, #{item.paymentDate,jdbcType=TIMESTAMP}, 
        #{item.isPayWait,jdbcType=CHAR}, #{item.noticeStatus,jdbcType=TINYINT}, #{item.auditStatus,jdbcType=TINYINT}, 
        #{item.visitStatus,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.campaignDefId,jdbcType=BIGINT}, #{item.packageDefId,jdbcType=BIGINT}, #{item.claimBankInfoId,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_pay_problem (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'policy_insurant'.toString() == column.value">
          #{item.policyInsurant,jdbcType=VARCHAR}
        </if>
        <if test="'insurance_type_code'.toString() == column.value">
          #{item.insuranceTypeCode,jdbcType=TINYINT}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=TINYINT}
        </if>
        <if test="'settle_user_name'.toString() == column.value">
          #{item.settleUserName,jdbcType=VARCHAR}
        </if>
        <if test="'close_date'.toString() == column.value">
          #{item.closeDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'indemnity_amount'.toString() == column.value">
          #{item.indemnityAmount,jdbcType=VARCHAR}
        </if>
        <if test="'pay_state'.toString() == column.value">
          #{item.payState,jdbcType=TINYINT}
        </if>
        <if test="'pay_info'.toString() == column.value">
          #{item.payInfo,jdbcType=VARCHAR}
        </if>
        <if test="'payment_date'.toString() == column.value">
          #{item.paymentDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_pay_wait'.toString() == column.value">
          #{item.isPayWait,jdbcType=CHAR}
        </if>
        <if test="'notice_status'.toString() == column.value">
          #{item.noticeStatus,jdbcType=TINYINT}
        </if>
        <if test="'audit_status'.toString() == column.value">
          #{item.auditStatus,jdbcType=TINYINT}
        </if>
        <if test="'visit_status'.toString() == column.value">
          #{item.visitStatus,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'campaign_def_id'.toString() == column.value">
          #{item.campaignDefId,jdbcType=BIGINT}
        </if>
        <if test="'package_def_id'.toString() == column.value">
          #{item.packageDefId,jdbcType=BIGINT}
        </if>
        <if test="'claim_bank_info_id'.toString() == column.value">
          #{item.claimBankInfoId,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>