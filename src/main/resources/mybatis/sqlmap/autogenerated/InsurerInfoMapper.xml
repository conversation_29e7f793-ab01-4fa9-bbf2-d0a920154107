<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.InsurerInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.InsurerInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="policy_id" jdbcType="BIGINT" property="policyId" />
    <result column="insurer_name" jdbcType="VARCHAR" property="insurerName" />
    <result column="insurer_certi_type" jdbcType="VARCHAR" property="insurerCertiType" />
    <result column="gender" jdbcType="CHAR" property="gender" />
    <result column="insurer_certi_no" jdbcType="VARCHAR" property="insurerCertiNo" />
    <result column="insurer_certi_effective" jdbcType="TIMESTAMP" property="insurerCertiEffective" />
    <result column="insurer_certi_expire" jdbcType="TIMESTAMP" property="insurerCertiExpire" />
    <result column="insurer_birthday" jdbcType="TIMESTAMP" property="insurerBirthday" />
    <result column="isnurer_insurance_relation" jdbcType="VARCHAR" property="isnurerInsuranceRelation" />
    <result column="insurer_email" jdbcType="VARCHAR" property="insurerEmail" />
    <result column="insurer_phone_no" jdbcType="VARCHAR" property="insurerPhoneNo" />
    <result column="death_reason_code" jdbcType="VARCHAR" property="deathReasonCode" />
    <result column="death_time" jdbcType="TIMESTAMP" property="deathTime" />
    <result column="die_flag" jdbcType="VARCHAR" property="dieFlag" />
    <result column="disability_degree" jdbcType="VARCHAR" property="disabilityDegree" />
    <result column="disability_grade" jdbcType="VARCHAR" property="disabilityGrade" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="batch_claim_bill_no" jdbcType="VARCHAR" property="batchClaimBillNo" />
    <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
    <result column="insurer_type" jdbcType="TINYINT" property="insurerType" />
    <result column="core_synced" jdbcType="BIT" property="coreSynced" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="claim_id" jdbcType="BIGINT" property="claimId" />
    <result column="insurant_type" jdbcType="VARCHAR" property="insurantType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, policy_id, insurer_name, insurer_certi_type, gender, insurer_certi_no, insurer_certi_effective, 
    insurer_certi_expire, insurer_birthday, isnurer_insurance_relation, insurer_email, 
    insurer_phone_no, death_reason_code, death_time, die_flag, disability_degree, disability_grade, 
    report_no, batch_claim_bill_no, policy_no, insurer_type, core_synced, creator, modifier, 
    gmt_created, gmt_modified, is_deleted, claim_id, insurant_type
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.InsurerInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from insurer_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from insurer_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.InsurerInfoDO">
    insert into insurer_info (id, policy_id, insurer_name, 
      insurer_certi_type, gender, insurer_certi_no, 
      insurer_certi_effective, insurer_certi_expire, 
      insurer_birthday, isnurer_insurance_relation, 
      insurer_email, insurer_phone_no, death_reason_code, 
      death_time, die_flag, disability_degree, 
      disability_grade, report_no, batch_claim_bill_no, 
      policy_no, insurer_type, core_synced, 
      creator, modifier, gmt_created, 
      gmt_modified, is_deleted, claim_id, 
      insurant_type)
    values (#{id,jdbcType=BIGINT}, #{policyId,jdbcType=BIGINT}, #{insurerName,jdbcType=VARCHAR}, 
      #{insurerCertiType,jdbcType=VARCHAR}, #{gender,jdbcType=CHAR}, #{insurerCertiNo,jdbcType=VARCHAR}, 
      #{insurerCertiEffective,jdbcType=TIMESTAMP}, #{insurerCertiExpire,jdbcType=TIMESTAMP}, 
      #{insurerBirthday,jdbcType=TIMESTAMP}, #{isnurerInsuranceRelation,jdbcType=VARCHAR}, 
      #{insurerEmail,jdbcType=VARCHAR}, #{insurerPhoneNo,jdbcType=VARCHAR}, #{deathReasonCode,jdbcType=VARCHAR}, 
      #{deathTime,jdbcType=TIMESTAMP}, #{dieFlag,jdbcType=VARCHAR}, #{disabilityDegree,jdbcType=VARCHAR}, 
      #{disabilityGrade,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{batchClaimBillNo,jdbcType=VARCHAR}, 
      #{policyNo,jdbcType=VARCHAR}, #{insurerType,jdbcType=TINYINT}, #{coreSynced,jdbcType=BIT}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, sysdate(), 
      sysdate(), #{isDeleted,jdbcType=CHAR}, #{claimId,jdbcType=BIGINT}, 
      #{insurantType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.InsurerInfoDO">
    insert into insurer_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="policyId != null">
        policy_id,
      </if>
      <if test="insurerName != null">
        insurer_name,
      </if>
      <if test="insurerCertiType != null">
        insurer_certi_type,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="insurerCertiNo != null">
        insurer_certi_no,
      </if>
      <if test="insurerCertiEffective != null">
        insurer_certi_effective,
      </if>
      <if test="insurerCertiExpire != null">
        insurer_certi_expire,
      </if>
      <if test="insurerBirthday != null">
        insurer_birthday,
      </if>
      <if test="isnurerInsuranceRelation != null">
        isnurer_insurance_relation,
      </if>
      <if test="insurerEmail != null">
        insurer_email,
      </if>
      <if test="insurerPhoneNo != null">
        insurer_phone_no,
      </if>
      <if test="deathReasonCode != null">
        death_reason_code,
      </if>
      <if test="deathTime != null">
        death_time,
      </if>
      <if test="dieFlag != null">
        die_flag,
      </if>
      <if test="disabilityDegree != null">
        disability_degree,
      </if>
      <if test="disabilityGrade != null">
        disability_grade,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="insurerType != null">
        insurer_type,
      </if>
      <if test="coreSynced != null">
        core_synced,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="claimId != null">
        claim_id,
      </if>
      <if test="insurantType != null">
        insurant_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="policyId != null">
        #{policyId,jdbcType=BIGINT},
      </if>
      <if test="insurerName != null">
        #{insurerName,jdbcType=VARCHAR},
      </if>
      <if test="insurerCertiType != null">
        #{insurerCertiType,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=CHAR},
      </if>
      <if test="insurerCertiNo != null">
        #{insurerCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="insurerCertiEffective != null">
        #{insurerCertiEffective,jdbcType=TIMESTAMP},
      </if>
      <if test="insurerCertiExpire != null">
        #{insurerCertiExpire,jdbcType=TIMESTAMP},
      </if>
      <if test="insurerBirthday != null">
        #{insurerBirthday,jdbcType=TIMESTAMP},
      </if>
      <if test="isnurerInsuranceRelation != null">
        #{isnurerInsuranceRelation,jdbcType=VARCHAR},
      </if>
      <if test="insurerEmail != null">
        #{insurerEmail,jdbcType=VARCHAR},
      </if>
      <if test="insurerPhoneNo != null">
        #{insurerPhoneNo,jdbcType=VARCHAR},
      </if>
      <if test="deathReasonCode != null">
        #{deathReasonCode,jdbcType=VARCHAR},
      </if>
      <if test="deathTime != null">
        #{deathTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dieFlag != null">
        #{dieFlag,jdbcType=VARCHAR},
      </if>
      <if test="disabilityDegree != null">
        #{disabilityDegree,jdbcType=VARCHAR},
      </if>
      <if test="disabilityGrade != null">
        #{disabilityGrade,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillNo != null">
        #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="insurerType != null">
        #{insurerType,jdbcType=TINYINT},
      </if>
      <if test="coreSynced != null">
        #{coreSynced,jdbcType=BIT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="claimId != null">
        #{claimId,jdbcType=BIGINT},
      </if>
      <if test="insurantType != null">
        #{insurantType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.InsurerInfoExample" resultType="java.lang.Long">
    select count(*) from insurer_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update insurer_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.policyId != null">
        policy_id = #{record.policyId,jdbcType=BIGINT},
      </if>
      <if test="record.insurerName != null">
        insurer_name = #{record.insurerName,jdbcType=VARCHAR},
      </if>
      <if test="record.insurerCertiType != null">
        insurer_certi_type = #{record.insurerCertiType,jdbcType=VARCHAR},
      </if>
      <if test="record.gender != null">
        gender = #{record.gender,jdbcType=CHAR},
      </if>
      <if test="record.insurerCertiNo != null">
        insurer_certi_no = #{record.insurerCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="record.insurerCertiEffective != null">
        insurer_certi_effective = #{record.insurerCertiEffective,jdbcType=TIMESTAMP},
      </if>
      <if test="record.insurerCertiExpire != null">
        insurer_certi_expire = #{record.insurerCertiExpire,jdbcType=TIMESTAMP},
      </if>
      <if test="record.insurerBirthday != null">
        insurer_birthday = #{record.insurerBirthday,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isnurerInsuranceRelation != null">
        isnurer_insurance_relation = #{record.isnurerInsuranceRelation,jdbcType=VARCHAR},
      </if>
      <if test="record.insurerEmail != null">
        insurer_email = #{record.insurerEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.insurerPhoneNo != null">
        insurer_phone_no = #{record.insurerPhoneNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deathReasonCode != null">
        death_reason_code = #{record.deathReasonCode,jdbcType=VARCHAR},
      </if>
      <if test="record.deathTime != null">
        death_time = #{record.deathTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dieFlag != null">
        die_flag = #{record.dieFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.disabilityDegree != null">
        disability_degree = #{record.disabilityDegree,jdbcType=VARCHAR},
      </if>
      <if test="record.disabilityGrade != null">
        disability_grade = #{record.disabilityGrade,jdbcType=VARCHAR},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchClaimBillNo != null">
        batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.insurerType != null">
        insurer_type = #{record.insurerType,jdbcType=TINYINT},
      </if>
      <if test="record.coreSynced != null">
        core_synced = #{record.coreSynced,jdbcType=BIT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.claimId != null">
        claim_id = #{record.claimId,jdbcType=BIGINT},
      </if>
      <if test="record.insurantType != null">
        insurant_type = #{record.insurantType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update insurer_info
    set id = #{record.id,jdbcType=BIGINT},
      policy_id = #{record.policyId,jdbcType=BIGINT},
      insurer_name = #{record.insurerName,jdbcType=VARCHAR},
      insurer_certi_type = #{record.insurerCertiType,jdbcType=VARCHAR},
      gender = #{record.gender,jdbcType=CHAR},
      insurer_certi_no = #{record.insurerCertiNo,jdbcType=VARCHAR},
      insurer_certi_effective = #{record.insurerCertiEffective,jdbcType=TIMESTAMP},
      insurer_certi_expire = #{record.insurerCertiExpire,jdbcType=TIMESTAMP},
      insurer_birthday = #{record.insurerBirthday,jdbcType=TIMESTAMP},
      isnurer_insurance_relation = #{record.isnurerInsuranceRelation,jdbcType=VARCHAR},
      insurer_email = #{record.insurerEmail,jdbcType=VARCHAR},
      insurer_phone_no = #{record.insurerPhoneNo,jdbcType=VARCHAR},
      death_reason_code = #{record.deathReasonCode,jdbcType=VARCHAR},
      death_time = #{record.deathTime,jdbcType=TIMESTAMP},
      die_flag = #{record.dieFlag,jdbcType=VARCHAR},
      disability_degree = #{record.disabilityDegree,jdbcType=VARCHAR},
      disability_grade = #{record.disabilityGrade,jdbcType=VARCHAR},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      batch_claim_bill_no = #{record.batchClaimBillNo,jdbcType=VARCHAR},
      policy_no = #{record.policyNo,jdbcType=VARCHAR},
      insurer_type = #{record.insurerType,jdbcType=TINYINT},
      core_synced = #{record.coreSynced,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      claim_id = #{record.claimId,jdbcType=BIGINT},
      insurant_type = #{record.insurantType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.InsurerInfoDO">
    update insurer_info
    <set>
      <if test="policyId != null">
        policy_id = #{policyId,jdbcType=BIGINT},
      </if>
      <if test="insurerName != null">
        insurer_name = #{insurerName,jdbcType=VARCHAR},
      </if>
      <if test="insurerCertiType != null">
        insurer_certi_type = #{insurerCertiType,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=CHAR},
      </if>
      <if test="insurerCertiNo != null">
        insurer_certi_no = #{insurerCertiNo,jdbcType=VARCHAR},
      </if>
      <if test="insurerCertiEffective != null">
        insurer_certi_effective = #{insurerCertiEffective,jdbcType=TIMESTAMP},
      </if>
      <if test="insurerCertiExpire != null">
        insurer_certi_expire = #{insurerCertiExpire,jdbcType=TIMESTAMP},
      </if>
      <if test="insurerBirthday != null">
        insurer_birthday = #{insurerBirthday,jdbcType=TIMESTAMP},
      </if>
      <if test="isnurerInsuranceRelation != null">
        isnurer_insurance_relation = #{isnurerInsuranceRelation,jdbcType=VARCHAR},
      </if>
      <if test="insurerEmail != null">
        insurer_email = #{insurerEmail,jdbcType=VARCHAR},
      </if>
      <if test="insurerPhoneNo != null">
        insurer_phone_no = #{insurerPhoneNo,jdbcType=VARCHAR},
      </if>
      <if test="deathReasonCode != null">
        death_reason_code = #{deathReasonCode,jdbcType=VARCHAR},
      </if>
      <if test="deathTime != null">
        death_time = #{deathTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dieFlag != null">
        die_flag = #{dieFlag,jdbcType=VARCHAR},
      </if>
      <if test="disabilityDegree != null">
        disability_degree = #{disabilityDegree,jdbcType=VARCHAR},
      </if>
      <if test="disabilityGrade != null">
        disability_grade = #{disabilityGrade,jdbcType=VARCHAR},
      </if>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="batchClaimBillNo != null">
        batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="insurerType != null">
        insurer_type = #{insurerType,jdbcType=TINYINT},
      </if>
      <if test="coreSynced != null">
        core_synced = #{coreSynced,jdbcType=BIT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="claimId != null">
        claim_id = #{claimId,jdbcType=BIGINT},
      </if>
      <if test="insurantType != null">
        insurant_type = #{insurantType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.InsurerInfoDO">
    update insurer_info
    set policy_id = #{policyId,jdbcType=BIGINT},
      insurer_name = #{insurerName,jdbcType=VARCHAR},
      insurer_certi_type = #{insurerCertiType,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=CHAR},
      insurer_certi_no = #{insurerCertiNo,jdbcType=VARCHAR},
      insurer_certi_effective = #{insurerCertiEffective,jdbcType=TIMESTAMP},
      insurer_certi_expire = #{insurerCertiExpire,jdbcType=TIMESTAMP},
      insurer_birthday = #{insurerBirthday,jdbcType=TIMESTAMP},
      isnurer_insurance_relation = #{isnurerInsuranceRelation,jdbcType=VARCHAR},
      insurer_email = #{insurerEmail,jdbcType=VARCHAR},
      insurer_phone_no = #{insurerPhoneNo,jdbcType=VARCHAR},
      death_reason_code = #{deathReasonCode,jdbcType=VARCHAR},
      death_time = #{deathTime,jdbcType=TIMESTAMP},
      die_flag = #{dieFlag,jdbcType=VARCHAR},
      disability_degree = #{disabilityDegree,jdbcType=VARCHAR},
      disability_grade = #{disabilityGrade,jdbcType=VARCHAR},
      report_no = #{reportNo,jdbcType=VARCHAR},
      batch_claim_bill_no = #{batchClaimBillNo,jdbcType=VARCHAR},
      policy_no = #{policyNo,jdbcType=VARCHAR},
      insurer_type = #{insurerType,jdbcType=TINYINT},
      core_synced = #{coreSynced,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
    
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR},
      claim_id = #{claimId,jdbcType=BIGINT},
      insurant_type = #{insurantType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into insurer_info
    (id, policy_id, insurer_name, insurer_certi_type, gender, insurer_certi_no, insurer_certi_effective, 
      insurer_certi_expire, insurer_birthday, isnurer_insurance_relation, insurer_email, 
      insurer_phone_no, death_reason_code, death_time, die_flag, disability_degree, disability_grade, 
      report_no, batch_claim_bill_no, policy_no, insurer_type, core_synced, creator, 
      modifier, gmt_created, gmt_modified, is_deleted, claim_id, insurant_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.policyId,jdbcType=BIGINT}, #{item.insurerName,jdbcType=VARCHAR}, 
        #{item.insurerCertiType,jdbcType=VARCHAR}, #{item.gender,jdbcType=CHAR}, #{item.insurerCertiNo,jdbcType=VARCHAR}, 
        #{item.insurerCertiEffective,jdbcType=TIMESTAMP}, #{item.insurerCertiExpire,jdbcType=TIMESTAMP}, 
        #{item.insurerBirthday,jdbcType=TIMESTAMP}, #{item.isnurerInsuranceRelation,jdbcType=VARCHAR}, 
        #{item.insurerEmail,jdbcType=VARCHAR}, #{item.insurerPhoneNo,jdbcType=VARCHAR}, 
        #{item.deathReasonCode,jdbcType=VARCHAR}, #{item.deathTime,jdbcType=TIMESTAMP}, 
        #{item.dieFlag,jdbcType=VARCHAR}, #{item.disabilityDegree,jdbcType=VARCHAR}, #{item.disabilityGrade,jdbcType=VARCHAR}, 
        #{item.reportNo,jdbcType=VARCHAR}, #{item.batchClaimBillNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.insurerType,jdbcType=TINYINT}, #{item.coreSynced,jdbcType=BIT}, #{item.creator,jdbcType=VARCHAR}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
        #{item.isDeleted,jdbcType=CHAR}, #{item.claimId,jdbcType=BIGINT}, #{item.insurantType,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into insurer_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'policy_id'.toString() == column.value">
          #{item.policyId,jdbcType=BIGINT}
        </if>
        <if test="'insurer_name'.toString() == column.value">
          #{item.insurerName,jdbcType=VARCHAR}
        </if>
        <if test="'insurer_certi_type'.toString() == column.value">
          #{item.insurerCertiType,jdbcType=VARCHAR}
        </if>
        <if test="'gender'.toString() == column.value">
          #{item.gender,jdbcType=CHAR}
        </if>
        <if test="'insurer_certi_no'.toString() == column.value">
          #{item.insurerCertiNo,jdbcType=VARCHAR}
        </if>
        <if test="'insurer_certi_effective'.toString() == column.value">
          #{item.insurerCertiEffective,jdbcType=TIMESTAMP}
        </if>
        <if test="'insurer_certi_expire'.toString() == column.value">
          #{item.insurerCertiExpire,jdbcType=TIMESTAMP}
        </if>
        <if test="'insurer_birthday'.toString() == column.value">
          #{item.insurerBirthday,jdbcType=TIMESTAMP}
        </if>
        <if test="'isnurer_insurance_relation'.toString() == column.value">
          #{item.isnurerInsuranceRelation,jdbcType=VARCHAR}
        </if>
        <if test="'insurer_email'.toString() == column.value">
          #{item.insurerEmail,jdbcType=VARCHAR}
        </if>
        <if test="'insurer_phone_no'.toString() == column.value">
          #{item.insurerPhoneNo,jdbcType=VARCHAR}
        </if>
        <if test="'death_reason_code'.toString() == column.value">
          #{item.deathReasonCode,jdbcType=VARCHAR}
        </if>
        <if test="'death_time'.toString() == column.value">
          #{item.deathTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'die_flag'.toString() == column.value">
          #{item.dieFlag,jdbcType=VARCHAR}
        </if>
        <if test="'disability_degree'.toString() == column.value">
          #{item.disabilityDegree,jdbcType=VARCHAR}
        </if>
        <if test="'disability_grade'.toString() == column.value">
          #{item.disabilityGrade,jdbcType=VARCHAR}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'batch_claim_bill_no'.toString() == column.value">
          #{item.batchClaimBillNo,jdbcType=VARCHAR}
        </if>
        <if test="'policy_no'.toString() == column.value">
          #{item.policyNo,jdbcType=VARCHAR}
        </if>
        <if test="'insurer_type'.toString() == column.value">
          #{item.insurerType,jdbcType=TINYINT}
        </if>
        <if test="'core_synced'.toString() == column.value">
          #{item.coreSynced,jdbcType=BIT}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'claim_id'.toString() == column.value">
          #{item.claimId,jdbcType=BIGINT}
        </if>
        <if test="'insurant_type'.toString() == column.value">
          #{item.insurantType,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>