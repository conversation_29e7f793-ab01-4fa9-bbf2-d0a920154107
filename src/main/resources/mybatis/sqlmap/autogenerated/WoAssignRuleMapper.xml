<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.WoAssignRuleMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.WoAssignRuleDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="wo_type" jdbcType="VARCHAR" property="woType" />
    <result column="product_item" jdbcType="VARCHAR" property="productItem" />
    <result column="product_category_value" jdbcType="VARCHAR" property="productCategoryValue" />
    <result column="first_type_id" jdbcType="VARCHAR" property="firstTypeId" />
    <result column="second_type_id" jdbcType="VARCHAR" property="secondTypeId" />
    <result column="third_type_id" jdbcType="VARCHAR" property="thirdTypeId" />
    <result column="four_type_id" jdbcType="VARCHAR" property="fourTypeId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="is_common" jdbcType="CHAR" property="isCommon" />
    <result column="common_rule" jdbcType="VARCHAR" property="commonRule" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="is_default" jdbcType="CHAR" property="isDefault" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, wo_type, product_item, product_category_value, first_type_id, second_type_id, 
    third_type_id, four_type_id, user_name, is_common, common_rule, creator, gmt_created, 
    modifier, gmt_modified, is_deleted, is_default
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.WoAssignRuleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from wo_assign_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wo_assign_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.WoAssignRuleDO">
    insert into wo_assign_rule (id, wo_type, product_item, 
      product_category_value, first_type_id, second_type_id, 
      third_type_id, four_type_id, user_name, 
      is_common, common_rule, creator, 
      gmt_created, modifier, gmt_modified, 
      is_deleted, is_default)
    values (#{id,jdbcType=BIGINT}, #{woType,jdbcType=VARCHAR}, #{productItem,jdbcType=VARCHAR}, 
      #{productCategoryValue,jdbcType=VARCHAR}, #{firstTypeId,jdbcType=VARCHAR}, #{secondTypeId,jdbcType=VARCHAR}, 
      #{thirdTypeId,jdbcType=VARCHAR}, #{fourTypeId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, 
      #{isCommon,jdbcType=CHAR}, #{commonRule,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      sysdate(), #{modifier,jdbcType=VARCHAR}, sysdate(), 
      #{isDeleted,jdbcType=CHAR}, #{isDefault,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.WoAssignRuleDO">
    insert into wo_assign_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="woType != null">
        wo_type,
      </if>
      <if test="productItem != null">
        product_item,
      </if>
      <if test="productCategoryValue != null">
        product_category_value,
      </if>
      <if test="firstTypeId != null">
        first_type_id,
      </if>
      <if test="secondTypeId != null">
        second_type_id,
      </if>
      <if test="thirdTypeId != null">
        third_type_id,
      </if>
      <if test="fourTypeId != null">
        four_type_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="isCommon != null">
        is_common,
      </if>
      <if test="commonRule != null">
        common_rule,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="isDefault != null">
        is_default,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="woType != null">
        #{woType,jdbcType=VARCHAR},
      </if>
      <if test="productItem != null">
        #{productItem,jdbcType=VARCHAR},
      </if>
      <if test="productCategoryValue != null">
        #{productCategoryValue,jdbcType=VARCHAR},
      </if>
      <if test="firstTypeId != null">
        #{firstTypeId,jdbcType=VARCHAR},
      </if>
      <if test="secondTypeId != null">
        #{secondTypeId,jdbcType=VARCHAR},
      </if>
      <if test="thirdTypeId != null">
        #{thirdTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fourTypeId != null">
        #{fourTypeId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="isCommon != null">
        #{isCommon,jdbcType=CHAR},
      </if>
      <if test="commonRule != null">
        #{commonRule,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.WoAssignRuleExample" resultType="java.lang.Long">
    select count(*) from wo_assign_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update wo_assign_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.woType != null">
        wo_type = #{record.woType,jdbcType=VARCHAR},
      </if>
      <if test="record.productItem != null">
        product_item = #{record.productItem,jdbcType=VARCHAR},
      </if>
      <if test="record.productCategoryValue != null">
        product_category_value = #{record.productCategoryValue,jdbcType=VARCHAR},
      </if>
      <if test="record.firstTypeId != null">
        first_type_id = #{record.firstTypeId,jdbcType=VARCHAR},
      </if>
      <if test="record.secondTypeId != null">
        second_type_id = #{record.secondTypeId,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdTypeId != null">
        third_type_id = #{record.thirdTypeId,jdbcType=VARCHAR},
      </if>
      <if test="record.fourTypeId != null">
        four_type_id = #{record.fourTypeId,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.isCommon != null">
        is_common = #{record.isCommon,jdbcType=CHAR},
      </if>
      <if test="record.commonRule != null">
        common_rule = #{record.commonRule,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.isDefault != null">
        is_default = #{record.isDefault,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update wo_assign_rule
    set id = #{record.id,jdbcType=BIGINT},
      wo_type = #{record.woType,jdbcType=VARCHAR},
      product_item = #{record.productItem,jdbcType=VARCHAR},
      product_category_value = #{record.productCategoryValue,jdbcType=VARCHAR},
      first_type_id = #{record.firstTypeId,jdbcType=VARCHAR},
      second_type_id = #{record.secondTypeId,jdbcType=VARCHAR},
      third_type_id = #{record.thirdTypeId,jdbcType=VARCHAR},
      four_type_id = #{record.fourTypeId,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      is_common = #{record.isCommon,jdbcType=CHAR},
      common_rule = #{record.commonRule,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      is_default = #{record.isDefault,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.WoAssignRuleDO">
    update wo_assign_rule
    <set>
      <if test="woType != null">
        wo_type = #{woType,jdbcType=VARCHAR},
      </if>
      <if test="productItem != null">
        product_item = #{productItem,jdbcType=VARCHAR},
      </if>
      <if test="productCategoryValue != null">
        product_category_value = #{productCategoryValue,jdbcType=VARCHAR},
      </if>
      <if test="firstTypeId != null">
        first_type_id = #{firstTypeId,jdbcType=VARCHAR},
      </if>
      <if test="secondTypeId != null">
        second_type_id = #{secondTypeId,jdbcType=VARCHAR},
      </if>
      <if test="thirdTypeId != null">
        third_type_id = #{thirdTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fourTypeId != null">
        four_type_id = #{fourTypeId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="isCommon != null">
        is_common = #{isCommon,jdbcType=CHAR},
      </if>
      <if test="commonRule != null">
        common_rule = #{commonRule,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.WoAssignRuleDO">
    update wo_assign_rule
    set wo_type = #{woType,jdbcType=VARCHAR},
      product_item = #{productItem,jdbcType=VARCHAR},
      product_category_value = #{productCategoryValue,jdbcType=VARCHAR},
      first_type_id = #{firstTypeId,jdbcType=VARCHAR},
      second_type_id = #{secondTypeId,jdbcType=VARCHAR},
      third_type_id = #{thirdTypeId,jdbcType=VARCHAR},
      four_type_id = #{fourTypeId,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      is_common = #{isCommon,jdbcType=CHAR},
      common_rule = #{commonRule,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR},
      is_default = #{isDefault,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into wo_assign_rule
    (id, wo_type, product_item, product_category_value, first_type_id, second_type_id, 
      third_type_id, four_type_id, user_name, is_common, common_rule, creator, gmt_created, 
      modifier, gmt_modified, is_deleted, is_default)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.woType,jdbcType=VARCHAR}, #{item.productItem,jdbcType=VARCHAR}, 
        #{item.productCategoryValue,jdbcType=VARCHAR}, #{item.firstTypeId,jdbcType=VARCHAR}, 
        #{item.secondTypeId,jdbcType=VARCHAR}, #{item.thirdTypeId,jdbcType=VARCHAR}, #{item.fourTypeId,jdbcType=VARCHAR}, 
        #{item.userName,jdbcType=VARCHAR}, #{item.isCommon,jdbcType=CHAR}, #{item.commonRule,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}, #{item.isDefault,jdbcType=CHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into wo_assign_rule (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'wo_type'.toString() == column.value">
          #{item.woType,jdbcType=VARCHAR}
        </if>
        <if test="'product_item'.toString() == column.value">
          #{item.productItem,jdbcType=VARCHAR}
        </if>
        <if test="'product_category_value'.toString() == column.value">
          #{item.productCategoryValue,jdbcType=VARCHAR}
        </if>
        <if test="'first_type_id'.toString() == column.value">
          #{item.firstTypeId,jdbcType=VARCHAR}
        </if>
        <if test="'second_type_id'.toString() == column.value">
          #{item.secondTypeId,jdbcType=VARCHAR}
        </if>
        <if test="'third_type_id'.toString() == column.value">
          #{item.thirdTypeId,jdbcType=VARCHAR}
        </if>
        <if test="'four_type_id'.toString() == column.value">
          #{item.fourTypeId,jdbcType=VARCHAR}
        </if>
        <if test="'user_name'.toString() == column.value">
          #{item.userName,jdbcType=VARCHAR}
        </if>
        <if test="'is_common'.toString() == column.value">
          #{item.isCommon,jdbcType=CHAR}
        </if>
        <if test="'common_rule'.toString() == column.value">
          #{item.commonRule,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'is_default'.toString() == column.value">
          #{item.isDefault,jdbcType=CHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>