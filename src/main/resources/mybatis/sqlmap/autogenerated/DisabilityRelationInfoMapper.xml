<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongan.lincoln.dal.mapper.DisabilityRelationInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhongan.lincoln.dal.domain.DisabilityRelationInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
    <result column="claim_hospital_liability_id" jdbcType="BIGINT" property="claimHospitalLiabilityId" />
    <result column="disability_identifiy_agency" jdbcType="VARCHAR" property="disabilityIdentifiyAgency" />
    <result column="disability_code" jdbcType="VARCHAR" property="disabilityCode" />
    <result column="disability_name" jdbcType="VARCHAR" property="disabilityName" />
    <result column="disability_type" jdbcType="VARCHAR" property="disabilityType" />
    <result column="appraisal_standard" jdbcType="VARCHAR" property="appraisalStandard" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_no, claim_hospital_liability_id, disability_identifiy_agency, disability_code, 
    disability_name, disability_type, appraisal_standard, creator, gmt_created, modifier, 
    gmt_modified, is_deleted, remark
  </sql>
  <select id="selectByExample" parameterType="com.zhongan.lincoln.dal.domain.DisabilityRelationInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from claim_hospital_disability_relation_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from claim_hospital_disability_relation_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.zhongan.lincoln.dal.domain.DisabilityRelationInfoDO">
    insert into claim_hospital_disability_relation_info (id, report_no, claim_hospital_liability_id, 
      disability_identifiy_agency, disability_code, 
      disability_name, disability_type, appraisal_standard, 
      creator, gmt_created, modifier, 
      gmt_modified, is_deleted, remark
      )
    values (#{id,jdbcType=BIGINT}, #{reportNo,jdbcType=VARCHAR}, #{claimHospitalLiabilityId,jdbcType=BIGINT}, 
      #{disabilityIdentifiyAgency,jdbcType=VARCHAR}, #{disabilityCode,jdbcType=VARCHAR}, 
      #{disabilityName,jdbcType=VARCHAR}, #{disabilityType,jdbcType=VARCHAR}, #{appraisalStandard,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, sysdate(), #{modifier,jdbcType=VARCHAR}, 
      sysdate(), #{isDeleted,jdbcType=CHAR}, #{remark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.zhongan.lincoln.dal.domain.DisabilityRelationInfoDO">
    insert into claim_hospital_disability_relation_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reportNo != null">
        report_no,
      </if>
      <if test="claimHospitalLiabilityId != null">
        claim_hospital_liability_id,
      </if>
      <if test="disabilityIdentifiyAgency != null">
        disability_identifiy_agency,
      </if>
      <if test="disabilityCode != null">
        disability_code,
      </if>
      <if test="disabilityName != null">
        disability_name,
      </if>
      <if test="disabilityType != null">
        disability_type,
      </if>
      <if test="appraisalStandard != null">
        appraisal_standard,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="true">
        gmt_created,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="true">
        gmt_modified,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="reportNo != null">
        #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimHospitalLiabilityId != null">
        #{claimHospitalLiabilityId,jdbcType=BIGINT},
      </if>
      <if test="disabilityIdentifiyAgency != null">
        #{disabilityIdentifiyAgency,jdbcType=VARCHAR},
      </if>
      <if test="disabilityCode != null">
        #{disabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="disabilityName != null">
        #{disabilityName,jdbcType=VARCHAR},
      </if>
      <if test="disabilityType != null">
        #{disabilityType,jdbcType=VARCHAR},
      </if>
      <if test="appraisalStandard != null">
        #{appraisalStandard,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        sysdate(),
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.zhongan.lincoln.dal.domain.DisabilityRelationInfoExample" resultType="java.lang.Long">
    select count(*) from claim_hospital_disability_relation_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update claim_hospital_disability_relation_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportNo != null">
        report_no = #{record.reportNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimHospitalLiabilityId != null">
        claim_hospital_liability_id = #{record.claimHospitalLiabilityId,jdbcType=BIGINT},
      </if>
      <if test="record.disabilityIdentifiyAgency != null">
        disability_identifiy_agency = #{record.disabilityIdentifiyAgency,jdbcType=VARCHAR},
      </if>
      <if test="record.disabilityCode != null">
        disability_code = #{record.disabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.disabilityName != null">
        disability_name = #{record.disabilityName,jdbcType=VARCHAR},
      </if>
      <if test="record.disabilityType != null">
        disability_type = #{record.disabilityType,jdbcType=VARCHAR},
      </if>
      <if test="record.appraisalStandard != null">
        appraisal_standard = #{record.appraisalStandard,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=CHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update claim_hospital_disability_relation_info
    set id = #{record.id,jdbcType=BIGINT},
      report_no = #{record.reportNo,jdbcType=VARCHAR},
      claim_hospital_liability_id = #{record.claimHospitalLiabilityId,jdbcType=BIGINT},
      disability_identifiy_agency = #{record.disabilityIdentifiyAgency,jdbcType=VARCHAR},
      disability_code = #{record.disabilityCode,jdbcType=VARCHAR},
      disability_name = #{record.disabilityName,jdbcType=VARCHAR},
      disability_type = #{record.disabilityType,jdbcType=VARCHAR},
      appraisal_standard = #{record.appraisalStandard,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
    
      modifier = #{record.modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{record.isDeleted,jdbcType=CHAR},
      remark = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.zhongan.lincoln.dal.domain.DisabilityRelationInfoDO">
    update claim_hospital_disability_relation_info
    <set>
      <if test="reportNo != null">
        report_no = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="claimHospitalLiabilityId != null">
        claim_hospital_liability_id = #{claimHospitalLiabilityId,jdbcType=BIGINT},
      </if>
      <if test="disabilityIdentifiyAgency != null">
        disability_identifiy_agency = #{disabilityIdentifiyAgency,jdbcType=VARCHAR},
      </if>
      <if test="disabilityCode != null">
        disability_code = #{disabilityCode,jdbcType=VARCHAR},
      </if>
      <if test="disabilityName != null">
        disability_name = #{disabilityName,jdbcType=VARCHAR},
      </if>
      <if test="disabilityType != null">
        disability_type = #{disabilityType,jdbcType=VARCHAR},
      </if>
      <if test="appraisalStandard != null">
        appraisal_standard = #{appraisalStandard,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="false">
        gmt_created = sysdate(),
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="true">
        gmt_modified = sysdate(),
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.zhongan.lincoln.dal.domain.DisabilityRelationInfoDO">
    update claim_hospital_disability_relation_info
    set report_no = #{reportNo,jdbcType=VARCHAR},
      claim_hospital_liability_id = #{claimHospitalLiabilityId,jdbcType=BIGINT},
      disability_identifiy_agency = #{disabilityIdentifiyAgency,jdbcType=VARCHAR},
      disability_code = #{disabilityCode,jdbcType=VARCHAR},
      disability_name = #{disabilityName,jdbcType=VARCHAR},
      disability_type = #{disabilityType,jdbcType=VARCHAR},
      appraisal_standard = #{appraisalStandard,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
    
      modifier = #{modifier,jdbcType=VARCHAR},
      gmt_modified = sysdate(),
      is_deleted = #{isDeleted,jdbcType=CHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into claim_hospital_disability_relation_info
    (id, report_no, claim_hospital_liability_id, disability_identifiy_agency, disability_code, 
      disability_name, disability_type, appraisal_standard, creator, gmt_created, modifier, 
      gmt_modified, is_deleted, remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.reportNo,jdbcType=VARCHAR}, #{item.claimHospitalLiabilityId,jdbcType=BIGINT}, 
        #{item.disabilityIdentifiyAgency,jdbcType=VARCHAR}, #{item.disabilityCode,jdbcType=VARCHAR}, 
        #{item.disabilityName,jdbcType=VARCHAR}, #{item.disabilityType,jdbcType=VARCHAR}, 
        #{item.appraisalStandard,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.gmtCreated,jdbcType=TIMESTAMP}, 
        #{item.modifier,jdbcType=VARCHAR}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=CHAR}, 
        #{item.remark,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into claim_hospital_disability_relation_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=BIGINT}
        </if>
        <if test="'report_no'.toString() == column.value">
          #{item.reportNo,jdbcType=VARCHAR}
        </if>
        <if test="'claim_hospital_liability_id'.toString() == column.value">
          #{item.claimHospitalLiabilityId,jdbcType=BIGINT}
        </if>
        <if test="'disability_identifiy_agency'.toString() == column.value">
          #{item.disabilityIdentifiyAgency,jdbcType=VARCHAR}
        </if>
        <if test="'disability_code'.toString() == column.value">
          #{item.disabilityCode,jdbcType=VARCHAR}
        </if>
        <if test="'disability_name'.toString() == column.value">
          #{item.disabilityName,jdbcType=VARCHAR}
        </if>
        <if test="'disability_type'.toString() == column.value">
          #{item.disabilityType,jdbcType=VARCHAR}
        </if>
        <if test="'appraisal_standard'.toString() == column.value">
          #{item.appraisalStandard,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_created'.toString() == column.value">
          #{item.gmtCreated,jdbcType=TIMESTAMP}
        </if>
        <if test="'modifier'.toString() == column.value">
          #{item.modifier,jdbcType=VARCHAR}
        </if>
        <if test="'gmt_modified'.toString() == column.value">
          #{item.gmtModified,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_deleted'.toString() == column.value">
          #{item.isDeleted,jdbcType=CHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>