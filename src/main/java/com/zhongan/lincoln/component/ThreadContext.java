package com.zhongan.lincoln.component;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.zhongan.lincoln.model.dto.smart.SmartUserInfoDTO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/30 16:47
 * @see <a herf="https://github.com/alibaba/transmittable-thread-local"/>
 */
public class ThreadContext {

    private final static TransmittableThreadLocal<HttpServletRequest> REQUEST = new TransmittableThreadLocal<>();
    private final static TransmittableThreadLocal<HttpServletResponse> RESPONSE = new TransmittableThreadLocal<>();
    private final static TransmittableThreadLocal<String> OPERATOR = new TransmittableThreadLocal<>();
    private final static TransmittableThreadLocal<Map> CONTEXT = new TransmittableThreadLocal<>();
    private final static TransmittableThreadLocal<SmartUserInfoDTO> SMARTUSER = new TransmittableThreadLocal<>();
    private final static TransmittableThreadLocal<String> TPA_OPERATOR = new TransmittableThreadLocal<>();


    public static TransmittableThreadLocal<HttpServletRequest> getRequest() {
        return REQUEST;
    }

    public static TransmittableThreadLocal<HttpServletResponse> getResponse() {
        return RESPONSE;
    }

    public static TransmittableThreadLocal<String> getOperator() {
        return OPERATOR;
    }

    public static TransmittableThreadLocal<Map> getContext() {
        return CONTEXT;
    }

    public static TransmittableThreadLocal<SmartUserInfoDTO> getSmartUserInfo() {
        return SMARTUSER;
    }

    public static TransmittableThreadLocal<String> getTpaOperator() {
        return TPA_OPERATOR;
    }

    public static void removeRequest() {
        ThreadContext.REQUEST.remove();
    }

    public static void removeResponse() {
        ThreadContext.RESPONSE.remove();
    }

    public static void removeOperator() {
        ThreadContext.OPERATOR.remove();
    }

    public static void removeContext() {
        ThreadContext.CONTEXT.remove();
    }

    public static void removeSmartUserInfo() {
        ThreadContext.SMARTUSER.remove();
    }

    public static void removeTpaOperator() {
        ThreadContext.TPA_OPERATOR.remove();
    }

    public static void clear() {
        removeRequest();
        removeResponse();
        removeOperator();
        removeContext();
        removeSmartUserInfo();
        removeTpaOperator();
    }

}
