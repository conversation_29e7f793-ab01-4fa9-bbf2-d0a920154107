package com.zhongan.lincoln.component.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhongan.ec.util.FastJsonUtil;
import com.zhongan.hacksaw.model.common.constant.ZaConsts;
import com.zhongan.hacksaw.model.xwork.enums.InteractiveTypeEnum;
import com.zhongan.hacksaw.model.xwork.request.CallBackToXworkReq;
import com.zhongan.hacksaw.model.xwork.request.XworkCallBackData;
import com.zhongan.hacksaw.model.xwork.request.XworkReceiveMsgReq;
import com.zhongan.lincoln.common.util.DateTimeUtil;
import com.zhongan.lincoln.config.properties.KafkaProperties;
import com.zhongan.lincoln.dal.mongo.MongodbRepository;
import com.zhongan.lincoln.model.dto.mongo.XworkInteractiveRecordDTO;
import com.zhongan.msg.api.bootstrap.MqClient;
import com.zhongan.msg.api.common.SerializerTypeEnum;
import com.zhongan.msg.api.consumer.AckAction;
import com.zhongan.msg.api.consumer.MqMessageListener;
import com.zhongan.msg.api.consumer.ReceiveRecord;
import com.zhongan.msg.api.consumer.binding.ConsumerConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: mch
 * 企业码变更消息通知
 * @Date: 2024/12/02 11:14
 */
@Slf4j
@Component
public class XworkRobotReplyConsumer implements InitializingBean {

    @Resource
    private KafkaProperties kafkaProperties;
    @Resource
    private MongodbRepository<XworkInteractiveRecordDTO> interactiveRecordRepository;

    //HUMAN_SERVER("humanServer", "坐席人工打断"),AI_HUMAN_SERVER("aiHumanServer", "AI人工打断"),
    private static final List<String> CLOSE_OPTSOURCE = Arrays.asList("humanServer");


    @Override
    public void afterPropertiesSet() {
        ConsumerConfig consumerConfig = new ConsumerConfig(kafkaProperties.getXworkRobotReplyServer(), kafkaProperties.getXworkRobotReplyTopic(),
                new MqMessageListener<String>() {
                    @Override
                    public AckAction onMessage(ReceiveRecord<String> record) {
                        String message = record.getMessage();
                        if (StringUtils.isBlank(message) || "null".equals(message) || !FastJsonUtil.isJsonObjectString(message)) {
                            return AckAction.Commit;
                        }
                        try {
                            JSONObject messageJson = JSONObject.parseObject(message);
                            JSONObject dataJson = messageJson.getJSONObject("data");
                            if (!"CLOSE".equals(dataJson.getString("aiStatus")) || StringUtils.isBlank(dataJson.getString("optSource"))) {
                                return AckAction.Commit;
                            }
                            //#{userId}_#{externalUserId}
                            String sessionId = dataJson.getString("userId") + ZaConsts.Sym.UNDERSCORE + dataJson.getString("externalUserId");
                            List<XworkInteractiveRecordDTO> recordDTOS = queryInteractiveRecord(sessionId, DateTimeUtil.addMinute(new Date(), -120));
                            if (CollectionUtils.isEmpty(recordDTOS)) {
                                return AckAction.Commit;
                            }
                            log.info("XworkRobotReplyConsumer,sessionId={},message={}", sessionId, JSON.toJSONString(message));
                            if (!CLOSE_OPTSOURCE.contains(dataJson.getString("optSource"))) {
                                return AckAction.Commit;
                            }
                            //先判断是否有转人工标识，没有的话插入一条记录
                            List<XworkInteractiveRecordDTO> manualRecordDTOS = recordDTOS.stream().filter(a -> StringUtils.isNotBlank(a.getResultJson())
                                    && a.getResultJson().contains("isManualServer")).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(manualRecordDTOS)) {
                                return AckAction.Commit;
                            }
                            recordDTOS = recordDTOS.stream().filter(a -> InteractiveTypeEnum.XWORK.getCode().equals(a.getType())
                                    || InteractiveTypeEnum.IM.getCode().equals(a.getType())).collect(Collectors.toList());
                            XworkReceiveMsgReq msgReq = JSONObject.parseObject(recordDTOS.get(recordDTOS.size() - 1).getReqJson(), XworkReceiveMsgReq.class);
                            saveManualInteractiveRecord(dataJson.getString("optSourceName"), msgReq);
                        } catch (Exception e) {
                            log.error("kafka消费数据异常", e);
                        }
                        return AckAction.Commit;
                    }

                    @Override
                    public boolean isRedeliver(ReceiveRecord<String> record) {
                        return false;
                    }
                });
        consumerConfig.setBatchSize(10);
        consumerConfig.setNumThreads(10);
        consumerConfig.setSerializer(SerializerTypeEnum.STRING);
        MqClient.buildConsumer(consumerConfig);
    }

    public void testXworkReply(String message) {
        try {
            JSONObject messageJson = JSONObject.parseObject(message);
            JSONObject dataJson = messageJson.getJSONObject("data");
            if (!"CLOSE".equals(dataJson.getString("aiStatus")) || StringUtils.isBlank(dataJson.getString("optSource"))) {
                return;
            }
            //#{userId}_#{externalUserId}
            String sessionId = dataJson.getString("userId") + ZaConsts.Sym.UNDERSCORE + dataJson.getString("externalUserId");
            List<XworkInteractiveRecordDTO> recordDTOS = queryInteractiveRecord(sessionId, DateTimeUtil.addMinute(new Date(), -120));
            if (CollectionUtils.isEmpty(recordDTOS)) {
                return;
            }
            log.info("testXworkReply,sessionId={},message={}", sessionId, JSON.toJSONString(message));
            if (!CLOSE_OPTSOURCE.contains(dataJson.getString("optSource"))) {
                return;
            }
            //先判断是否有转人工标识，没有的话插入一条记录
            List<XworkInteractiveRecordDTO> manualRecordDTOS = recordDTOS.stream().filter(a -> StringUtils.isNotBlank(a.getResultJson())
                    && a.getResultJson().contains("isManualServer")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(manualRecordDTOS)) {
                return;
            }
            recordDTOS = recordDTOS.stream().filter(a -> InteractiveTypeEnum.XWORK.getCode().equals(a.getType())
                    || InteractiveTypeEnum.IM.getCode().equals(a.getType())).collect(Collectors.toList());
            XworkReceiveMsgReq msgReq = JSONObject.parseObject(recordDTOS.get(recordDTOS.size() - 1).getReqJson(), XworkReceiveMsgReq.class);
            saveManualInteractiveRecord(dataJson.getString("optSourceName"), msgReq);
        } catch (Exception e) {
            log.error("kafka消费数据异常", e);
        }
    }

    private List<XworkInteractiveRecordDTO> queryInteractiveRecord(String sessionId, Date lastDate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("sessionId").is(sessionId));
        query.addCriteria(Criteria.where("status").is(ZaConsts.Num.ZERO));
        if (lastDate != null) {
            query.addCriteria(Criteria.where("gmtCreated").gte(lastDate));
        }
        query.with(Sort.by(new Sort.Order(Sort.Direction.ASC, "gmtCreated")));
        return interactiveRecordRepository.findByQuery(query, XworkInteractiveRecordDTO.class);
    }

    private void saveManualInteractiveRecord(String noticeMessage, XworkReceiveMsgReq msgReq) {
        String sessionId = msgReq.getSessionId();
        if (msgReq == null || StringUtils.isBlank(sessionId)) {
            return;
        }
        CallBackToXworkReq toXworkReq = new CallBackToXworkReq();
        toXworkReq.setResultCode("200");
        XworkCallBackData backData = new XworkCallBackData();
        backData.setIsManualServer(ZaConsts.Y);
        backData.setNoticeMessage(noticeMessage);
        toXworkReq.setData(backData);
        JSONObject callbackJson = JSONObject.parseObject(JSONObject.toJSONString(toXworkReq));

        XworkInteractiveRecordDTO mongoDTO = new XworkInteractiveRecordDTO();
        mongoDTO.setSessionId(sessionId);
        if (msgReq.getVariables() != null) {
            mongoDTO.setChannelSource(msgReq.getVariables().getChannelSource());
        }
        mongoDTO.setReqJson(JSONObject.toJSONString(msgReq));
        mongoDTO.setResultJson(callbackJson.toJSONString());
        mongoDTO.setStatus(ZaConsts.Num.ZERO);
        mongoDTO.setType(InteractiveTypeEnum.XWORK.getCode());
        mongoDTO.setGmtCreated(new Date());
        mongoDTO.setGmtModified(new Date());
        mongoDTO.setIsDeleted(ZaConsts.N);
        interactiveRecordRepository.save(mongoDTO);
        log.info("XworkRobotReplyConsumer,sessionId={},save={}", sessionId, noticeMessage);
    }


}
