package com.zhongan.lincoln.component;

import com.zhongan.lincoln.common.util.StreamUtil;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/5/20 15:26
 */
@Component
public class SpringContextHolder implements ApplicationContextAware {

	private static ApplicationContext applicationContext;

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) {
		SpringContextHolder.applicationContext = applicationContext;
	}

	/**
	 * 得到Spring 上下文环境
	 *
	 * @return
	 */
	public static ApplicationContext getApplicationContext() {
		checkApplicationContext();
		return applicationContext;
	}

	/**
	 * 通过Spring Bean name 得到Bean
	 *
	 * @param name
	 *            bean 上下文定义名称
	 */
	@SuppressWarnings("unchecked")
	public static <T> T getBean(String name) {
		checkApplicationContext();
		return (T) applicationContext.getBean(name);
	}

	public static <T> T getBean(String name, Class<T> requiredType) {
		checkApplicationContext();
		return (T) applicationContext.getBean(name, requiredType);
	}

	/**
	 * 通过类型得到Bean
	 *
	 * @param clazz
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T getBean(Class<T> clazz) {
		checkApplicationContext();
		return applicationContext.getBean(clazz);
	}

    /**
     * 获取所有该类型Bean
     * @param clazz
     * @return
     * @param <T>
     */
	public static <T> List<T> getBeans(Class<T> clazz) {
		checkApplicationContext();
		Map<String, T> beanMap = applicationContext.getBeansOfType(clazz);
		if (MapUtils.isEmpty(beanMap)){
			return Collections.emptyList();
		}
		return StreamUtil.of(beanMap.values()).collect(Collectors.toList());
	}


	private static void checkApplicationContext() {
		if (applicationContext == null) {
			throw new IllegalStateException("applicationContext未注入,请在application-context.xml中定义SpringContextHolder");
		}
	}

}
