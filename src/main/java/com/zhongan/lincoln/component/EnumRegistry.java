package com.zhongan.lincoln.component;

import com.alibaba.fastjson.JSONObject;
import com.zhongan.hacksaw.model.common.anno.ExposedEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.type.ClassMetadata;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.core.type.filter.TypeFilter;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * 扫描BASE_PACKAGES的被 {@link ExposedEnum} 标注的枚举类
 *
 * <AUTHOR>
 */
@Component
public class EnumRegistry implements ResourceLoaderAware, EnvironmentAware, InitializingBean {

    static final String[] BASE_PACKAGES = {
            "com.zhongan.lincoln.common.enums", "com.zhongan.hacksaw.model.**.enums"
    };

    Environment environment;

    ResourceLoader resourceLoader;

    Map<String, JSONObject> cachedEnumRegistry;

    public JSONObject findEnum(String key) {
        return cachedEnumRegistry.get(key);
    }

    public Map<String, JSONObject> findEnums(String... keys) {
        if (keys.length == 0) {
            return Collections.emptyMap();
        }
        Map<String, JSONObject> result = new LinkedHashMap<>();
        for (String key : keys) {
            JSONObject e = findEnum(key);
            if (e != null) {
                result.put(key, e);
            }
        }
        return result;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            Map<String, JSONObject> enumMap = new HashMap<>();
            for (String basePackage : BASE_PACKAGES) {
                scan(enumMap, basePackage);
            }
            cachedEnumRegistry = Collections.unmodifiableMap(enumMap);
        } catch (Exception e) {
            // ignore
        }
    }

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    @Override
    public void setResourceLoader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    protected void scan(Map<String, JSONObject> enumMap, String basePackage) throws Exception {
        ClassPathScanningCandidateComponentProvider provider = new ClassPathScanningCandidateComponentProvider(false);
        provider.setResourceLoader(resourceLoader);
        provider.setEnvironment(environment);
        provider.addIncludeFilter(new EnumTypeFilter());
        Set<BeanDefinition> beanDefinitions = provider.findCandidateComponents(basePackage);
        for (BeanDefinition bd : beanDefinitions) {
            String beanClassName = bd.getBeanClassName();
            Class<?> cls = ClassUtils.resolveClassName(beanClassName, null);

            ExposedEnum exposedEnum = cls.getAnnotation(ExposedEnum.class);
            if (exposedEnum == null) {
                continue;
            }

            String exposedName = exposedEnum.value();
            if (exposedName.isEmpty()) {
                exposedName = cls.getSimpleName();
            }

            String keyName = StringUtils.defaultString(exposedEnum.keyName(), "code");
            String valueName = StringUtils.defaultString(exposedEnum.valueName(), "value");

            Set<Object> hiddenEnumConstants = new HashSet<>();
            Field[] declaredFields = cls.getDeclaredFields();
            for (Field f : declaredFields) {
                if (f.isEnumConstant() &&
                        f.isAnnotationPresent(ExposedEnum.EFiled.class) &&
                        f.getAnnotation(ExposedEnum.EFiled.class).hidden()) {
                    hiddenEnumConstants.add(f.get(null));
                }
            }

            Object[] enumConstants = cls.getEnumConstants();
            List<JSONObject> dictList = new ArrayList<>();
            JSONObject dictObj = new JSONObject();
            for (Object enumConstant : enumConstants) {
                if (hiddenEnumConstants.contains(enumConstant)) {
                    continue;
                }
                JSONObject fieldJson = resolveEnum((Enum<?>) enumConstant);
                dictList.add(fieldJson);
                if (fieldJson.containsKey(keyName) && fieldJson.containsKey(valueName)) {
                    dictObj.put(fieldJson.getString(keyName), fieldJson.get(valueName));
                }
            }

            JSONObject exposedValue = new JSONObject();
            exposedValue.put("dictList", dictList);
            exposedValue.put("dictObj", dictObj);
            enumMap.put(exposedName, exposedValue);
        }
    }

    static JSONObject resolveEnum(Enum<?> e) throws IllegalAccessException {
        JSONObject enumFieldJson = new JSONObject();
        Class<?> enumClass = e.getClass();
        ExposedEnum exposedEnum = enumClass.getAnnotation(ExposedEnum.class);
        boolean displayEnumName = exposedEnum.displayEnumName();
        Field[] declaredFields = enumClass.getDeclaredFields();
        for (Field f : declaredFields) {
            if (Modifier.isStatic(f.getModifiers())) {
                continue;
            }

            ExposedEnum.EFiled exposedField = f.getAnnotation(ExposedEnum.EFiled.class);
            if (exposedField != null && exposedField.hidden()) {
                continue;
            }

            f.setAccessible(true);
            Object value = f.get(e);

            if (displayEnumName) {
                enumFieldJson.put("enumName", e.name());
            }

            String fieldName = f.getName();
            if (exposedField == null) {
                enumFieldJson.put(fieldName, value);
                continue;
            }

            if (!exposedField.override()) {
                enumFieldJson.put(fieldName, value);
            }

            if (exposedField.value().isEmpty()) {
                enumFieldJson.put(fieldName, value);
            } else {
                enumFieldJson.put(exposedField.value(), value);
            }
        }

        return enumFieldJson;
    }

    static class EnumTypeFilter implements TypeFilter {
        @Override
        public boolean match(MetadataReader metadataReader, MetadataReaderFactory metadataReaderFactory) throws IOException {
            ClassMetadata classMetadata = metadataReader.getClassMetadata();
            Class<?> type = ClassUtils.resolveClassName(classMetadata.getClassName(), null);
            return type.isEnum() && metadataReader.getAnnotationMetadata().hasAnnotation(ExposedEnum.class.getName());
        }
    }

}
