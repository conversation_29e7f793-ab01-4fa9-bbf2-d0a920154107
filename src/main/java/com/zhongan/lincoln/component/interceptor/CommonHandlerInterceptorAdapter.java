package com.zhongan.lincoln.component.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.zhongan.ec.util.IpUtil;
import com.zhongan.hacksaw.model.common.bean.Consts;
import com.zhongan.hacksaw.model.common.constant.ZaConsts;
import com.zhongan.lincoln.common.constant.WeComConstant;
import com.zhongan.lincoln.common.toolkit.sso.SsoSession;
import com.zhongan.lincoln.component.ThreadContext;
import com.zhongan.lincoln.manager.external.WeComManager;
import com.zhongan.lincoln.model.dto.smart.SmartUserInfoDTO;
import com.zhongan.sso.client.SsoUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.Enumeration;

import static com.zhongan.hacksaw.model.common.bean.Consts.HEADER_OPERATOR;
import static com.zhongan.lincoln.common.constant.SmartConstants.SMART_USER_INFO;


@Slf4j
@Component
@RefreshScope
public class CommonHandlerInterceptorAdapter extends HandlerInterceptorAdapter {

    /**
     * 参数级别
     */
    @Value("${za.log.level.param:ERROR}")
    private String logLevel;
    @Resource
    private WeComManager weComManager;

    private void printRequestInfo(HttpServletRequest request) {
        String traceId = StringUtils.defaultString(request.getHeader(Consts.TRACE_ID), RandomStringUtils.randomAlphabetic(24));
        MDC.put(Consts.TRACE_ID, traceId);

        request.setAttribute("beginTimeForLog", System.currentTimeMillis());
        if (Level.INFO.toString().equalsIgnoreCase(logLevel)) {
            StringBuilder sb = new StringBuilder();
            Enumeration<String> itera = request.getParameterNames();
            while (itera.hasMoreElements()) {
                String name = itera.nextElement();
                if (!"pic".equals(name)) {
                    sb.append(name + ":" + request.getParameter(name) + ",");
                }
            }
            log.info("===> {} {} {} ({})", request.getMethod(), request.getRequestURI(), request.getHeader(Consts.IP), sb.toString());
        } else {
            log.info("===> {} {} {}", request.getMethod(), request.getRequestURI(), request.getHeader(Consts.IP));
        }
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 打印请求信息
        printRequestInfo(request);
        // 获取当前用户
        SsoUser ssoUser = SsoSession.getCurrentUser(request);
        if (ssoUser != null) {
            ThreadContext.getOperator().set(ssoUser.getUsername());
        } else {
            String operator = request.getHeader(HEADER_OPERATOR);
            if (StringUtils.isNotBlank(operator)) {
                ThreadContext.getOperator().set(operator);
            }
        }
        //获取smart用户信息
        String smartUserInfo = request.getHeader(SMART_USER_INFO);
        if (StringUtils.isNotBlank(smartUserInfo)) {
            ThreadContext.getSmartUserInfo().set(
                    JSONObject.parseObject(URLDecoder.decode(smartUserInfo, "UTF-8"), SmartUserInfoDTO.class));
        }
        //获取企业微信用户信息
        String corpToken = request.getHeader(WeComConstant.CORP_HEADER_NAME);
        if (StringUtils.isNotBlank(corpToken) && StringUtils.isBlank(ThreadContext.getOperator().get())) {
            String user = weComManager.getAndRefreshToken(corpToken);
            ThreadContext.getOperator().set(user);
        }
        //获取TPA操作人 - 其为 tpa_employee_info.employee_account
        String tpaOperator = request.getHeader(ZaConsts.Http.TPA_HEADER_OPERATOR);
        if (StringUtils.isNotBlank(tpaOperator)) {
            ThreadContext.getTpaOperator().set(tpaOperator);
        }

        // 返回跟踪信息
        response.addHeader(Consts.TRACE_ID, MDC.get(Consts.TRACE_ID));
        response.addHeader(Consts.IP, IpUtil.getLocalIp());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        super.afterCompletion(request, response, handler, ex);

        long consumedTime = System.currentTimeMillis() - (Long) request.getAttribute("beginTimeForLog");
        log.info("<=== {} {}ms", request.getRequestURI(), consumedTime);
        MDC.clear();
        ThreadContext.clear();
    }
}
