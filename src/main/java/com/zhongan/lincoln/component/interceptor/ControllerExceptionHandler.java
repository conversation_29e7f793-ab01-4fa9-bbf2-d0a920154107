package com.zhongan.lincoln.component.interceptor;

import com.alibaba.fastjson.JSON;
import com.zhongan.ec.util.exception.BizException;
import com.zhongan.ec.util.exception.NotIgnoredException;
import com.zhongan.hacksaw.model.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.io.PrintWriter;

import static org.apache.commons.lang3.StringUtils.EMPTY;

/**
 * <AUTHOR>
 * @date 2018/7/17 15:38
 */
@Slf4j
@ControllerAdvice
public class ControllerExceptionHandler {


    @ExceptionHandler(BizException.class)
    public void handleBizException(HttpServletRequest request, HttpServletResponse response, BizException e) {
        log.warn("za-lincoln bizException. url:{}, resultCode:{}, resultMessage:{}", request.getRequestURI(), e.getCode(), e.getMessage(), e);
        Result result = Result.failed(e.getMessage(), e.getCode());
        responseJsonResult(result, response);
    }

    @ExceptionHandler(com.zhongan.hacksaw.model.common.exception.BizException.class)
    public void handleBizException(HttpServletRequest request, HttpServletResponse response, com.zhongan.hacksaw.model.common.exception.BizException e) {
        log.warn("za-lincoln bizException. url:{}, resultCode:{}, resultMessage:{}", request.getRequestURI(), e.getCode(), e.getMessage(), e);
        Result result = Result.failed(e.getMessage(), e.getCode());
        responseJsonResult(result, response);
    }

    @ExceptionHandler(NotIgnoredException.class)
    public void handleNotIgnoredException(HttpServletRequest request, HttpServletResponse response, NotIgnoredException e) {
        log.warn("za-lincoln NotIgnoredException. url:{}, resultCode:{}, resultMessage:{}", request.getRequestURI(), e.getCode(), e.getMessage(), e);
        Result result = Result.failed(e.getMessage(), e.getCode());
        responseJsonResult(result, response);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public void handleArgumentException(HttpServletRequest request, HttpServletResponse response, MethodArgumentNotValidException e) {
        String errorMessage = e.getBindingResult().getAllErrors().get(0).getDefaultMessage();   // fast fail模式下取第一个错误信息即可
        log.info("za-lincoln methodArgumentNotValidException. url:{}, resultMessage:{}", request.getRequestURI(), errorMessage);
        Result result = Result.failed(errorMessage);
        responseJsonResult(result, response);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public void handleConstraintViolationException(HttpServletRequest request, HttpServletResponse response, ConstraintViolationException e) {
        e.getConstraintViolations().stream().findFirst().ifPresent(p -> {
            String errorMessage = p.getMessage();
            log.info("za-lincoln methodArgumentNotValidException. url:{}, resultMessage:{}", request.getRequestURI(), errorMessage);
            Result result = Result.failed(errorMessage);
            responseJsonResult(result, response);
        });

    }

    @ExceptionHandler(Exception.class)
    public void handleException(HttpServletRequest request, HttpServletResponse response, Exception e) {
        log.warn("za-lincoln exception. url:" + request.getRequestURI(), e);
        Result result = Result.failed("系统繁忙，请稍后重试！"
                + (StringUtils.contains(request.getRequestURI(), "kiss") ? request.getRequestURI() : EMPTY));
        responseJsonResult(result, response);
    }

    public static void responseJsonResult(Result result, HttpServletResponse response) {
        if (null == result || null == response) {
            return;
        }
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        try (PrintWriter wr = response.getWriter()) {
            wr.write(JSON.toJSONString(result));
            wr.flush();
        } catch (IOException e) {
            log.warn("response writer error:{}", e);
        }
    }
}
