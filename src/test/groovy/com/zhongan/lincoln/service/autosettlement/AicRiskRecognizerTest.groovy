package com.zhongan.lincoln.service.autosettlement

import com.zhongan.core.policy.constants.CertificateTypeEnum
import com.zhongan.core.policy.dto.PolicyCustomerDTO
import com.zhongan.core.policy.dto.PolicyDTO
import com.zhongan.ec.util.wizard.opt.ZaDate
import com.zhongan.hacksaw.model.deadpool.enums.CompensateTypeEnum
import com.zhongan.hacksaw.model.zbxintelli.dto.ZbxIntelliRecordResult
import com.zhongan.hacksaw.model.zbxintelli.enums.ZbxQueryRtCode
import com.zhongan.hacksaw.model.zbxintelli.enums.ZbxRule
import com.zhongan.lincoln.config.properties.AicProperties
import com.zhongan.lincoln.config.properties.BizProperties
import com.zhongan.lincoln.dal.domain.AutoSettlementRiskInfoDO
import com.zhongan.lincoln.dal.domain.BillOcrCompareRecordDO
import com.zhongan.lincoln.dal.domain.CargoClaimDO
import com.zhongan.lincoln.dal.domain.ClaimHospitalDO
import com.zhongan.lincoln.dal.domain.LiabilitySettlementDO
import com.zhongan.lincoln.dal.domain.ZbxIntelliRecordDO
import com.zhongan.lincoln.dal.repository.AutoSettlementRiskInfoRepository
import com.zhongan.lincoln.dal.repository.BillOcrCompareRecordRepository
import com.zhongan.lincoln.dal.repository.CargoClaimRepository
import com.zhongan.lincoln.dal.repository.LiabilitySettlementRepository
import com.zhongan.lincoln.manager.core.NewPolicyManager
import com.zhongan.lincoln.manager.external.ZbxIntelliManager
import com.zhongan.lincoln.model.dto.autosettlement.AicHandleContext
import com.zhongan.lincoln.service.claim.ClaimGangFraudService
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.AfterEach
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import static com.zhongan.hacksaw.model.zbxintelli.enums.ZbxInsuranceType.ACCIDENT_PERSONAL
import static com.zhongan.hacksaw.model.zbxintelli.enums.ZbxInsuranceType.HEALTH_MEDICAL
import static com.zhongan.lincoln.common.constant.ReportClaimVerifyConstant.FRACTURE_RISK_KISS_INFO
import static org.mockito.Mockito.*
import static org.junit.jupiter.api.Assertions.*

/**
 *
 * <AUTHOR>
 * @since 2024/5/8
 */
class AicRiskRecognizerTest {
    @Mock
    AicProperties aicProperties
    @Mock
    BizProperties bizProperties
    @Mock
    NewPolicyManager newPolicyManager
    @Mock
    ZbxIntelliManager zbxIntelliManager
    @Mock
    ClaimGangFraudService gangFraudService
    @Mock
    AutoSettlementRiskInfoRepository riskInfoRepository
    @Mock
    BillOcrCompareRecordRepository billOcrCompareRecordRepository
    @Mock
    CargoClaimRepository cargoClaimRepository
    @Mock
    LiabilitySettlementRepository liabilitySettlementRepository
    @InjectMocks
    AicRiskRecognizer aicRiskRecognizer

    AutoCloseable _mockitoMock

    @BeforeEach
    void setUp() {
        _mockitoMock = MockitoAnnotations.openMocks(this)
    }

    @AfterEach
    void tearDown() {
        if (_mockitoMock) {
            _mockitoMock.close()
        }
    }

    @Test
    void testRecognizeEarly() {
        def spyAicRiskRecognizer = spy(aicRiskRecognizer)

        doNothing().when(spyAicRiskRecognizer).recognizeGangFraud(any())
        doNothing().when(spyAicRiskRecognizer).recognizeZbx(any())

        def mockedContext = mock(AicHandleContext)
        spyAicRiskRecognizer.recognizeEarly(mockedContext)

        verify(spyAicRiskRecognizer, times(1)).recognizeGangFraud(any())
        verify(spyAicRiskRecognizer, times(1)).recognizeZbx(any())
    }

    @Test
    void testRecognizeGangFraud() {

    }

    @Test
    void testRecognizeZbx() {
        def spyAicRiskRecognizer = spy(aicRiskRecognizer)

        when(newPolicyManager.findPolicyWithCustomInfo(any()))
                .thenReturn(new PolicyDTO(insurant: new PolicyCustomerDTO(certificateNo: '123456', certificateType: CertificateTypeEnum.I)))

        def accidentZbxRecord1 = mock(ZbxIntelliRecordDO)
        when(zbxIntelliManager.queryAllIntelliRecord(eq(ACCIDENT_PERSONAL), any(), any())).thenReturn([accidentZbxRecord1])

        def healthZbxRecord1 = mock(ZbxIntelliRecordDO)
        when(zbxIntelliManager.queryAllIntelliRecord(eq(HEALTH_MEDICAL), any(), any())).thenReturn([healthZbxRecord1])

        when(zbxIntelliManager.queryAllInsuranceIntelliRecord(any(), any())).thenReturn([accidentZbxRecord1, healthZbxRecord1])

        def context = mock(AicHandleContext)
        when(context.getCargoClaim()).thenReturn(new CargoClaimDO(reportNo: 'CN123'))
        def mockedBill1 = mock(ClaimHospitalDO)
        when(context.reloadClaimHospitalList()).thenReturn([mockedBill1])

        def limit = "3000"
        when(aicProperties.getZbxRiskBillTotalAmountLimit()).thenReturn(limit)

        // totalAmount <= 3000
        when(mockedBill1.getBillAmount()).thenReturn('3000')
        spyAicRiskRecognizer.recognizeZbx(context)
        verify(newPolicyManager, never()).findPolicyWithCustomInfo(any())

        // totalAmount > 3000
        when(mockedBill1.getBillAmount()).thenReturn('3001')

        // zbx not found
        when(accidentZbxRecord1.getQueryStatus()).thenReturn(ZbxQueryRtCode.NOT_FOUND)
        when(healthZbxRecord1.getQueryStatus()).thenReturn(ZbxQueryRtCode.NOT_FOUND)
        spyAicRiskRecognizer.recognizeZbx(context)
        verify(newPolicyManager, times(1)).findPolicyWithCustomInfo(any())
        verify(zbxIntelliManager, never()).queryAllIntelliRecord(eq(ACCIDENT_PERSONAL), any(), any())
        verify(zbxIntelliManager, never()).queryAllIntelliRecord(eq(HEALTH_MEDICAL), any(), any())
        verify(zbxIntelliManager, times(1)).queryAllInsuranceIntelliRecord(any(), any())
        verify(spyAicRiskRecognizer, never()).hitPolicyZbxRisk(any(), any())
        verify(spyAicRiskRecognizer, never()).hitClaimZbxRisk(any(), any())

        // zbx success
        when(accidentZbxRecord1.getQueryStatus()).thenReturn(ZbxQueryRtCode.SUCCESS)
        when(accidentZbxRecord1.getRuleResults()).thenReturn([new ZbxIntelliRecordResult.RuleResult()])
        when(healthZbxRecord1.getQueryStatus()).thenReturn(ZbxQueryRtCode.SUCCESS)
        when(healthZbxRecord1.getRuleResults()).thenReturn([new ZbxIntelliRecordResult.RuleResult()])
        // hit
        doReturn(true).when(spyAicRiskRecognizer).hitPolicyZbxRisk(any(), any())
        doReturn(true).when(spyAicRiskRecognizer).hitClaimZbxRisk(any(), any())
        spyAicRiskRecognizer.recognizeZbx(context)
        verify(spyAicRiskRecognizer, atLeast(1)).hitPolicyZbxRisk(any(), any())
        verify(spyAicRiskRecognizer, atLeast(1)).hitClaimZbxRisk(any(), any())
        verify(riskInfoRepository, times(2)).addRiskIfNotExists(any())

        // non hit
        reset(spyAicRiskRecognizer, riskInfoRepository)
        doReturn(false).when(spyAicRiskRecognizer).hitPolicyZbxRisk(any(), any())
        doReturn(false).when(spyAicRiskRecognizer).hitClaimZbxRisk(any(), any())
        spyAicRiskRecognizer.recognizeZbx(context)
        verify(spyAicRiskRecognizer, atLeast(1)).hitPolicyZbxRisk(any(), any())
        verify(spyAicRiskRecognizer, atLeast(1)).hitClaimZbxRisk(any(), any())
        verify(riskInfoRepository, never()).addRiskIfNotExists(any())
    }

    @Test
    void testHitPolicyZbxRisk() {
        def cargoClaimDO = mock(CargoClaimDO.class);
        def ruleResult = mock(ZbxIntelliRecordResult.RuleResult)
        def ruleData = mock(ZbxIntelliRecordResult.RuleData)
        when(ruleResult.getData()).thenReturn([ruleData])
        when(ruleData.getStartTime()).thenReturn("2022-01-10")
        when(ruleData.getEndTime()).thenReturn("2022-01-20")

        when(aicProperties.getZbxPolicyRiskNames()).thenReturn([ZbxRule.YWG_ID_02.desc])

        // invalid rule
        when(ruleResult.getRuleCode()).thenReturn("invalid_code")
        assertFalse(aicRiskRecognizer.hitPolicyZbxRisk(cargoClaimDO, ruleResult))

        // non risk
        when(ruleResult.getRuleCode()).thenReturn(ZbxRule.C_ID_01.code)
        assertFalse(aicRiskRecognizer.hitPolicyZbxRisk(cargoClaimDO, ruleResult))

        // risk
        when(ruleResult.getRuleCode()).thenReturn(ZbxRule.YWG_ID_02.code)

        // match
        when(cargoClaimDO.getAccidentDate()).thenReturn(ZaDate.of("2022-01-15 12:00:00").oldDate().get())
        assertTrue(aicRiskRecognizer.hitPolicyZbxRisk(cargoClaimDO, ruleResult))

        // non match
        when(cargoClaimDO.getAccidentDate()).thenReturn(ZaDate.of("2022-01-05 12:00:00").oldDate().get())
        assertFalse(aicRiskRecognizer.hitPolicyZbxRisk(cargoClaimDO, ruleResult))

        // bound match
        when(cargoClaimDO.getAccidentDate()).thenReturn(ZaDate.of("2022-01-10 12:00:00").oldDate().get())
        assertTrue(aicRiskRecognizer.hitPolicyZbxRisk(cargoClaimDO, ruleResult))
        when(cargoClaimDO.getAccidentDate()).thenReturn(ZaDate.of("2022-01-20 12:00:00").oldDate().get())
        assertTrue(aicRiskRecognizer.hitPolicyZbxRisk(cargoClaimDO, ruleResult))
    }

    @Test
    void testHitClaimZbxRisk() {
        def cargoClaimDO = mock(CargoClaimDO.class);
        def ruleResult = mock(ZbxIntelliRecordResult.RuleResult)
        def ruleData = mock(ZbxIntelliRecordResult.RuleData)
        when(ruleResult.getData()).thenReturn([ruleData])
        when(ruleData.getLossTime()).thenReturn("2022-02-10")

        when(aicProperties.getZbxClaimRiskNames()).thenReturn([ZbxRule.TY_YWG_01.desc])

        // invalid rule
        when(ruleResult.getRuleCode()).thenReturn("invalid_code")
        assertFalse(aicRiskRecognizer.hitClaimZbxRisk(cargoClaimDO, ruleResult))

        // non risk
        when(ruleResult.getRuleCode()).thenReturn(ZbxRule.C_ID_01.code)
        assertFalse(aicRiskRecognizer.hitClaimZbxRisk(cargoClaimDO, ruleResult))

        // risk
        when(ruleResult.getRuleCode()).thenReturn(ZbxRule.TY_YWG_01.code)

        // match
        when(cargoClaimDO.getAccidentDate()).thenReturn(ZaDate.of("2022-02-01 12:00:00").oldDate().get())
        assertTrue(aicRiskRecognizer.hitClaimZbxRisk(cargoClaimDO, ruleResult))
        when(cargoClaimDO.getAccidentDate()).thenReturn(ZaDate.of("2022-02-20 12:00:00").oldDate().get())
        assertTrue(aicRiskRecognizer.hitClaimZbxRisk(cargoClaimDO, ruleResult))

        // non match
        when(cargoClaimDO.getAccidentDate()).thenReturn(ZaDate.of("2022-01-01 12:00:00").oldDate().get())
        assertFalse(aicRiskRecognizer.hitClaimZbxRisk(cargoClaimDO, ruleResult))
        when(cargoClaimDO.getAccidentDate()).thenReturn(ZaDate.of("2022-03-20 12:00:00").oldDate().get())
        assertFalse(aicRiskRecognizer.hitClaimZbxRisk(cargoClaimDO, ruleResult))

    }

    @Test
    void testRecognizeInvoiceExpire() {
        def cargoClaimDO = new CargoClaimDO(reportNo: "CN11111")

        def billRecord1 = new BillOcrCompareRecordDO(ocrJson: "{}")
        when(billOcrCompareRecordRepository.listByReportNo(anyString())).thenReturn([billRecord1])

        when(aicProperties.getInvoiceExpireRuleExcludePackageDefIds()).thenReturn([1L])
        cargoClaimDO.packageDefId = 1L
        assertFalse(aicRiskRecognizer.recognizeInvoiceExpire(cargoClaimDO, null, null))

        cargoClaimDO.packageDefId = 2L
        // case2 就诊起期在有效期前
        def effectiveDate = ZaDate.of("2020-06-15 00:00:00").oldDate().get()
        def expiryDate = ZaDate.of("2021-06-14 23:59:59").oldDate().get()
        billRecord1.ocrJson = """{
            "patientName": "吴昊",
            "visitHospital": "西东新区中心医院",
            "visitStart": ${ZaDate.of("2020-06-14 00:00:00").epochMillis().get()},
            "visitEnd": ${ZaDate.of("2020-06-15 23:59:59").epochMillis().get()},
            "validDays": 2
        }"""
        assertTrue(aicRiskRecognizer.recognizeInvoiceExpire(cargoClaimDO, effectiveDate, expiryDate))

        // case3 就诊在有效期后
        billRecord1.ocrJson = """{
            "patientName": "吴昊",
            "visitHospital": "西东新区中心医院",
            "visitStart": ${ZaDate.of("2021-06-15 00:00:00").epochMillis().get()},
            "visitEnd": ${ZaDate.of("2021-06-15 23:59:59").epochMillis().get()},
            "validDays": 1
        }"""
        assertTrue(aicRiskRecognizer.recognizeInvoiceExpire(cargoClaimDO, effectiveDate, expiryDate))

        // case4 发票在有效期内
        billRecord1.ocrJson = """{
            "patientName": "吴昊",
            "visitHospital": "西东新区中心医院",
            "visitStart": ${ZaDate.of("2020-07-14 00:00:00").epochMillis().get()},
            "visitEnd": ${ZaDate.of("2020-07-14 23:59:59").epochMillis().get()},
            "validDays": 1
        }"""
        assertFalse(aicRiskRecognizer.recognizeInvoiceExpire(cargoClaimDO, effectiveDate, expiryDate))

        def billRecord2 = new BillOcrCompareRecordDO(ocrJson: "{}")
        when(billOcrCompareRecordRepository.listByReportNo(anyString())).thenReturn([billRecord1])
        // case5 多张发票在有效期外 1内+1外
        billRecord1.ocrJson = """{
            "patientName": "吴昊",
            "visitHospital": "西东新区中心医院",
            "visitStart": ${ZaDate.of("2020-07-14 00:00:00").epochMillis().get()},
            "visitEnd": ${ZaDate.of("2020-07-14 23:59:59").epochMillis().get()},
            "validDays": 1
        }"""
        billRecord2.ocrJson = """{
            "patientName": "吴昊",
            "visitHospital": "西东新区中心医院",
            "visitStart": ${ZaDate.of("2021-07-14 00:00:00").epochMillis().get()},
            "visitEnd": ${ZaDate.of("2021-07-14 23:59:59").epochMillis().get()},
            "validDays": 1
        }"""
        assertFalse(aicRiskRecognizer.recognizeInvoiceExpire(cargoClaimDO, effectiveDate, expiryDate))

        // case6  无发票
        when(billOcrCompareRecordRepository.listByReportNo(anyString())).thenReturn([])
        assertFalse(aicRiskRecognizer.recognizeInvoiceExpire(cargoClaimDO, effectiveDate, expiryDate))

        // case7 存在发票失败
        when(billOcrCompareRecordRepository.listByReportNo(anyString())).thenReturn([
                new BillOcrCompareRecordDO(ocrJson: "{}"),
                new BillOcrCompareRecordDO(ocrJson: """{
                    "patientName": "吴昊",
                    "visitHospital": "西东新区中心医院",
                    "visitStart": ${ZaDate.of("2020-07-14 00:00:00").epochMillis().get()},
                    "visitEnd": ${ZaDate.of("2020-07-14 23:59:59").epochMillis().get()},
                    "validDays": 1
                }""")
        ])
        assertFalse(aicRiskRecognizer.recognizeInvoiceExpire(cargoClaimDO, effectiveDate, expiryDate))

    }

    @Test
    void testNeedToConfirmFractureFee() {

        def claimDO = new CargoClaimDO(reportNo: 'CN123', accidentDate: ZaDate.of('2024-07-01 00:00:00').oldDate().get())
        when(bizProperties.getFractureLiabilityCodes()).thenReturn(['001', '002'])

        when(riskInfoRepository.findByReportNoAndRiskName(any(), eq(FRACTURE_RISK_KISS_INFO))).thenReturn([])
        assertFalse(aicRiskRecognizer.needToConfirmFractureFee(claimDO))

        when(riskInfoRepository.findByReportNoAndRiskName(any(), eq(FRACTURE_RISK_KISS_INFO))).thenReturn([
                new AutoSettlementRiskInfoDO(riskName: FRACTURE_RISK_KISS_INFO, ruleHitStatus: 'HIT', isRight: null)
        ])
        assertFalse(aicRiskRecognizer.needToConfirmFractureFee(claimDO))

        when(riskInfoRepository.findByReportNoAndRiskName(any(), eq(FRACTURE_RISK_KISS_INFO))).thenReturn([
                new AutoSettlementRiskInfoDO(riskName: FRACTURE_RISK_KISS_INFO, ruleHitStatus: 'HIT', isRight: 0)
        ])
        assertFalse(aicRiskRecognizer.needToConfirmFractureFee(claimDO))

        when(riskInfoRepository.findByReportNoAndRiskName(any(), eq(FRACTURE_RISK_KISS_INFO))).thenReturn([
                new AutoSettlementRiskInfoDO(riskName: FRACTURE_RISK_KISS_INFO, ruleHitStatus: 'MISS', isRight: 1)
        ])
        assertFalse(aicRiskRecognizer.needToConfirmFractureFee(claimDO))

        verify(liabilitySettlementRepository, never()).listByReportNo(any())
        reset(liabilitySettlementRepository)

        when(riskInfoRepository.findByReportNoAndRiskName(any(), eq(FRACTURE_RISK_KISS_INFO))).thenReturn([
                new AutoSettlementRiskInfoDO(riskName: FRACTURE_RISK_KISS_INFO, ruleHitStatus: 'HIT', isRight: 1)
        ])


        // 本案存在协议赔付
        when(liabilitySettlementRepository.listByReportNo(any())).thenReturn([
            new LiabilitySettlementDO(liabilityCode: '000', settlementType: CompensateTypeEnum.AGREEMENT_PAID.getCode())
        ])
        assertFalse(aicRiskRecognizer.needToConfirmFractureFee(claimDO))
        verify(liabilitySettlementRepository, times(1)).listByReportNo(any())

        // 本案骨折金有理算记录
        when(liabilitySettlementRepository.listByReportNo(any())).thenReturn([
                new LiabilitySettlementDO(liabilityCode: '001', settlementType: CompensateTypeEnum.REJECT_PAID.getCode())
        ])
        assertFalse(aicRiskRecognizer.needToConfirmFractureFee(claimDO))

        reset(liabilitySettlementRepository)

        when(cargoClaimRepository.listByPolicyNo(any())).thenReturn([
                claimDO,
                new CargoClaimDO(reportNo: 'CN444', accidentDate: ZaDate.of('2024-06-01 00:00:00').oldDate().get()),
                new CargoClaimDO(reportNo: 'CN222', accidentDate: ZaDate.of('2024-07-01 12:00:00').oldDate().get()),
                new CargoClaimDO(reportNo: 'CN333', accidentDate: ZaDate.of('2024-07-01 00:00:00').oldDate().get()),
        ])

        when(liabilitySettlementRepository.listByReportNo(eq('CN222'))).thenReturn([
                new LiabilitySettlementDO(liabilityCode: '000', settlementType: CompensateTypeEnum.REJECT_PAID.getCode())
        ])
        when(liabilitySettlementRepository.listByReportNo(eq('CN333'))).thenReturn([
                new LiabilitySettlementDO(liabilityCode: '001', settlementType: CompensateTypeEnum.NORMAL_PAID.getCode())
        ])

        assertFalse(aicRiskRecognizer.needToConfirmFractureFee(claimDO))
        verify(liabilitySettlementRepository, times(1)).listByReportNo(eq('CN222'))
        verify(liabilitySettlementRepository, times(1)).listByReportNo(eq('CN333'))
        verify(liabilitySettlementRepository, never()).listByReportNo(eq('CN444'))

        when(liabilitySettlementRepository.listByReportNo(eq('CN333'))).thenReturn([
                new LiabilitySettlementDO(liabilityCode: '001', settlementType: CompensateTypeEnum.REJECT_PAID.getCode())
        ])
        assertTrue(aicRiskRecognizer.needToConfirmFractureFee(claimDO))
    }
}
