package com.zhongan.lincoln.service.autosettlement

import com.zhongan.ec.util.exception.BizException
import com.zhongan.hacksaw.model.aic.dto.AicRiskRuleExecReqDTO
import com.zhongan.hacksaw.model.aic.enums.AicRiskRuleActiveStage
import com.zhongan.lincoln.component.redis.RedisOperation
import com.zhongan.lincoln.dal.domain.AutoSettlementCommonRuleDO
import com.zhongan.lincoln.dal.repository.AutoSettlementRiskInfoRepository
import com.zhongan.lincoln.dal.repository.CargoClaimRepository
import com.zhongan.lincoln.feign.NeumannClient
import com.zhongan.lincoln.manager.aic.AicPaperInvoiceManager
import com.zhongan.lincoln.manager.aic.AicRiskRuleConfManager
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.AfterEach
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import static org.mockito.Mockito.*

/**
 *
 * <AUTHOR>
 * @since 2024/5/7
 */
class AicRiskRuleServiceTest {
    @Mock
    RedisOperation redis
    @Mock
    NeumannClient neumannClient
    @Mock
    CargoClaimRepository cargoClaimRepository
    @Mock
    AicPaperInvoiceManager aicPaperInvoiceManager
    @Mock
    AicRiskRuleConfManager aicRiskRuleConfManager
    @Mock
    AutoSettlementRiskInfoRepository autoSettlementRiskInfoRepository
    @InjectMocks
    AicRiskRuleService aicRiskRuleService

    AutoCloseable _mockitoMock

    @BeforeEach
    void setUp() {
        _mockitoMock = MockitoAnnotations.openMocks(this)
    }

    @AfterEach
    void tearDown() {
        if (_mockitoMock) {
            _mockitoMock.close()
        }
    }

    @Test
    void testSaveActiveConfig() {
        doNothing().when(aicRiskRuleConfManager).saveConfig(any())
        aicRiskRuleService.saveActiveConfig(null)
        verify(aicRiskRuleConfManager, times(1)).saveConfig(any())
    }

    @Test
    void testFindAllActiveConfigs() {
        when(aicRiskRuleConfManager.findAllActiveConfigs()).thenReturn([])
        def result = aicRiskRuleService.findAllActiveConfigs()
        verify(aicRiskRuleConfManager, times(1)).findAllActiveConfigs()
        Assertions.assertIterableEquals([], result)
    }

    @Test
    void testFindByActiveConfig() {
        def rule = new AutoSettlementCommonRuleDO()
        when(aicRiskRuleConfManager.findGroupedActiveConfigs()).thenReturn([
                "1_SETTLEMENT": [rule],
                "2_CLOSE": [rule]
        ])

        Assertions.assertIterableEquals([rule], aicRiskRuleService.findByActiveConfig("1", AicRiskRuleActiveStage.SETTLEMENT))
        Assertions.assertIterableEquals([rule], aicRiskRuleService.findByActiveConfig("2", AicRiskRuleActiveStage.CLOSE))
        Assertions.assertIterableEquals([], aicRiskRuleService.findByActiveConfig("2", AicRiskRuleActiveStage.SETTLEMENT))
        Assertions.assertIterableEquals([], aicRiskRuleService.findByActiveConfig("1", AicRiskRuleActiveStage.CLOSE))
        verify(aicRiskRuleConfManager, times(4)).findGroupedActiveConfigs()

    }

    @Test
    void testExecuteRiskRuleWithLock() {
        def reqDTO = new AicRiskRuleExecReqDTO(reportNo: "CN123")

        def spyAicRiskRuleService = spy(aicRiskRuleService)
        doReturn([]).when(spyAicRiskRuleService).executeRiskRule(any())

        when(redis.releaseLock(anyString(), anyString())).thenReturn(true)

        // lock success
        when(redis.tryUnblockLock(anyString(), anyString(), anyInt())).thenReturn(true)
        Assertions.assertIterableEquals([], spyAicRiskRuleService.executeRiskRuleWithLock(reqDTO))
        verify(redis, times(1)).tryUnblockLock(anyString(), anyString(), anyInt())
        verify(redis, times(1)).releaseLock(anyString(), anyString())

        reset(redis, spyAicRiskRuleService)

        // lock fail
        when(redis.tryUnblockLock(anyString(), anyString(), anyInt())).thenReturn(false)
        Assertions.assertThrowsExactly(BizException, {
            spyAicRiskRuleService.executeRiskRuleWithLock(reqDTO)
        })
        verify(redis, times(1)).tryUnblockLock(anyString(), anyString(), anyInt())
        verify(redis, times(1)).releaseLock(anyString(), anyString())

    }

    @Test
    void testExecuteRiskRule() {
//        when(neumannClient.analyze(any(), any())).thenReturn(null);
//        when(cargoClaimRepository.findByReportNo(anyString())).thenReturn(new CargoClaimDO(insuranceTypeCode: Integer.valueOf(0)));
//        when(aicRiskRuleConfManager.findGroupedActiveConfigs()).thenReturn(new HashMap<CacheKey, List<AutoSettlementCommonRuleDO>>() {
//            {
//                put(null, Arrays.<AutoSettlementCommonRuleDO> asList(new AutoSettlementCommonRuleDO(ruleName: "ruleName", ruleCode: "ruleCode", ruleLogic: "ruleLogic")));
//            }
//        });
//
//        List<AicRiskInfoDTO> result = aicRiskRuleService.executeRiskRule(null);
//        Assertions.assertEquals(Arrays.<AicRiskInfoDTO> asList(null), result);
    }
}
