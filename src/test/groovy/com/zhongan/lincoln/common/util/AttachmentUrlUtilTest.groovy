package com.zhongan.lincoln.common.util

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

/**
 *
 * <AUTHOR>
 * @since 2024/6/12
 */
class AttachmentUrlUtilTest {

    @Test
    void testResolveFileName() {
        String result = AttachmentUrlUtil.resolveFileName("http://za-ark-cs-prd.oss-cn-hzfinance.aliyuncs.com/wo/2024-05-19/72b967796fb24a36a2ca8ff89ac35107.jpeg?Expires=**********&OSSAccessKeyId=LTAI4FgHxdynPi2oruKBwPQ3&Signature=u3qfZHMpb29hdhIfvqX6FvtFCIU%3D")
        Assertions.assertEquals("72b967796fb24a36a2ca8ff89ac35107.jpeg", result)

        result = AttachmentUrlUtil.resolveFileName("http://wjsprd.oss-cn-hzjbp-a-internal.aliyuncs.com/20240520/54f55c92-0939-407f-b14e-8a08acfa28f9.png")
        Assertions.assertEquals("54f55c92-0939-407f-b14e-8a08acfa28f9.png", result)

        result = AttachmentUrlUtil.resolveFileName("http://ecargo.zhongan.com/ecargo/downLoadExcel.do?key=20240520/54f55c92-0939-407f-b14e-8a08acfa28f9.png")
        Assertions.assertEquals("54f55c92-0939-407f-b14e-8a08acfa28f9.png", result)
    }

    @Test
    void testResolveFileType() {
        String result = AttachmentUrlUtil.resolveFileType("http://za-ark-cs-prd.oss-cn-hzfinance.aliyuncs.com/wo/2024-05-19/72b967796fb24a36a2ca8ff89ac35107.jpeg?Expires=**********&OSSAccessKeyId=LTAI4FgHxdynPi2oruKBwPQ3&Signature=u3qfZHMpb29hdhIfvqX6FvtFCIU%3D")
        Assertions.assertEquals("image", result)

        result = AttachmentUrlUtil.resolveFileType("http://wjsprd.oss-cn-hzjbp-a-internal.aliyuncs.com/20240520/54f55c92-0939-407f-b14e-8a08acfa28f9.png")
        Assertions.assertEquals("image", result)

        result = AttachmentUrlUtil.resolveFileType("http://ecargo.zhongan.com/ecargo/downLoadExcel.do?key=20240520/54f55c92-0939-407f-b14e-8a08acfa28f9.png")
        Assertions.assertEquals("image", result)
    }
}
