package com.zhongan.lincoln.manager.attachment

import com.alibaba.fastjson.JSON
import com.zhongan.hacksaw.model.dto.Result
import com.zhongan.hacksaw.model.gpt.aigc.MedicalRecordInferReqDTO
import com.zhongan.hacksaw.model.icarus.enums.ClaimEntityNature
import com.zhongan.hacksaw.model.icarus.enums.ClaimEntityType
import com.zhongan.lincoln.component.redis.RedisOperation
import com.zhongan.lincoln.config.properties.AicProperties
import com.zhongan.lincoln.config.properties.DomainProperties
import com.zhongan.lincoln.config.properties.SwitchProperties
import com.zhongan.lincoln.dal.domain.OcrResultDO
import com.zhongan.lincoln.dal.repository.AttachmentRepository
import com.zhongan.lincoln.dal.repository.CargoClaimRepository
import com.zhongan.lincoln.feign.ClaimGptClient
import com.zhongan.lincoln.feign.GalaxyClient
import com.zhongan.lincoln.feign.ImgDocClassifyClient
import com.zhongan.lincoln.feign.MedRecordExtractClient
import com.zhongan.lincoln.handler.processor.FractureRiskInfoProcessor
import com.zhongan.lincoln.manager.aic.AicCasesNerManager
import com.zhongan.lincoln.manager.external.NuanWaClaimManager
import com.zhongan.lincoln.service.biz.CargoSpiderService
import com.zhongan.lincoln.service.biz.pet.PetClaimService
import okhttp3.OkHttpClient
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.AfterEach
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.springframework.data.mongodb.core.MongoTemplate

import static org.mockito.Mockito.*
import static org.junit.jupiter.api.Assertions.*

/**
 *
 * <AUTHOR>
 * @since 2024/6/4
 */
class AttachmentOcrManagerTest {
    @Mock
    OkHttpClient HTTP_CLIENT
    @Mock
    DomainProperties domainProperties
    @Mock
    SwitchProperties switchProperties
    @Mock
    AicProperties aicProperties
    @Mock
    RedisOperation redisOperation
    @Mock
    MongoTemplate mongoTemplate
    @Mock
    AttachmentRepository attachmentRepository
    @Mock
    FileUploadManager fileUploadManager
    @Mock
    CargoClaimRepository cargoClaimRepository
    @Mock
    ImgDocClassifyClient imgDocClassifyClient
    @Mock
    MedRecordExtractClient medRecordExtractClient
    @Mock
    NuanWaClaimManager nuanWaClaimManager
    @Mock
    GalaxyClient galaxyClient
    @Mock
    PetClaimService petClaimService
    @Mock
    FractureRiskInfoProcessor fractureRiskInfoProcessor
    @Mock
    AicCasesNerManager aicCasesNerManager
    @Mock
    ClaimGptClient claimGptClient
    @Mock
    CargoSpiderService cargoSpiderService
    @InjectMocks
    AttachmentOcrManager attachmentOcrManager

    AutoCloseable _mockitoMock

    @BeforeEach
    void setUp() {
        _mockitoMock = MockitoAnnotations.openMocks(this)
    }

    @AfterEach
    void tearDown() {
        if (_mockitoMock) {
            _mockitoMock.close()
        }
    }

    @Test
    void testOcrAndNlpByGpt() {
        def mockGptResult = '''{
    "就诊起期": {
        "原始文本": "2024年05月19日20:56:42",
        "标准时间": "2024-05-19 20:56:42"
    },
    "就诊止期": {
        "原始文本": "5-20",
        "标准时间": "2024-05-20 00:00:00"
    },
    "事故时间": [
        {
            "原始文本": "右踝关节外伤疼痛不适1天",
            "标准时间": "2024-05-18 00:00:00"
        }
    ],
    "就诊医院": [
        {
            "原始文本": "华山医院",
            "标准名称": "华山医院"
        },
        {
            "原始文本": "第六人民医院",
            "标准名称": "第六人民医院"
        }
    ],
    "就诊科室": [
        {
            "原始文本": "骨科",
            "标准名称": "骨科"
        }
    ],
    "主诉": {
        "原始文本": [
            "右踝关节外伤疼痛不适1天"
        ],
        "疾病与诊断": [
            {
                "是否患有": true,
                "原始文本": "右踝关节外伤疼痛不适",
                "标准名称": "踝关节外伤"
            }
        ]
    },
    "现病史": {
        "原始文本": [
            "右踝关节疼痛不适"
        ],
        "疾病与诊断": [
            {
                "是否患有": true,
                "原始文本": "右踝关节疼痛不适",
                "标准名称": "踝关节疼痛"
            }
        ]
    },
    "诊断": {
        "原始文本": [
            "踝关节扭伤",
            "下肢韧带损伤"
        ],
        "疾病与诊断": [
            {
                "是否患有": true,
                "原始文本": "踝关节扭伤",
                "标准名称": "踝关节扭伤"
            },
            {
                "是否患有": false,
                "原始文本": "下肢韧带损伤",
                "标准名称": "下肢韧带损伤"
            }
        ]
    }
}'''

        when(switchProperties.getOpenGptMedicalRecord()).thenReturn(Boolean.TRUE)
        when(claimGptClient.inferMedicalRecord(any(), any())).thenReturn(Result.success(mockGptResult))
        when(aicProperties.getMedicalRecordInferModel()).thenReturn(MedicalRecordInferReqDTO.Model.HEHE_WENXIN)

        def resultDO = new OcrResultDO()
        attachmentOcrManager.ocrAndNlpByGpt(resultDO)
        assertEquals(mockGptResult, resultDO.gptMedicalRecordRawText)
        def gptMedicalRecord = resultDO.gptMedicalRecord
        assertNotNull(gptMedicalRecord)
        assertEquals('2024-05-19 20:56:42', gptMedicalRecord.visitStartDate)
        assertEquals('2024-05-20 00:00:00', gptMedicalRecord.visitEndDate)
        assertEquals('2024-05-18 00:00:00', gptMedicalRecord.accidentDates[0])
        assertEquals('华山医院', gptMedicalRecord.hospitalName)
        assertEquals('骨科', gptMedicalRecord.department)

        assertEquals(ClaimEntityType.DIAGNOSIS, gptMedicalRecord.chiefComplaint.entities[0].type)
        assertEquals(ClaimEntityNature.POSITIVE, gptMedicalRecord.chiefComplaint.entities[0].nature)
        assertEquals('右踝关节外伤疼痛不适', gptMedicalRecord.chiefComplaint.entities[0].content)
        assertEquals('踝关节外伤', gptMedicalRecord.chiefComplaint.entities[0].stdContent)

        assertEquals(ClaimEntityType.DIAGNOSIS, gptMedicalRecord.diagnosis.entities[0].type)
        assertEquals(ClaimEntityNature.POSITIVE, gptMedicalRecord.diagnosis.entities[0].nature)
        assertEquals('踝关节扭伤', gptMedicalRecord.diagnosis.entities[0].content)
        assertEquals('踝关节扭伤', gptMedicalRecord.diagnosis.entities[0].stdContent)

        assertEquals(ClaimEntityType.DIAGNOSIS, gptMedicalRecord.diagnosis.entities[1].type)
        assertEquals(ClaimEntityNature.NEGATIVE, gptMedicalRecord.diagnosis.entities[1].nature)
        assertEquals('下肢韧带损伤', gptMedicalRecord.diagnosis.entities[1].content)
        assertEquals('下肢韧带损伤', gptMedicalRecord.diagnosis.entities[1].stdContent)

        println JSON.toJSONString(gptMedicalRecord)
    }

}
