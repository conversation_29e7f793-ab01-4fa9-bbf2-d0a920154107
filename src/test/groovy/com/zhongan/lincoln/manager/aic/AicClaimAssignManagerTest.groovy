package com.zhongan.lincoln.manager.aic

import com.zhongan.core.policy.dto.PolicyDTO
import com.zhongan.hacksaw.model.aic.dto.AiAnalysisReportDTO
import com.zhongan.hacksaw.model.dto.Result
import com.zhongan.lincoln.config.properties.AicProperties
import com.zhongan.lincoln.config.properties.SwitchProperties
import com.zhongan.lincoln.dal.domain.BillOcrCompareRecordDO
import com.zhongan.lincoln.dal.domain.CargoClaimDO
import com.zhongan.lincoln.dal.repository.BillOcrCompareRecordRepository
import com.zhongan.lincoln.dal.repository.CargoClaimRepository
import com.zhongan.lincoln.feign.NeumannClient
import com.zhongan.lincoln.manager.core.NewPolicyManager
import com.zhongan.lincoln.service.autosettlement.AicRiskRecognizer
import com.zhongan.lincoln.service.claim.tpa.TpaAllottedClaimService
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.AfterEach
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import static org.mockito.Mockito.*

/**
 *
 * <AUTHOR>
 * @since 2024/6/18
 */
class AicClaimAssignManagerTest {
    @Mock
    NeumannClient neumann;
    @Mock
    AicProperties aicProperties
    @Mock
    SwitchProperties switchProperties
    @Mock
    NewPolicyManager newPolicyManager
    @Mock
    CargoClaimRepository claimRepository
    @Mock
    TpaAllottedClaimService tpaAllottedClaimService
    @Mock
    AicRiskRecognizer aicRiskRecognizer
    @Mock
    BillOcrCompareRecordRepository billOcrCompareRecordRepository
    @InjectMocks
    AicClaimAssignManager aicClaimAssignManager

    AutoCloseable _mockitoMock

    @BeforeEach
    void setUp() {
        _mockitoMock = MockitoAnnotations.openMocks(this)
    }

    @AfterEach
    void tearDown() {
        if (_mockitoMock) {
            _mockitoMock.close()
        }
    }

    @Test
    void testTryAssignIfHardCase() {

        def spyMgr = spy(aicClaimAssignManager)

        when(aicProperties.getHardCaseTpaIds()).thenReturn([123L])
        doReturn(true).when(tpaAllottedClaimService).tryAllot(anyString(), anyLong(), anyBoolean(), anyString())

        when(switchProperties.getOpenHardCaseAutoAssign()).thenReturn(false)
        Assertions.assertFalse(spyMgr.tryAssignIfHardCase(""))
        verify(spyMgr, never()).isHardCase(anyString())
        verify(tpaAllottedClaimService, never()).tryAllot(anyString(), anyLong(), anyBoolean(), anyString())

        reset(spyMgr, tpaAllottedClaimService)

        when(switchProperties.getOpenHardCaseAutoAssign()).thenReturn(true)
        doReturn(false).when(spyMgr).isHardCase(anyString())
        Assertions.assertFalse(spyMgr.tryAssignIfHardCase(""))
        verify(spyMgr, times(1)).isHardCase(anyString())
        verify(tpaAllottedClaimService, never()).tryAllot(anyString(), anyLong(), anyBoolean(), anyString())

        reset(spyMgr, tpaAllottedClaimService)

        when(switchProperties.getOpenHardCaseAutoAssign()).thenReturn(true)
        doReturn(true).when(spyMgr).isHardCase(anyString())
        Assertions.assertTrue(spyMgr.tryAssignIfHardCase(""))
        verify(spyMgr, times(1)).isHardCase(anyString())
        verify(tpaAllottedClaimService, times(1)).tryAllot(anyString(), eq(123L), anyBoolean(), anyString())

    }

    @Test
    void testIsHardCase() {

        when(aicProperties.getTpaMaxAcceptedAmount()).thenReturn("100")

        def analysisReportDTO = new AiAnalysisReportDTO()
        when(neumann.aiAnalysisReport(anyString())).thenReturn(Result.success(analysisReportDTO))

        def policyDTO = new PolicyDTO()
        when(newPolicyManager.findPolicy(anyString())).thenReturn(policyDTO)

        def cargoClaimDO = new CargoClaimDO(policyNo: "policyNo", reportAmount: "10")
        when(claimRepository.findByReportNo(anyString())).thenReturn(cargoClaimDO)

        when(billOcrCompareRecordRepository.listByReportNo(any())).thenReturn([
                new BillOcrCompareRecordDO(ocrJson: '{"billAmount":"10"}'),
                new BillOcrCompareRecordDO(ocrJson: '{"billAmount":"100"}'),
        ])

        // bill amount exceeded
        Assertions.assertFalse(aicClaimAssignManager.isHardCase("reportNo"))
        verify(neumann, never()).aiAnalysisReport(any())

        when(billOcrCompareRecordRepository.listByReportNo(any())).thenReturn([])
        cargoClaimDO.reportAmount = '150'
        Assertions.assertFalse(aicClaimAssignManager.isHardCase("reportNo"))
        verify(neumann, never()).aiAnalysisReport(any())

        cargoClaimDO.reportAmount = '99'

        // case1 AI建议拒赔
        analysisReportDTO.claimSuggestion = '建议拒赔'
        Assertions.assertTrue(aicClaimAssignManager.isHardCase("reportNo"))

        // case2 AI建议赔付 and hit发票就诊日期
        analysisReportDTO.claimSuggestion = '建议赔付'
        when(aicRiskRecognizer.recognizeInvoiceExpire(any(), any(), any())).thenReturn(true)
        Assertions.assertTrue(aicClaimAssignManager.isHardCase("reportNo"))

        // case4 AI建议赔付 and 发票在有效期内
        when(aicRiskRecognizer.recognizeInvoiceExpire(any(), any(), any())).thenReturn(false)
        Assertions.assertFalse(aicClaimAssignManager.isHardCase("reportNo"))

    }
}
