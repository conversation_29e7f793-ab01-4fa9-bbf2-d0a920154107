package com.zhongan.lincoln.manager.aic

import com.zhongan.hacksaw.model.aic.dto.AicRiskRuleActiveConfigSaveDTO
import com.zhongan.hacksaw.model.aic.enums.AicRiskRuleActiveStage
import com.zhongan.lincoln.dal.domain.AutoSettlementCommonRuleDO
import com.zhongan.lincoln.dal.repository.AutoSettlementCommonRuleRepository
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import static org.junit.jupiter.api.Assertions.assertNull
import static org.junit.jupiter.api.Assertions.assertSame
import static org.junit.jupiter.api.Assertions.assertTrue
import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.argThat
import static org.mockito.Mockito.*

/**
 *
 * <AUTHOR>
 * @since 2024/4/30
 */
class AicRiskRuleConfManagerTest {
    @Mock
    AutoSettlementCommonRuleRepository repository
    @InjectMocks
    AicRiskRuleConfManager aicRiskRuleConfManager

    AutoCloseable _mockitoMock

    @BeforeEach
    void setUp() {
        _mockitoMock = MockitoAnnotations.openMocks(this)
    }

    @AfterEach
    void tearDown() {
        if (_mockitoMock) {
            _mockitoMock.close()
        }
    }

    @Test
    void testSaveConfig() {
        when(repository.updateById(any())).thenReturn(1)
        when(repository.findById(any()))
                .thenReturn(new AutoSettlementCommonRuleDO(id: 123))

        aicRiskRuleConfManager.saveConfig(new AicRiskRuleActiveConfigSaveDTO(
                ruleId: 1,
                insuranceTypeCodes: ["3"],
                activeStages: [AicRiskRuleActiveStage.SETTLEMENT]
        ))

        verify(repository, times(1)).updateById(argThat { saveDO ->
            saveDO.id == 123 && saveDO.extraInfo == '{"insuranceTypeCodes":["3"],"activeStages":["SETTLEMENT"]}'
        })

    }

    @Test
    void testFindGroupedActiveConfigs() {
        when(repository.findAll()).thenReturn([
                new AutoSettlementCommonRuleDO(id: 1, extraInfo: '{"insuranceTypeCodes":["1", "3"], "activeStages":["SETTLEMENT", "CLOSE"]}'),
                new AutoSettlementCommonRuleDO(id: 2, extraInfo: '{"insuranceTypeCodes":["2", "3", "4"], "activeStages":["SETTLEMENT"]}'),
                new AutoSettlementCommonRuleDO(id: 3, extraInfo: '{"insuranceTypeCodes":["2"], "activeStages":["CLOSE"]}'),
                new AutoSettlementCommonRuleDO(id: 4),
        ])

        def result = aicRiskRuleConfManager.findGroupedActiveConfigs()

        def rule0 = result['4_CLOSE']
        assertNull(rule0)

        def rule1 = result['1_SETTLEMENT']
        assertTrue([1] == rule1.collect { it.id })

        def rule2 = result['2_SETTLEMENT']
        assertTrue([2] == rule2.collect { it.id })

        def rule3 = result['2_CLOSE']
        assertTrue([3] == rule3.collect { it.id })

        def rule4 = result['3_SETTLEMENT']
        assertTrue([1, 2] == rule4.collect { it.id })

    }

    @Test
    void testFindAllActiveConfigs() {
        when(repository.findAll()).thenReturn([
                new AutoSettlementCommonRuleDO(id: 1, extraInfo: '{"insuranceTypeCodes":["1"], "activeStages":["CLOSE"]}'),
                new AutoSettlementCommonRuleDO(id: 2),
                new AutoSettlementCommonRuleDO(id: 3, extraInfo: '{"insuranceTypeCodes":["1"]}'),
        ])

        def configs = aicRiskRuleConfManager.findAllActiveConfigs()
        assertSame(3, configs.size())
        assertTrue(configs[0].ruleId == 1)
        assertTrue(configs[0].insuranceTypeCodes == ['1'])
        assertTrue(configs[0].activeStages == [AicRiskRuleActiveStage.CLOSE])
        assertTrue(configs[1].ruleId == 2)
        assertTrue(configs[1].insuranceTypeCodes == [])
        assertTrue(configs[1].activeStages == [])
        assertTrue(configs[2].ruleId == 3)
        assertTrue(configs[2].insuranceTypeCodes == ['1'])
        assertTrue(configs[2].activeStages == [])
    }
}
