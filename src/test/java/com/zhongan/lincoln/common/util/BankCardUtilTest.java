package com.zhongan.lincoln.common.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 * @since 2022/7/21
 */
public class BankCardUtilTest {

    @Test
    public void validate() {

        assertFalse(BankCardUtil.validate(null));
        assertFalse(BankCardUtil.validate(""));
        assertFalse(BankCardUtil.validate("   "));
        assertFalse(BankCardUtil.validate("********* *********"));
        assertFalse(BankCardUtil.validate("****************"));
        assertFalse(BankCardUtil.validate("****************01X"));
        assertFalse(BankCardUtil.validate("12345678909876532101"));

        assertTrue(BankCardUtil.validate("****************018"));
        assertFalse(BankCardUtil.validate("****************011"));
        assertTrue(BankCardUtil.validate("*********2637874213"));
        assertFalse(BankCardUtil.validate("*********2637874214"));

    }
}