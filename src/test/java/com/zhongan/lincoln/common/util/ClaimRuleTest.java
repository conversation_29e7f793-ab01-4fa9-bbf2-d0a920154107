package com.zhongan.lincoln.common.util;

import com.alibaba.fastjson.JSON;
import com.zhongan.lincoln.TestTags;
import com.zhongan.lincoln.common.enums.visual.ConfigAttributeEnum;
import com.zhongan.lincoln.common.enums.visual.ConfigEnum;
import com.zhongan.lincoln.model.dto.config.visual.def.ConfigAttributeDefDTO;
import com.zhongan.lincoln.model.dto.config.visual.def.ConfigDefDTO;
import com.zhongan.lincoln.model.dto.config.visual.rule.ClaimRuleDTO;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/8/15 11:59
 */
@Tag(TestTags.TEMPORARY)
public class ClaimRuleTest {

    @Test
    public void test() {
        List<ConfigDefDTO> claimRuleDefs = new ArrayList<>();
        Field[] rules = ClaimRuleDTO.class.getDeclaredFields();
        for (Field rule : rules) {
            ConfigEnum configEnum = rule.getAnnotation(ConfigEnum.class);
            if (configEnum != null) {
                Field[] ruleAttributes = configEnum.clazz().getDeclaredFields();
                List<ConfigAttributeDefDTO> claimRuleAttributeDefs = Arrays.asList(ruleAttributes).stream()
                        .map(ruleAttribute -> ruleAttribute.getAnnotation(ConfigAttributeEnum.class))
                        .filter(Objects::nonNull)
                        .map(ConfigAttributeDefDTO::by)
                        .collect(Collectors.toList());
                ConfigDefDTO claimRuleDef = new ConfigDefDTO(configEnum.code(), configEnum.name(),
                        configEnum.desc(), configEnum.type(), claimRuleAttributeDefs);
                claimRuleDefs.add(claimRuleDef);
            }
        }
        System.out.println(JSON.toJSONString(claimRuleDefs));
    }
}
