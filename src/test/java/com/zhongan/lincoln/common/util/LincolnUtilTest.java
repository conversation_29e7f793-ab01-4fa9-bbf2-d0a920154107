package com.zhongan.lincoln.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhongan.lincoln.TestTags;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import static com.zhongan.lincoln.common.util.LincolnUtil.deepParseJSON;

/**
 * <AUTHOR>
 * @since 2022/8/24 15:26
 */
@Tag(TestTags.TEMPORARY)
public class LincolnUtilTest {

    @Test
    public void testDeepParseJSON() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", 1233L);
        System.out.println(JSON.toJSONString(jsonObject));
        System.out.println(JSON.toJSONString(JSON.toJSONString(jsonObject)));
        System.out.println(JSON.toJSONString(JSON.toJSONString(JSON.toJSONString(jsonObject))));
        System.out.println(JSON.parse(JSON.toJSONString(JSON.toJSONString(JSON.toJSONString(jsonObject)))));
        System.out.println(JSON.parse(JSON.toJSONString(JSON.toJSONString(jsonObject))));
        System.out.println(JSON.parse(JSON.toJSONString(JSON.toJSONString(jsonObject))) instanceof String);
        System.out.println(JSON.parse(JSON.toJSONString(jsonObject)) instanceof String);

        System.out.println(deepParseJSON("\"{\\\"id\\\":1233}\""));
        System.out.println(deepParseJSON(null));
        System.out.println(deepParseJSON(jsonObject));
        System.out.println(deepParseJSON("{\"id\":1233}"));
        System.out.println(deepParseJSON("null"));
        System.out.println(deepParseJSON(null));
        System.out.println(deepParseJSON(new JSONObject()));
        System.out.println(deepParseJSON("{}"));
        System.out.println(deepParseJSON("\"null\""));
        System.out.println(deepParseJSON("\"\\\"{}\\\"\""));
    }
}
