package com.zhongan.lincoln.common.util;

import com.alibaba.fastjson.JSONObject;
import com.zhongan.lincoln.TestTags;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * 功能描述：
 *
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/2/10 15:04
 */
@Tag(TestTags.TEMPORARY)
public class IdCardUtilTest {


    @Test
    public void validate(){

        JSONObject param=new JSONObject();
        param.put("auditResult", "本次为协议赔付5000元，理赔结论不作为后续任何理赔案件的理赔依据。本次理赔赔付后，不再承担仕均龙(身份证号： 371122198611076616）的包含本次事故的其他任何保险责任并解,370402199801028353除保单不退还保费");
        String jsonObject= param.toJSONString();
        System.out.println("jsonObject:"+jsonObject);
        List<String> pickUpCardNo = IdcardUtils.pickUpCardNo(jsonObject);
        if (CollectionUtils.isEmpty(pickUpCardNo)){
            return ;
        }

        for (String cardNo:pickUpCardNo){
           jsonObject= jsonObject.replaceAll(cardNo,IdcardUtils.idMask(cardNo,6,4));
            System.out.println("codeCertNo:"+cardNo);
        }

        System.out.printf("result"+jsonObject);
    }
}
