package com.zhongan.lincoln;


import com.zhongan.hacksaw.model.common.enums.BizDynamicConfigEnum;
import com.zhongan.lincoln.dal.mongo.AntClaimBodyJpaRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static com.zhongan.hacksaw.model.common.enums.BizDynamicConfigEnum.DJI_THIRDPARTY_SPECIAL_ASSIGN;

/**
 * Unit test for simple App.
 */
@Slf4j
//@Tag(TestTags.SPRING)
//@SpringBootTest(properties = "DEPLOY_ENV=test")
public class ApplicationTest {

    @Resource
    private AntClaimBodyJpaRepository antClaimBodyJpaRepository;

    @Test
    public void shouldAnswerWithTrue() {
        System.out.println(BizDynamicConfigEnum.convertToSql(DJI_THIRDPARTY_SPECIAL_ASSIGN));
    }
}
