package com.zhongan.lincoln.manager.biz;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zhongan.ec.util.AssertUtil;
import com.zhongan.ec.util.DateUtil;
import com.zhongan.ec.util.HttpHelper;
import com.zhongan.lincoln.TestTags;
import com.zhongan.lincoln.model.dto.pet.PetInsuredDTO;
import com.zhongan.lincoln.model.dto.pet.PetSyncInsuranceDTO;
import com.zhongan.lincoln.model.request.XrpAddCrmInsuranceReq;
import com.zhongan.lincoln.model.request.XrpAddCrmUserPetReq;
import com.zhongan.lincoln.model.request.XrpAddCrmUserPetResp;
import com.zhongan.lincoln.model.request.XrpResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.zhongan.lincoln.common.util.MD5Util.Md5;

/**
 * <AUTHOR>
 * @since 2023/7/18 10:53
 */
@Slf4j
@Tag(TestTags.TEMPORARY)
public class PetConsultationDataManagerTest {

    private static final String URL_addCrmUserPetInsurance = "/scrm-organization-api/user/insurance/addScrmUserPetInsurance";
    private static final String URL_addCrmInsurance = "/scrm-organization-api/user/insurance/addScrmInsurance";

    public void xrpAddCrmInsurance() {
        String userName = "李民";
        String userMobile = "18615166779";
        String policyNo = "PI05576230458243448682";
        String productName = "宠物医疗险（方案一）";
        Date effectiveDate = DateUtil.parse("2023-04-05 00:00:00", DateUtil.DATE_PATTERN_LONG);
        Date expiryDate = DateUtil.parse("2024-04-04 23:59:59", DateUtil.DATE_PATTERN_LONG);

        PetInsuredDTO petInsuredDTO = new PetInsuredDTO();
        petInsuredDTO.setName("果冻");
        petInsuredDTO.setBirthday(DateUtil.addDay(new Date(), -365));
        PetSyncInsuranceDTO petSyncInsuranceDTO = new PetSyncInsuranceDTO();
        petSyncInsuranceDTO.setUserName(userName);
        petSyncInsuranceDTO.setUserMobile(userMobile);
        XrpAddCrmUserPetResp addUserPetResp = xrpAddCrmUserPet(petSyncInsuranceDTO, petInsuredDTO);

        XrpAddCrmInsuranceReq addInsuranceReq = new XrpAddCrmInsuranceReq()
                .withUser(addUserPetResp.getUserId(), userName, userMobile)
                .withPet(addUserPetResp.getPets().get(0).getPetId(), petInsuredDTO);

        addInsuranceReq.setInsuranceName(productName);
        addInsuranceReq.setInsuranceCompany("众安在线财产保险股份有限公司");
        addInsuranceReq.setInsuranceNumber(policyNo);
        addInsuranceReq.setStartTime(effectiveDate);
        addInsuranceReq.setEndTime(expiryDate);
        addInsuranceReq.setType("BS");
        addInsuranceReq.setCode("ZABX");
        JSONObject resp = HttpHelper.httpPost("https://erp-zuul-outside.rp-field.com" + URL_addCrmInsurance,
                addInsuranceReq, buildRequestHeader(URL_addCrmInsurance));
        XrpResp<String> addInsuranceResp = JSONObject.parseObject(resp.toJSONString(), new TypeReference<XrpResp<String>>() {
        });
        AssertUtil.assertTrue(addInsuranceResp.getSuccess(), addInsuranceResp.getMessage());
    }

    /**
     * 新瑞鹏绑定用户、宠物
     */
    private XrpAddCrmUserPetResp xrpAddCrmUserPet(PetSyncInsuranceDTO dto, PetInsuredDTO petInsuredDTO) {
        XrpAddCrmUserPetReq xrpAddCrmUserPetReq = new XrpAddCrmUserPetReq().setUserName(dto.getUserName());
        xrpAddCrmUserPetReq.setUserMobile(dto.getUserMobile());
        xrpAddCrmUserPetReq.setPets(Arrays.asList(XrpAddCrmUserPetReq.convert(petInsuredDTO)));
        JSONObject resp = HttpHelper.httpPost("https://erp-zuul-outside.rp-field.com" + URL_addCrmUserPetInsurance,
                xrpAddCrmUserPetReq, buildRequestHeader(URL_addCrmUserPetInsurance));
        XrpResp<XrpAddCrmUserPetResp> addUserPetResp = JSONObject.parseObject(resp.toJSONString(),
                new TypeReference<XrpResp<XrpAddCrmUserPetResp>>() {
                });
        AssertUtil.assertTrue(addUserPetResp.getSuccess(), addUserPetResp.getMessage());
        return addUserPetResp.getResult();
    }

    /**
     * 构建新瑞鹏接口请求头
     */
    private Map<String, String> buildRequestHeader(String requestUrl) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("focus-auth-appid", "insure_zabx");
        headerMap.put("focus-auth-timestamp", String.valueOf(System.currentTimeMillis()));
        headerMap.put("focus-auth-version", "1");
        headerMap.put("focus-auth-url", requestUrl);
        headerMap.put("focus-auth-userid", "yunchong");
        headerMap.put("focus-auth-username", "yunchong");
        headerMap.put("focus-auth-sign", xrpSign(headerMap, "20mpp8ktmh9h94gl"));
        return headerMap;
    }

    /**
     * 新瑞鹏接口签名
     */
    private String xrpSign(Map<String, String> maps, String secret) {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("AppId=").append(maps.get("focus-auth-appid"))
                .append("&Secret=").append(secret)
                .append("&Url=").append(maps.get("focus-auth-url"))
                .append("&Timestamp=").append(maps.get("focus-auth-timestamp"))
                .append("&Version=").append(maps.get("focus-auth-version"));
        log.info("get xinruipeng stringBuffer:{}, sign:{}", stringBuffer, Md5(stringBuffer.toString()));
        return Md5(stringBuffer.toString()).toUpperCase();
    }
}
