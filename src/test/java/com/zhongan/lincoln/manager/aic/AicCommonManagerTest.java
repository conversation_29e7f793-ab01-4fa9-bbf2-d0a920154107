package com.zhongan.lincoln.manager.aic;

import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.Lists;
import com.zhongan.core.cdc.common.PageDTO;
import com.zhongan.core.cdc.dto.ClaimDTO;
import com.zhongan.core.cdc.service.ClaimQueryService;
import com.zhongan.ec.util.AssertUtil;
import com.zhongan.ec.util.DateUtil;
import com.zhongan.lincoln.dal.domain.CargoClaimDO;
import com.zhongan.lincoln.dal.repository.CargoClaimRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Sets;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @since 2024/07/16 11:11
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
public class AicCommonManagerTest {

    @InjectMocks
    private AicCommonManager aicCommonManager;

    @Mock
    private CargoClaimRepository cargoClaimRepository;

    @Mock
    private ClaimQueryService claimQueryService;

    @Test
    public void testCountInsurantCdcClosedClaimsLastNDays() {
        Date startDate = DateUtil.parse("2024-07-01");
        Date endDate = DateUtil.parse("2024-07-16");
        long diffInMillies = Math.abs(endDate.getTime() - startDate.getTime());

        PageDTO<ClaimDTO> pageDTO = new PageDTO<>();
        Set<String> dateStrSet = Sets.newHashSet();
        List<ClaimDTO> rows = Lists.newArrayList();
        for (int i = 0; i < 10; i++) {
            ClaimDTO claimDTO = new ClaimDTO();
            Date date = new Date(startDate.getTime() + RandomUtil.randomLong(0, diffInMillies));
            claimDTO.setAccidentDate(date);
            rows.add(claimDTO);

            dateStrSet.add(DateUtil.format(date, DateUtil.DATE_COMPRESS_PATTERN));
        }
        pageDTO.setRows(rows);

        when(claimQueryService.queryClaimList(any(), any())).thenReturn(pageDTO);

        int result = aicCommonManager.countInsurantCdcClosedClaimsLastNDays("insurantCentNo", 1);
        AssertUtil.assertExpression(result == dateStrSet.size(), "result should be equal to expect");
        log.info("testCountInsurantCdcClosedClaimsLastNDays success!");
    }


    @Test
    public void testCountPolicyClosedClaimsAllDays(){

        Pair<List<CargoClaimDO>, Integer> pair = buildCargoClaimDOList();
        List<CargoClaimDO> cargoClaimDOList = pair.getLeft();
        Integer expect = pair.getRight();

        when(cargoClaimRepository.listByQuery(any())).thenReturn(cargoClaimDOList);

        int result = aicCommonManager.countPolicyClosedClaimsAllDays("PI1572Y200400035000213");
        AssertUtil.assertExpression(result == expect, "result should be equal to expect");
        log.info("testCountPolicyClosedClaimsAllDays success!");
    }


    @Test
    public void testCountPolicyClosedClaimsLastNDays(){
        Pair<List<CargoClaimDO>, Integer> pair = buildCargoClaimDOList();
        List<CargoClaimDO> cargoClaimDOList = pair.getLeft();
        Integer expect = pair.getRight();

        when(cargoClaimRepository.listByPolicyNoAndStatusListAndCloseDateBegin(any(),any(),any())).thenReturn(cargoClaimDOList);

        int result = aicCommonManager.countPolicyClosedClaimsLastNDays("PI1572Y200400035000213",30);
        AssertUtil.assertExpression(result == expect, "result should be equal to expect");
        log.info("testCountPolicyClosedClaimsLastNDays success!");
    }


    private Pair<List<CargoClaimDO>,Integer> buildCargoClaimDOList(){

        Date startDate = DateUtil.parse("2024-07-01");
        Date endDate = DateUtil.parse("2024-07-16");
        long diffInMillies = Math.abs(endDate.getTime() - startDate.getTime());

        List<CargoClaimDO> cargoClaimDOList = Lists.newArrayList();
        Set<String> dateStrSet = Sets.newHashSet();
        for (int i = 0; i < 10; i++) {
            CargoClaimDO cargoClaimDO = new CargoClaimDO();
            Date date = new Date(startDate.getTime() + RandomUtil.randomLong(0, diffInMillies));
            cargoClaimDO.setAccidentDate(date);
            cargoClaimDOList.add(cargoClaimDO);

            dateStrSet.add(DateUtil.format(date, DateUtil.DATE_COMPRESS_PATTERN));
        }
        return Pair.of(cargoClaimDOList,dateStrSet.size());
    }

}


