package com.zhongan.lincoln.manager.aic;

import com.alibaba.fastjson.JSON;
import com.google.common.io.Files;
import com.zhongan.lincoln.TestTags;
import com.zhongan.lincoln.dal.domain.OcrResultDO;
import com.zhongan.lincoln.model.dto.autosettlement.DiagnosisDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.nio.charset.Charset;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;


/**
 * <AUTHOR>
 * @since 2023/6/5
 */
@Tag(TestTags.TEMPORARY)
class AicCasesNerManagerTest {
    AicCasesNerManager aicCasesNerManager;

    @BeforeEach
    void setUp() {
        aicCasesNerManager = new AicCasesNerManager();
    }

    @Test
    public void testParseDiagnosis() throws Exception {
        File testFile = ResourceUtils.getFile("classpath:testfile/test_in.json");
        String json = String.join("", Files.readLines(testFile, Charset.defaultCharset()));
        OcrResultDO ocrResultDO = JSON.parseObject(json, OcrResultDO.class);
        List<DiagnosisDTO> result = aicCasesNerManager.parseDiagnosis(ocrResultDO);
        assertSame(1, result.size());
        assertEquals("新型冠状病毒感染", result.get(0).getStd_name());

        testFile = ResourceUtils.getFile("classpath:testfile/test_out.json");
        json = String.join("", Files.readLines(testFile, Charset.defaultCharset()));
        ocrResultDO = JSON.parseObject(json, OcrResultDO.class);
        result = aicCasesNerManager.parseDiagnosis(ocrResultDO);
        assertSame(2, result.size());
        assertEquals("腰椎间盘突出", result.get(0).getStd_name());
        assertEquals("骨质增生", result.get(1).getStd_name());
    }
}
