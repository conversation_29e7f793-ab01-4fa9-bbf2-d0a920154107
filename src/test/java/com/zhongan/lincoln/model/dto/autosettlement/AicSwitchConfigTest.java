package com.zhongan.lincoln.model.dto.autosettlement;

import com.zhongan.lincoln.common.enums.autosettlement.AicEventEnum;
import org.junit.jupiter.api.Test;

import java.util.EnumMap;
import java.util.concurrent.ThreadLocalRandom;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 * @since 2023/5/5
 */
public class AicSwitchConfigTest {

    static final int SAMPLE_SIZE = 20;

    @Test
    public void testNull() {
        AicSwitchConfig config = AicSwitchConfig.builder().build();
        assertFalse(config.isOpen(null));
        assertFalse(config.isClose(null));
    }

    @Test
    public void testAllOpen() throws Exception {
        AicSwitchConfig config = AicSwitchConfig.allOpen();
        for (AicEventEnum event : AicEventEnum.values()) {
            assertOpen(config, event);
        }
    }

    @Test
    public void testAllClose() throws Exception {
        AicSwitchConfig config = AicSwitchConfig.allClose();
        for (AicEventEnum event : AicEventEnum.values()) {
            assertClose(config, event);
        }
    }

    @Test
    public void testDefault() throws Exception {
        AicSwitchConfig config = AicSwitchConfig.builder().build();
        for (AicEventEnum event : AicEventEnum.values()) {
            assertClose(config, event);
        }
    }

    @Test
    public void testIsClose() throws Exception {
        AicSwitchConfig.Builder builder = AicSwitchConfig.builder();
        EnumMap<AicEventEnum, Boolean> enumMap = randomEvents();

        enumMap.forEach((k, v) -> {
            if (v) {
                builder.open(k);
            } else {
                builder.close(k);
            }
        });

        AicSwitchConfig config = builder.build();

        for (AicEventEnum event : AicEventEnum.values()) {
            if (enumMap.containsKey(event)) {
                if (enumMap.get(event)) {
                    assertOpen(config, event);
                } else {
                    assertClose(config, event);
                }
            } else {
                assertClose(config, event);
            }
        }
    }

    static void assertOpen(AicSwitchConfig config, AicEventEnum event) {
        assertFalse(config.isClose(event));
        assertTrue(config.isOpen(event));
    }

    static void assertClose(AicSwitchConfig config, AicEventEnum event) {
        assertTrue(config.isClose(event));
        assertFalse(config.isOpen(event));
    }

    static EnumMap<AicEventEnum, Boolean> randomEvents() {
        EnumMap<AicEventEnum, Boolean> enumMap = new EnumMap<>(AicEventEnum.class);
        for (int i = 0; i < SAMPLE_SIZE; i++) {
            int idx = ThreadLocalRandom.current().nextInt(AicEventEnum.values().length);
            enumMap.put(AicEventEnum.values()[idx], ThreadLocalRandom.current().nextBoolean());
        }
        return enumMap;
    }

}