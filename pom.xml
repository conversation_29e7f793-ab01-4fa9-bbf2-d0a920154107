<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.zhongan.sky</groupId>
        <artifactId>za-sky-parent</artifactId>
        <version>2.7.x.v20240630.RELEASE</version>
    </parent>
    <artifactId>za-lincoln</artifactId>
    <version>1.0.0</version>

    <properties>
        <project.name>za-lincoln</project.name>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>
        <spring-boot.version>2.7.15</spring-boot.version>
        <spring-cloud.version>Greenwich.SR6</spring-cloud.version>
        <hacksaw-model.version>2.10.32-SNAPSHOT</hacksaw-model.version>
        <ec-util.version>1.1.4-SNAPSHOT</ec-util.version>
        <mybatis.version>3.5.7</mybatis.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zhongan.sky</groupId>
            <artifactId>next-framework-starter-service</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.zhongan.sky</groupId>
                    <artifactId>next-boot-starter-nacos-config</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.zhongan.sky</groupId>
                    <artifactId>next-boot-starter-swagger</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-config-client</artifactId>
        </dependency>

        <!-- 三方包-->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
            <exclusions>
                <exclusion>
                    <groupId>dom4j</groupId>
                    <artifactId>dom4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.0.5</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- websocket -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!--external sdk-->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.33.1.ALL</version>
        </dependency>
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-easysdk</artifactId>
            <version>2.2.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
        </dependency>
        <!--缩略图-->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.8</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.14</version>
        </dependency>
        <dependency>
            <groupId>com.netflix.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>1.3.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-words</artifactId>
            <scope>system</scope>
            <version>15.8.0</version>
            <systemPath>${project.basedir}/src/main/resources/lib/aspose-words-15.8.0-jdk16.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.23</version>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.8</version>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.21.0-GA</version>
        </dependency>

        <!--HttpClient-->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>

        <!-- Redis -->
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-aliyun-redis-client</artifactId>
            <version>0.0.1</version>
        </dependency>

        <!-- OSS -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <!-- ONS -->
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
            <version>1.8.0.Final</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-openservices</artifactId>
            <version>1.2.3</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-api</artifactId>
            <version>1.8.0.Final</version>
        </dependency>

        <!-- Neptune -->
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>neptune-client</artifactId>
            <version>1.1.5</version>
        </dependency>

        <!--  taobao tddl, dependency on diamond, log4j start  -->
        <dependency>
            <groupId>com.taobao.tddl</groupId>
            <artifactId>tddl-client</artifactId>
            <version>3.3.2.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.taobao.diamond</groupId>
                    <artifactId>diamond-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.taobao.tddl</groupId>
                    <artifactId>tddl-sequence</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-all</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>junit</artifactId>
                    <groupId>junit</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taobao.tddl</groupId>
            <artifactId>tddl-sequence</artifactId>
            <version>3.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.taobao.tddl</groupId>
                    <artifactId>tddl-group-datasource</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taobao.diamond</groupId>
            <artifactId>diamond-client</artifactId>
            <version>3.6.8</version>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.14</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
        </dependency>
        <!--  taobao tddl, dependency on diamond, log4j end  -->

        <!-- Data Access -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.1.22</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>${mybatis.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>2.0.5</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>1.3.6</version>
        </dependency>
        <dependency>
            <groupId>com.itfsw</groupId>
            <artifactId>mybatis-generator-plugin</artifactId>
            <version>1.3.8</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.49</version>
        </dependency>

        <!--swagger-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>3.0.0</version>
        </dependency>

        <!--由原来的itext5.x升级为openPDF-1.3.28.1(内部版本)-->
        <dependency>
            <groupId>com.github.librepdf</groupId>
            <artifactId>openpdf</artifactId>
            <version>1.3.28.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>bcprov-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bcpkix-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-words</artifactId>
            <version>15.8.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.23</version>
        </dependency>
        <!--读取文件-->
        <dependency>
            <groupId>com.drewnoakes</groupId>
            <artifactId>metadata-extractor</artifactId>
            <version>2.16.0</version>
        </dependency>

        <!-- validation -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <!-- validation EL表达式支持 -->
        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>jakarta.el</artifactId>
        </dependency>
        <!--限流器-->
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-ratelimiter</artifactId>
        </dependency>
        <!--淘宝开放平台sdk-->
        <dependency>
            <groupId>com.taobao</groupId>
            <artifactId>taobao-sdk-java-auto</artifactId>
            <version>20210415</version>
        </dependency>
        <!--jsonpath-->
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.7.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--JWT(Json Web Token)登录支持-->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>

        <!-- zookeeper -->
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>3.9.1</version>
        </dependency>

        <!-- common util -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.17.0</version>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency> <!-- 用于logback.xml中使用 if condition 判断 -->
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>
        <dependency> <!-- for local cached -->
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId> <!--for ThreadLocal 线程间传递-->
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.10.1</version>
        </dependency>

        <!--zhongan share-->
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>ec-util</artifactId>
            <version>${ec-util.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-hacksaw-model</artifactId>
            <version>${hacksaw-model.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-user-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-cdc-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.core</groupId>
            <artifactId>cdc-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-permission-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-claim-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-policy-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-doc-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-product-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>dataapi.sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-fcp-feignclient-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-newclaim-wrapper-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.core</groupId>
            <artifactId>cdc-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.core</groupId>
            <artifactId>za-core-cloud-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-newpolicy-wrapper-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-newendorsement-wrapper-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-core-enum-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-digital-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-hrcutils-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-cmds-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-ecms-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-group-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-gateway-common-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-talos-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>jobkeeper</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>dataapi.sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-login-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-staff-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-blacklist-center-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-bridge-share</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-xdecision-turbo-share</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-sso-client-share</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>junit</artifactId>
                    <groupId>junit</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-msg-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>sign-juhe-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>junit</artifactId>
                    <groupId>junit</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-newcmds-wrapper-share</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>za-edict-share</artifactId>
            <version>1.0.6</version>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.5.1</version>
            <scope>test</scope>
        </dependency>

        <!-- SQL to mongo query -->
        <dependency>
            <groupId>com.github.vincentrussell</groupId>
            <artifactId>sql-to-mongo-db-query-converter</artifactId>
            <version>1.22</version>
            <exclusions>
                <exclusion>
                    <artifactId>mongodb-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bson</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-codec</artifactId>
                    <groupId>commons-codec</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>13.0</version>
        </dependency>

        <dependency>
            <groupId>com.zhongan.sky</groupId>
            <artifactId>sky-agent-metrics</artifactId>
            <version>1.0.12</version>
        </dependency>

        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv-platform</artifactId>
            <version>1.5.11</version>
        </dependency>
        <!--直营获取短链-->
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>dm-instrument-share</artifactId>
            <version>2.1.19-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>ec-superman-doc-share</artifactId>
            <version>1.3.22</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-user-share</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.zhongan</groupId>
                        <artifactId>za-dispatcher-share</artifactId>
                    </exclusion>
                </exclusions>
                <version>vpc-1.1.18</version>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-cdc-share</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.zhongan.core</groupId>
                <artifactId>cdc-share</artifactId>
                <version>1.1.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--        <dependency>-->
            <!--            <groupId>com.zhongan</groupId>-->
            <!--            <artifactId>za-workorder-center-share</artifactId>-->
            <!--            <version>1.2.16</version>-->
            <!--        </dependency>-->
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-permission-share</artifactId>
                <version>0.1.9</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-claim-share</artifactId>
                <version>1.1.5</version>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-policy-share</artifactId>
                <version>1.0.578</version>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-doc-share</artifactId>
                <version>1.0.389</version>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-product-share</artifactId>
                <version>1.0.361</version>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>dataapi.sdk</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-fcp-feignclient-share</artifactId>
                <version>1.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-ribbon</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-feign</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>za-fcp-common-share</artifactId>
                        <groupId>com.zhongan</groupId>
                    </exclusion>
                    <!-- <exclusion>
                        <artifactId>commons-httpclient</artifactId>
                        <groupId>commons-httpclient</groupId>
                    </exclusion> -->
                    <exclusion>
                        <artifactId>tair-client</artifactId>
                        <groupId>com.taobao.tair</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-newclaim-wrapper-share</artifactId>
                <version>1.2.35</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan.core</groupId>
                <artifactId>cdc-share</artifactId>
                <version>1.1.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan.core</groupId>
                <artifactId>za-core-cloud-common</artifactId>
                <version>2.0.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.diamond</groupId>
                        <artifactId>diamond-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-cloud-starter-feign</artifactId>
                        <groupId>org.springframework.cloud</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis-spring</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>swagger-annotations</artifactId>
                        <groupId>io.swagger</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>2.4.17</version>
            </dependency>

            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-newpolicy-wrapper-share</artifactId>
                <version>1.1.159</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-newendorsement-wrapper-share</artifactId>
                <version>1.0.296</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-core-enum-share</artifactId>
                <version>1.9.85</version>
                <exclusions>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>*</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-digital-share</artifactId>
                <version>1.0.418</version>
                <exclusions>
                    <exclusion>
                        <artifactId>bcpkix-jdk15on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis-spring</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-hrcutils-share</artifactId>
                <version>1.0.15</version>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-cmds-share</artifactId>
                <version>2.2.3</version>
            </dependency>
            <!--ecms 费控系统-->
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-ecms-share</artifactId>
                <version>1.1.26</version>
                <exclusions>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>*</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-group-share</artifactId>
                <version>1.0.111</version>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-gateway-common-share</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-talos-share</artifactId>
                <version>1.1.012</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.github.openfeign.form</groupId>
                        <artifactId>feign-form</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.github.openfeign.form</groupId>
                        <artifactId>feign-form-spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk16</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-feign</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-netflix-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.zhongan</groupId>
                        <artifactId>za-hclaim-remote-share</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>jobkeeper</artifactId>
                <version>1.0.3</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.zookeeper</groupId>
                        <artifactId>zookeeper</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>3.5.3-beta</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-reload4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>dataapi.sdk</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-login-share</artifactId>
                <version>1.1.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>jboss</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-reload4j</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-staff-share</artifactId>
                <version>1.0.16</version>
            </dependency>
            <!--core blacklist 二方包-->
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-blacklist-center-share</artifactId>
                <version>1.0.9</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-openfeign</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.github.openfeign.form</groupId>
                        <artifactId>feign-form</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>jul-to-slf4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-properties-migrator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-bridge-share</artifactId>
                <version>1.0.68</version>
            </dependency>
            <!--决策系统share-->
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-xdecision-turbo-share</artifactId>
                <version>1.1.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>groovy-all</artifactId>
                        <groupId>org.codehaus.groovy</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.github.openfeign</groupId>
                        <artifactId>feign-gson</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-cloud-starter-feign</artifactId>
                        <groupId>org.springframework.cloud</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- SSO -->
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-sso-client-share</artifactId>
                <version>2.0.6</version>
            </dependency>

            <!-- kafka-->
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>za-msg-kafka</artifactId>
                <version>1.0.2</version>
            </dependency>

            <!--最福利电签SDK-->
            <dependency>
                <groupId>com.zhongan</groupId>
                <artifactId>sign-juhe-sdk</artifactId>
                <version>1.1.3-custom</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-reload4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <dependencies>
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter</artifactId>
                        <version>5.8.2</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <excludedGroups>spring, temporary</excludedGroups>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <mainClass>com.zhongan.lincoln.Application</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>