FROM base-registry.zhonganinfo.com/env/jdk8:tac-alpine
MAINTAINER <EMAIL>
WORKDIR /root/startup/
COPY ./target/za-lincoln.jar ./
EXPOSE 8588 8080
# CMD java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8788 -jar -Xmx512m -XX:+TieredCompilation -XX:+DisableExplicitGC -XX:+UseParNewGC -XX:+UseConcMarkSweepGC -XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -Xloggc:/alidata1/admin/za-lincoln/logs/gc.log -XX:+UseGCLogFileRotation -XX:GCLogFileSize=1M -XX:NumberOfGCLogFiles=50 za-lincoln.jar
CMD ["java","-jar","-Xmx512m","-XX:+TieredCompilation","-XX:+DisableExplicitGC","-XX:+UseParNewGC","-XX:+UseConcMarkSweepGC","-XX:+PrintGC","-XX:+PrintGCDetails","-XX:+PrintGCDateStamps","-XX:+PrintGCTimeStamps","-Xloggc:/alidata1/admin/za-lincoln/logs/gc.log","-XX:+UseGCLogFileRotation","-XX:GCLogFileSize=1M","-XX:NumberOfGCLogFiles=50","za-lincoln.jar"]